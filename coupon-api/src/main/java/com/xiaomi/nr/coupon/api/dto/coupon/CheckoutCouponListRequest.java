package com.xiaomi.nr.coupon.api.dto.coupon;


import com.google.common.collect.Lists;
import com.xiaomi.dubbo.validation.annotation.Mid;
import com.xiaomi.nr.coupon.api.dto.BaseRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.checkout.GoodsInfo;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 结算页用户优惠劵列表请求参数
 *
 * <AUTHOR>
 */
@Data
public class CheckoutCouponListRequest extends BaseRequest implements Serializable {
    private static final long serialVersionUID = -8553652217343204029L;

    /**
     * 业务领域（兼容之前的，默认3c）0:3c, 3 汽车
     */
    private Integer bizPlatform = BizPlatformEnum.RETAIL.getCode();

    /**
     * 用户id
     */
    @NotNull
    private Long userId=0L;

    /**
     * 应用id
     */
    private Long clientId=0L;

    /**
     * 门店id
     */
    private String orgCode="";

    /**
     * 城市id
     */
    private Long cityId=0L;

    /**
     * 履约id
     */
    private Integer shipmentId;

    /**
     * 购物模式，0-默认值，1-物流，2-现场购，3-物流和现场购混合
     */
    private Long shoppingMode=0L;

    /**
     * 商品列表
     */
    private List<GoodsInfo> skuPackageList=new ArrayList<>();

    /**
     * 门店类型(1：直营店，2：专卖店，3：授权店)
     */
    private Integer orgType;

}
