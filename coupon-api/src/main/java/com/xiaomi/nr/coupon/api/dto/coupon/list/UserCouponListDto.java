package com.xiaomi.nr.coupon.api.dto.coupon.list;

import com.xiaomi.nr.coupon.api.dto.coupon.info.CouponInfoItemDto;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户优惠券列表所返回Dto
 *
 * <AUTHOR>
 */
@Data
public class UserCouponListDto extends CouponInfoItemDto implements Serializable {

    private static final long serialVersionUID = 1256541191072839270L;

    /**
     * 调价商品是否可用（true：可用，false：不可用）
     */
    private Boolean isCheckPrice;

    /**
     * 使用渠道分类（MISHOP_ONLINE：小米线上商城, MIHOME_OFFLINE：小米线下门店）
     * 对应原接口的 app_tag
     */
    private List<String> useChannelGroup;

    /**
     * 标签列表
     * car_owner_gift_exchange：车主礼品兑换
     */
    private List<String> tags = new ArrayList<>();
}
