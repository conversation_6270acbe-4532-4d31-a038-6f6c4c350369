package com.xiaomi.nr.coupon.api.dto.ecard;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class EcardDto implements Serializable {

    private static final long serialVersionUID = -3260009000303963911L;

    /**
     * ecard ID
     */
    private Long cardId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 类型id
     */
    private Integer typeId;

    /**
     * ecard名称
     */
    private String cardName;

    /**
     * 现金券金额(元)
     */
    private String money;

    /**
     * 现金券余额(元)
     */
    private String balance;

    /**
     * 状态描述
     */
    private String statDesc;

    /**
     * 现金券有效期开始时间（时间戳）
     */
    private String startTime;

    /**
     * 现金券有效期结束时间（时间戳）
     */
    private String endTime;

    /**
     * 绑定时间（时间戳）
     */
    private Integer bindTime;

    /**
     * 作废时间（时间戳）
     */
    private Integer invalidTime;

    /**
     * 激活时间（时间戳）
     */
    private Integer activeTime;

    /**
     * 购买的用户ID
     */
    private Long fromUserId;

    /**
     * 购买的订单号
     */
    private String fromOrderId;

}
