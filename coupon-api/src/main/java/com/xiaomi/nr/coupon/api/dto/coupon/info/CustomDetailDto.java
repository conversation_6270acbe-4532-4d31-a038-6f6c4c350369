package com.xiaomi.nr.coupon.api.dto.coupon.info;

import lombok.Data;

import java.io.Serializable;

/**
 * 优惠券定制信息，仅用于券面信息展示使用
 * 端上也有自己处理的，并针对不同的券进行信息组合展示
 *
 * <AUTHOR>
 */
@Data
public class CustomDetailDto implements Serializable {

    private static final long serialVersionUID = 897178888187292937L;

    /**
     * 优惠描述（具体优惠下面展示的文案）
     */
    private String desc;

    /**
     * 门槛阈值
     */
    private String threshold;

    /**
     * 门槛单位
     */
    private String thresholdUnit;

    /**
     * 具体优惠
     */
    private String benefit;

    /**
     * 优惠前缀
     */
    private String benefitPre;

    /**
     * 优惠单位
     */
    private String benefitUnit;

    /**
     * 券类型描述
     */
    private String useTypeDesc;

    /**
     * 券规则描述
     */
    private String couponRuleDesc;

    /**
     * 规则名称(单品页横条展示)
     */
    private String ruleIndex;
}
