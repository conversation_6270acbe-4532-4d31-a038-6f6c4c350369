package com.xiaomi.nr.coupon.api.dto.couponconfig;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CouponConfigRelationGoodsResponse implements Serializable {

    private static final long serialVersionUID = 7079383414688820458L;

    /**
     * 券配置ID
     */
    private long id;

    /**
     * 券名称
     */
    private String name;

    /**
     * 优惠券类型码 cash：现金券，discount：折扣券，deductible：抵扣券
     * 这个即将要作废
     */
    private String typeCode;

    /**
     * 优惠券类型码描述 cash：现金券，discount：折扣券，deductible：抵扣券
     * 这个即将要作废
     */
    private String typeCodeDesc;

    /**
     * 使用类型 cash：现金券，discount：折扣券，deductible：抵扣券
     */
    private String useType;

    /**
     * 使用类型 cash：现金券，discount：折扣券，deductible：抵扣券
     */
    private String useTypeDesc;

    /**
     * 优惠券的面值，100：代表满*减100元，0.85：代表满*打8.5折，空：代表是抵扣券的
     */
    private String showValue;

    /**
     * 优惠券的面额单位（现金券的单位为元，折扣券的单位为折，抵扣券的单位为空）
     */
    private String showUnit;


    /**
     * 可用货品或套装列表
     */
    private List<GoodsItem> list;

    @Data
    public static class GoodsItem implements Serializable {

        private static final long serialVersionUID = 8230086363805120452L;

        /**
         * 货品ID或套装ID
         */
        private long id;

        /**
         * 商品品级 goods代表货品ID，package代表是套装，目前只能是这两种
         */
        private String level;

        /**
         * 产品ID
         */
        private long productId;
    }


}