package com.xiaomi.nr.coupon.api.dto.coupon.checkout;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 商品信息
 *
 * <AUTHOR>
 */
@Data
public class CompareItem implements Serializable {
    private static final long serialVersionUID = -2463467493912174306L;
    /**
     * sku
     */
    private List<String> sku;
    /**
     * 货品id
     */
    private List<String> goods;
    /**
     * 套装ID
     * 注： 因为关键词 对应字段package
     */
    @SerializedName("package")
    private List<String> packages;
}
