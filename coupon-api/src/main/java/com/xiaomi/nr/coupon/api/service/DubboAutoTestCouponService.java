package com.xiaomi.nr.coupon.api.service;

import com.xiaomi.nr.coupon.api.dto.autotest.BatchFetchCouponRequest;
import com.xiaomi.nr.coupon.api.dto.autotest.BatchFetchCouponResponse;
import com.xiaomi.nr.coupon.api.dto.autotest.BatchFetchRedpacketRequest;
import com.xiaomi.nr.coupon.api.dto.autotest.BatchFetchRedpacketResponse;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * 自动化测试
 */
public interface DubboAutoTestCouponService {


    /**
     * 批量发券
     * @param request
     * @return
     */
    Result<BatchFetchCouponResponse> batchFetchCoupon(BatchFetchCouponRequest request);


    /**
     * 批量发红包
     */
    Result<BatchFetchRedpacketResponse> batchFetchRedpacket(BatchFetchRedpacketRequest request);



}
