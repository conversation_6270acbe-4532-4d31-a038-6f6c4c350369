package com.xiaomi.nr.coupon.api.dto.trade;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: 券核销项
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2023/1/5 2:33 下午
 * @Version: 1.0
 **/
@Data
public class CouponLockItem implements Serializable {

    private static final long serialVersionUID = -1076472988312451545L;

    /**
     * 用户优惠券ID
     */
    private Long couponId;

    /**
     * 券码
     */
    private String couponCode;

    /**
     * 抵扣金额
     */
    private BigDecimal replaceMoney;
    /**
     * 抵扣邮费
     */
    private BigDecimal reduceExpress;
}
