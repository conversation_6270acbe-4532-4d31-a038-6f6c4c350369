package com.xiaomi.nr.coupon.api.dto.couponconfig;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 优惠规则VO
 * @Date: 2022.05.12 11:33
 */
@Data
public class PromotionVO implements Serializable {
    /**
     * 优惠类型, 1:满减, 2:满折, 3:N元券, 4:立减
     */
    private int promotionType;
    /**
     * 优惠值(单位分/折)
     */
    private long promotionValue;
    /**
     * 门槛值满元（单位分)
     */
    private long bottomPrice;
    /**
     * 门槛值满件（单位个)
     */
    private int bottomCount;
    /**
     * 门槛类型, 1:满元, 2:满件, 3:每满元, 4:每满件, 5:满元且满件
     */
    private int bottomType;
    /**
     * 最大减免金额 单位分
     */
    private long maxReduce;
}
