package com.xiaomi.nr.coupon.api.dto;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

public class PageBeanStream<X> implements Serializable {

    private static final long serialVersionUID = 7338664867644140761L;

    /**
     * 分页ID
     */
    public long lastId;

    /**
     * 页面大小
     */
    public int pageSize;

    /**
     * 是否是最后一页(true:最后一页，false：非最后一页)
     */
    public Boolean lastPage;

    /**
     * 分页数据
     */
    public List<X> data;

    /**
     * 构造
     */
    public PageBeanStream() {}
    public PageBeanStream(long lastId, int pageSize, boolean lastPage, List<X> data) {
        this.lastId = lastId;
        this.pageSize = pageSize;
        this.lastPage = lastPage;
        this.data = data;
    }

    public long getLastId() {
        return lastId;
    }

    public void setPageNo(long lastId) {
        this.lastId = lastId;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public Boolean getLastPage() {
        return lastPage;
    }

    public void setLastPage(boolean lastPage) {
        this.lastPage = lastPage;
    }

    public List<X> getData() {
        return data;
    }

    public void setData(List<X> list) {
        this.data = list;
    }

}
