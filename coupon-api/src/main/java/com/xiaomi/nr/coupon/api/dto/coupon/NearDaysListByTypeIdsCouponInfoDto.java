package com.xiaomi.nr.coupon.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;

/**
 * 根据券配置ID批量查询用户近*天的券列表中券的信息
 *
 * <AUTHOR>
 */
@Data
public class NearDaysListByTypeIdsCouponInfoDto implements Serializable {

    private static final long serialVersionUID = 3415042318560127984L;

    /**
     * 券ID
     */
    private Long couponId;

    /**
     * 券的真实开始时间
     */
    private Long startTime;

    /**
     * 券的真实结束时间
     */

    private Long endTime;

    /**
     * 状态
     */
    private String status;

    /**
     * 状态中文描述
     */
    private String statusDesc;

}
