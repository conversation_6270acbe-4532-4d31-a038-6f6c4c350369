package com.xiaomi.nr.coupon.api.dto.couponconfig;

import com.xiaomi.nr.coupon.api.dto.coupon.info.CustomDetailDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class CouponConfigBaseInfo implements Serializable {
    private static final long serialVersionUID = 8572285615059732429L;

    /**
     * 优惠券类型id (优惠券配置ID)
     */
    private Long configId;

    /**
     * 优惠券类型名称
     */
    private String configName;

    /**
     * 优惠券类型码 cash：现金券，discount：折扣券，deductible：抵扣券
     * 这个即将要作废
     */
    private String typeCode;

    /**
     * 优惠券类型码描述 cash：现金券，discount：折扣券，deductible：抵扣券
     * 这个即将要作废
     */
    private String typeCodeDesc;

    /**
     * 使用类型 cash：现金券，discount：折扣券，deductible：抵扣券
     */
    private String useType;

    /**
     * 使用类型 cash：现金券，discount：折扣券，deductible：抵扣券
     */
    private String useTypeDesc;

    /**
     * 配额类型 money：满元，count：满件，eve_money：每满元 eve_count：每满件
     */
    private String quotaType;

    /**
     * 优惠券的配额，100：代表满100元减*元，3：代表满3件打*折
     */
    private String quotaValue;

    /**
     * 优惠券的面值，100：代表满*减100元，8.5：代表满*打8.5折，空：代表是抵扣券的
     */
    private String showValue;

    /**
     * 优惠券的面额单位（现金券的单位为元，折扣券的单位为折，抵扣券的单位为空）
     */
    private String showUnit;

    /**
     * 折扣券的最大减免金额（单位元）
     */
    private BigDecimal reduceMaxPrice;

    /**
     * 优惠券状态
     */
    private String status;

    /**
     * 优惠券描述
     */
    private String statusDesc;

    /**
     * 优惠券发放渠道，store_manager：店长券，空：其他渠道
     */
    private String sendChannel;

    /**
     * 使用范围说明
     */
    private String rangeDesc;

    /**
     * 优惠券规则
     */
    private CustomDetailDto customDetail;

    /**
     * 优惠券类型的创建时间
     */
    private Long addTime;

    /**
     * 上下线状态,状态 1:上线, 2:下线, 3:终止
     */
    private Integer onlineStatus;

    /**
     * 开始时间
     */
    private Long startFetchTime;
    /**
     * 结束时间
     */
    private Long endFetchTime;
    /**
     * 使用渠道分类,1 - 小米商城,2-小米之家 直营店,3-小米之家 专卖店,4-授权店,5-堡垒店
     */
    private List<Integer> useChannels;
    /**
     * 投放场景
     */
    private String sendScene;

    /**
     * 优惠券类型
     */
    private Integer couponType;

}
