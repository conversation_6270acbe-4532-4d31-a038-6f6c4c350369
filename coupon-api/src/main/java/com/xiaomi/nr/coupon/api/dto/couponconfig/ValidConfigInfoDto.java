package com.xiaomi.nr.coupon.api.dto.couponconfig;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ValidConfigInfoDto extends CouponConfigBaseInfo implements Serializable {
    private static final long serialVersionUID = 7808999134245204096L;

    /**
     * 发放任务列表，按任务的发放开始时间从小到大排序
     * 如果一个券配置了多个发放任务，这里会都返回的，要想知道券的真实有效期，就不能直接说是多少了
     */
    private List<MissionDto> missions;

}
