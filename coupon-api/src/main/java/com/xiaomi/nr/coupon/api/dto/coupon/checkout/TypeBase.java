package com.xiaomi.nr.coupon.api.dto.coupon.checkout;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 劵配置（老缓存）基本信息
 *
 * <AUTHOR>
 */
@Data
public class TypeBase implements Serializable {
    private static final long serialVersionUID = 3831610260790217935L;
    /**
     * 劵配置ID
     *
     */
    private Long id = 0L;

    /**
     * 劵类型
     */
    private Integer type = 0;

    /**
     * 抵扣类型：0-0元抵扣，1-0.01元抵扣
     */
    @SerializedName("deduct_type")
    private Integer deductType = 0;

    /**
     * 活动类型标题
     */
    private String name = "";

    /**
     * 范围描述
     */
    @SerializedName("range_desc")
    private String rangeDesc = "";

    /**
     * 展示标题
     */
    @SerializedName("show_title")
    private String showTitle = "";

    /**
     * 展示单位
     */
    @SerializedName("show_unit")
    private String showUnit;

    /**
     * 类型码：
     * cash-现金劵 discount-折扣劵 deduct-抵扣劵
     */
    @SerializedName("type_code")
    private String typeCode = "";

    /**
     * 是否可以分享
     */
    @SerializedName("is_share")
    private Integer isShare = 0;

    /**
     * 是否包邮
     */
    private Integer postfree = 0;


}
