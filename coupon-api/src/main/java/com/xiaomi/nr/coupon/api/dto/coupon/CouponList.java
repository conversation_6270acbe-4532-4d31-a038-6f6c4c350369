package com.xiaomi.nr.coupon.api.dto.coupon;

import com.xiaomi.nr.coupon.api.dto.coupon.info.CustomDetailDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 单品页可领券与可用券返回接口
 */
@Data
public class CouponList implements Serializable {

    private static final long serialVersionUID = -2630136333415276314L;

    /**
     * 用户券id
     */
    private Long couponId = 0L;

    /**
     * 优惠金额
     */
    private String value;

    /**
     * 开始时间
     */
    private Long beginTime = 0L;

    /**
     * 结束时间
     */
    private Long endTime = 0L;

    /**
     * 券名称
     */
    private String couponName;

    /**
     * 券名称描述
     */
    private String couponNameDesc;

    /**
     * 优惠券状态(判断是否已经使用，并赋值对应状态)
     * 对应原接口的 stat
     */
    private String stat;

    /**
     * 券实际状态 (优惠券原始状态)
     */
    private String statOrigin;

    /**
     * 优惠券赠送状态：0：未赠送, 1：已赠送, 2：不可赠送, 3：已领取
     */
    private Integer giftSend;

    /**
     * 原分享券的ID
     */
    private Long parentId = 0L;

    /**
     * 是否可分享
     */
    private Boolean isShare;

    /**
     * 是否包邮
     */
    private Integer additional;

    /**
     * 券类型id
     */
    private Long typeId;

    /**
     * 订单id
     */
    private Long orderId = 0L;

    /**
     * 使用券的时间（秒）
     */
    private Long useTime = 0L;

    /**
     * 不可用原因
     */
    private String usableRange;

    /**
     * 是否可用
     */
    private Boolean isAvailable;

    /**
     * 是否单个商品可用
     */
    private Boolean isOneGoodsUse;

    /**
     * 抵扣类型
     */
    private Integer deductType;

    /**
     * appTag
     */
    private List<String> appTag;

    /**
     * appTag描述
     */
    private String appTagInfo;

    /**
     * 可用范围描述
     */
    private String rangeDesc;

    /**
     *
     */
    private String rangeShortDesc;


    /**
     *
     */
    private Boolean defaultCheck;

    /**
     *
     */
    private String realReduce;

    /**
     * 调价商品是否可用
     */
    private Integer checkPrice;

    /**
     * 金额单位
     */
    private String showUnit;

    /**
     * 优惠金额
     */
    private String showTitle;

    /**
     * 领券活动 activity_tag
     */
    private String actTag;

    /**
     * 领券活动   coupon_tag
     */
    private String couponTag;

    /**
     *
     */
    private String customRuleIndex;


    /**
     *
     */
    private List<String> limitUseRegion;

    /**
     * 可用渠道
     */
    private Set<String> availableChannel;

    /**
     * 优惠券规则
     * 对应原接口的custom_rule_details
     */
    private List<CustomDetailDto> customRuleDetails;

    /**
     * 标签信息
     */
    private List<String> tags;

    /**
     *
     */
    private List<String> extTags;

    /**
     *
     */
    private String validGoodsPrice;

    /**
     * 券类型()
     */
    private String type;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 券类型(1:商品券，2:运费券)
     */
    private Integer couponType;

    /**
     * 履约方式
     */
    private Integer shipmentId;

}
