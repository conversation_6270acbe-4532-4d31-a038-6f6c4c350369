package com.xiaomi.nr.coupon.api.dto.couponconfig;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
public class CouponTypeInfoListRequest implements Serializable {
    private static final long serialVersionUID = 1788697596183621642L;

    /**
     * SKU/套装请求参数结构
     */
    @NotEmpty(message = "商品列表不能为空")
    private List<GoodsItem> goodsItemList;

    /**
     * 发放渠道，如果传store_manager则取店长券的，如果为空则取send_channel为空的券配置列表
     */
    @NotEmpty(message = "发放渠道不能为空")
    private String sendChannel;


    /**
     * appId权限验证
     */
    private String appId;


    /**
     * 根据参数规则生成的token
     */
    private String token;
}
