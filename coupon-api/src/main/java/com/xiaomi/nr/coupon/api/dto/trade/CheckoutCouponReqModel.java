package com.xiaomi.nr.coupon.api.dto.trade;

import com.xiaomi.nr.coupon.api.dto.coupon.checkout.GoodsInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 优惠券结算
 *
 * <AUTHOR>
 */
@Data
public class CheckoutCouponReqModel implements Serializable {

    private static final long serialVersionUID = -1125994481089836821L;

    /**
     * 是否需要校验已使用的券
     * 场景：价保、汽车订单改配
     */
    private boolean usedCouponCheck = false;

    /**
     * 业务领域
     */
    private Integer bizPlatform;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * clientId
     */
    private Long clientId;

    /**
     * 用户券ID列表
     */
    private List<Long> couponIds;

    /**
     * 券码（明码券）
     */
    private String couponCode;

    /**
     * 门店ID
     */
    private String orgCode;

    /**
     * 门店类型
     */
    private Integer orgType;

    /**
     * 城市id
     */
    private Long cityId;

    /**
     * 购物模式
     */
    private Long shoppingMode = 0L;

    /**
     * 履约方式
     * -1：所有方式，
     * 139：门店闪送（这个是跟交易中台统一定义的）
     */
    private Integer shipmentId = -1;

    /**
     * 商品列表
     */
    private List<GoodsInfo> skuPackageList;

    /**
     * 已用券ID
     */
    private Long usedCoupon;

    /**
     * 下单类型
     */
    private Integer submitType;

    /**
     * 车辆vid
     */
    private String vid;

    /**
     * 优惠类型筛选
     */
    private List<Integer> promotionTypeList;

}
