package com.xiaomi.nr.coupon.api.dto.couponassign;

import com.xiaomi.dubbo.validation.annotation.Mid;
import com.xiaomi.nr.coupon.api.dto.BaseRequest;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * @Description: 券可领性校验
 * @Date: 2022.05.17 23:22
 */
@Data
public class CouponFetchCheckRequest extends BaseRequest implements Serializable {
    /**
     * 业务领域（兼容之前，默认3c）
     */
    private Integer bizPlatform = BizPlatformEnum.RETAIL.getCode();

    /**
     * 用户id
     */
    //@Mid
    private Long uid;

    /**
     * 券配置id
     */
    @Size(min = 1, max = 20, message = "优惠券配置ID个数必须在1-20个")
    private List<Long> configIdList;

    /**
     * 发放场景
     */
    @NotBlank(message = "投放场景不能为空")
    private String sceneCode;

    /**
     * 是否返回可用券
     */
    private boolean withValidCoupon;

}
