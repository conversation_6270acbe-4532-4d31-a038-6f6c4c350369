package com.xiaomi.nr.coupon.api.dto.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Objects;

/**
 * 券配置上可用商品的扩展tag枚举
 *
 * <AUTHOR>
 * @date 2024/2/2
 */
@Getter
@AllArgsConstructor
public enum GoodsExtTagEnum {
    /**
     * 工时
     */
    LABOR("labor", "工时"),

    PART("part", "配件"),
    ;

    /**
     * 枚举tag
     */
    private final String tag;

    /**
     * 枚举描述
     */
    private final String name;

    private static final HashMap<String, GoodsExtTagEnum> MAPPING = new HashMap<>();

    static {
        for (GoodsExtTagEnum e : GoodsExtTagEnum.values()) {
            MAPPING.put(e.getTag(), e);
        }
    }

    public static GoodsExtTagEnum queryTag(String tag) {
        if (Objects.isNull(tag)) {
            return null;
        }
        return MAPPING.get(tag);
    }
}
