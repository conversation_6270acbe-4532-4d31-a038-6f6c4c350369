package com.xiaomi.nr.coupon.api.dto.couponconfig;

import com.xiaomi.nr.coupon.api.dto.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class CouponConfigGoodsRelRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = -7046939820402201169L;

    /**
     * 优惠券配置Id
     */
    @NotEmpty(message = "优惠券配置ID列表不能为空")
    private List<Long> configIds;

    /**
     * 包含sku
     */
    private boolean withSku;

}