package com.xiaomi.nr.coupon.api.dto.couponconfig;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 查询券发放剩余量请求参数
 *
 * <AUTHOR>
 */
@Data
public class CouponConfigSurplusRequest implements Serializable {

    private static final long serialVersionUID = 4987237304631008447L;

    /**
     * 券配置ID
     */
    @NotEmpty(message = "优惠券配置ID列表不能为空")
    private List<Integer> configIds;

    /**
     * appId权限验证
     */
    private String appId;


    /**
     * 根据参数规则生成的token
     */
    private String token;
}
