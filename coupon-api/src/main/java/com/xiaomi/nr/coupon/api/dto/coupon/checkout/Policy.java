package com.xiaomi.nr.coupon.api.dto.coupon.checkout;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 政策
 *
 * <AUTHOR>
 */
@Data
public class Policy implements Serializable {

    /**
     * 券或者活动的类型
     */
    private Integer type = 0;

    /**
     * 各优先级的政策
     */
    @SerializedName(value = "policy")
    private List<PolicyLevel> policies;
}
