package com.xiaomi.nr.coupon.api.dto.couponconfig;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 商品有效券列表返回值
 */
@Getter
@Setter
public class ValidConfigListResponse implements Serializable {
    private static final long serialVersionUID = -679125668587070058L;

    /**
     * sku对应券列表
     */
    private Map<Long, Set<Long>> skuToCouponMap;

    /**
     * package对应券列表
     */
    private Map<Long, Set<Long>> packageToCouponMap;

    /**
     * ssu对应券列表
     */
    private Map<Long, Set<Long>> ssuToCouponMap;

    /**
     * 券信息map
     */
    private Map<Long, ValidConfigInfoDto> couponConfigMap;

    public ValidConfigListResponse(){}
    public ValidConfigListResponse(Map<Long, Set<Long>> skuToCouponMap, Map<Long, Set<Long>> packageToCouponMap, Map<Long, Set<Long>> ssuToCouponMap, Map<Long, ValidConfigInfoDto> couponConfigMap){
        this.skuToCouponMap = skuToCouponMap;
        this.packageToCouponMap = packageToCouponMap;
        this.ssuToCouponMap = ssuToCouponMap;
        this.couponConfigMap = couponConfigMap;
    }

}
