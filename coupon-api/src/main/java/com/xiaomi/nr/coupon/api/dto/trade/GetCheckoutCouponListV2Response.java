package com.xiaomi.nr.coupon.api.dto.trade;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 结算券信息 - 接口返回值
 *
 * <AUTHOR>
 */
@Data
public class GetCheckoutCouponListV2Response implements Serializable {

    private static final long serialVersionUID = 3431209944266406730L;

    /**
     * 无码券信息
     */
    private Map<Long, CheckoutCouponInfo> noCodeCoupons;

    /**
     * 明码券信息
     */
    private Map<String, CheckoutCouponInfo> codeCoupons;

    /**
     * 用券叠加分组信息
     */
    private Map<String, CouponGroupInfo> couponGroupInfoMap;
}