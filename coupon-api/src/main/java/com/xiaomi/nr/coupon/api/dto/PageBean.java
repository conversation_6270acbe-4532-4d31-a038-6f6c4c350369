package com.xiaomi.nr.coupon.api.dto;


import java.io.Serializable;
import java.util.List;

/**
 * 页码式分页
 * <AUTHOR>
 */
public class PageBean<X> implements Serializable {

    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = -362450984932162465L;

    /**
     * 页码
     */
    public int pageNo;

    /**
     * 每页数据量
     */
    public int pageSize;

    /**
     * 总数据量
     */
    public int total;

    /**
     * 分页数据
     */
    public List<X> data;

    /**
     * 构造
     */
    public PageBean() {
    }

    public PageBean(int pageNo, int pageSize, int total, List<X> data) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.total = total;
        this.data = data;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public List<X> getList() {
        return data;
    }

    public void setList(List<X> list) {
        this.data = list;
    }
}