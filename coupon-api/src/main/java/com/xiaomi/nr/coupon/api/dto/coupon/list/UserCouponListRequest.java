package com.xiaomi.nr.coupon.api.dto.coupon.list;

import com.google.common.collect.Lists;
import com.xiaomi.dubbo.validation.annotation.Mid;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 用户优惠券列表请求参数
 *
 * <AUTHOR>
 */
@Data
public class UserCouponListRequest implements Serializable {

    private static final long serialVersionUID = 582269009003888763L;

    /**
     * 业务领域（兼容之前的，默认3c）0:3c, 3 汽车
     */
    private List<Integer> bizPlatform = Lists.newArrayList(BizPlatformEnum.RETAIL.getCode());

    /**
     * 小米ID
     */
    private Long userId;

    /**
     * 优惠券状态
     */
    @NotEmpty(message = "查询券状态不能为空")
    private String status;

    /**
     * 发放渠道
     */
    private String sendChannel;

    /**
     * 使用渠道
     */
    private String useChannel;

    /**
     * 分页ID
     */
    private Long lastId;

    /**
     * 页面大小
     */
    private Integer pageSize;

    /**
     * appId权限验证
     */
    private String appId;

    /**
     * 根据参数规则生成的token
     */
    private String token;

    /**
     * vid
     */
    private String vid;

    /**
     * 服务类型列表，详见@CouponServiceTypeEnum
     */
    private List<Integer> serviceTypeList;

}
