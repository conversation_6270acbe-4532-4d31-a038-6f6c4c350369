package com.xiaomi.nr.coupon.api.dto.coupon;

import com.google.common.collect.Lists;
import com.xiaomi.dubbo.validation.annotation.Mid;
import com.xiaomi.nr.coupon.api.dto.BaseRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.checkout.GoodsInfo;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 获取已用券信息 - 请求参数
 *
 * <AUTHOR>
 */
@Data
public class GetUsedCouponRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = -8105412630865446360L;

    /**
     * 业务领域（兼容之前，默认3c）
     */
    private Integer bizPlatform = BizPlatformEnum.RETAIL.getCode();

    /**
     * 用户ID
     */
    @NotNull
    private Long userId;

    /**
     * clientId
     */
    private Long clientId;

    /**
     * 用户券ID列表
     */
    private List<Long> couponIds;

    /**
     * 券码（明码券）
     */
    private String couponCode;

    /**
     * 门店ID
     */
    private String orgCode;

    /**
     * 门店类型
     */
    private Integer orgType;

    /**
     * 履约方式
     * -1：所有方式，
     * 139：门店闪送（这个是跟交易中台统一定义的）
     */
    private Integer shipmentId = -1;

    /**
     * 商品列表
     */
    private List<GoodsInfo> skuPackageList;

}
