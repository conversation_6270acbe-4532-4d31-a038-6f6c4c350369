package com.xiaomi.nr.coupon.api.dto.couponconfig;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class MissionDto implements Serializable {
    private static final long serialVersionUID = -8618664816502377401L;

    /**
     * 发放任务id
     */
    private Long missionId;

    /**
     * 发放任务名称
     */
    private String missionName;

    /**
     * 发放任务类型（1:接口调用发放 2:自动任务按人群批量发放）
     */
    private String missionType;

    /**
     * 发放任务最多可发放的券数量
     */
    private Long sendNumLimit;

    /**
     * 券的真实有效期类别（section:时间段内有效 days:从发放起*天内有效）
     */
    private String timeType;

    /**
     * 券的真实有效期类别描述
     */
    private String timeTypeDesc;

    /**
     * 券的真实有效开始时间
     */
    private Long couponStartTime;

    /**
     * 券的真实有效结束时间
     */
    private Long couponEndTime;

    /**
     * 券从发放起*天内有效（真实有效时间）
     */
    private Integer couponDays;

    /**
     * 券从发放起*小时内有效（真实有效时间）
     */
    private Integer couponHours;

    /**
     * 发放任务的创建时间
     */
    private Long addTime;
}
