package com.xiaomi.nr.coupon.api.dto.couponcode;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/8 14:20
 */
@Data
public class GetCouponCodeInfoRequest implements Serializable {
    private static final long serialVersionUID = -347541171415934343L;

    /**
     * 优惠码
     */
    @NotNull
    private String couponCode;

    /**
     * 订单号
     */
    @NotNull
    private Long orderId;

    /**
     * 发送方式
     */
    private String sendType;

    /**
     * 使用方式
     */
    private Integer useMode;

    /**
     * 状态
     */
    private String stat;
}
