package com.xiaomi.nr.coupon.api.dto.coupon.checkout;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 劵信息
 *
 * <AUTHOR>
 */
@Data
public class CouponOwnedInfo implements Serializable {
     private static final long serialVersionUID = -5623641770137219572L;
     /**
     * 优惠券Id
     */
    private Long couponId;

    /**
     * 优惠券类型(1:商品券｜2:运费券)
     */
    private Integer couponType;

    /**
     * 履约id
     */
    private Integer shipmentId;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 劵类型id（劵配置id）
     */
    private Long typeId;

    /**
     * 劵类型信息（劵配置老缓存结构）
     */
    private CouponOldConfigInfo couponOldConfigInfo;

    /**
     * 优惠劵状态
     */
    private String stat;

    /**
     * 劵是否可用，0-不可用，1-可用
     */
    private Long allow = 0L;

    /**
     * 扩展信息
     */
    private Map<String, String> exInfo;

    /**
     * 发放渠道
     */
    private String sendChannel;

    /**
     * 使用区域限制
     */
    private List<String> limitUseRegion;

    /**
     * 分组tag  券类型ID_履约方式
     */
    private String groupTag;
}
