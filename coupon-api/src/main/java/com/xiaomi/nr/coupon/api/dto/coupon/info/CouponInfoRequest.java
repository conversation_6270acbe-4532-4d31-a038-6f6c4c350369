package com.xiaomi.nr.coupon.api.dto.coupon.info;


import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 查询优惠券信息
 *
 * <AUTHOR>
 */
@Data
public class CouponInfoRequest implements Serializable {

    private static final long serialVersionUID = 8087374251482973060L;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 优惠券ID
     */
    @Size(min = 1, max = 20, message = "优惠券ID列表个数在1-20个")
    private List<Long> couponIds;

    /**
     * appId权限验证
     */
    private String appId;

    /**
     * 根据参数规则生成的token
     */
    private String token;

    /**
     * vid bizPlatform为4时必填
     */
    private String vid;

    /**
     * 优惠券所属业务领域，详见@BizPlatformEnum
     */
    private Integer bizPlatform = BizPlatformEnum.RETAIL.getCode();
}
