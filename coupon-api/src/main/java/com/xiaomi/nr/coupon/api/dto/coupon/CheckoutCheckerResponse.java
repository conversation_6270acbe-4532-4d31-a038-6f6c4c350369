package com.xiaomi.nr.coupon.api.dto.coupon;

import com.xiaomi.nr.coupon.api.dto.coupon.checkout.CouponOwnedInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 结算校验返回结果
 *
 * <AUTHOR>
 */
@Data
public class CheckoutCheckerResponse implements Serializable {

    private static final long serialVersionUID = -6553652212343204029L;

    private Map<String, CouponOwnedInfo> couponCodes;
}
