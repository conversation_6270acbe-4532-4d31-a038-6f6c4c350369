package com.xiaomi.nr.coupon.api.dto.trade;

import com.xiaomi.nr.coupon.api.dto.enums.GoodsExtTagEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 商品扩展信息
 *
 * <AUTHOR>
 * @date 2024/2/2
 **/
@Data
public class SsuExtItemDto implements Serializable {

    private static final long serialVersionUID = -8927441844310287002L;

    /**
     * tag labor:工时，part:配件
     */
    private String tag;

    /**
     * 数量，核销时可用券的工时或配件ssu个数
     */
    private Integer count;

    /**
     * 是否为工时ssu
     *
     * @return bool
     */
    public boolean isLaborSsu() {
        return GoodsExtTagEnum.LABOR.getTag().contains(tag);
    }

    /**
     * 是否为配件ssu
     *
     * @return bool
     */
    public boolean isPartSsu() {
        return GoodsExtTagEnum.PART.getTag().contains(tag);
    }
}
