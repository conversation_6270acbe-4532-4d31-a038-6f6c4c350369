package com.xiaomi.nr.coupon.api.dto.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;

/**
 * 优惠券所属业务领域
 *
 * <AUTHOR>
 * @date 2023/9/13
 */
@Getter
@AllArgsConstructor
public enum BizPlatformEnum {

    /**
     * 0: 3C零售
     */
    RETAIL(0, "retail", "3C零售"),

    /**
     * 3: 汽车整车销售
     */
    CAR(3, "car", "汽车整车销售"),

    /**
     * 4: 汽车售后服务
     */
    CAR_AFTER_SALE(4, "car_after_sale", "汽车售后服务"),

    /**
     * 5: 车商城
     */
    CAR_SHOP(5, "car_shop", "车商城"),
    ;

    /**
     * 枚举code
     */
    private final Integer code;

    /**
     * 名称
     */
    private final String name;

    /**
     * 枚举描述
     */
    private final String desc;

    private static final HashMap<Integer, BizPlatformEnum> MAPPING = new HashMap<>();

    static {
        for (BizPlatformEnum e : BizPlatformEnum.values()) {
            MAPPING.put(e.getCode(), e);
        }
    }

    public static BizPlatformEnum valueOf(Integer code) {
        return MAPPING.get(code);
    }




}
