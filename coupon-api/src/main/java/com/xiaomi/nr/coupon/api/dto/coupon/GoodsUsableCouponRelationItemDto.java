package com.xiaomi.nr.coupon.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;

/**
 * 有效的用户已领券和可领券关系数据
 *
 * <AUTHOR>
 * @date 2023/7/19
 */
@Data
public class GoodsUsableCouponRelationItemDto implements Serializable {

    private static final long serialVersionUID = 6140326753110988442L;

    /**
     * 关系类型（FETCHABLE:可领券，FETCHED:用户已领券），可领券和已领券均有此字段
     */
    private String relationType;

    /**
     * 券配置ID，可领券和已领券均有此字段
     */
    private Long configId;

    /**
     * 用户券ID,已领券才有的字段
     */
    private Long id;

    /**
     * 开始时间（券可使用的有效开始时间，秒）,已领券才有的字段
     */
    private Long useStartTime;

    /**
     * 结束时间（券可使用的有效结束时间，秒）,已领券才有的字段
     */
    private Long useEndTime;

    /**
     * 发放给用户的时间（秒）,已领券才有的字段
     */
    private Long sendTime;

}