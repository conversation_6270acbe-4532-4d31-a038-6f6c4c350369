package com.xiaomi.nr.coupon.api.dto.couponinvalid;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 优惠券作废、作废校验入参
 *
 * <AUTHOR>
 * @date 2024/5/16
 */
@Data
public class InvalidCouponReq implements Serializable {

    private static final long serialVersionUID = 8087374251482973060L;

    /**
     * 优惠券所属业务领域
     */
    @NotNull(message = "业务领域不能为空")
    private Integer bizPlatform;

    /**
     * vid
     */
    private String vid;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 优惠券id列表
     */
    @NotNull(message = "优惠券id列表不能为空")
    @Size(min = 1, max = 50, message = "作废优惠券数量需要控制在50个以内")
    private List<Long> couponIdList;

}
