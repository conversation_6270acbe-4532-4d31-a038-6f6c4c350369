package com.xiaomi.nr.coupon.api.dto.couponconfig;

import com.xiaomi.nr.coupon.api.dto.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
public class ValidConfigListRequest extends BaseRequest implements Serializable {
    private static final long serialVersionUID = 234345437221062764L;

    /**
     * SKU/套装请求参数结构
     */
    @NotEmpty(message = "参数商品列表不能为空")
    private List<GoodsItem> goodsItemList;

    /**
     * 发放渠道，如果传store_manager则取店长券的，如果为空则取send_channel为空的券配置列表
     */
    @NotEmpty(message = "参数投放场景不能为空")
    private String sceneCode;

    /**
     * 可领开始时间
     */
    private Long startFetchTime;

    /**
     * 可领结束时间
     */
    private Long endFetchTime;
}
