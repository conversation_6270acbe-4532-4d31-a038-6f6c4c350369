package com.xiaomi.nr.coupon.api.dto.coupon;

import com.xiaomi.nr.coupon.api.dto.trade.CheckoutCouponInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 获取已用券信息 - 接口返回值
 *
 * <AUTHOR>
 */
@Data
public class GetUsedCouponResponse implements Serializable {

    private static final long serialVersionUID = -7422245239719126148L;

    /**
     * 无码券信息
     */
    private Map<Long, CheckoutCouponInfo> noCodeCoupons;

    /**
     * 明码券信息
     */
    private Map<String, CheckoutCouponInfo> codeCoupons;
}
