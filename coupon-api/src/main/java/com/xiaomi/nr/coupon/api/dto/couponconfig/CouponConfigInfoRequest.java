package com.xiaomi.nr.coupon.api.dto.couponconfig;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
public class CouponConfigInfoRequest implements Serializable {
    private static final long serialVersionUID = -1480546560660792084L;

    /**
     * 券配置ID列表
     */
    @NotEmpty(message = "券配置ID列表不能为空")
    private List<Long> configIdList;

    /**
     * 券配置ID列表
     */
    private boolean withProducts;

    /**
     * appId权限验证
     */
    private String appId;

    /**
     * 根据参数规则生成的token
     */
    private String token;
}
