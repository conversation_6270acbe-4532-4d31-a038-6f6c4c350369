package com.xiaomi.nr.coupon.api.dto.coupon;

import com.xiaomi.nr.coupon.api.dto.coupon.info.CustomDetailDto;
import com.xiaomi.nr.coupon.api.dto.coupon.info.UseTermDto;
import com.xiaomi.nr.coupon.api.dto.couponconfig.PromotionVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * UsableConfigItemDto
 *
 * <AUTHOR>
 * @date 2023/7/13
 */
@Data
public class UsableConfigItemDto implements Serializable {

    private static final long serialVersionUID = -3643506094288123830L;

    /**
     * 券配置ID
     */
    private Long configId;

    /**
     * 券名称
     */
    private String name;

    /**
     * 券类型（1:商品券，2:运费券，3:超级补贴券）
     */
    private Integer type;

    /**
     * 开始时间（券可领取的有效开始时间，秒）
     */
    private Long fetchStartTime = 0L;

    /**
     * 结束时间（券可领取的有效结束时间，秒）
     */
    private Long fetchEndTime = 0L;

    /**
     * 是否可分享
     */
    private Boolean share = false;

    /**
     * 是否包邮
     */
    private Boolean postFree = false;

    /**
     * 是否调价商品可用
     */
    private Boolean checkPrice = false;

    /**
     * 券使用范围描述
     */
    private String rangeDesc;

    /**
     * 优惠类型（cash:满减或立减，discount:折扣，deductible:抵扣）
     */
    private String promotionType;

    /**
     * 优惠类型ID
     * 满减: 1->(cash,现金券)
     * 满折: 2 ->(discount,折扣券)
     * 兑换券: 3->(deductible,抵扣券)
     * 立减: 4->(cash,现金券)
     */
    private Integer promotionTypeCode;

    /**
     * 抵扣类型（只仅针对抵扣券，该字段才有值，0：0元抵扣，1:1分钱抵扣）
     */
    private Integer deductType;

    /**
     * 抵扣支付金额（只仅针对抵扣券，该字段才有值，0：0元抵扣，0.01：1分钱抵扣）
     */
    private String deductValue;

    /**
     * 可用渠道分组（MISHOP：小米线上商城, MIHOME：小米线下门店）
     */
    private List<String> useChannelGroup;

    /**
     * 可用渠道描述（比如 仅小米商城/小米授权店/小米之家可用）
     */
    private String useChannelDesc;

    /**
     * 标签信息（比如 proMember：会员券）
     */
    private List<String> tags;

    /**
     * 定制展示信息
     */
    private CustomDetailDto customRuleDetails;

    /**
     * 可用sku列表（匹配的是传入的sku列表）
     */
    private List<Long> validSkuList;

    /**
     * 可用套装ID列表（匹配的是传入的套装ID列表）
     */
    private List<Long> validPackageIdList;

    /**
     * 可用ssu列表（匹配的是传入的ssu列表）
     */
    private List<Long> validSsuList;

    /**
     * 优惠规则vo
     */
    private PromotionVO promotionVO;

    /**
     * 使用有效期
     */
    private UseTermDto useTermDto;

}
