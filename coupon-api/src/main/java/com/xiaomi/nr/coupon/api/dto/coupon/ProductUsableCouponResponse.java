package com.xiaomi.nr.coupon.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 产品站-商品可用券接口返回值
 *
 * <AUTHOR>
 * @date 2023/7/19
 */
@Data
public class ProductUsableCouponResponse implements Serializable {

    private static final long serialVersionUID = -6860566823591453480L;

    /**
     * 有效的用户已领券列表（有序），商品信息 => 用户券列表
     * 1. 针对的是传入的小米ID 和 sku或套装ID
     * 2. 针对的是传入的配置ID列表 和 传入的sku或套装ID
     * 3. 基础排序后的结果
     * 4. 不可用的用户券则不返回
     */
    private Map<ProductGoodsItemDto, List<GoodsUsableCouponRelationItemDto>> validCoupons = new HashMap<>();

    /**
     * 商品可用券配置信息
     * 券配置ID => 对应券配置信息
     */
    private Map<Long, UsableConfigItemDto> configInfos = new HashMap<>();
}
