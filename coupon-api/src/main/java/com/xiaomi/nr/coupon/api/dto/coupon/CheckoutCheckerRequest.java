package com.xiaomi.nr.coupon.api.dto.coupon;


import com.xiaomi.dubbo.validation.annotation.Mid;
import com.xiaomi.nr.coupon.api.dto.BaseRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.checkout.GoodsInfo;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 结算校验请求参数
 *
 * <AUTHOR>
 */
@Data
public class CheckoutCheckerRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = -6553652212343204029L;

    /**
     * 券码
     */
    @NotEmpty(message = "优惠码不能为空")
    private List<String> couponCode = new ArrayList<>();

    /**
     * 用户id
     */
    @NotNull
    private Long userId = 0L;

    /**
     * 应用id
     */
    private Long clientId = 0L;

    /**
     * 门店id
     */
    private String orgCode = "";

    /**
     * 城市id
     */
    private Long cityId = 0L;

    /**
     * 履约id
     */
    private Integer shipmentId;

    /**
     * 购物模式，0-默认值，1-物流，2-现场购，3-物流和现场购混合
     */
    private Long shoppingMode = 0L;

    /**
     * 商品列表
     */
    @NotEmpty(message = "商品列表不能为空")
    private List<GoodsInfo> skuPackageList = new ArrayList<>();

    /**
     * 是否来自价保
     */
    private Boolean fromPriceProtect = false;
}
