package com.xiaomi.nr.coupon.api.dto.couponassign;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 券可领性校验结果
 * @Date: 2022.05.17 23:25
 */
@Data
public class CouponFetchCheckDTO implements Serializable {
    /**
     * 是否已领
     */
    private boolean fetched;
    /**
     * 是否可领
     */
    private boolean fetchAble;
    /**
     * 不可领原因
     */
    private String invalidReason;
    /**
     * 不可用原因
     */
    private Integer invalidCode;
    /**
     * 可用券列表
     */
    private List<Long> validCouponList;
}
