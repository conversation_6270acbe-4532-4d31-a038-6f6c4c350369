package com.xiaomi.nr.coupon.api.dto.ecard;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class RecycleAssignRequest implements Serializable {

    private static final long serialVersionUID = 8684849794926016062L;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 小米生成的回收订单号，要求唯一，会作幂等判断
     */
    private String miRecycleOrderId;

    /**
     * 现金券面额（单位元）
     */
    private String money;

    /**
     * 用户手机号
     */
    private String userTel;

    /**
     * appId权限验证
     */
    private String appId;

    /**
     * 根据参数规则生成的token
     */
    private String token;

    /**
     * 业务场景  1-空调换新退单补发   2-标准换新现金券  不传默认为标准换新现金券
     */
    private Integer bussnessType;

}
