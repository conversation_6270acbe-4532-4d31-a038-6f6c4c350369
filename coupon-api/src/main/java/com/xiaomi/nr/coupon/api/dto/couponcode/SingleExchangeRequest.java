package com.xiaomi.nr.coupon.api.dto.couponcode;

import com.xiaomi.dubbo.validation.annotation.Mid;
import com.xiaomi.nr.coupon.api.dto.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 券码兑换接口请求参数
 *
 * <AUTHOR>
 */
@Data
public class SingleExchangeRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = -5521809798503489020L;

    /**
     * 用户id（发给此用户）
     */
    //@Mid
    private Long userId = 0L;

    /**
     * 券码
     */
    @NotBlank(message = "兑换码不能为空")
    private String couponCode = "";

    /**
     * 门店ID（门店兑换要传）
     */
    private String orgCode = "";
}
