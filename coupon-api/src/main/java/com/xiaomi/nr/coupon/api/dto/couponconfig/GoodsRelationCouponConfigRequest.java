package com.xiaomi.nr.coupon.api.dto.couponconfig;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;


/**
 * <AUTHOR>
 */
@Data
public class GoodsRelationCouponConfigRequest implements Serializable {

    private static final long serialVersionUID = 4858132744605561541L;

    /**
     * sku或套装ID
     */
    @Min(value = 1, message = "ID必须为正整数")
    private long id;

    /**
     * 品级 sku:代表sku，package:代表套装，ssu:代表ssu
     */
    @NotEmpty(message = "商品级别不能为空")
    private String level;

    /**
     * appId权限验证
     */
    private String appId;

    /**
     * 根据参数规则生成的token
     */
    private String token;
}