package com.xiaomi.nr.coupon.api.service;

import com.xiaomi.nr.coupon.api.dto.couponconfig.*;
import com.xiaomi.youpin.infra.rpc.Result;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 优惠券服务
 *
 * <AUTHOR>
 */
public interface CouponConfigService {

    /**
     * 根据优惠券配置ID获取可用的货品或套装列表
     *
     * @param request CouponConfigRelationGoodsRequest
     * @return CouponConfigRelationGoodsResponse
     */
    @Deprecated
    Result<CouponConfigRelationGoodsResponse> getGoodsList(CouponConfigRelationGoodsRequest request);

    /**
     * 根据SKU/套装ID获取可用的优惠券配置列表
     *
     * @param request GoodsRelationCouponConfigRequest
     * @return GoodsRelationCouponConfigResponse
     */
    Result<GoodsRelationCouponConfigResponse> getConfigList(@Valid GoodsRelationCouponConfigRequest request);

    /**
     * 根据货品id获取可用的优惠券配置列表
     *
     * gid/cid
     *
     * @param request GoodsRelationCouponConfigRequest
     * @return GoodsRelationCouponConfigResponse
     */
    Result<GoodsCouponConfigRelResponse> getGoodsCouponConfigRel(@Valid GoodsCouponConfigRelRequest request);

    /**
     * 根据货品id获取可用的优惠券配置列表
     *
     * gid/cid
     *
     * @param request GoodsRelationCouponConfigRequest
     * @return GoodsRelationCouponConfigResponse
     */
    Result<CouponConfigGoodsRelResponse> getCouponConfigGoodsRel(@Valid CouponConfigGoodsRelRequest request);

    /**
     * 根据SKU/套装ID、发放渠道查询有效券配置信息列表接口
     * @param request SKU/套装ID参数
     * @return CouponTypeInfoDto
     */
    @Deprecated
    Result<List<CouponTypeInfoListResponse>> getValidConfigList(@Valid CouponTypeInfoListRequest request);


    /**
     * 根据券配置ID查询券配置信息(批量)
     *
     * @param request 券配置ID列表
     * @return List<> 券配置信息列表
     */
    Result<List<CouponTypeInfoDto>> getCouponConfigInfoList(@Valid CouponConfigInfoRequest request);

    /**
     * 根据券配置ID查询券配置发放剩余量(批量)
     *
     * @param request 券配置ID列表
     * @return List<> 剩余量列表
     */
    Result<Map<Integer, CouponConfigSurplusDto>> getCouponConfigSurplus(@Valid CouponConfigSurplusRequest request);


    /**
     * 根据SKU/套装ID、投放场景查询有效券配置信息列表接口
     * @param request SKU/套装ID参数
     * @return CouponTypeInfoDto
     */
    Result<ValidConfigListResponse> getValidConfigListV2(@Valid ValidConfigListRequest request);

}

