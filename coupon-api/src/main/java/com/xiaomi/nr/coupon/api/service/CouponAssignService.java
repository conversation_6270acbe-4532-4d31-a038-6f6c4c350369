package com.xiaomi.nr.coupon.api.service;

import com.xiaomi.nr.coupon.api.dto.couponassign.*;
import com.xiaomi.nr.coupon.api.dto.couponcode.SingleExchangeRequest;
import com.xiaomi.nr.coupon.api.dto.couponcode.SingleExchangeResponse;
import com.xiaomi.youpin.infra.rpc.Result;

import javax.validation.Valid;

/**
 * 优惠券服务
 *
 * <AUTHOR>
 */
public interface CouponAssignService {

    /**
     * 单张券发放接口
     *
     * @param request SingleAssignRequest
     * @return SingleAssignResponse
     */
    Result<SingleAssignResponse> single(@Valid SingleAssignRequest request);

    /**
     * 券可领性校验
     * @param request
     * @return
     */
    Result<CouponFetchCheckResponse> couponFetchCheck(@Valid CouponFetchCheckRequest request);


    /**
     * 灌券任务专用单张券发放接口
     *
     * @param request SingleAssignRequest
     * @return SingleAssignResponse
     */
    Result<SingleAssignResponse> fillSingle(@Valid FillSingleAssignRequest request);

    /**
     * 单个券码兑换接口
     *
     * @param request ExchangeRequest
     * @return ExchangeResponse
     */
    Result<SingleExchangeResponse> singleExchange(@Valid SingleExchangeRequest request);

}
