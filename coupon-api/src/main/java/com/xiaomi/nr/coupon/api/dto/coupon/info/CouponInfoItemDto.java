package com.xiaomi.nr.coupon.api.dto.coupon.info;

import com.xiaomi.nr.coupon.api.dto.coupon.OneGoodsUseInfoDto;
import com.xiaomi.nr.coupon.api.dto.couponconfig.PromotionVO;
import lombok.Data;

import java.io.Serializable;

/**
 * 优惠券信息
 *
 * <AUTHOR>
 */
@Data
public class CouponInfoItemDto implements Serializable {

    private static final long serialVersionUID = 4725927044065611370L;

    /**
     * 优惠券ID
     * 对应于原接口的 couponId
     */
    private Long couponId;

    /**
     * 优惠券类型名称
     * 对应原接口的 couponName
     */
    private String couponName;

    /**
     * 优惠券状态(判断是否已经使用，并赋值对应状态)
     * 对应原接口的 stat
     */
    private String status;

    /**
     * 优惠券状态描述
     */
    private String statusDesc;

    /**
     * 优惠券原始状态
     * 对应原接口的 stat_origin
     */
    private String originStatus;

    /**
     * 优惠券的面值，100：代表满*减100元，8.5：代表满*打8.5折，空：代表是抵扣券的
     * 对应于原接口的 value
     */
    private String showValue;

    /**
     * 优惠券的面额单位（现金券的单位为元，折扣券的单位为折，抵扣券的单位为空）
     */
    private String showUnit;

    /**
     * 开始时间(用户券实际的开始时间)
     * 对应于原接口的 beginTime
     */
    private Long startTime;

    /**
     * 结束时间(用户券实际的结束时间)
     * 对应原接口的 endTime
     */
    private Long endTime;

    /**
     * 优惠券赠送状态：0：未赠送, 1：已赠送, 2：不可赠送, 3：已领取
     * 对应原接口的 gift_send
     */
    private Integer giftSendStatus;

    /**
     * 原分享券的ID
     * 对应原接口的 parent_id
     */
    private Long fromCouponId;

    /**
     * 发放券的订单号
     */
    private String fromOrderId;

    /**
     * 优惠券是否可分享 true：可分享，false：不可分享
     * 对应原接口的 is_share
     */
    private Boolean isShare;

    /**
     * 优惠券类型  1: 商品券 2: 运费券 3:超级补贴券
     */
    private Integer couponType;

    /**
     * 履约方式 -1: 所有方式 139: 门店闪送 (这个跟交易中台统一定义)
     */
    private Integer shipmentId;

    /**
     * 使用类型 cash：现金券，discount：折扣券，deductible：抵扣券
     * 对应原接口的 type
     */
    private String useType;

    /**
     * 优惠券配置ID (优惠券类型id)
     * 对应原接口的 type_id
     */
    private Long configId;

    /**
     * 使用券的订单号
     * 对应原接口的 order_id
     */
    private Long useOrderId;

    /**
     * 使用券的时间（秒）
     */
    private Long useTime;

    /**
     * 是否是单个商品可用
     * 对应原接口的 is_one_goods_use
     */
    private Boolean isOneGoodsUse;

    /**
     * 单个商品可用信息
     * 对应原接口的 one_goods_use_info
     */
    private OneGoodsUseInfoDto oneGoodsUseInfoDto;

    /**
     * 优惠券使用渠道描述（仅小米商城/小米授权店/小米之家可用）
     */
    private String useChannelDesc;

    /**
     * 券使用范围描述
     */
    private String rangeDesc;

    /**
     * 优惠券发放时间
     */
    private Long addTime;

    /**
     * 优惠券规则
     * 对应原接口的custom_rule_details
     */
    private CustomDetailDto customDetail;

    /**
     * 服务场景，详见@CouponServiceTypeEnum
     */
    private Integer serviceType;

    /**
     * 服务场景名称
     */
    private String serviceTypeName;

    /**
     * 业务领域
     */
    private Integer bizPlatform;

    /**
     * 促销规则
     */
    private PromotionVO promotionVO;

    /**
     * 是否展示有效期
     */
    private Boolean isDisplayDate;
}