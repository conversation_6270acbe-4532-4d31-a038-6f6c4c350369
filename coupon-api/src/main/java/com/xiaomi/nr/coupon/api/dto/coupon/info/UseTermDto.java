package com.xiaomi.nr.coupon.api.dto.coupon.info;

import lombok.Data;

import java.io.Serializable;

/**
 * 使用有效期
 *
 * <AUTHOR>
 * @date 2024/9/13
 */
@Data
public class UseTermDto implements Serializable {

    private static final long serialVersionUID = 9146450560022239391L;

    /**
     * 使用有效期类型 1 固定有效,2 相对有效期
     */
    private Integer useTimeType;

    /**
     * 可使用的开始时间
     */
    private Long startUseTime;

    /**
     * 可使用的结束时间
     */
    private Long endUseTime;

    /**
     * 有效时长(单位小时)
     */
    private Integer useDuration;

}
