package com.xiaomi.nr.coupon.api.dto.trade;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;


/**
 * 优惠券结算
 *
 * <AUTHOR>
 */
@Data
public class CheckoutCouponResModel implements Serializable {

    private static final long serialVersionUID = -7092289846948973074L;

    private Map<Long, CheckoutCouponInfo> noCodeCoupons;

    private Map<String, CheckoutCouponInfo> codeCoupons;

    private Map<String, CouponGroupInfo> couponGroupInfoMap;
}
