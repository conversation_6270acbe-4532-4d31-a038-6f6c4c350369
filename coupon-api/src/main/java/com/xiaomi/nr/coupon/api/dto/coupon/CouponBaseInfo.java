package com.xiaomi.nr.coupon.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 优惠券对外输出的公共信息（结算）
 *
 * <AUTHOR>
 */
@Data
public class CouponBaseInfo implements Serializable {

    private static final long serialVersionUID = 3448577503393507512L;

    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 优惠券配置ID
     */
    private Long configId;

    /**
     * 状态
     */
    private String status;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 优惠券类型
     * 1：商品券
     * 2：运费券
     * 3：超级补贴券
     */
    private Integer couponType;

    /**
     * 使用范围描述
     */
    private String couponRangeDesc;

    /**
     * 使用规则描述
     */
    private String couponRuleDesc;

    /**
     * 优惠类型
     * 满减: 1->(cash,现金券)
     * 满折: 2 ->(discount,折扣券)
     * 兑换券: 3->(deductible,抵扣券)
     * 立减: 4->(cash,现金券)
     */
    private Integer promotionType;

    /**
     * 优惠值（单位个/分 折）
     */
    private Long promotionValue;

    /**
     * 优惠值展示给用户看的单位（现金券的单位为“元”，折扣券的单位为“折”，抵扣券的单位为空）
     */
    private String showUnit;

    /**
     * 最大减免金额（单位分）
     */
    private Integer maxReduce;

    /**
     * 门槛类型
     * 1满元
     * 2满件
     * 3每满元
     * 4每满件
     */
    private Integer bottomType;

    /**
     * 满元门槛值（单位分）
     */
    private Integer bottomPrice;

    /**
     * 满件门槛值（单位个）
     */
    private Integer bottomCount;

    /**
     * 是否包邮
     */
    private Integer postFree;

    /**
     * 是否可分享
     */
    private Integer share;

    /**
     * 优惠类型(老模型)
     * 1：现金券
     * 2：折扣券
     * 3：抵扣券
     */
    private Integer type;

    /**
     * 类型码(老模型)
     * cash-现金劵
     * discount-折扣劵
     * deduct-抵扣劵
     */
    private String typeCode;

    /**
     * 使用区域限制
     */
    private List<String> limitUseRegion;

    /**
     * 投放渠道
     */
    private String sendChannel;

    /**
     * 使用渠道
     */
    private String useChannel;

    /**
     * 优惠券使用渠道描述（仅小米商城/小米授权店/小米之家可用）
     */
    private String useChannelDesc;

    /**
     * BR单据
     */
    private String budgetApplyNo;

    /**
     * BR行号
     */
    private Long lineNum;

    /**
     * 所属业务领域
     */
    private Integer bizPlatform;

    /**
     * 服务场景：基础保养、漆面修复、上门补胎
     */
    private Integer serviceScene;

    /**
     * 使用次数限制，1-限制，2-不限制
     */
    private Integer timesLimit;

    /**
     * 工时标准面
     */
    private Integer workHourStandardPage = 10;

    /**
     * 发放场景
     */
    private String sendScene;

    /**
     * 券tag
     */
    private List<String> tags = new ArrayList<>();
    /**
     * 抵扣金额类型（1：定金 2：尾款）
     */
    private Integer checkoutStage;

}
