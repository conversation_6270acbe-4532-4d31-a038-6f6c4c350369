package com.xiaomi.nr.coupon.api.dto.ecard;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class RecycleEcardDto implements Serializable {

    private static final long serialVersionUID = -3715552896600572905L;

    /**
     * 现金券id
     */
    private Long cardId;

    /**
     * 类型id
     */
    private Long typeId;

    /**
     * 现金券名称
     */
    private String cardName;

    /**
     * 现金券有效期开始时间（时间戳）
     */
    private Long startTime;

    /**
     * 现金券有效期结束时间（时间戳）
     */
    private Long endTime;

    /**
     * 现金券面额（单位元）
     */
    private String money;

    /**
     * 现金券余额（单位元）
     */
    private String balance;

}
