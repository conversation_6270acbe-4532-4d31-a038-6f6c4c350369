package com.xiaomi.nr.coupon.api.dto.couponassign;

import com.xiaomi.nr.coupon.api.dto.BaseRequest;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 单张券发放接口请求参数
 *
 * <AUTHOR>
 */
@Data
public class SingleAssignRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 4816883669701963183L;

    /**
     * 业务领域（兼容之前，默认3c）
     */
    private Integer bizPlatform = BizPlatformEnum.RETAIL.getCode();

    /**
     * 请求id（长度限制在40个字符内，非常重要，要求单用户级的全局唯一）
     */
    @Size(min = 1, max = 40, message = "请求流水号长度必须在1-40之间")
    private String requestId;

    /**
     * 用户id（发给此用户）
     */
    //@Mid
    private Long userId;

    /**
     * vid，发放汽车售后服务券时必填
     */
    private String vid;

    /**
     * 投放场景编码（不同的场景编码不同）
     */
    @NotBlank(message = "投放场景编码不能为空")
    private String sceneCode;

    /**
     * 优惠券配置ID
     */
    @Min( value = 1, message = "优惠券配置ID不符合发放要求")
    private Long configId;

    /**
     * 发放方式  1:外部系统发券（默认）, 2:内部系统灌券
     */
    @NotNull(message = "发放方式不符合要求")
    private Integer assignMode = 1;

    /**
     * 门店ID（门店发券要传）
     */
    private String orgCode = "";

    /**
     * 发放关联的订单号（因某个订单发券的需要传）
     */
    private Long orderId = 0L;

    /**
     * 分享人小米ID（因某人分享发券的需要传）
     */
    private Long shareUserId = 0L;

    /**
     * 券开始使用时间（指定有效期需要传）
     * 已有场景：服务包场景
     */
    private Long startUseTime;

    /**
     * 券结束使用时间（指定有效期需要传）
     * 已有场景：服务包场景
     */
    private Long endUseTime;
}
