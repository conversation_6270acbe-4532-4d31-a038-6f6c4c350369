package com.xiaomi.nr.coupon.api.dto.coupon.checkout;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 劵配置（老缓存）信息条件
 *
 * <AUTHOR>
 */
@Data
public class Condition implements Serializable {
    private static final long serialVersionUID = -2234036847213269139L;
    /**
     * 劵类型
     */
    private Integer type = 0;

    /**
     * 能参与的客户端列表（线上）
     */
    private List<String> client;

    /**
     * 政策里的限件限额等信息，这里单独复制了一份
     */
    private List<QuotaEle> quota;

    /**
     * 可参与的商品
     */
    @SerializedName("goods_include")
    private List<CompareItem> goodsInclude;

    /**
     * 套装是否允许部分商品参加活动(套装是否拆分) 1允许 2不允许
     */
    @SerializedName("check_package")
    private Integer checkPackage = 2;

    /**
     * 特价商品是否参与  1表示不参与，2表示参与
     */
    @SerializedName("check_price")
    private Integer checkPrice = 2;

    /**
     * 是否线下可用 1仅线上使用，2仅线下使用，3均可使用
     */
    private Integer offline = 0;

    /**
     * 使用渠道
     */
    @SerializedName("use_channel")
    private String useChannel = "";

}
