package com.xiaomi.nr.coupon.api.dto.coupon.checkout;

import lombok.Data;

import java.io.Serializable;

/**
 * 商品信息
 *
 * <AUTHOR>
 */
@Data
public class GoodsInfo implements Serializable {
    private static final long serialVersionUID = 2928993745051421183L;
    /**
     * "sku"，套装 "package"，新套装 "ssu"
     */
    private String level;

    /**
     * sku或者套装id
     */
    private Long id;

    /**
     * tag labor:工时，part:配件
     */
    private String tag;

    /**
     * 可用券的工时或配件个数
     */
    private Integer count;
}
