package com.xiaomi.nr.coupon.api.dto.coupon;

import com.xiaomi.nr.coupon.api.dto.BaseRequest;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 商品可用券请求参数
 */
@Data
public class ProductCouponRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 6320834889221669164L;
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * clientId
     */
    @Min(value = 1, message = "clientId不能为空")
    private Long clientId;

    /**
     * 门店编码
     */
    private String orgCode;

    /**
     *  渠道Id
     */
    private String channelId;

    /**
     * 米金兑券
     */
    private List<String>  coinCouponTypeIdList;

    /**
     * 会员券
     */
    private List<String>  proMemberCouponIdList;

    /**
     * 默认goods
     */
    private String level;

    /**
     * 商品信息
     * key: sku | packageId
     * value : 商品信息
     */
    @NotEmpty(message = "商品信息不能为空")
    private List<ProductInfo> goodsInfoList;

}
