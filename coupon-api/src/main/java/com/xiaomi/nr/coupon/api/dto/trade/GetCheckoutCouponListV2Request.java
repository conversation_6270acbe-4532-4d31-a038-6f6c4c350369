package com.xiaomi.nr.coupon.api.dto.trade;

import com.google.common.collect.Lists;
import com.xiaomi.dubbo.validation.annotation.Mid;
import com.xiaomi.nr.coupon.api.dto.BaseRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.checkout.GoodsInfo;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import lombok.Data;

import javax.validation.constraints.Min;
import java.io.Serializable;
import java.util.List;

/**
 * 获取已用券信息 - 请求参数
 *
 * <AUTHOR>
 */
@Data
public class GetCheckoutCouponListV2Request extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 8928607847384183409L;

    /**
     * 业务领域
     */
    private List<Integer> bizPlatform = Lists.newArrayList(BizPlatformEnum.RETAIL.getCode());

    /**
     * 用户ID
     */
    //@Mid
    private Long userId;

    /**
     * 车辆vid，当bizPlatform=4时（汽车售后服务券），要求必传
     */
    private String vid;

    /**
     * clientId
     */
    //@Min(value = 1, message = "应用ID不能为空")
    private Long clientId;

    /**
     * 用户券ID列表（当获取券列表的时候，券id和券码并不是必传的）
     */
    private List<Long> couponIds;

    /**
     *  券码（明码券）
     */
    private String couponCode;

    /**
     * 门店ID
     */
    private String orgCode;

    /**
     * 门店类型
     */
    private Integer orgType;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 购物模式
     * 0-默认值，
     * 1-物流，
     * 2-现场购，
     * 3-物流和现场购混合
     */
    private Integer shoppingMode;

    /**
     * 履约方式
     * -1：所有方式，
     * 139：门店闪送（这个是跟交易中台统一定义的）
     */
    private Integer shipmentId = -1;

    /**
     * 商品列表，门店在获取券列表的时候不传商品列表，在真正结算的时候才传
     */
    private List<GoodsInfo> skuPackageList;

    /**
     * 已用券ID
     */
    @Min(value = 1, message = "已用券ID不符合规范")
    private Long usedCoupon;

    /**
     * 下单类型
     */
    private Integer submitType;

    /**
     * 优惠类型筛选
     */
    private List<Integer> promotionTypeList;

}
