package com.xiaomi.nr.coupon.api.dto.ecard;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class QueryEcardRequest implements Serializable {

    private static final long serialVersionUID = -4542844921218822458L;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 小米生成的回收订单号，要求唯一，会作幂等判断
     */
    private String miRecycleOrderId;

    /**
     * appId权限验证
     */
    private String appId;

    /**
     * 根据参数规则生成的token
     */
    private String token;

}
