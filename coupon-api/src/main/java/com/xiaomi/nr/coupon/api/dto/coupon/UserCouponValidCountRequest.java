package com.xiaomi.nr.coupon.api.dto.coupon;


import com.xiaomi.dubbo.validation.annotation.Mid;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 用户可用券总数请求参数
 *
 * <AUTHOR>
 */
@Data
public class UserCouponValidCountRequest implements Serializable {

    private static final long serialVersionUID = 1537870163611214050L;

    /**
     * 用户id
     */
    @NotNull
    private Long userId;

    /**
     * 优惠券使用渠道（1～N个，逗号分隔），mi_shop：小米商城，mi_home：小米之家，mi_authorized：授权店
     */
    private String useChannel;

    /**
     * 0:3c, 3 汽车
     */
    private List<Integer> bizPlatform;

    /**
     * appId权限验证
     */
    private String appId;

    /**
     * 根据参数规则生成的token
     */
    private String token;
}
