package com.xiaomi.nr.coupon.api.dto.couponconfig;

import com.xiaomi.nr.coupon.api.dto.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class GoodsCouponConfigRelRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = -7046939820402201169L;

    /**
     * 优惠券列表
     */
    @NotEmpty(message = "优惠券列表不能为空")
    private List<Long> configIds;

    /**
     * 商品项
     */
    @NotEmpty(message = "商品列表不能为空")
    private List<GoodsItem> goodsItems;

    /**
     *  是否未来可用
     */
    private boolean withFuture = false;

}