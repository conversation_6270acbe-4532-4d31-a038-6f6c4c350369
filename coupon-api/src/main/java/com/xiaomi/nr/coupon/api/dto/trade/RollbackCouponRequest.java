package com.xiaomi.nr.coupon.api.dto.trade;

import com.xiaomi.dubbo.validation.annotation.Mid;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import lombok.Data;

import javax.validation.constraints.Min;
import java.io.Serializable;
import java.util.List;


@Data
public class RollbackCouponRequest implements Serializable {

    /**
     * 业务领域（兼容之前，默认3c）
     */
    private Integer bizPlatform = BizPlatformEnum.RETAIL.getCode();

    /**
     * 用户的id
     */
    //@Mid
    private long userId;

    /**
     * 车辆VID
     */
    private String vid;

    /**
     * 订单的id
     */
    @Min(value = 1, message = "订单号不能为空或为0")
    private long orderId;
    /**
     * 优惠券ID
     */
    private List<Long> couponIds;

    /**
     * 码券code
     */
    private String couponCode;

    /**
     * 应用id
     */
    private Long clientId;

    /**
     * 线上线下
     */
    private Integer offline;

    /**
     * 已用券ID
     */
    private Long usedCoupon;

    /**
     * 下单类型
     */
    private Integer submitType;

}
