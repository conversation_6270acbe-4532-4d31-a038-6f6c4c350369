package com.xiaomi.nr.coupon.api.dto.coupon;


import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 单用户多任务券发放接口请求参数
 *
 * <AUTHOR>
 */
@Data
public class AssignRequest implements Serializable {

    private static final long serialVersionUID = -9164661214853916121L;

    /**
     * 业务领域（兼容之前，默认3c）
     */
    private Integer bizPlatform = BizPlatformEnum.RETAIL.getCode();

    /**
     * 请求id（长度限制在40个字符内，非常重要，要求单用户级的全局唯一）
     */
    private String requestId;

    /**
     * 用户id（发给此用户）
     */
    private Long userId;

    /**
     * 分享人小米ID
     */
    private Long shareUserId;

    /**
     * 门店ID
     */
    private String orgCode;

    /**
     * 优惠券投放渠道,store_manager：店长券渠道，store_order_gift：下单赠券
     * */
    private String sendChannel;

    /**
     * items
     */
    private List<AssignMissionItemDto> items;

    /**
     * appId权限验证
     */
    private String appId;

    /**
     * 根据参数规则生成的token
     */
    private String token;

}
