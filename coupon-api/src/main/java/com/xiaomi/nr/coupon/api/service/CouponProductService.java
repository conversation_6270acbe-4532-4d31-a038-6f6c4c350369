package com.xiaomi.nr.coupon.api.service;

import com.xiaomi.nr.coupon.api.dto.coupon.ProductUsableCouponRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.ProductUsableCouponResponse;
import com.xiaomi.youpin.infra.rpc.Result;

import javax.validation.Valid;

/**
 * 优惠券产品站服务
 *
 * <AUTHOR>
 * @date 2023/7/19
 */
public interface CouponProductService {

    /**
     * 获取单品页商品可用券接
     *
     * @param request ProductUsableCouponRequest
     * @return ProductUsableCouponResponse
     */
    Result<ProductUsableCouponResponse> getProductUsableCoupon(@Valid ProductUsableCouponRequest request);

}
