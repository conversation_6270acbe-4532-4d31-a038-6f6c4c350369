package com.xiaomi.nr.coupon.api.dto.trade;

import lombok.Data;

import java.io.Serializable;

/**
 * @description 汽车售后服务券过滤条件
 * <AUTHOR>
 * @date 2024-12-31 16:37
*/
@Data
public class AfterSaleFilterParam implements Serializable {
    private static final long serialVersionUID = 90963446324080869L;

    /**
     * 年度类型：1-单年度；2-双年度
     */
    private Integer annualType = 0;

    /**
     * 工单类型：1-到店维保；2-上门维保；3-售前维修；4-赛道整备
     */
    private Integer orderType = 1;
}
