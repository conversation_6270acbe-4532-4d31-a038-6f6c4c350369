package com.xiaomi.nr.coupon.api.dto.coupon;

import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 产品站-商品可用券请求参数
 *
 * <AUTHOR>
 * @date 2023/7/19
 */
@Data
public class ProductUsableCouponRequest implements Serializable {

    private static final long serialVersionUID = 813488737789444679L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * clientId
     */
    private Long clientId;

    /**
     * 门店编码
     */
    private String orgCode;

    /**
     * 优惠券配置列表（比如 米金兑券、领券、会员券之类的券配置ID）
     */
    @Size(max = 200, message = "券配置列表长度需要控制在200个以内")
    private List<ProductConfigItemDto> configList;

    /**
     * 商品信息(套装或单品)
     */
    @NotEmpty(message = "商品信息列表不能为空")
    @Size(min = 1, max = 50, message = "商品信息列表长度需要控制在50个以内")
    private List<GoodsItemDto> goodsList;

    /**
     * 业务领域
     */
    private Integer bizPlatform = BizPlatformEnum.RETAIL.getCode();

    /**
     * 使用渠道
     */
    private Integer useChannel;

    /**
     * 是否需要查询已领券
     */
    private Boolean needFetchedCoupon = true;

    /**
     * 是否需要查询可领券
     */
    private Boolean needFetchableCoupon = true;

    /**
     * 优惠类型列表，当前只作用于车商城已领券
     */
    private List<Integer> promotionTypeList = new ArrayList<>();

}
