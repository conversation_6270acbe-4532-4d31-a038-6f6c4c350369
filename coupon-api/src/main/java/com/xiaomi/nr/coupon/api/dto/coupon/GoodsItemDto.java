package com.xiaomi.nr.coupon.api.dto.coupon;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * GoodsItemDto
 *
 * <AUTHOR>
 * @date 2023/7/19
 */
@Data
public class GoodsItemDto implements Serializable {

    private static final long serialVersionUID = 319283193288324937L;

    /**
     * SKU，与套装ID二传一
     */
    @NotNull
    private Long id;

    /**
     * 商品品级 sku:SKU，package:套装ID，ssu:新套装
     */
    @NotNull
    private String level;

    /**
     * 当前销售价（直降后的，单位分）
     */
    private Long salePrice;

    /**
     * 市场价（标价，单位分）
     */
    private Long marketPrice;

    /**
     * 是否为虚拟商品
     */
    private Boolean virtual;

    /**
     * 商品销售模式，比如定金预售对应的标识
     */
    private String saleMode;

    /**
     * 商家类型，比如小米自营商品/POP对应的标识
     */
    private Integer businessType;

    /**
     * 尾款支付开始时间，定金预售是必须要传的（单位秒）
     */
    private Long finalStartTime;

    /**
     * 尾款支付结束时间，定金预售是必须要传的（单位秒）
     */
    private Long finalEndTime;
}