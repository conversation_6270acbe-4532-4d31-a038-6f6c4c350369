package com.xiaomi.nr.coupon.api.service;

import com.xiaomi.nr.coupon.api.dto.PageBeanStream;
import com.xiaomi.nr.coupon.api.dto.coupon.*;
import com.xiaomi.nr.coupon.api.dto.coupon.AssignRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.AssignResponse;
import com.xiaomi.nr.coupon.api.dto.coupon.UserCouponValidCountRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.UserCouponValidCountResponse;
import com.xiaomi.nr.coupon.api.dto.coupon.info.CouponInfoRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.info.CouponInfoResponse;
import com.xiaomi.nr.coupon.api.dto.coupon.list.UserCouponListRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.list.UserCouponListDto;
import com.xiaomi.nr.coupon.api.dto.couponcode.GetCouponCodeInfoRequest;
import com.xiaomi.nr.coupon.api.dto.couponcode.GetCouponCodeInfoResponse;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import javax.validation.Valid;
import java.util.Map;

/**
 * 优惠券服务
 *
 * <AUTHOR>
 */
public interface CouponService {

    /**
     * 用户可用优惠券总数接口
     *
     * @param userCouponValidCountRequest UserCouponValidCountRequest
     * @return UserCouponValidCountResponse UserCouponValidCountResponse
     */
    Result<UserCouponValidCountResponse> userCouponValidCount(@Valid UserCouponValidCountRequest userCouponValidCountRequest);


    /**
     * 根据券配置ID批量查询用户近*天的券列表（支持批量）
     *
     * @param request NearDaysListByTypeIdsRequest
     * @return Result<Map < String, NearDaysListByTypeIdsItemDto>>
     */
    Result<Map<String, NearDaysListByTypeIdsItemDto>> nearDaysListByTypeIds(@Valid NearDaysListByTypeIdsRequest request);


    /**
     * 券发放接口
     *
     * @param assignRequest AssignRequest
     * @return AssignResponse
     */
    @Deprecated
    Result<AssignResponse> assign(AssignRequest assignRequest);


    /**
     * 获取用户优惠券列表接口
     *
     * @param userCouponListRequest UserCouponListRequest
     * @return List<UserCouponListResponse>
     */
    Result<PageBeanStream<UserCouponListDto>> userCouponList(@Valid UserCouponListRequest userCouponListRequest);


    /**
     * 获取优惠券信息接口（支持批量查询，最多20个）
     *
     * @param request CouponInfoRequest
     * @return CouponInfoResponse
     */
    Result<CouponInfoResponse> couponInfo(@Valid CouponInfoRequest request);


    /**
     * 获取结算页优惠劵列表接口
     *
     * @param request CheckoutCouponListRequest
     * @return CheckoutCouponListResponse
     */
    Result<CheckoutCouponListResponse> checkoutCouponList(@Valid CheckoutCouponListRequest request);

    /**
     * 单品页获取获取商品可用券接口
     *
     * @param request ProductCouponRequest
     * @return ProductCouponResponse
     */
    Result<ProductCouponResponse> getProductCoupon(@Valid ProductCouponRequest request);

    /**
     * 结算校验接口
     *
     * @param request CheckoutCheckerRequest
     * @return CheckoutCheckerResponse
     */
    Result<CheckoutCheckerResponse> checkoutChecker(@Valid CheckoutCheckerRequest request);


    /**
     * 发送券使用消息
     */
    Result<Void> couponUsePush(CouponUsePushRequest request);


    /**
     * 价保获取已用券信息
     *
     * @param request UsedCouponRequest
     * @return UsedCouponResponse
     */
    Result<GetUsedCouponResponse> getUsedCoupons(@Valid GetUsedCouponRequest request);

    /**
     * 获取用户可领取且可用的券和商品信息接口（简版校验）
     * 针对《云店主推线索》这种特殊场景而设计
     *
     * @param request SimpleGoodsFetchUsableRequest
     * @return SimpleGoodsFetchUsableResponse
     */
    Result<SimpleGoodsFetchUsableResponse> getSimpleGoodsFetchUsable(@Valid SimpleGoodsFetchUsableRequest request);

    /**
     * 获取用户已领取且可用的券和商品信息接口（简版校验）
     * 针对《云店主推线索》这种特殊场景而设计
     *
     * @param request SimpleGoodsUserUsableRequest
     * @return SimpleGoodsUserUsableResponse
     */
    Result<SimpleGoodsUserUsableResponse> getSimpleGoodsUserUsable(@Valid SimpleGoodsUserUsableRequest request) throws BizError;

    /**
     * 查询用户优惠码信息
     *
     * @param request GetCouponCodeInfoRequest
     * @return GetCouponCodeInfoResponse
     */
    Result<GetCouponCodeInfoResponse> getCouponCodeInfo(@Valid GetCouponCodeInfoRequest request);
}
