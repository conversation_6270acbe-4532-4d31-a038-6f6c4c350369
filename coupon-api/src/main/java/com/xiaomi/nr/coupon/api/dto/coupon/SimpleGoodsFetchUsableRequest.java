package com.xiaomi.nr.coupon.api.dto.coupon;

import com.xiaomi.dubbo.validation.annotation.Mid;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 商品可领可用券信息（简版校验）
 *
 * <AUTHOR>
 * @date 2023/7/13
 */
@Data
public class SimpleGoodsFetchUsableRequest implements Serializable {

    private static final long serialVersionUID = -3211236948309735235L;

    @NotNull
    private Long userId;

    private String orgCode;

    @NotNull
    @Size(min=1, max=20, message="券配置ID列表不能为空且最多可传20个")
    private List<Long> configIdList;

    @NotNull
    @Size(min=1, max=100, message="商品列表不能为空且最多可传100个")
    private List<SimpleGoodsItem> goodsList;
}
