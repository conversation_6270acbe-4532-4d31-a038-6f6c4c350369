package com.xiaomi.nr.coupon.api.dto.couponconfig;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Data
public class GoodsCouponConfigRelResponse implements Serializable {

    private static final long serialVersionUID = 4858132744605561541L;

    /**
     * gid -> configIds
     */
    private Map<Long, List<Long>> gidCouponConfigIds;

    /**
     * cid -> configIds
     */
    private Map<Long, List<Long>> cidCouponConfigIds;

    /**
     * ssu -> configIds
     */
    private Map<Long, List<Long>> ssuCouponConfigIds;

    /**
     * sku -> configIds
     */
    private Map<Long, List<Long>> skuCouponConfigIds;

}