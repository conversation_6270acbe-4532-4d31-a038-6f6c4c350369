package com.xiaomi.nr.coupon.api.service;

import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponCheckResp;
import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponReq;
import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponResp;
import com.xiaomi.youpin.infra.rpc.Result;

import javax.validation.Valid;

/**
 * 券作废
 *
 * <AUTHOR>
 * @date 2024/5/19
 */
public interface DubboCouponInvalidService {

    /**
     * 批量作废券校验
     *
     * @param req   InvalidCouponReq
     * @return  InvalidCouponCheckResp
     */
    Result<InvalidCouponCheckResp> invalidCouponCheck(@Valid InvalidCouponReq req);

    /**
     * 批量作废券
     *
     * @param req   InvalidCouponReq
     * @return  InvalidCouponResp
     */
    Result<InvalidCouponResp> invalidCoupon(@Valid InvalidCouponReq req);

}
