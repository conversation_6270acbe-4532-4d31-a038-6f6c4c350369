package com.xiaomi.nr.coupon.api.service;

import com.xiaomi.nr.coupon.api.dto.trade.*;
import com.xiaomi.youpin.infra.rpc.Result;

import javax.validation.Valid;

public interface CouponTradeDubboService {

    /**
     * 结算用券查询
     *
     * @param request GetCheckoutCouponListV2Request
     * @return GetCheckoutCouponListV2Response
     */
    Result<GetCheckoutCouponListV2Response> getCheckoutCouponListV2(@Valid GetCheckoutCouponListV2Request request);

    /**
     * 锁定优惠券
     *
     * @param request
     * @return
     */
    Result<LockCouponResponse> lockUserCoupon(@Valid LockCouponRequest request);

    /**
     * 核销优惠券
     *
     * @param request
     * @return
     */
    Result<ConsumeCouponResponse> consumeUserCoupon(@Valid ConsumeCouponRequest request);

    /**
     * 回滚优惠券
     *
     * @param request
     * @return
     */
    Result<RollbackCouponResponse> rollbackUserCoupon(@Valid RollbackCouponRequest request);

}