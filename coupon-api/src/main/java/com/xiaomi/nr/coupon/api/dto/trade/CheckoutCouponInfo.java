package com.xiaomi.nr.coupon.api.dto.trade;

import com.xiaomi.nr.coupon.api.dto.coupon.CouponBaseInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 结算券信息
 *
 * <AUTHOR>
 */
@Data
public class CheckoutCouponInfo implements Serializable {

    private static final long serialVersionUID = -1743397348726223834L;

    /**
     * 券基本信息
     */
    private CouponBaseInfo couponBaseInfo;

    /**
     * 是否可用 - code码
     */
    private Integer validCode;

    /**
     * 不可用原因
     */
    private String invalidReason;

    /**
     * 不可用时的数据信息
     */
    private String invalidData;

    /**
     * 叠加分组
     */
    private String couponGroupNo;

    /**
     * 券可用SKU
     */
    private List<Long> validSkuList;

    /**
     * 券可用套装ID
     */
    private List<Long> validPackageList;

    /**
     * 券可用SSU
     */
    private List<Long> validSsuList;

    /**
     * 扩展信息（ssu => 扩展信息）
     */
    private Map<Long, SsuExtItemDto> couponSsuExtInfo = new HashMap<>();

}
