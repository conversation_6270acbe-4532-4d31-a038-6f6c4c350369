package com.xiaomi.nr.coupon.api.dto.couponassign;

import lombok.Data;

import java.io.Serializable;

/**
 * 单张券发放接口返回
 *
 * <AUTHOR>
 */
@Data
public class SingleAssignResponse implements Serializable {

    private static final long serialVersionUID = 9033665897308512170L;

    /**
     * 是否幂等
     */
    private Boolean isIdempotent;

    /**
     * 实发优惠券ID
     */
    private Long couponId;

    /**
     * 开始使用有效期
     */
    private Long startTime;

    /**
     * 结束使用有效期
     */
    private Long endTime;

}