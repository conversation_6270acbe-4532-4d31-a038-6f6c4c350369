package com.xiaomi.nr.coupon.api.dto.coupon.checkout;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * 劵配置信息（老缓存）
 *
 */
@Data
public class CouponOldConfigInfo implements Serializable {
    private static final long serialVersionUID = -3715903740464464141L;
    /**
     * 基本信息
     */
    private TypeBase basetype;

    /**
     * 条件信息
     */
    private Condition condition;

    /**
     * 政策信息
     */
    @SerializedName(value = "policy")
    private Policy policy;
}
