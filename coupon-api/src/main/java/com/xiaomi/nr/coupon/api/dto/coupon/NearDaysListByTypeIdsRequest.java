package com.xiaomi.nr.coupon.api.dto.coupon;


import com.xiaomi.dubbo.validation.annotation.Mid;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 根据券配置ID批量查询用户近*天的券列表接口请求参数（主要是商城活动页使用）
 *
 * <AUTHOR>
 */
@Data
public class NearDaysListByTypeIdsRequest implements Serializable {

    private static final long serialVersionUID = 4930031680392503606L;

    /**
     * 用户id
     */
    @NotNull
    private Long userId;

    /**
     * 券配置ID
     */
    @NotEmpty( message = "优惠券配置ID列表不能为空")
    private List<Integer> typeIds;

    /**
     * 券的生成时间，取在此时间之后生成的优惠券列表
     */
    @Min( value = 1, message = "生成券的时间必须要大于0")
    private Long addTime;

    /**
     * appId权限验证
     */
    private String appId;

    /**
     * 根据参数规则生成的token
     */
    private String token;
}
