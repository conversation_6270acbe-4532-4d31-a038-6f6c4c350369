package com.xiaomi.nr.coupon.api.dto.trade;

import com.xiaomi.nr.coupon.api.dto.BaseRequest;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.util.List;


@Data
public class LockCouponRequest extends BaseRequest {

    /**
     * 业务领域（兼容之前，默认3c）
     */
    private Integer bizPlatform = BizPlatformEnum.RETAIL.getCode();

    /**
     * 用户的id
     */
    //@Mid
    private long userId;

    /**
     * 车辆VID
     */
    private String vid;

    /**
     * 订单的id
     */
    @Min(value = 1, message = "订单号不能为空或为0")
    private long orderId;

    /**
     * 优惠券核销项
     */
    private List<CouponLockItem> couponItems;

    /**
     * 线上线下
     */
    private Integer offline;

    /**
     * 应用id
     */
    @Min(value = 0, message = "下单应用信息不正确")
    private Long clientId;

    /**
     * 门店编码
     */
    private String orgCode;

    /**
     * 已用券ID
     */
    private Long usedCoupon;

    /**
     * 下单类型
     */
    private Integer submitType;

}
