package com.xiaomi.nr.coupon.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;


/**
 * 单个商品可用
 *
 * <AUTHOR>
 */
@Data
public class OneGoodsUseDto implements Serializable {

    private static final long serialVersionUID = -4508652510370751937L;

    /**
     * 是否单个商品可用
     */
    private Boolean isOneGoodsUse;

    /**
     * 如果是单个商品可用，则返回如下信息，否则为null
     */
    private OneGoodsUseInfoDto oneGoodsUseInfo;
}