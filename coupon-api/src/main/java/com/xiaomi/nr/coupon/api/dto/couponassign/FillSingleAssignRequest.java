package com.xiaomi.nr.coupon.api.dto.couponassign;

import com.xiaomi.nr.coupon.api.dto.BaseRequest;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
public class FillSingleAssignRequest extends BaseRequest implements Serializable {
    private static final long serialVersionUID = -8955938824022767231L;

    /**
     * 业务领域（兼容之前，默认3c）
     */
    private Integer bizPlatform = BizPlatformEnum.RETAIL.getCode();

    /**
     * 请求id（长度限制在40个字符内，非常重要，要求单用户级的全局唯一）
     */
    @Size(min = 1, max = 40, message = "请求流水号长度必须在1-40之间")
    private String requestId;

    /**
     * 任务id
     */
    @Min( value = 1, message = "优惠券发放任务ID不能为空")
    private Long taskId;

    /**
     * 用户id（发给此用户）
     */
    //@Mid
    private Long userId;

    /**
     * vid，发放售后服务券里必填
     */
    @Length(min = 17, max = 17, message = "车辆VID不符合规范")
    private String vid;

    /**
     * 投放场景编码（不同的场景编码不同）
     */
    @NotBlank(message = "投放场景编码不能为空")
    private String sceneCode;

    /**
     * 优惠券配置ID
     */
    @Min( value = 1, message = "优惠券配置ID不符合发放要求")
    private Long configId;

    /**
     * 发放方式  1:外部系统发券（默认）, 2:内部系统灌券
     */
    @NotNull(message = "发放方式不符合要求")
    private Integer assignMode = 2;

    /**
     * 门店ID（门店发券要传）
     */
    private String orgCode = "";

    /**
     * 发放关联的订单号（因某个订单发券的需要传）
     */
    private Long orderId = 0L;

    /**
     * 分享人小米ID（因某人分享发券的需要传）
     */
    private Long shareUserId = 0L;

}
