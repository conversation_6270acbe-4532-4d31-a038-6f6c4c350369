package com.xiaomi.nr.coupon.api.dto.couponconfig;

import com.google.gson.annotations.SerializedName;
import com.xiaomi.nr.coupon.api.dto.couponconfig.CouponTypeInfoDto;
import com.xiaomi.nr.coupon.api.dto.couponconfig.GoodsItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CouponTypeInfoListResponse implements Serializable {

    private static final long serialVersionUID = 1761678512441782467L;

    /**
     * SKU/套装的id和level
     */
    @SerializedName("goodsItem")
    private GoodsItem goodsItem;

    /**
     * 券配置相关信息
     */
    @SerializedName("couponTypeInfoList")
    private List<CouponTypeInfoDto> couponTypeInfoList;

    public CouponTypeInfoListResponse(){}

    public CouponTypeInfoListResponse(GoodsItem goodsItem,List<CouponTypeInfoDto> couponTypeInfoList){
        this.goodsItem = goodsItem;
        this.couponTypeInfoList = couponTypeInfoList;
    }

}
