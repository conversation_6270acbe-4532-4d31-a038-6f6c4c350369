package com.xiaomi.nr.coupon.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 商品可用券接口返回值
 */
@Data
public class ProductCouponResponse implements Serializable {

    private static final long serialVersionUID = 2060421350507945430L;

    /**
     * 用户可用券信息
     */
    private Map<Long, List<CouponList>> userCoupons;

    /**
     * 用户可领券信息
     */
    private Map<Long,List<GoodsCouponEvent>> canApplyCoupons;
}
