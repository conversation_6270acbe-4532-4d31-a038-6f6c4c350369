package com.xiaomi.nr.coupon.api.dto.coupon.checkout;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 各级政策
 *
 * <AUTHOR>
 */
@Data
public class PolicyLevel implements Serializable {
    private static final long serialVersionUID = 8503134307881021177L;
    /**
     * 可以优惠的具体政策
     */
    private RuleEle rule;

    /**
     * 需要满足的条件
     */
    private List<QuotaEle> quota;
}
