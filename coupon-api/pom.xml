<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <artifactId>coupon</artifactId>
        <groupId>com.xiaomi.nr</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.xiaomi.nr</groupId>
    <artifactId>coupon-api</artifactId>
    <version>1.1.9-SNAPSHOT</version>

    <!--  <distributionManagement>
      <repository>
          <id>central</id>
          <name>maven-release-virtual</name>
          <url>https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual</url>
      </repository>
      <snapshotRepository>
          <id>snapshots</id>
          <name>maven-snapshot-virtual</name>
          <url>https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual</url>
      </snapshotRepository>
  </distributionManagement>-->

    <dependencies>

        <dependency>
            <groupId>com.xiaomi.youpin</groupId>
            <artifactId>youpin-infra-rpc</artifactId>
            <version>2.0.0-CNZONE-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.youpin</groupId>
            <artifactId>common</artifactId>
            <version>1.7-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>xiaomi-dubbo-validator</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

    </dependencies>



</project>