#server
app.name=coupon
server.type=ut
server.port=8081
server.debug=true
server.connection-timeout=1000

dubbo.group=ut
dubbo.group.rpc=ut
dubbo.protocol.id=dubbo
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.registry.address=nacos://nacos.test.b2c.srv:80
nacos.config.addrs=nacos.test.b2c.srv:80

youpin.log.group=ut
log.path=/tmp

talos.topic=cnzone_newretail_common_test
talos.sendpoint=http://staging-cnbj2-talos.api.xiaomi.net
talos.access.key=AKTAFN57IDDEY7VBBP
talos.access.secret=GDA8I/MpcbAosZv0H2jtdG9GEVXnXrAYS0ovVeaAYs61dMdz4CIUTiiJkkLi0AmzDwcYEiXVYiQ7vEmetLSQvOPjnP58/xgQzCjOhVoFQxy/u1c86wpYExgUvzpxIsNmLvtYJmLd7q8liEEtt7QA
talos.access.secret@kc-sid=mi_newretail_risk.g


logging.level.com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig=DEBUG

spring.xmpulsecoupon-datasource.url=jdbc:h2:mem:nr_coupon_admin;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.xmpulsecoupon-datasource.driver-class-name=org.h2.Driver
spring.xmpulsecoupon-datasource.username=root
spring.xmpulsecoupon-datasource.password=root

spring.xmpulse-datasource.url=jdbc:h2:mem:xm_pulse;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.xmpulse-datasource.driver-class-name=org.h2.Driver
spring.xmpulse-datasource.username=root
spring.xmpulse-datasource.password=root

spring.xmpulse-slave-datasource.url=jdbc:h2:mem:xm_pulse;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.xmpulse-slave-datasource.driver-class-name=org.h2.Driver
spring.xmpulse-slave-datasource.username=root
spring.xmpulse-slave-datasource.password=root

spring.h2.console.enabled=true
spring.h2.console.settings.web-port=9092

spring.newcoupon.redis.host=127.0.0.1
spring.newcoupon.redis.port=6379
spring.newcoupon.redis.password=root
spring.newcoupon.redis.password@kc-sid=mi_newretail_risk.g
spring.newcoupon.redis.timeout=1000
spring.newcoupon.redis.database=0
spring.newcoupon.redis.lettuce.pool.max-idle=32
spring.newcoupon.redis.lettuce.pool.min-idle=1
spring.newcoupon.redis.lettuce.pool.shutdown-timeout=5000
spring.newcoupon.redis.lettuce-pool.max-total=200
spring.newcoupon.redis.lettuce-pool.timeout=3000
spring.newcoupon.redis.lettuce.pool.max-wait-millis=3000


spring.pulse.redis.host=127.0.0.1
spring.pulse.redis.port=6379
spring.pulse.redis.password=root
spring.pulse.redis.password@kc-sid=mi_newretail_risk.g
spring.pulse.redis.timeout=1000
spring.pulse.redis.database=0
spring.pulse.redis.lettuce.pool.max-idle=32
spring.pulse.redis.lettuce.pool.min-idle=1
spring.pulse.redis.lettuce.pool.shutdown-timeout=5000
spring.pulse.redis.lettuce-pool.max-total=200
spring.pulse.redis.lettuce-pool.timeout=3000
spring.pulse.redis.lettuce.pool.max-wait-millis=3000

spring.karos.misc.redis.host=127.0.0.1
spring.karos.misc.redis.port=6379
spring.karos.misc.redis.password=root
spring.karos.misc.redis.password@kc-sid=mi_newretail_risk.g
spring.karos.misc.redis.timeout=1000
spring.karos.misc.redis.database=0
spring.karos.misc.redis.lettuce.pool.max-idle=32
spring.karos.misc.redis.lettuce.pool.min-idle=1
spring.karos.misc.redis.lettuce.pool.shutdown-timeout=5000
spring.karos.misc.redis.lettuce-pool.max-total=200
spring.karos.misc.redis.lettuce-pool.timeout=3000
spring.karos.misc.redis.lettuce.pool.max-wait-millis=3000


spring.misc.redis.host=127.0.0.1
spring.misc.redis.port=6379
spring.misc.redis.password=root
spring.misc.redis.password@kc-sid=mi_newretail_risk.g
spring.misc.redis.timeout=3000
spring.misc.redis.database=0
spring.misc.redis.lettuce.pool.max-idle=8
spring.misc.redis.lettuce.pool.min-idle=1
spring.misc.redis.lettuce.pool.shutdown-timeout=10000
spring.misc.redis.lettuce-pool.max-total=200
spring.misc.redis.lettuce-pool.timeout=3000
spring.misc.redis.lettuce.pool.max-wait-millis=3000

keycenter.sid=keycenter-test

rpc.etcd.host=http://etcd.test.mi.com


rocketmq.name-server=staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876
rocketmq.consumer.access-key=AKB7FI7MHQH2BBQQDW
rocketmq.consumer.secret-key=GDB4g6+6gBe1C83rQRzWaUYqGhJbJuet2Y+F1W32vOgtFF5Bh5I8hzFLNFcUx5Ch49AYEhnAZIZ3202AiqgLa/+iFr+X/xgQ8kNkLrQNRIGQTCP5lvQJSRgUjws80fH3UqpSu4sfi7Ic+/LQrFsA
rocketmq.consumer.secret-key@kc-sid=mi_newretail_risk.g
rocketmq.push-consumer.access-key=AKB7FI7MHQH2BBQQDW
rocketmq.push-consumer.secret-key=GDB4g6+6gBe1C83rQRzWaUYqGhJbJuet2Y+F1W32vOgtFF5Bh5I8hzFLNFcUx5Ch49AYEhnAZIZ3202AiqgLa/+iFr+X/xgQ8kNkLrQNRIGQTCP5lvQJSRgUjws80fH3UqpSu4sfi7Ic+/LQrFsA
rocketmq.push-consumer.secret-key@kc-sid=mi_newretail_risk.g
rocketmq.consumer.order.online.topic=nr_message_bus
rocketmq.consumer.order.offline.topic=nr_message_bus_offline
rocketmq.orderCouponCount-consumer.group.online=nr_coupon_orderCount
rocketmq.orderCouponCount-consumer.group.offline=nr_coupon_orderCount_offline
rocketmq.order.coupon.tag=OrderPaid

rocketmq.couponUsePush.name-server=staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876
rocketmq.carCouponUsePush.name-server=staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876
rocketmq.couponAssignNotify.name-server=staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876

rocketmq.producer.secret-key=GDBAJMXVJZ2FysWQR9WlxsmmXZhsF1RHVgN+3o6jy7+s96bdexVz5BY/OYT7q4EoGWsYEm2RdXe9VELVnGgd9tbQvCmP/xgQwxVBlSQQSnqXI5IxLnapkBgUuryhq/ELaIPrG0W+yBadiMm2BEoA
rocketmq.producer.secret-key@kc-sid=mi_newretail_risk.g

sentinel.dashboard.host=http://sentinel.test.be.mi.com

coupon.config.cache.biz.platform=0,3,4,5

unittest.mock.datasource[0].beanName=xmPulseWriteDatasource
unittest.mock.datasource[0].ddlSql=db/schema/xm_pulse.sql
unittest.mock.datasource[0].dmlSql=db/case/xm_pulse.sql
unittest.mock.datasource[1].beanName=xmPulseCouponDatasource
unittest.mock.datasource[1].ddlSql=db/schema/nr_coupon_admin.sql
unittest.mock.datasource[1].dmlSql=db/case/nr_coupon_admin.sql

keycenter.decrypt.enable=false