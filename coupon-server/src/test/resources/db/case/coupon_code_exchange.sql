/* 券配置数据 */
INSERT INTO `nr_coupon_config`(`id`, `name`, `status`, `coupon_desc`, `promotion_type`, `send_scene`, `send_purpose`, `start_fetch_time`, `end_fetch_time`, `use_time_type`, `start_use_time`, `end_use_time`, `use_duration`, `use_channel`, `use_platform`, `use_store`, `bottom_type`, `bottom_price`, `bottom_count`, `promotion_value`, `max_reduce`, `scope_type`, `goods_include`, `goods_departments`, `goods_exclude`, `category_ids`, `apply_count`, `fetch_limit`, `ext_prop`, `area_ids`, `cost_share`, `creator`, `create_time`, `department_id`, `source`, `code`, `send_channel`, `coupon_type`, `time_granularity`, `shipment_id`) VALUES (30318, '券码测试', 2, '不适用于秒杀、拼团、套装、众筹、第三方产品、虚拟产品等，涉及产品以优惠券实际使用情况为准。', 4, 'A306A1AD9AB1C2925F7DBEA051858C09', 3, 1655740800, 1908201600, 2, 1655740800, 1908201600, 120, '1', '{"1":{"all":true}}', '', 2, 0, 1, 4000, 0, 1, 0x7B22736B75223A5B363935395D2C227061636B61676573223A5B5D7D, '1', '', '', 10, 2, '{"postFree":2,"share":2,"area":2,"proMember":2}', '', '{"6":100}', 'zhumingxia', 1654138699, 0, 2, 2, 'other', 1, 1, -1);

/* 兑换码数据 */
INSERT INTO `nr_coupon_code`(`id`, `code`, `code_md`, `status`, `config_id`, `task_id`, `exchange_time`, `user_id`, `coupon_id`, `end_time`, `create_time`) VALUES (998, 'CUR5DUZR7XMSLVXB', '460245eec2a02e6d173b779dbee6b8b9', 1, 30318, 155, 0, 0, 0, 1908201600, 1655740800);
INSERT INTO `nr_coupon_code`(`id`, `code`, `code_md`, `status`, `config_id`, `task_id`, `exchange_time`, `user_id`, `coupon_id`, `end_time`, `create_time`) VALUES (999, '6NTF7NHFAYLXU7VA', '5eee9509277861a47eea9de416ca0f77', 1, 30318, 155, 0, 0, 0, 1908201600, 1655740800);
INSERT INTO `nr_coupon_code`(`id`, `code`, `code_md`, `status`, `config_id`, `task_id`, `exchange_time`, `user_id`, `coupon_id`, `end_time`, `create_time`) VALUES (1000, 'YG5RZGFWMX9CGBYM', 'a93e28b0c79bedc50f06ecac1ac16938', 1, 30318, 155, 0, 0, 0, 1908201600, 1655740800);