-- nc_coupon_config > -1d
INSERT INTO `nr_coupon_config` (`id`, `name`, `status`, `coupon_desc`, `budget_apply_no`, `line_num`, `budget_create_time`, `br_apply_no`, `promotion_type`, `send_scene`, `send_purpose`, `start_fetch_time`, `end_fetch_time`, `use_time_type`, `start_use_time`, `end_use_time`, `use_duration`, `use_channel`, `use_platform`, `use_store`, `bottom_type`, `bottom_price`, `bottom_count`, `promotion_value`, `max_reduce`, `scope_type`, `goods_include`, `goods_departments`, `goods_exclude`, `category_ids`, `apply_count`, `release_count`, `fetch_limit`, `ext_prop`, `area_ids`, `cost_share`, `creator`, `create_time`, `update_time`, `department_id`, `source`, `code`, `send_channel`, `coupon_type`, `time_granularity`, `shipment_id`, `auto_update_goods`, `biz_platform`, `service_type`, `fetch_limit_type`, `times_limit`, `public_promotion`) VALUES
('194714', '乾坤满件折减券-卡券自动化测试专用-jpj', '1', '乾坤折扣券-促销自动化测试专用-zmx', '', null, '', '', '2', 'DCCC8C085E79839661D9B93E9A996EC8', '8', '1737579734', '2055489536', '2', '1737579734', '2055489536', '48', '1,2,3,4', '{"1":{"all":true,"limitIds":["2","3","4"]}}', '{"2":{"all":false,"limitIds":["direct","MI0101","MI0102","MI0201"]},"3":{"all":true,"limitIds":[]},"4":{"all":true,"limitIds":[]}}', '2', '0', '3', '900', '9998', '1', '{"sku":[6982],"packages":[]}', '1', '', '0', '10000', null, '1', '{"postFree":1,"share":0,"area":0,"proMember":2,"specialStore":0,"publicPromotion":2,"checkoutStage":2,"displayDate":1,"annualType":0}', '', '{"1":100}', 'hanyuxia', '1737579734', '2025-01-23 05:02:14', '0', '2', '2', 'other', '1', '2', '-1', '2', '0', '0', '1', '1', '1'),
('194715', '超级补贴券立减券-卡券自动化测试专用-jpj', '1', '超级补贴券立减券-卡券自动化测试专用', '', null, '', '', '4', 'DCCC8C085E79839661D9B93E9A996EC8', '8', '1737579734', '2055489536', '1', '1737579734', '2055489536', '0', '1,2,3,4', '{"1":{"all":true,"limitIds":["2","3","4"]}}', '{"2":{"all":false,"limitIds":["spec","MI0101","MI0102","JM002"]},"3":{"all":true,"limitIds":[]},"4":{"all":true,"limitIds":[]}}', '2', '0', '1', '1000', '0', '1', '{"sku":[6982],"packages":[]}', '1', '', '0', '10', null, '1', '{"postFree":1,"share":0,"area":0,"proMember":2,"specialStore":0,"publicPromotion":2,"checkoutStage":2,"displayDate":1,"annualType":0}', '', '{"1":100}', 'jipujun', '1737579734', '2025-01-23 05:02:14', '0', '2', '2', 'test', '3', '0', '-1', '2', '5', '0', '1', '1', '1'),
('194712', '超级补贴券立减券-卡券自动化测试专用-jpj', '1', '超级补贴券立减券-卡券自动化测试专用', '', null, '', '', '4', '977E98E6C659D1BCB23C47D685B45BC9', '8', '1737579733', '2055489536', '1', '1737579733', '2055489536', '0', '1,2,3,4', '{"1":{"all":true,"limitIds":["2","3","4"]}}', '{"2":{"all":false,"limitIds":["MI0101","MI0102","JM002"]},"3":{"all":true,"limitIds":[]},"4":{"all":true,"limitIds":[]}}', '2', '0', '1', '1000', '0', '1', '{"sku":[6982],"packages":[]}', '1', '', '0', '10', null, '1', '{"postFree":1,"share":0,"area":0,"proMember":2,"specialStore":0,"publicPromotion":2,"checkoutStage":2,"displayDate":1,"annualType":0}', '', '{"1":100}', 'jipujun', '1737579733', '2025-01-23 05:02:13', '0', '2', '2', 'test', '3', '0', '-1', '2', '4', '1', '1', '1', '2');

-- nc_coupon_config > -3m and < -1d
INSERT INTO `nr_coupon_config` (`id`, `name`, `status`, `coupon_desc`, `budget_apply_no`, `line_num`, `budget_create_time`, `br_apply_no`, `promotion_type`, `send_scene`, `send_purpose`, `start_fetch_time`, `end_fetch_time`, `use_time_type`, `start_use_time`, `end_use_time`, `use_duration`, `use_channel`, `use_platform`, `use_store`, `bottom_type`, `bottom_price`, `bottom_count`, `promotion_value`, `max_reduce`, `scope_type`, `goods_include`, `goods_departments`, `goods_exclude`, `category_ids`, `apply_count`, `release_count`, `fetch_limit`, `ext_prop`, `area_ids`, `cost_share`, `creator`, `create_time`, `update_time`, `department_id`, `source`, `code`, `send_channel`, `coupon_type`, `time_granularity`, `shipment_id`, `auto_update_goods`, `biz_platform`, `service_type`, `fetch_limit_type`, `times_limit`, `public_promotion`) VALUES
('194505', '运费券-卡券自动化测试专用-jpj', '1', '运费券-卡券自动化测试专用', '', null, '', '', '4', 'DCCC8C085E79839661D9B93E9A996EC8', '8', '1737493331', '1737493431', '1', '1737493331', '1737503331', '0', '2,3', '{}', '{"2":{"all":true,"limitIds":["MI0101","MI0102"]},"3":{"all":true,"limitIds":[]}}', '2', '0', '1', '1000', '0', '1', '{"sku":[6982],"packages":[]}', '1', '', '0', '10', null, '1', '{"postFree":2,"share":2,"area":2,"proMember":2,"specialStore":2,"publicPromotion":2,"checkoutStage":2,"displayDate":1,"annualType":0}', '', '{"1":100}', 'jipujun', '1737493331', '2025-01-22 05:02:11', '0', '2', '2', 'test', '2', '0', '139', '2', '0', '0', '1', '1', '2'),
('194507', '超级补贴券立减券-卡券自动化测试专用-jpj', '1', '超级补贴券立减券-卡券自动化测试专用', '', null, '', '', '4', 'DCCC8C085E79839661D9B93E9A996EC8', '8', '1737493331', '1737493431', '1', '1737493331', '1737503331', '0', '1,2,3,4', '{"1":{"all":true,"limitIds":["2","3","4"]}}', '{"2":{"all":false,"limitIds":["MI0101","MI0102","JM002"]},"3":{"all":true,"limitIds":[]},"4":{"all":true,"limitIds":[]}}', '2', '0', '1', '1000', '0', '1', '{"sku":[6982],"packages":[]}', '1', '', '0', '10', null, '1', '{"postFree":1,"share":0,"area":0,"proMember":2,"specialStore":0,"publicPromotion":2,"checkoutStage":2,"displayDate":1,"annualType":0}', '', '{"1":100}', 'jipujun', '1737493331', '2025-01-22 05:02:11', '0', '2', '2', 'test', '3', '0', '-1', '2', '5', '0', '1', '1', '2'),
('194502', '运费券-卡券自动化测试专用-jpj', '1', '运费券-卡券自动化测试专用', '', null, '', '', '4', 'DCCC8C085E79839661D9B93E9A996EC8', '8', '1737493330', '1737493430', '1', '1737493330', '1737503330', '0', '2,3', '{}', '{"2":{"all":true,"limitIds":["MI0101","MI0102"]},"3":{"all":true,"limitIds":[]}}', '2', '0', '1', '1000', '0', '1', '{"sku":[6982],"packages":[]}', '1', '', '0', '10', null, '1', '{"postFree":2,"share":2,"area":2,"proMember":2,"specialStore":2,"publicPromotion":2,"checkoutStage":2,"displayDate":1,"annualType":0}', '', '{"1":100}', 'jipujun', '1737493330', '2025-01-22 05:02:10', '0', '2', '2', 'test', '2', '0', '139', '2', '0', '0', '1', '1', '2');

INSERT INTO `nr_coupon_config`(`id`, `name`, `status`, `coupon_desc`, `budget_apply_no`, `line_num`, `budget_create_time`, `br_apply_no`, `promotion_type`, `send_scene`, `send_purpose`, `start_fetch_time`, `end_fetch_time`, `use_time_type`, `start_use_time`, `end_use_time`, `use_duration`, `use_channel`, `use_platform`, `use_store`, `bottom_type`, `bottom_price`, `bottom_count`, `promotion_value`, `max_reduce`, `scope_type`, `goods_include`, `goods_departments`, `goods_exclude`, `category_ids`, `apply_count`, `release_count`, `fetch_limit`, `ext_prop`, `area_ids`, `cost_share`, `creator`, `create_time`, `update_time`, `department_id`, `source`, `code`, `send_channel`, `coupon_type`, `time_granularity`, `shipment_id`, `auto_update_goods`, `biz_platform`, `service_type`, `fetch_limit_type`, `times_limit`, `public_promotion`)
VALUES (190396, '600013449优惠券', 1, '不适用于秒杀、拼团、套装、众筹、第三方产品、虚拟产品等，涉及产品以优惠券实际使用情况为准。', '', 0, '', '3e4dbb5161e94ce5bb81d339a85b8ee8', 4, '********************************', 1, 1735833600, 1740758399, 1, 1735833600, 1740758399, 0, '1,2,3,4', '{"1":{"all":true}}', '{"2":{"all":true,"limitIds":[]},"3":{"all":true,"limitIds":[]},"4":{"all":true,"limitIds":[]}}', 2, 0, 1, 1000, 0, 1, '{"sku":[],"packages":[],"ssu":[],"suit":[600013449]}', '1', '', '', 1000, 0, 100, '{"postFree":2,"share":2,"area":2,"proMember":2,"specialStore":2,"publicPromotion":2,"checkoutStage":2,"displayDate":1,"annualType":0}', '', '{"1":100}', 'liwenlong3', 1735885255, '2025-01-03 14:21:05', 0, 2, 2, 'other', 1, 2, -1, 2, 0, 0, 1, 1, 2);
INSERT INTO `nr_coupon_config`(`id`, `name`, `status`, `coupon_desc`, `budget_apply_no`, `line_num`, `budget_create_time`, `br_apply_no`, `promotion_type`, `send_scene`, `send_purpose`, `start_fetch_time`, `end_fetch_time`, `use_time_type`, `start_use_time`, `end_use_time`, `use_duration`, `use_channel`, `use_platform`, `use_store`, `bottom_type`, `bottom_price`, `bottom_count`, `promotion_value`, `max_reduce`, `scope_type`, `goods_include`, `goods_departments`, `goods_exclude`, `category_ids`, `apply_count`, `release_count`, `fetch_limit`, `ext_prop`, `area_ids`, `cost_share`, `creator`, `create_time`, `update_time`, `department_id`, `source`, `code`, `send_channel`, `coupon_type`, `time_granularity`, `shipment_id`, `auto_update_goods`, `biz_platform`, `service_type`, `fetch_limit_type`, `times_limit`, `public_promotion`)
VALUES (193463, 'test_0117', 1, '不适用于秒杀、拼团、套装、众筹、第三方产品、虚拟产品等，涉及产品以优惠券实际使用情况为准。', '', 0, '', 'acf4ce4dcca44d5e98d82cbd98acb143', 1, '********************************', 1, 1737043200, 1740758399, 1, 1737043200, 1740758399, 0, '1,2,3,4', '{"1":{"all":true}}', '{"2":{"all":true,"limitIds":[]},"3":{"all":true,"limitIds":[]},"4":{"all":true,"limitIds":[]}}', 1, 1000, 0, 200, 0, 1, '{"sku":[26671],"packages":[],"ssu":[],"suit":[]}', '1', '', '', 100, 0, 10, '{"postFree":1,"share":2,"area":2,"proMember":2,"specialStore":2,"publicPromotion":2,"checkoutStage":2,"displayDate":1,"annualType":0}', '', '{"1":100}', 'liwenlong3', 1737100158, '2025-01-17 15:49:29', 0, 2, 2, 'other', 1, 2, -1, 2, 0, 0, 1, 1, 2);

-- nr_coupon_scene
INSERT INTO `nr_coupon_scene` (`id`, `id_generation_type`, `name`, `relation_scene_id`, `scene_code`, `scene_desc`, `send_mode`, `assign_mode`, `status`, `apply_mark`, `creator`, `create_time`, `modifier`, `update_time`, `coupon_type`, `ext_props`, `biz_platform`) VALUES
('125', '1', '0122耗材券场景', '102', 'DCCC8C085E79839661D9B93E9A996EC8', '说明', '1', '1,2', '1', '123', 'p-chenweiming7', '2025-01-22 16:40:15', 'p-chenweiming7', '1737536678', '11,12', 'customUseTime', '4'),
('124', '1', '抵扣2元券场景', '104', '977E98E6C659D1BCB23C47D685B45BC9', '抵扣2元券场景说明', '1', '1,2', '1', '抵扣2元券场景原因', 'p-chenweiming7', '2025-01-21 20:15:21', '', '0', '1', 'postFree,customUseTime', '5'),
('123', '1', '0121购买耗材包发券', '104', 'D5E8A684890F26064A8E658B3D49301C', '0121购买耗材包发券说明', '1', '1', '2', '0121购买耗材包发券原因', 'p-chenweiming7', '2025-01-21 15:58:14', 'p-chenweiming7', '1737457085', '1', 'postFree,customUseTime', '5');

INSERT INTO `nr_coupon_scene`(`id`, `id_generation_type`, `name`, `relation_scene_id`, `scene_code`, `scene_desc`, `send_mode`, `assign_mode`, `status`, `apply_mark`, `creator`, `create_time`, `modifier`, `update_time`, `coupon_type`, `ext_props`, `biz_platform`)
VALUES (1, 1, 'bingo营销活动', 1, '********************************', '通过bingo活动后台发券，或者通过海葵系统发券，也可通过灌券系统发券', 1, '2,1', 1, '支持灌券任务、营销互动平台，bingo活动后台发券', 'zhujingyu1', '2022-03-17 08:57:54', 'zhujingyu1', 1677140527, '1,2,3', 'postFree,specialStore,auGoods,share', 0);

-- nr_coupon_service_scene
INSERT INTO `nr_coupon_service_scene` (`id`, `name`, `coupon_type`, `deduct_rule`, `mutual_rule`, `status`, `creator`, `create_time`, `support_order_type`) VALUES
('4', '按需保养', '11', '1', '1', '1', 'wanghaotian7', '2025-01-09 19:13:45', '1,2'),
('5', '耗材券', '11', '2', '2', '1', 'wanghaotian7', '2025-01-09 19:13:45', '1,2,4'),
('3', '上门补胎', '12', '2', '1', '1', 'liwenlong3', '2024-05-13 19:17:35', '1,2'),
('2', '漆面修复', '11', '2', '2', '1', 'liwenlong3', '2024-03-05 20:23:52', '1,2'),
('1', '基础保养', '11', '1', '1', '1', 'liwenlong3', '2024-03-05 20:23:26', '1,2');

-- nr_coupon_scene_permission
INSERT INTO `nr_coupon_scene_permission` (`id`, `scene_id`, `app_id`, `app_name`, `app_contact`, `channel`, `creator`, `add_time`, `status`, `modifier`, `update_time`) VALUES
('125', '125', 'XM2114', '灌券系统', 'wanghaotian7', '0', 'p-chenweiming7', '2025-01-22 16:40:53', '1', 'p-chenweiming7', '1737535256'),
('124', '125', 'testAppId', '灌券系统', 'wanghaotian7', '0', 'p-chenweiming7', '2025-01-22 16:40:53', '1', 'p-chenweiming7', '1737535256'),
('121', '124', 'testAppId', '灌券系统', 'wanghaotian7', '0', 'p-chenweiming7', '2025-01-21 16:26:58', '1', 'p-chenweiming7', '1737448021'),
('123', '121', 'XM2114', '灌券系统', 'wanghaotian7', '0', 'p-chenweiming7', '2025-01-21 16:26:58', '1', 'p-chenweiming7', '1737448021'),
('122', '119', 'XM2114', '灌券系统', 'wanghaotian7', '0', 'p-jianwei', '2025-01-20 19:48:19', '1', 'p-jianwei', '1737373704'),
(1, 1, '101', '小米商城/商城活动系统', 'mi..', 0, 'hejiapeng', '2022-03-17 09:29:17', 1, '', 0),
(84, 1, 'XM2107', '测试', '汤晓艳', 0, 'tangxiaoyan', '2022-09-16 15:17:14', 1, 'tangxiaoyan', 1663312638),
(7, 1, 'XM2114', '乾坤后台', 'yang', 0, 'yangmenghui', '2022-03-31 17:38:11', 1, 'yangmenghui', 1649225109),
(82, 1, 'XM2115', '零售中台-互动营销系统', '陈佳斌', 0, 'hejiapeng', '2022-09-01 16:11:04', 1, 'hejiapeng', 1662019869);

-- config_id = 190396
INSERT INTO `nr_coupon_task` (`task_id`, `task_name`, `status`, `type`, `priority`, `parent_id`, `params`, `source`, `offset`, `process_rate`, `process_desc`, `create_time`, `start_time`, `finish_time`, `result`, `config_id`, `update_time`, `retry_times`, `alarm_status`, `department_id`, `creator`, `version`, `biz_platform`) VALUES
('1978', '190396', '4', '11', '0', '0', '{"address":"/user/s_nr_center/cardvoucher/coupon/fillcoupon/dataset/input_1735885288183","count":15,"dataType":1,"manualRetry":0,"applyCount":15,"customizeGroup":2,"endFetchTime":0}', '2', '0', '100', '', '1735885299', '1735885299', '1735885305', '{"totalCount":15,"currentCount":15,"successCount":15}', '190396', '1735885299', '0', '0', '0', 'liwenlong3', '0', '0');

INSERT INTO `nr_coupon_config` (`id`, `name`, `status`, `coupon_desc`, `budget_apply_no`, `line_num`, `budget_create_time`, `br_apply_no`, `promotion_type`, `send_scene`, `send_purpose`, `start_fetch_time`, `end_fetch_time`, `use_time_type`, `start_use_time`, `end_use_time`, `use_duration`, `use_channel`, `use_platform`, `use_store`, `bottom_type`, `bottom_price`, `bottom_count`, `promotion_value`, `max_reduce`, `scope_type`, `goods_include`, `goods_departments`, `goods_exclude`, `category_ids`, `apply_count`, `release_count`, `fetch_limit`, `ext_prop`, `area_ids`, `cost_share`, `creator`, `create_time`, `update_time`, `department_id`, `source`, `code`, `send_channel`, `coupon_type`, `time_granularity`, `shipment_id`, `auto_update_goods`, `biz_platform`, `service_type`, `fetch_limit_type`, `times_limit`, `public_promotion`) VALUES
('197569', 'test_couponcode', '1', 'test', '', '0', '', 'eaf1bbd145c04d209e664bda6cb76138', '4', 'A306A1AD9AB1C2925F7DBEA051858C09', '1', '1738771200', '1774972799', '1', '1738771200', '1774972799', '0', '1,2,3,4', '{"1":{"all":true}}', '{"2":{"all":true,"limitIds":[]},"3":{"all":true,"limitIds":[]},"4":{"all":true,"limitIds":[]}}', '2', '0', '1', '1000', '0', '1', '{"sku":[4706],"packages":[],"ssu":[],"suit":[600013293]}', '1', '', '', '1000', '0', '0', '{"postFree":2,"share":2,"area":2,"proMember":2,"specialStore":2,"publicPromotion":2,"annualType":0,"checkoutStage":2,"displayDate":1}', '', '{"1":100}', 'liwenlong3', '1738827057', '2025-02-06 15:31:18', '0', '2', '2', 'other', '1', '2', '-1', '2', '0', '0', '1', '1', '2');

INSERT INTO `nr_coupon_scene` (`id`, `id_generation_type`, `name`, `relation_scene_id`, `scene_code`, `scene_desc`, `send_mode`, `assign_mode`, `status`, `apply_mark`, `creator`, `create_time`, `modifier`, `update_time`, `coupon_type`, `ext_props`, `biz_platform`) VALUES
('15', '1', '校园招聘券', '100', 'A306A1AD9AB1C2925F7DBEA051858C09', '用户参与校园招聘活动获得的券，通过优惠码形式发放', '2', '', '1', '用户参与校园招聘活动获得的券，通过优惠码形式发放', 'zhujingyu1', '2022-03-31 14:57:36', '', '0', '1', '', '0');

INSERT INTO `nr_coupon_scene_permission` (`id`, `scene_id`, `app_id`, `app_name`, `app_contact`, `channel`, `creator`, `add_time`, `status`, `modifier`, `update_time`) VALUES
('127', '15', 'XM2107', 'test', 'liwenlong3', '0', 'liwenlong3', '2025-02-07 14:26:07', '1', 'liwenlong3', '1738909571');

INSERT INTO `nr_coupon_config` (`id`, `name`, `status`, `coupon_desc`, `budget_apply_no`, `line_num`, `budget_create_time`, `br_apply_no`, `promotion_type`, `send_scene`, `send_purpose`, `start_fetch_time`, `end_fetch_time`, `use_time_type`, `start_use_time`, `end_use_time`, `use_duration`, `use_channel`, `use_platform`, `use_store`, `bottom_type`, `bottom_price`, `bottom_count`, `promotion_value`, `max_reduce`, `scope_type`, `goods_include`, `goods_departments`, `goods_exclude`, `category_ids`, `apply_count`, `release_count`, `fetch_limit`, `ext_prop`, `area_ids`, `cost_share`, `creator`, `create_time`, `update_time`, `department_id`, `source`, `code`, `send_channel`, `coupon_type`, `time_granularity`, `shipment_id`, `auto_update_goods`, `biz_platform`, `service_type`, `fetch_limit_type`, `times_limit`, `public_promotion`) VALUES
('197772', 'test_0207', '1', 'test', 'BR202403200004', '9013', '2024-03-20 14:07:20', 'ddbf315e5c7d49b9aadb00d1eded399b', '4', '11BFB15C0E2BA4D03DFF93D95C843D0E', '9', '1738857600', '1806508799', '1', '1738857600', '1806508799', '0', '2', '{}', '{"2":{"all":true,"limitIds":[]}}', '2', '0', '1', '100', '0', '1', '{"ssu":[600000451,600000504,600000488]}', '', '', '', '1000', '0', '10', '{"postFree":2,"share":2,"area":2,"proMember":2,"specialStore":2,"publicPromotion":2,"annualType":0,"checkoutStage":1,"displayDate":1}', '', '{"21":100}', 'liwenlong3', '1738926743', '2025-02-07 19:12:38', '0', '2', '2', 'other', '1', '0', '-1', '2', '3', '0', '1', '1', '2'),
('197773', 'test_jichubaoyang_0207', '1', 'test', '', '0', '', '06c354782cd940a295fd0fb73c23e467', '3', '5D058D0B5C6FDC1BDD5AF5A1961DDE3B', '10', '1738857600', '1806508799', '1', '1738857600', '1806508799', '0', '2', '{}', '{"2":{"all":true,"limitIds":[]}}', '2', '0', '1', '0', '0', '1', '{"labourHourSsu":{"600016459":1},"partsSsu":{"600000981":1}}', '', '', '', '100', '0', '10', '{"postFree":2,"share":2,"area":2,"proMember":2,"specialStore":2,"publicPromotion":2,"annualType":0}', '', '{"1":100}', 'liwenlong3', '1738927258', '2025-02-07 19:21:09', '0', '2', '2', 'other', '11', '2', '-1', '2', '4', '1', '1', '1', '2');

INSERT INTO `nr_coupon_scene` (`id`, `id_generation_type`, `name`, `relation_scene_id`, `scene_code`, `scene_desc`, `send_mode`, `assign_mode`, `status`, `apply_mark`, `creator`, `create_time`, `modifier`, `update_time`, `coupon_type`, `ext_props`, `biz_platform`) VALUES
('79', '1', '汽车测试场景-cxp1', '101', '11BFB15C0E2BA4D03DFF93D95C843D0E', '汽车测试场景-cxp1场景描述', '1', '2,1', '1', '汽车测试场景-cxp1申请备注', 'caoxiaopeng1', '2023-10-07 14:03:22', 'caoxiaopeng1', '1696852575', '1', 'specialStore', '3'),
('105', '1', '测试汽车售后', '102', '5D058D0B5C6FDC1BDD5AF5A1961DDE3B', '测试汽车售后', '1', '2', '1', '测试可包邮是否勾选', 'p-wushaopeng', '2024-12-23 16:10:58', 'p-wushaopeng', '1734944473', '11', '', '4');

INSERT INTO `nr_coupon_scene_permission` (`id`, `scene_id`, `app_id`, `app_name`, `app_contact`, `channel`, `creator`, `add_time`, `status`, `modifier`, `update_time`) VALUES
('94', '79', 'XM2107', 'XM2107', '测试', '0', 'hejiapeng', '2023-10-20 15:54:25', '1', 'hejiapeng', '1697788468'),
('91', '79', 'XM2114', '优惠券后台', '何佳鹏', '0', 'hejiapeng', '2023-10-20 15:45:10', '1', 'hejiapeng', '1697788421'),
('128', '105', 'XM2114', 'test', 'liwenlong3', '0', 'liwenlong3', '2025-02-07 19:41:09', '1', 'liwenlong3', '1738928472');

INSERT INTO `nr_coupon_scene` (`id`, `id_generation_type`, `name`, `relation_scene_id`, `scene_code`, `scene_desc`, `send_mode`, `assign_mode`, `status`, `apply_mark`, `creator`, `create_time`, `modifier`, `update_time`, `coupon_type`, `ext_props`, `biz_platform`) VALUES
('103', '1', '礼品发放（可包邮、通过灌券发放）', '103', '1DC2FBE1B76AE8E10CBD23381492A66A', '用于给车主等用户发放礼品，需要0元订单、包邮的业务场景', '1', '2', '1', '测试申请', 'gaocaiyuan', '2024-12-16 10:25:49', 'gaocaiyuan', '1734319140', '1', 'postFree', '5');

INSERT INTO `nr_coupon_scene_permission` (`id`, `scene_id`, `app_id`, `app_name`, `app_contact`, `channel`, `creator`, `add_time`, `status`, `modifier`, `update_time`) VALUES
('111', '103', 'XM2114', '优惠券后台', 'caoxiaopeng1', '0', 'caoxiaopeng1', '2024-12-16 10:30:10', '1', 'caoxiaopeng1', '1734316213');

INSERT INTO `nr_coupon_mission` (`id`, `stat`, `name`, `group_id`, `send_num`, `type_id`, `coupon_start_time`, `coupon_end_time`, `send_time`, `send_end_time`, `admin_id`, `admin_name`, `add_time`, `area_id`, `download`, `mission_type`, `coupon_days`, `max_num`, `now_num`, `approved_id`, `last_update_time`) VALUES
('193686', 'approved', '', '0', '1000', '194714', '1737129600', '2055489536', '1737129600', '2055489536', '0', 'p-chenweiming7', '1737183436', '0', '', '1', '1440', '1000', '0', '0', '2025-02-12 14:16:30'),
('198793', 'approved', '', '0', '10', '194712', '1739289600', '2055489536', '1739289600', '2055489536', '0', 'p-caozihan', '1739326862', '0', '', '1', '0', '10', '0', '0', '2025-02-12 10:21:35');
