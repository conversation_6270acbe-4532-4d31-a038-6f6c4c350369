CREATE TABLE IF NOT EXISTS `nr_coupon_scene_permission` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
  `scene_id` int(10) NOT NULL DEFAULT '0' COMMENT '渠道Id',
  `app_id` varchar(16) NOT NULL DEFAULT '0' COMMENT 'appId',
  `app_name` varchar(50) NOT NULL DEFAULT '' COMMENT '应用名称',
  `app_contact` varchar(50) NOT NULL DEFAULT '' COMMENT '应用联系人',
  `creator` varchar(30) NOT NULL DEFAULT '' COMMENT '创建人',
  `add_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1 有效 2 停用',
  `modifier` varchar(30) NOT NULL DEFAULT '' COMMENT '修改人邮箱',
  `update_time` int(10) NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scene_appId` (`scene_id`,`app_id`)
);