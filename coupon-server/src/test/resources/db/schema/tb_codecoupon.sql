CREATE TABLE `tb_codecoupon` (
     `id` bigint(20) NOT NULL DEFAULT '0' COMMENT '优惠券编号',
     `coupon_code` varchar(255) NOT NULL DEFAULT '' COMMENT '加密code',
     `coupon_index` varchar(32) NOT NULL DEFAULT '' COMMENT '索引',
     `type_id` int(11) NOT NULL DEFAULT '0' COMMENT '优惠券类型编号',
     `batch_id` varchar(32) NOT NULL DEFAULT '0' COMMENT '活动编号',
     `start_time` int(11) NOT NULL DEFAULT '0' COMMENT '开始时间',
     `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '过期时间',
     `stat` enum('used','unused','expired','locked','invalid','cancel') NOT NULL DEFAULT 'unused' COMMENT '优惠券状态',
     `multiplex` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否可重复使用',
     `total_count` int(11) NOT NULL DEFAULT '0' COMMENT '初始总次数',
     `total_money` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '初始总金额',
     `replace_money` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '实际抵用金额',
     `reduce_express` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '实际减免邮费',
     `residue_count` int(11) NOT NULL DEFAULT '0' COMMENT '剩余次数    ',
     `residue_money` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '剩余金额',
     `offline` tinyint(1) NOT NULL DEFAULT '0' COMMENT '线下使用',
     `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订单编号',
     `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户编号',
     `use_time` int(11) NOT NULL DEFAULT '0' COMMENT '使用时间',
     `expire_time` int(11) NOT NULL DEFAULT '0' COMMENT '过期时间',
     `admin_id` int(11) NOT NULL DEFAULT '0',
     `admin_desc` varchar(64) NOT NULL DEFAULT '',
     `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
     `send_type` enum('reissue','marketing','orderEngine','external') NOT NULL DEFAULT 'marketing' COMMENT '发送类型, reissue(pulse用户中心补发优惠券),marketing(活动发券),external(外部调用pulse接口发券),orderEngine(追单发送的优惠券)',
     `from_order_id` varchar(64) NOT NULL DEFAULT '0' COMMENT '发券的订单编号',
     `invalid_time` int(11) NOT NULL DEFAULT '0' COMMENT '作废时间',
     `last_update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
     `use_mode` tinyint(1) NOT NULL DEFAULT '1' COMMENT '使用方式 1:明码 2:兑换',
     `coupon_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '兑换的优惠券ID',
     `org_code` varchar(30) NOT NULL DEFAULT '' COMMENT '兑换或使用的门店ID',
     PRIMARY KEY (`id`),
     KEY `ix_add_index` (`coupon_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='有码优惠券';