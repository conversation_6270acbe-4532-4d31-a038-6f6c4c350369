CREATE TABLE IF NOT EXISTS `nr_coupon_code` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `code` varchar(255) NOT NULL DEFAULT '' COMMENT '加密的券码',
  `code_md` varchar(32) NOT NULL DEFAULT '' COMMENT '索引',
  `status` tinyint(3) NOT NULL DEFAULT '1' COMMENT '状态 1 未兑换 2 已兑换',
  `config_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '对应的配置ID',
  `task_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '对应的任务ID',
  `exchange_time` int(10) NOT NULL DEFAULT '0' COMMENT '兑换时间',
  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '兑换账户ID',
  `coupon_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '兑换后的券ID',
  `end_time` int(10) NOT NULL DEFAULT '0' COMMENT '截止兑换时间',
  `create_time` int(10) NOT NULL DEFAULT '0' COMMENT '生成时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`),
  KEY `idx_coupon_id` (`coupon_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_code_md` (`code_md`) USING BTREE
);

