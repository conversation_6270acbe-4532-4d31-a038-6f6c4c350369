CREATE TABLE IF NOT EXISTS `tb_codecoupon_log` (
  `coupon_index` varchar(32) NOT NULL DEFAULT '' COMMENT '索引',
  `log_type` int(11) NOT NULL DEFAULT '0' COMMENT '日志类型',
  `replace_money` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '实际抵用金额',
  `reduce_express` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '实际减免邮费',
  `old_residue_count` int(11) NOT NULL DEFAULT '0' COMMENT '老剩余次数',
  `old_residue_money` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '老剩余金额',
  `new_residue_count` int(11) NOT NULL DEFAULT '0' COMMENT '新剩余次数    ',
  `new_residue_money` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '新剩余金额',
  `offline` tinyint(1) NOT NULL DEFAULT '0' COMMENT '线下使用',
  `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订单编号',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户编号',
  `use_desc` varchar(64) NOT NULL DEFAULT '' COMMENT '使用描述',
  `admin_id` int(11) NOT NULL DEFAULT '0',
  `admin_desc` varchar(64) NOT NULL DEFAULT '',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`coupon_index`,`log_type`,`add_time`)
);