 CREATE TABLE IF NOT EXISTS `nr_coupon_config` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '优惠券编号',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '优惠券类型名称',
  `status` tinyint(1) NOT NULL DEFAULT '2' COMMENT '状态, 1:上线, 2:下线, 3:终止',
  `coupon_desc` varchar(1024) NOT NULL DEFAULT '' COMMENT '优惠券描述',
  `promotion_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '优惠类型, 1:满减, 2:满折, 3:N元券, 4:立减',
  `send_scene` varchar(32) NOT NULL DEFAULT '' COMMENT '投放场景',
  `send_purpose` tinyint(1) NOT NULL DEFAULT '0' COMMENT '投放目的',
  `start_fetch_time` int(10) NOT NULL DEFAULT '0' COMMENT '开始领取时间',
  `end_fetch_time` int(10) NOT NULL DEFAULT '0' COMMENT '结束领取时间',
  `use_time_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '使用有效期类型 1 固定有效期,2 相对有效期',
  `start_use_time` int(10) NOT NULL DEFAULT '0' COMMENT '开始使用时间',
  `end_use_time` int(10) NOT NULL DEFAULT '0' COMMENT '结束使用时间',
  `use_duration` int(10) NOT NULL DEFAULT '0' COMMENT '有效时长(单位小时)',
  `use_channel` varchar(50) NOT NULL DEFAULT '' COMMENT '使用渠道 1:小米商城 2:直营店 3:专卖店 4:授权店 5:堡垒店',
  `use_platform` varchar(255) NOT NULL DEFAULT '' COMMENT '使用平台',
  `use_store` blob COMMENT '使用门店',
  `bottom_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '门槛类型 1:满元, 2:满件, 3:每满元, 4:每满件, 5:满元且满件',
  `bottom_price` int(10) NOT NULL DEFAULT '0' COMMENT '满元门槛值 单位分',
  `bottom_count` int(10) NOT NULL DEFAULT '0' COMMENT '满件门槛值 单位个',
  `promotion_value` bigint(20) NOT NULL DEFAULT '0' COMMENT '优惠值(单位分/折)',
  `max_reduce` int(10) NOT NULL DEFAULT '0' COMMENT '最大减免金额 单位分',
  `scope_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '商品范围类型 1 商品券 2 品类券',
  `goods_include` text COMMENT '券可用商品',
  `goods_departments` varchar(255) NOT NULL DEFAULT '' COMMENT '商品渠道',
  `goods_exclude` text COMMENT '排除商品',
  `category_ids` mediumtext COMMENT '类目Id列表',
  `apply_count` int(10) NOT NULL DEFAULT '0' COMMENT '可发放总量',
  `fetch_limit` int(10) NOT NULL DEFAULT '0' COMMENT '每人领取限制',
  `ext_prop` varchar(255) NOT NULL DEFAULT '' COMMENT '附加属性 1可包邮 2可转增 3指定地区',
  `area_ids` varchar(255) NOT NULL DEFAULT '' COMMENT '可使用地区',
  `cost_share` text COMMENT '成本分摊',
  `creator` varchar(100) NOT NULL DEFAULT '' COMMENT '创建人邮箱',
  `create_time` int(10) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后一次修改时间',
  `department_id` int(10) NOT NULL DEFAULT '0' COMMENT '创建部门',
  `source` tinyint(1) NOT NULL DEFAULT '2' COMMENT '券来源 1 老券迁移 2乾坤建券',
  `code` tinyint(1) NOT NULL DEFAULT '2' COMMENT '老数据有码券',
  `send_channel` varchar(255) NOT NULL DEFAULT 'other' COMMENT '老数据发放渠道',
  `coupon_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '优惠券类型 1:商品券 2:运费券',
  `time_granularity` tinyint(1) NOT NULL DEFAULT '1' COMMENT '配置相对时间粒度，1-小时， 2 - 天',
  `shipment_id` int(11) NOT NULL DEFAULT '-1' COMMENT '履约部门，-1代表所有部门',
  `auto_update_goods` tinyint(1) NOT NULL DEFAULT '2' COMMENT '自动更新新品，1-是， 2-否',
  PRIMARY KEY (`id`),
  KEY `idx_end_use_time` (`end_use_time`),
  KEY `idx_update_time` (`update_time`),
  KEY `idx_edn_fetch_time` (`end_fetch_time`) USING BTREE
);