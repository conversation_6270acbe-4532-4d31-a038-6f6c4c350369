CREATE TABLE `tb_car_coupon` (
    `id` bigint(20) NOT NULL DEFAULT '0' COMMENT '优惠券编号',
    `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户编号',
    `vid` varchar(20) NOT NULL DEFAULT '' COMMENT '汽车对应vid',
    `type_id` int(11) NOT NULL DEFAULT '0' COMMENT '优惠券类型编号',
    `activity_id` varchar(32) NOT NULL DEFAULT '0' COMMENT '活动编号',
    `start_time` varchar(20) NOT NULL DEFAULT '0' COMMENT '开始时间',
    `end_time` varchar(20) NOT NULL DEFAULT '0' COMMENT '过期时间',
    `days` int(11) NOT NULL DEFAULT '0' COMMENT '有效天数',
    `stat` varchar(10) NOT NULL DEFAULT 'unused' COMMENT '优惠券状态',
    `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订单编号',
    `use_time` int(11) NOT NULL DEFAULT '0' COMMENT '使用时间',
    `expire_time` int(11) NOT NULL DEFAULT '0' COMMENT '过期时间',
    `is_pass` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否可用',
    `admin_id` int(11) NOT NULL DEFAULT '0' COMMENT '发放部门id',
    `admin_name` varchar(64) NOT NULL DEFAULT '' COMMENT '发放部门名称',
    `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
    `send_type` varchar(10) NOT NULL DEFAULT 'marketing' COMMENT '优惠券发送类型, reissue(pulse用户中心补发优惠券),marketing(活动发券),external(外部调用pulse接口发券),orderEngine(追单发送的优惠券)',
    `from_order_id` varchar(64) NOT NULL DEFAULT '0' COMMENT '发券的订单编号',
    `replace_money` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '实际抵用金额',
    `invalid_time` int(11) NOT NULL DEFAULT '0' COMMENT '作废时间',
    `last_update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    `offline` tinyint(1) NOT NULL DEFAULT '0' COMMENT '线下使用',
    `reduce_express` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '减免邮费',
    `parent_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '券父id',
    `send_channel` varchar(32) NOT NULL DEFAULT 'other' COMMENT '投放渠道',
    `biz_platform` tinyint(1) NOT NULL DEFAULT '0' COMMENT '所属业务 0 零售业务 3 整车销售 4 汽车售后',
    `request_id` varchar(64) NOT NULL DEFAULT '' COMMENT '请求ID',
    `extend_info` text COMMENT '扩展JSON信息',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_idem_id` (`vid`,`send_channel`,`request_id`,`activity_id`),
    KEY `ix_add_time` (`add_time`),
    KEY `ix_vid` (`vid`),
    KEY `idx_type_id_stat` (`type_id`,`stat`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='汽车优惠券';