CREATE TABLE IF NOT EXISTS `tb_coupon_log` (
  `coupon_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '优惠券ID',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户编号',
  `type` varchar(16) DEFAULT '' COMMENT '日志类型',
  `old_stat` varchar(16) NOT NULL DEFAULT '' COMMENT '优惠券原始状态',
  `new_stat` varchar(16) NOT NULL DEFAULT '' COMMENT '优惠券最新状态',
  `admin_id` int(11) DEFAULT '0',
  `admin_name` varchar(64) DEFAULT NULL,
  `coupon_desc` varchar(256) DEFAULT NULL COMMENT '描述',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `offline` tinyint(1) NOT NULL DEFAULT '0' COMMENT '线下使用',
  PRIMARY KEY (`coupon_id`,`user_id`,`add_time`),
  KEY `ix_user_id` (`user_id`),
  KEY `ix_type` (`type`),
  KEY `coupon_id` (`coupon_id`)
);