CREATE TABLE `tb_car_coupon_opt` (
    `id` bigint(20) unsigned NOT NULL COMMENT 'id',
    `vid` varchar(20) NOT NULL DEFAULT '' COMMENT '汽车对应vid',
    `user_id` bigint(20) unsigned NOT NULL COMMENT '用户编号',
    `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订单编号',
    `opt_type` tinyint(3) NOT NULL DEFAULT '1' COMMENT '状态 1 锁定 2 回滚 3 核销',
    `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_order_id_opt_type` (`vid`,`order_id`,`opt_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='汽车优惠券下单操作表';