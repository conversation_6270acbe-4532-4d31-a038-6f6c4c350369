CREATE TABLE IF NOT EXISTS `tb_coupon` (
  `id` bigint(20) NOT NULL COMMENT '优惠券编号',
  `user_id` bigint(20) unsigned DEFAULT NULL COMMENT '用户编号',
  `type_id` int(11) DEFAULT '0' COMMENT '优惠券类型编号',
  `activity_id` varchar(32) DEFAULT '0' COMMENT '活动编号',
  `start_time` varchar(20) DEFAULT '0' COMMENT '开始时间',
  `end_time` varchar(20) DEFAULT '0' COMMENT '过期时间',
  `days` int(11) DEFAULT '0' COMMENT '有效天数',
  `stat` enum('used','unused','expired','locked','invalid','cancel','presented','received') DEFAULT 'unused' COMMENT '优惠券状态',
  `order_id` bigint(20) DEFAULT '0' COMMENT '订单编号',
  `use_time` int(11) DEFAULT '0' COMMENT '使用时间',
  `expire_time` int(11) DEFAULT '0' COMMENT '过期时间',
  `is_pass` tinyint(1) DEFAULT '1' COMMENT '是否可用',
  `admin_id` int(11) DEFAULT '0',
  `admin_name` varchar(64) DEFAULT NULL,
  `add_time` int(11) DEFAULT '0' COMMENT '添加时间',
  `send_type` enum('reissue','marketing','orderEngine','external') NOT NULL DEFAULT 'marketing' COMMENT '优惠券发送类型, reissue(pulse用户中心补发优惠券),marketing(活动发券),external(外部调用pulse接口发券),orderEngine(追单发送的优惠券)',
  `from_order_id` varchar(64) DEFAULT '0' COMMENT '发券的订单编号',
  `replace_money` decimal(16,2) DEFAULT '0.00' COMMENT '实际抵用金额',
  `invalid_time` int(11) DEFAULT NULL COMMENT '作废时间',
  `last_update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `offline` tinyint(1) NOT NULL DEFAULT '0' COMMENT '线下使用',
  `reduce_express` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '减免邮费',
  `parent_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '券父id',
  `send_channel` varchar(32) NOT NULL DEFAULT 'other' COMMENT '投放渠道',
  `request_id` varchar(64) DEFAULT NULL COMMENT '请求ID',
  `extend_info` text NOT NULL COMMENT '扩展信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_idem_id` (`user_id`,`send_channel`,`request_id`,`activity_id`),
  KEY `ix_add_time` (`add_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_t_s` (`type_id`,`stat`),
  KEY `idx_s_e` (`stat`,`end_time`),
  KEY `idx_admin_name` (`admin_name`),
  KEY `ix_use_time` (`use_time`),
  KEY `parent_id` (`parent_id`),
  KEY `idx_last_update_time_id` (`last_update_time`),
  KEY `idx_user_stat_end_time` (`user_id`,`stat`,`end_time`),
  KEY `idx_activity_id` (`activity_id`)
);