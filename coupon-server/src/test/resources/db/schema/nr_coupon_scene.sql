CREATE TABLE IF NOT EXISTS `nr_coupon_scene` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '场景ID',
  `id_generation_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '场景ID生成方式 1:自动生成, 2:手动生成',
  `name` varchar(32) NOT NULL DEFAULT '' COMMENT '投放场景名称',
  `relation_scene_id` tinyint(1) NOT NULL DEFAULT '0' COMMENT '关联场景ID 1:官方营销活动, 2:外部企业合作, 3:售后服务, 100:其他场景',
  `scene_code` varchar(32) NOT NULL DEFAULT '' COMMENT '投放场景编码',
  `scene_desc` varchar(100) NOT NULL DEFAULT '' COMMENT '场景描述',
  `send_mode` tinyint(1) NOT NULL DEFAULT '1' COMMENT '投放方式 1:优惠券, 2:兑换码',
  `assign_mode` varchar(32) NOT NULL DEFAULT '' COMMENT '发放方式(以逗号分隔) 1:外部系统发券, 2:内部系统灌券',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '可用状态 1:上线 2:下线',
  `apply_mark` varchar(256) NOT NULL DEFAULT '' COMMENT '申请备注',
  `creator` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modifier` varchar(30) NOT NULL DEFAULT '' COMMENT '修改人邮箱',
  `update_time` int(10) NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scene_code` (`scene_code`)
);