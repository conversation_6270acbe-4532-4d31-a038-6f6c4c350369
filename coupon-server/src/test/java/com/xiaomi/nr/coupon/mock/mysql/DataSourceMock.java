package com.xiaomi.nr.coupon.mock.mysql;


import com.xiaomi.nr.coupon.config.datasource.XmPulseCouponMysql;
import com.xiaomi.nr.coupon.config.datasource.XmPulseMysqlSlave;
import com.xiaomi.nr.coupon.config.datasource.XmPulseMysqlWrite;
import com.xiaomi.youpin.utest.envmock.MockAction;
import com.xiaomi.youpin.utest.envmock.mysql.MySqlMock;
import mockit.Mock;
import mockit.MockUp;

import javax.sql.DataSource;

/**
 * 数据源mock，需要用户自行添加
 */
public class DataSourceMock extends MockAction {

    @Override
    public void doMock() {
        //mock方法名称与业务方相关
        new XmPulseCouponMysqlMock();
        new XmPulseMysqlSlaveMock();
        new XmPulseMysqlWriteMock();
    }

    /**
     * 普通数据源mock
     */
    public class XmPulseCouponMysqlMock extends MockUp<XmPulseCouponMysql> {

        @Mock
        public DataSource mysqlDataSource() {
            return MySqlMock.getEmbeddedDatabase();
        }
    }

    /**
     * 普通数据源mock
     */
    public class XmPulseMysqlSlaveMock extends MockUp<XmPulseMysqlSlave> {

        @Mock
        public DataSource mysqlDataSource() {
            return MySqlMock.getEmbeddedDatabase();
        }
    }

    /**
     * 普通数据源mock
     */
    public class XmPulseMysqlWriteMock extends MockUp<XmPulseMysqlWrite> {

        @Mock
        public DataSource mysqlDataSource() {
            return MySqlMock.getEmbeddedDatabase();
        }
    }


}
