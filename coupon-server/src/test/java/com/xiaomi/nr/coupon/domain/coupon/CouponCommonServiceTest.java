package com.xiaomi.nr.coupon.domain.coupon;

import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponFormatCommon;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.impl.CouponConfigRedisDaoImpl;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponConvert;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponBootstrap.class)
@ActiveProfiles("dev")
public class CouponCommonServiceTest {

    @Autowired
    private CouponFormatCommon couponCommonService;

    @Autowired
    private UserCouponList userCouponList;

    @Autowired
    private CouponConfigRedisDaoImpl couponConfigRedisDao;

    @Autowired
    private CouponConvert couponConvert;

    @Test
    public void getCouponPoTest() throws BizError {
       /* long userId = 3150000058L;
        String status = "all";
        String sendChannel = "";
        String useChannel = "";
        UserCouponListRequest req = new UserCouponListRequest();
        req.setLastId(1594609023L);
        req.setStatus(status);
        req.setPageSize(30);
        req.setSendChannel(sendChannel);
        req.setUserId(userId);
        req.setUseChannel(useChannel);
        com.google.common.base.Stopwatch stopwatch = Stopwatch.createStarted();
        System.out.println(GsonUtil.toJson(userCouponList.getUserCouponList(req)));
        System.out.println("runTime="+stopwatch.elapsed(TimeUnit.MILLISECONDS));
        stopwatch.stop();
*/
       /* List<CouponInfoModel> couponInfoModelList = userCouponList.formatCouponList(userCouponList.getCouponPoList(req));
        List<CouponInfoItemDto> list = new ArrayList<>();
        for(CouponInfoModel couponInfoModel : couponInfoModelList){
            CouponInfoItemDto couponInfoItemDto = couponConvert.convert2CouponInfoDto(couponInfoModel, CouponInfoItemDto::new);
            list.add(couponInfoItemDto);
        }
        System.out.println(GsonUtil.toJson(list));*/
    }
}
