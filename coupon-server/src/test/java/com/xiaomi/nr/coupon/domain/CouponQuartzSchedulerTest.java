package com.xiaomi.nr.coupon.domain;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.application.scheduler.CouponQuartzScheduler;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.infrastructure.repository.localcache.CouponWhiteCache;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-01 16:16
*/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponBootstrap.class)
@ActiveProfiles("dev")
public class CouponQuartzSchedulerTest {

    @Autowired
    private CouponQuartzScheduler couponQuartzScheduler;

    @Autowired
    private CouponWhiteCache couponWhiteCache;

    @Test
    public void testLoadCouponWhiteCache() {
        couponQuartzScheduler.loadCouponWhiteCache();
        List<Long> userIdList = Lists.newArrayList(3150443896L, 3150443986L, 3150447102L, 3150447103L, 3150447104L, 3150447105L);
        for (Long userId : userIdList) {
            boolean exist = couponWhiteCache.checkWhiteListContain(userId);
            log.info("{} = {}", userId, exist);
        }
    }
}
