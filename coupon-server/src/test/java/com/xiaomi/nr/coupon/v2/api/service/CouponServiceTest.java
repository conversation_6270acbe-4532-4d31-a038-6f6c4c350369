package com.xiaomi.nr.coupon.v2.api.service;

import com.xiaomi.nr.coupon.api.dto.PageBeanStream;
import com.xiaomi.nr.coupon.api.dto.coupon.AssignMissionItemDto;
import com.xiaomi.nr.coupon.api.dto.coupon.AssignRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.AssignResponse;
import com.xiaomi.nr.coupon.api.dto.coupon.CheckoutCheckerRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.CheckoutCheckerResponse;
import com.xiaomi.nr.coupon.api.dto.coupon.CheckoutCouponListRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.CheckoutCouponListResponse;
import com.xiaomi.nr.coupon.api.dto.coupon.CouponUsePushRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.GetUsedCouponRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.GetUsedCouponResponse;
import com.xiaomi.nr.coupon.api.dto.coupon.NearDaysListByTypeIdsItemDto;
import com.xiaomi.nr.coupon.api.dto.coupon.NearDaysListByTypeIdsRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.ProductCouponRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.ProductCouponResponse;
import com.xiaomi.nr.coupon.api.dto.coupon.ProductInfo;
import com.xiaomi.nr.coupon.api.dto.coupon.SimpleGoodsFetchUsableRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.SimpleGoodsFetchUsableResponse;
import com.xiaomi.nr.coupon.api.dto.coupon.SimpleGoodsItem;
import com.xiaomi.nr.coupon.api.dto.coupon.SimpleGoodsUserUsableRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.SimpleGoodsUserUsableResponse;
import com.xiaomi.nr.coupon.api.dto.coupon.UserCouponValidCountRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.UserCouponValidCountResponse;
import com.xiaomi.nr.coupon.api.dto.coupon.checkout.GoodsInfo;
import com.xiaomi.nr.coupon.api.dto.coupon.info.CouponInfoRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.info.CouponInfoResponse;
import com.xiaomi.nr.coupon.api.dto.coupon.list.UserCouponListDto;
import com.xiaomi.nr.coupon.api.dto.coupon.list.UserCouponListRequest;
import com.xiaomi.nr.coupon.api.dto.couponcode.GetCouponCodeInfoRequest;
import com.xiaomi.nr.coupon.api.dto.couponcode.GetCouponCodeInfoResponse;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.api.dto.enums.GoodsExtTagEnum;
import com.xiaomi.nr.coupon.api.service.CouponService;
import com.xiaomi.nr.coupon.enums.couponconfig.CouponServiceTypeEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.SendChannelEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.UseChannelEnum;
import com.xiaomi.nr.coupon.enums.goods.BusinessTypeEnum;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.enums.store.StoreTypeEnum;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.nr.coupon.v2.BaseTest;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.LongStream;

/**
 * <AUTHOR>
 * @date 2025/2/7 16:56
 */
@Slf4j
public class CouponServiceTest extends BaseTest {
    @Autowired
    private CouponService couponService;

    @Test
    public void test_UserCouponValidCount_3C() {
        UserCouponValidCountRequest req = new UserCouponValidCountRequest();
        req.setUserId(3150420867L);
        req.setBizPlatform(Lists.newArrayList(BizPlatformEnum.RETAIL.getCode()));

        Result<UserCouponValidCountResponse> resp = couponService.userCouponValidCount(req);

        log.info("test_UserCouponValidCount_3C resp = {}", GsonUtil.toJson(resp));

        Assertions.assertEquals(GeneralCodes.OK.getCode(), resp.getCode());
        Assertions.assertEquals(2, resp.getData().getCount());
    }

    @Test
    public void test_UserCouponValidCount_car() {
        UserCouponValidCountRequest req = new UserCouponValidCountRequest();
        req.setUserId(3150420867L);
        req.setBizPlatform(Lists.newArrayList(BizPlatformEnum.CAR.getCode()));

        Result<UserCouponValidCountResponse> resp = couponService.userCouponValidCount(req);

        log.info("test_UserCouponValidCount_car resp = {}", GsonUtil.toJson(resp));

        Assertions.assertEquals(GeneralCodes.OK.getCode(), resp.getCode());
        Assertions.assertEquals(5, resp.getData().getCount());
    }

    @Test
    public void test_UserCouponValidCount_carShop() {
        UserCouponValidCountRequest req = new UserCouponValidCountRequest();
        req.setUserId(3150420867L);
        req.setBizPlatform(Lists.newArrayList(BizPlatformEnum.CAR_SHOP.getCode()));

        Result<UserCouponValidCountResponse> resp = couponService.userCouponValidCount(req);

        log.info("test_UserCouponValidCount_carShop resp = {}", GsonUtil.toJson(resp));

        Assertions.assertEquals(GeneralCodes.OK.getCode(), resp.getCode());
        Assertions.assertEquals(5, resp.getData().getCount());
    }

    @Test
    public void test_userCouponList_3C() {
        UserCouponListRequest req = new UserCouponListRequest();
        req.setUserId(3150420867L);
        req.setBizPlatform(Collections.singletonList(BizPlatformEnum.RETAIL.getCode()));
        req.setStatus("all");

        Result<PageBeanStream<UserCouponListDto>> resp = couponService.userCouponList(req);

        log.info("test_userCouponList_3C resp = {}", GsonUtil.toJson(resp));

        Assertions.assertEquals(GeneralCodes.OK.getCode(), resp.getCode());
    }

    @Test
    public void test_userCouponList_car() {
        UserCouponListRequest req = new UserCouponListRequest();
        req.setUserId(3150420867L);
        req.setBizPlatform(Collections.singletonList(BizPlatformEnum.CAR.getCode()));
        req.setStatus("all");

        Result<PageBeanStream<UserCouponListDto>> resp = couponService.userCouponList(req);

        log.info("test_userCouponList_car resp = {}", GsonUtil.toJson(resp));

        Assertions.assertEquals(GeneralCodes.OK.getCode(), resp.getCode());

    }

    @Test
    public void test_userCouponList_carAfterSale() {
        UserCouponListRequest req = new UserCouponListRequest();
        // req.setUserId(3150420867L);
        req.setVid("LKBQWDL7MWSKL4NX0");
        req.setBizPlatform(Collections.singletonList(BizPlatformEnum.CAR_AFTER_SALE.getCode()));
        req.setStatus("unused");

        Result<PageBeanStream<UserCouponListDto>> resp = couponService.userCouponList(req);

        log.info("test_userCouponList_carAfterSale resp = {}", GsonUtil.toJson(resp));

        Assertions.assertEquals(GeneralCodes.OK.getCode(), resp.getCode());
    }

    @Test
    public void test_userCouponList_carShop() {
        UserCouponListRequest req = new UserCouponListRequest();
        req.setUserId(3150420867L);
        req.setBizPlatform(Collections.singletonList(BizPlatformEnum.CAR_SHOP.getCode()));
        req.setStatus("all");

        Result<PageBeanStream<UserCouponListDto>> resp = couponService.userCouponList(req);

        log.info("test_userCouponList_carShop resp = {}", GsonUtil.toJson(resp));

        Assertions.assertEquals(GeneralCodes.OK.getCode(), resp.getCode());
    }

    @Test
    public void test_couponInfo_3C() {
        CouponInfoRequest req = new CouponInfoRequest();
        req.setUserId(3150420867L);
        req.setBizPlatform(BizPlatformEnum.RETAIL.getCode());
        req.setCouponIds(Lists.newArrayList(1011106596L, 1011130002L));


        Result<CouponInfoResponse> resp = couponService.couponInfo(req);

        log.info("test_couponInfo_3C resp = {}", GsonUtil.toJson(resp));

        Assertions.assertEquals(GeneralCodes.OK.getCode(), resp.getCode());
    }

    @Test
    public void test_couponInfo_car() {
        CouponInfoRequest req = new CouponInfoRequest();
        req.setUserId(3150420867L);
        req.setBizPlatform(BizPlatformEnum.CAR.getCode());
        req.setCouponIds(Lists.newArrayList(1011143192L, 1011171619L));


        Result<CouponInfoResponse> resp = couponService.couponInfo(req);

        log.info("test_couponInfo_car resp = {}", GsonUtil.toJson(resp));

        Assertions.assertEquals(GeneralCodes.OK.getCode(), resp.getCode());
    }

    @Test
    public void test_couponInfo_carAfterSale() {
        CouponInfoRequest req = new CouponInfoRequest();
        req.setVid("LKBQWDL7MWSKL4NX0");
        req.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());
        req.setCouponIds(Lists.newArrayList(1011143191L, 1011171616L));


        Result<CouponInfoResponse> resp = couponService.couponInfo(req);

        log.info("test_couponInfo_carAfterSale resp = {}", GsonUtil.toJson(resp));

        Assertions.assertEquals(GeneralCodes.OK.getCode(), resp.getCode());
    }

    @Test
    public void test_couponInfo_carShop() {
        CouponInfoRequest req = new CouponInfoRequest();
        req.setUserId(3150420867L);
        req.setBizPlatform(BizPlatformEnum.CAR_SHOP.getCode());
        req.setCouponIds(Lists.newArrayList(1011171728L, 1011143287L));

        Result<CouponInfoResponse> resp = couponService.couponInfo(req);

        log.info("test_couponInfo_carShop resp = {}", GsonUtil.toJson(resp));

        Assertions.assertEquals(GeneralCodes.OK.getCode(), resp.getCode());
    }

    @Test
    public void test_checkoutCouponList_3c() {
        CheckoutCouponListRequest req = new CheckoutCouponListRequest();
        req.setBizPlatform(BizPlatformEnum.RETAIL.getCode());
        req.setUserId(3150420867L);

        GoodsInfo goodsInfo1 = new GoodsInfo();
        goodsInfo1.setId(26671L);
        goodsInfo1.setLevel("sku");

        GoodsInfo goodsInfo2 = new GoodsInfo();
        goodsInfo2.setId(26672L);
        goodsInfo2.setLevel("sku");
        List<GoodsInfo> skuGoodsList = Lists.newArrayList(goodsInfo1, goodsInfo2);
        req.setSkuPackageList(skuGoodsList);

        Result<CheckoutCouponListResponse> resp = couponService.checkoutCouponList(req);

        log.info("test_checkoutCouponList_3c req = {}, resp = {}", GsonUtil.toJson(req), GsonUtil.toJson(resp));

        Assertions.assertEquals(GeneralCodes.OK.getCode(), resp.getCode());

        Assertions.assertEquals(1, resp.getData().getCouponList().get(0).getAllow());
        Assertions.assertEquals(1, resp.getData().getCouponList().get(1).getAllow());
    }

    @Test
    public void test_getProductCoupon_3C() {
        ProductCouponRequest req = new ProductCouponRequest();


        Result<ProductCouponResponse> resp = couponService.getProductCoupon(req);

        log.info("test_getProductCoupon_3C req = {}, resp = {}", GsonUtil.toJson(req), GsonUtil.toJson(resp));
    }

    // @Test
    public void test_nearDaysListByTypeIds_paramError() {
        NearDaysListByTypeIdsRequest request = new NearDaysListByTypeIdsRequest();
        request.setUserId(-1L);
        Result<Map<String, NearDaysListByTypeIdsItemDto>> result = couponService.nearDaysListByTypeIds(request);
        Assertions.assertEquals("小米ID不符合要求", result.getMessage());

        request.setTypeIds(IntStream.rangeClosed(1, 21).boxed().collect(Collectors.toList()));
        result = couponService.nearDaysListByTypeIds(request);
        Assertions.assertEquals("券配置ID不能超过20个", result.getMessage());

        request.setAddTime(-1L);
        result = couponService.nearDaysListByTypeIds(request);
        Assertions.assertEquals("生成券的时间必须要大于0", result.getMessage());

        request.setAddTime(1L);
        result = couponService.nearDaysListByTypeIds(request);
        Assertions.assertEquals("只能取90天之内生成的券", result.getMessage());

        request.setAddTime(TimeUtil.getNowUnixSecond() + 10);
        result = couponService.nearDaysListByTypeIds(request);
        Assertions.assertEquals("生成时间不能为未来的时间", result.getMessage());
    }

    @Test
    public void test_nearDaysListByTypeIds() {
        NearDaysListByTypeIdsRequest request = new NearDaysListByTypeIdsRequest();
        request.setUserId(3150445185L);
        request.setTypeIds(Lists.newArrayList(190396));
        request.setAddTime(TimeUtil.getNowUnixSecond() - 80 * 24 * 3600);
        Result<Map<String, NearDaysListByTypeIdsItemDto>> result = couponService.nearDaysListByTypeIds(request);
        log.info("nearDaysListByTypeIds result-{}", GsonUtil.toJson(result));
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());
    }

    @Test
    public void test_assign_paramError() {
        AssignRequest request = new AssignRequest();
        Result<AssignResponse> result = couponService.assign(request);
        Assertions.assertEquals("小米ID不符合要求", result.getMessage());
        request.setUserId(1L);

        result = couponService.assign(request);
        Assertions.assertEquals("appID不符合要求", result.getMessage());
        request.setAppId("1");

        result = couponService.assign(request);
        Assertions.assertEquals("请求ID不符合要求", result.getMessage());
        request.setRequestId(RandomStringUtils.random(41));

        result = couponService.assign(request);
        Assertions.assertEquals("请求ID的长度不能超过40个字符", result.getMessage());
        request.setRequestId("1");

        result = couponService.assign(request);
        Assertions.assertEquals("投放渠道不符合要求", result.getMessage());
        request.setSendChannel(SendChannelEnum.StoreManager.getRedisValue());

        result = couponService.assign(request);
        Assertions.assertEquals("分享人小米ID不符合要求", result.getMessage());
        request.setShareUserId(1L);

        result = couponService.assign(request);
        Assertions.assertEquals("门店ID不能为空", result.getMessage());
        request.setOrgCode("direct");

        result = couponService.assign(request);
        Assertions.assertEquals("发放任务不能为空", result.getMessage());
        request.setItems(Lists.newArrayList(new AssignMissionItemDto(), new AssignMissionItemDto()));

        result = couponService.assign(request);
        Assertions.assertEquals("每次只能发放1张券", result.getMessage());
        AssignMissionItemDto assignMissionItemDto = new AssignMissionItemDto();
        request.setItems(Lists.newArrayList(assignMissionItemDto));

        assignMissionItemDto.setMissionId(-1L);
        result = couponService.assign(request);
        Assertions.assertEquals("发放任务不符合发放要求", result.getMessage());
        assignMissionItemDto.setMissionId(1L);

        assignMissionItemDto.setOrderId(-1L);
        request.setItems(Lists.newArrayList(assignMissionItemDto));
        result = couponService.assign(request);
        Assertions.assertEquals("发放关联的订单号不符合要求", result.getMessage());
    }

    @Test
    public void test_assign_single() {
        AssignRequest request = new AssignRequest();
        request.setRequestId("1942_1758_0");
        request.setBizPlatform(BizPlatformEnum.RETAIL.getCode());
        request.setUserId(3150272288L);
        request.setOrgCode("direct");
        request.setSendChannel(SendChannelEnum.StoreManager.getRedisValue());
        request.setShareUserId(1L);
        AssignMissionItemDto itemDto = new AssignMissionItemDto();
        itemDto.setMissionId(193686L);
        itemDto.setOrderId(1L);
        request.setItems(Lists.newArrayList(itemDto));
        request.setAppId("testAppId");
        request.setToken("1");
        Result<AssignResponse> result = couponService.assign(request);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());
        log.info("assgin single result-{}", GsonUtil.toJson(result));
    }

    /**
     * 非乾坤平台发券会报错，此逻辑已废弃，故不修复
     */
    @Test
    public void test_assign_fail() {
        AssignRequest request = new AssignRequest();
        request.setRequestId("1");
        request.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());
        request.setUserId(3150448260L);
        request.setOrgCode("direct");
        request.setSendChannel(SendChannelEnum.StoreManager.getRedisValue());
        request.setShareUserId(1L);
        AssignMissionItemDto itemDto = new AssignMissionItemDto();
        itemDto.setMissionId(198793L);
        itemDto.setOrderId(1L);
        request.setItems(Lists.newArrayList(itemDto));
        request.setAppId("testAppId");
        request.setToken("1");
        Result<AssignResponse> result = couponService.assign(request);
        Assertions.assertEquals("优惠券发放失败", result.getMessage());
        log.info("assgin result-{}", GsonUtil.toJson(result));
    }

    @Test
    public void test_getCouponCodeInfo() {
        GetCouponCodeInfoRequest request = new GetCouponCodeInfoRequest();
        request.setOrderId(5250103948741860L);
        request.setCouponCode("M3KKRKZJK7K9P5KU");
        Result<GetCouponCodeInfoResponse> result = couponService.getCouponCodeInfo(request);
        log.info("getCouponCodeInfo result-{}", GsonUtil.toJson(result));
        Assertions.assertNotNull(result.getData());
    }

    @Test
    public void test_getCouponCodeInfo_paramError() {
        GetCouponCodeInfoRequest request = new GetCouponCodeInfoRequest();
        request.setCouponCode("NOT_EXIST_CODE");
        request.setOrderId(1L);
        Result<GetCouponCodeInfoResponse> result = couponService.getCouponCodeInfo(request);
        Assertions.assertEquals("查询用户优惠码信息异常接口异常", result.getMessage());
    }

    @Test
    public void test_getSimpleGoodsUserUsable() {
        SimpleGoodsUserUsableRequest request = new SimpleGoodsUserUsableRequest();
        request.setUserId(3150272288L);
        request.setOrgCode("direct");
        request.setConfigIdList(Lists.newArrayList(194714L));
        SimpleGoodsItem goodsItem = new SimpleGoodsItem();
        goodsItem.setSkuPackageId(22540L);
        goodsItem.setLevel(GoodsLevelEnum.Sku.getValue());
        request.setGoodsList(Lists.newArrayList(goodsItem));
        Result<SimpleGoodsUserUsableResponse> result;
        try {
            result = couponService.getSimpleGoodsUserUsable(request);
        } catch (BizError e) {
            result = Result.fromException(e);
        }
        log.info("getSimpleGoodsUserUsable result-{}", GsonUtil.toJson(result));
        Assertions.assertNotNull(result.getData());
    }

    @Test
    public void test_getSimpleGoodsFetchUsable() {
        SimpleGoodsFetchUsableRequest request = new SimpleGoodsFetchUsableRequest();
        request.setUserId(3150272288L);
        request.setOrgCode("direct");
        request.setConfigIdList(Lists.newArrayList(194714L));
        SimpleGoodsItem goodsItem = new SimpleGoodsItem();
        goodsItem.setSkuPackageId(22540L);
        goodsItem.setLevel(GoodsLevelEnum.Sku.getValue());
        request.setGoodsList(Lists.newArrayList(goodsItem));
        Result<SimpleGoodsFetchUsableResponse> result = couponService.getSimpleGoodsFetchUsable(request);
        log.info("getSimpleGoodsFetchUsable result-{}", GsonUtil.toJson(result));
        Assertions.assertNotNull(result.getData());
    }

    @Test
    public void test_getUsedCoupons() {
        GetUsedCouponRequest request = new GetUsedCouponRequest();
        request.setUserId(3150272288L);
        request.setClientId(180100041114L);
        request.setCouponCode("M3KKRKZJK7K9P5KU");
        request.setOrgCode("direct");
        request.setOrgType(StoreTypeEnum.ORG_TYPE_DIRECT.getOrgType());
        GoodsInfo goodsInfo = new GoodsInfo();
        request.setSkuPackageList(Lists.newArrayList(goodsInfo));
        goodsInfo.setId(22540L);
        goodsInfo.setLevel(GoodsLevelEnum.Sku.getValue());
        goodsInfo.setTag(GoodsExtTagEnum.LABOR.getTag());
        goodsInfo.setCount(1);
        Result<GetUsedCouponResponse> result = couponService.getUsedCoupons(request);
        log.info("getUsedCoupons result-{}", GsonUtil.toJson(result));

        Assertions.assertNotNull(result.getData());
    }

    @Test
    public void test_getUsedCoupons_paramError() {
        GetUsedCouponRequest request = new GetUsedCouponRequest();
        request.setUserId(-1L);
        Result<GetUsedCouponResponse> result = couponService.getUsedCoupons(request);
        Assertions.assertEquals("小米ID不能为空", result.getMessage());
        request.setUserId(1L);

        result = couponService.getUsedCoupons(request);
        Assertions.assertEquals("应用ID不能为空", result.getMessage());
        request.setClientId(1L);

        result = couponService.getUsedCoupons(request);
        Assertions.assertEquals("优惠券ID和优惠码不能同时为空", result.getMessage());

        request.setCouponIds(Lists.newArrayList(-1L));
        request.setCouponCode("1");
        result = couponService.getUsedCoupons(request);
        Assertions.assertEquals("优惠券ID和优惠码不能同时使用", result.getMessage());

        request.setCouponCode(null);
        result = couponService.getUsedCoupons(request);
        Assertions.assertEquals("优惠券ID存在不合法的值", result.getMessage());

        request.setCouponIds(null);
        request.setCouponCode(" ");
        result = couponService.getUsedCoupons(request);
        Assertions.assertEquals("优惠码存不合法的值", result.getMessage());

        request.setCouponCode("1");
        result = couponService.getUsedCoupons(request);
        Assertions.assertEquals("商品列表不能为空", result.getMessage());
        request.setSkuPackageList(Lists.newArrayList(new GoodsInfo()));

        request.setBizPlatform(-1);
        result = couponService.getUsedCoupons(request);
        Assertions.assertEquals("业务领域不符合要求", result.getMessage());
    }

    @Test
    public void test_couponUsePush() {
        CouponUsePushRequest request = new CouponUsePushRequest();
        request.setCouponId(190396L);
        request.setUserId(3150420867L);
        request.setAppId("XM2107");
        request.setStatus(1);
        request.setModifyTime(System.currentTimeMillis());
        request.setOrderId(1L);
        Result<Void> result = couponService.couponUsePush(request);
        log.info("couponUsePush result-{}", GsonUtil.toJson(result));
        Assertions.assertEquals(GeneralCodes.OK .getCode(), result.getCode());
    }

    @Test
    public void test_checkoutChecker() {
        CheckoutCheckerRequest request = new CheckoutCheckerRequest();
        request.setCouponCode(Lists.newArrayList("M3KKRKZJK7K9P5KU"));
        request.setUserId(3150272288L);
        request.setClientId(180100041114L);
        request.setOrgCode("direct");
        request.setCityId(1L);
        request.setShipmentId(-1);
        GoodsInfo goodsInfo = new GoodsInfo();
        request.setSkuPackageList(Lists.newArrayList(goodsInfo));
        goodsInfo.setId(22540L);
        goodsInfo.setLevel(GoodsLevelEnum.Sku.getValue());
        goodsInfo.setTag(GoodsExtTagEnum.LABOR.getTag());
        goodsInfo.setCount(1);
        Result<CheckoutCheckerResponse> result = couponService.checkoutChecker(request);
        Assertions.assertEquals(GeneralCodes.InternalError.getCode(), result.getCode());
        Assertions.assertEquals("系统繁忙，请稍后再试", result.getMessage());
    }

    @Test
    public void test_checkoutChecker_paramError() {
        CheckoutCheckerRequest request = new CheckoutCheckerRequest();
        request.setUserId(-1L);
        GoodsInfo goodsInfo = new GoodsInfo();
        request.setSkuPackageList(Lists.newArrayList(goodsInfo));
        Result<CheckoutCheckerResponse> result = couponService.checkoutChecker(request);
        Assertions.assertEquals("小米ID不能为空", result.getMessage());

        request.setUserId(1L);
        result = couponService.checkoutChecker(request);
        Assertions.assertEquals("优惠码不能为空", result.getMessage());
        request.setCouponCode(Lists.newArrayList(""));

        result = couponService.checkoutChecker(request);
        Assertions.assertEquals("优惠码不能为空", result.getMessage());
        request.setCouponCode(Lists.newArrayList("1"));

        request.setSkuPackageList(Collections.emptyList());
        result = couponService.checkoutChecker(request);
        Assertions.assertEquals("商品不能为空", result.getMessage());
    }

    @Test
    public void test_getProductCoupon() {
        ProductCouponRequest request = new ProductCouponRequest();
        request.setAppId("testAppId");
        request.setToken("1");
        request.setUserId(3150272288L);
        request.setClientId(180100041114L);
        request.setOrgCode("direct");
        request.setChannelId(SendChannelEnum.Others.getRedisValue());
        request.setCoinCouponTypeIdList(Lists.newArrayList("194714"));
        request.setProMemberCouponIdList(Lists.newArrayList("194714"));
        request.setLevel(GoodsLevelEnum.Goods.getValue());
        ProductInfo goodsInfo = new ProductInfo();
        request.setGoodsInfoList(Lists.newArrayList(goodsInfo));
        goodsInfo.setSku(2181100032L);
        goodsInfo.setPackageId(1193300001L);
        goodsInfo.setBusinessType(BusinessTypeEnum.CN_ORDER.getValue());
        goodsInfo.setSalePrice(1234L);
        goodsInfo.setMarketPrice(2234L);
        goodsInfo.setSource(1);
        Result<ProductCouponResponse> result = couponService.getProductCoupon(request);
        log.info("getProductCoupon result-{}", GsonUtil.toJson(result));
        Assertions.assertNotNull(result.getData());
    }

    @Test
    public void test_checkoutCouponList() {
        CheckoutCouponListRequest request = new CheckoutCouponListRequest();
        request.setUserId(3150272288L);
        request.setClientId(180100041114L);
        request.setOrgCode("direct");
        request.setCityId(1L);
        request.setShipmentId(-1);
        GoodsInfo goodsInfo = new GoodsInfo();
        request.setSkuPackageList(Lists.newArrayList(goodsInfo));
        goodsInfo.setId(22540L);
        goodsInfo.setLevel(GoodsLevelEnum.Sku.getValue());
        goodsInfo.setTag(GoodsExtTagEnum.LABOR.getTag());
        goodsInfo.setCount(1);
        request.setOrgType(StoreTypeEnum.ORG_TYPE_DIRECT.getOrgType());
        Result<CheckoutCouponListResponse> result = couponService.checkoutCouponList(request);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());
    }

    @Test
    public void test_couponInfo_retail() {
        CouponInfoRequest request = new CouponInfoRequest();
        request.setUserId(3150272288L);
        request.setCouponIds(Lists.newArrayList(1011029394L));
        request.setAppId("testAppId");
        request.setToken("1");
        Result<CouponInfoResponse> result = couponService.couponInfo(request);
        log.info("couponInfo retail result-{}", GsonUtil.toJson(result));
        Assertions.assertNotNull(result.getData());
    }

    @Test
    public void test_couponInfo_carAftersale() {
        CouponInfoRequest request = new CouponInfoRequest();
        request.setCouponIds(Lists.newArrayList(1011143524L));
        request.setAppId("testAppId");
        request.setToken("1");
        request.setVid("LKBQNT18WW33VYXL0");
        request.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());
        Result<CouponInfoResponse> result = couponService.couponInfo(request);
        log.info("couponInfo carAftersale result-{}", GsonUtil.toJson(result));
        Assertions.assertNotNull(result.getData());
    }

    @Test
    public void test_couponInfo_paramError() {
        CouponInfoRequest request = new CouponInfoRequest();
        Result<CouponInfoResponse> result = couponService.couponInfo(request);
        Assertions.assertEquals("必须要传优惠券ID", result.getMessage());

        request.setCouponIds(LongStream.rangeClosed(1, 21).boxed().collect(Collectors.toList()));
        result = couponService.couponInfo(request);
        Assertions.assertEquals("一次最多支持20个优惠券的查询", result.getMessage());

        request.setCouponIds(Lists.newArrayList(-1L));
        result = couponService.couponInfo(request);
        Assertions.assertEquals("优惠券ID不符合要求", result.getMessage());
        request.setCouponIds(Lists.newArrayList(1L));

        request.setBizPlatform(-1);
        result = couponService.couponInfo(request);
        Assertions.assertEquals("业务领域不符合要求", result.getMessage());

        request.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());
        request.setCouponIds(Lists.newArrayList(1L));
        result = couponService.couponInfo(request);
        Assertions.assertEquals("vid非法", result.getMessage());
    }

    @Test
    public void test_userCouponList_retail() {
        UserCouponListRequest request = new UserCouponListRequest();
        request.setUserId(3150272288L);
        request.setStatus("unused");
        request.setSendChannel(SendChannelEnum.Others.getRedisValue());
        request.setUseChannel(UseChannelEnum.MiShop.getValue());
        request.setLastId(1L);
        request.setPageSize(10);
        request.setAppId("testAppId");
        request.setToken("1");
        request.setServiceTypeList(Lists.newArrayList(CouponServiceTypeEnum.BASIC_MAINTENANCE.getCode()));
        Result<PageBeanStream<UserCouponListDto>> result = couponService.userCouponList(request);
        log.info("userCouponList retail result-{}", GsonUtil.toJson(result));
        Assertions.assertNotNull(result.getData());
    }

    @Test
    public void test_userCouponList_paramError() {
        Result<PageBeanStream<UserCouponListDto>> result = couponService.userCouponList(null);
        Assertions.assertEquals("userCouponList() 请求参数request=null!", result.getMessage());

        UserCouponListRequest request = new UserCouponListRequest();
        request.setBizPlatform(null);
        result = couponService.userCouponList(request);
        Assertions.assertEquals("userCouponList() 请求参数bizPlatform不符合要求!", result.getMessage());

        request.setBizPlatform(Lists.newArrayList(-1));
        result = couponService.userCouponList(request);
        Assertions.assertEquals("存在非法的业务领域：[-1]", result.getMessage());

        request.setBizPlatform(Lists.newArrayList(BizPlatformEnum.RETAIL.getCode(), BizPlatformEnum.CAR_AFTER_SALE.getCode()));
        result = couponService.userCouponList(request);
        Assertions.assertEquals("不支持跨主体查询", result.getMessage());

        request.setBizPlatform(Lists.newArrayList(BizPlatformEnum.CAR_AFTER_SALE.getCode()));
        result = couponService.userCouponList(request);
        Assertions.assertEquals("userCouponList() 请求参数vid不能为空!", result.getMessage());
        request.setVid("1");

        request.setBizPlatform(Lists.newArrayList(BizPlatformEnum.RETAIL.getCode()));
        request.setUserId(-1L);
        result = couponService.userCouponList(request);
        Assertions.assertEquals("userCouponList() 请求参数userId不合法!", result.getMessage());

        request.setUserId(1L);
        request.setStatus(" ");
        result = couponService.userCouponList(request);
        Assertions.assertEquals("userCouponList() 请求参数status不合法!", result.getMessage());
    }
}
