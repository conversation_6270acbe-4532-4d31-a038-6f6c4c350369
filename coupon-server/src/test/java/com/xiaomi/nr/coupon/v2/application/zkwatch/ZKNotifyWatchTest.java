package com.xiaomi.nr.coupon.v2.application.zkwatch;

import com.xiaomi.miliao.zookeeper.ZKClient;
import com.xiaomi.miliao.zookeeper.ZKDataChangeListener;
import com.xiaomi.miliao.zookeeper.ZKFacade;
import com.xiaomi.nr.coupon.application.zkwatch.ZKNotifyWatch;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.boot.DefaultApplicationArguments;

import static org.mockito.Mockito.*;

/**
 * @author: zhang<PERSON>wei6
 * @date: 2025/1/16 20:17
 * @description:
 */
@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest({ZKClient.class, ZKFacade.class})
public class ZKNotifyWatchTest {

    private ZKClient mockZKClient;

    private CouponConfigRepository mockCouponConfigRepository;

    private ZKNotifyWatch zkNotifyWatch;

    @Before
    public void mockDependencies() {
        // mock ZKNotifyWatch需要的依赖，
        mockZKClient = PowerMockito.mock(ZKClient.class);
        mockCouponConfigRepository = PowerMockito.mock(CouponConfigRepository.class);
        PowerMockito.mockStatic(ZKFacade.class);
        // mock ZKFacade.getAbsolutePathClient，否则需要连zk
        PowerMockito.when(ZKFacade.getAbsolutePathClient()).thenReturn(mockZKClient);

        // spy ZKNotifyWatch
        zkNotifyWatch = PowerMockito.spy(new ZKNotifyWatch());
        Whitebox.setInternalState(zkNotifyWatch, "zkClient", mockZKClient);
        Whitebox.setInternalState(zkNotifyWatch, "couponConfigRepository", mockCouponConfigRepository);
    }

    @Test
    public void test() {
        // 调用被测试的方法
        try {
            zkNotifyWatch.run(PowerMockito.mock(DefaultApplicationArguments.class));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // 使用ArgumentCaptor来捕获registerDataChanges方法的第三个参数
        ArgumentCaptor<ZKDataChangeListener<String>> argumentCaptor = ArgumentCaptor.forClass(ZKDataChangeListener.class);
        String couponConfigChangeZkPath = Whitebox.getInternalState(ZKNotifyWatch.class, "COUPON_CONFIG_CHANGE_ZK_PATH");
        verify(mockZKClient).registerDataChanges(eq(String.class), eq(couponConfigChangeZkPath), argumentCaptor.capture());

        // 获取捕获的listener
        ZKDataChangeListener<String> listener = argumentCaptor.getValue();

        // 模拟ZK节点数据变化
        String mockData = "mockData";
        long mockCurrentSeqId = 12345L;
        // 此处可以进一步mock覆盖CouponConfigRepository
        when(mockCouponConfigRepository.loadIncrCouponConfigToCache()).thenReturn(mockCurrentSeqId);

        // 调用listener的onDataChange方法
        listener.onChanged(couponConfigChangeZkPath, mockData);

        // 验证listener中的方法是否调用
        verify(mockCouponConfigRepository).loadIncrCouponConfigToCache();
    }
}
