package com.xiaomi.nr.coupon.mock.redis;

import com.xiaomi.nr.coupon.config.RedisConfig;
import com.xiaomi.nr.coupon.config.support.RedisNodeConfig;
import com.xiaomi.youpin.utest.envmock.MockAction;
import com.xiaomi.youpin.utest.envmock.MockActionInterface;
import com.xiaomi.youpin.utest.envmock.redis.RedisMock;
import mockit.Mock;
import mockit.MockUp;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

public class RedisConnectionMock extends MockAction {

    @Override
    public void doMock() {
        //redis数据源mock，需要业务方自己添加
        new RedisConfigMock();
    }

    /**
     * redis连接工厂mock
     */
    public class RedisConfigMock extends MockUp<RedisConfig> {

        @Mock
        public LettuceConnectionFactory createLettuceConnectionFactory(RedisNodeConfig config) {
            return RedisMock.getConnectionFactory();
        }
    }

}
