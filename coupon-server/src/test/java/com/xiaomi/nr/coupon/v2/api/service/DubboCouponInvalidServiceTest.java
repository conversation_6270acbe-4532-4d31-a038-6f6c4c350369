package com.xiaomi.nr.coupon.v2.api.service;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponCheckResp;
import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponReq;
import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponResp;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.api.service.DubboCouponInvalidService;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.v2.BaseTest;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @author: zhangliwei6
 * @date: 2025/2/14 9:57
 * @description:
 */
@Slf4j
public class DubboCouponInvalidServiceTest extends BaseTest {

    @Autowired
    private DubboCouponInvalidService dubboCouponInvalidService;

    @Test
    public void testInvalidCouponCheck() {
        InvalidCouponReq request = new InvalidCouponReq();
        request.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());
        request.setVid("LKBQG9X4NEBTX28K2");
        request.setCouponIdList(Lists.newArrayList(1011143518L));
        Result<InvalidCouponCheckResp> result = dubboCouponInvalidService.invalidCouponCheck(request);
        log.info("invalidCouponCheck result-{}", GsonUtil.toJson(result));
        assertEquals(GeneralCodes.OK.getCode(), result.getCode());
    }

    @Test
    public void testInvalidCoupon() {
        InvalidCouponReq request = new InvalidCouponReq();
        request.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());
        request.setVid("LKBQGSSNZM24VGA79");
        request.setCouponIdList(Lists.newArrayList(1011143519L));

        // 幂等
        Result<InvalidCouponResp> result = dubboCouponInvalidService.invalidCoupon(request);
        assertTrue(result.getData().getIdempotent());

        // 已用
        request.setVid("LKBQG9X4NEBTX28K2");
        request.setCouponIdList(Lists.newArrayList(1011143518L));
        result = dubboCouponInvalidService.invalidCoupon(request);
        assertNotNull(result.getData().getFailReason());

        request.setVid("LKBQNT18WW33VYXL0");
        request.setCouponIdList(Lists.newArrayList(1011143524L));
        result = dubboCouponInvalidService.invalidCoupon(request);
        assertTrue(result.getData().getSuccess());
    }

    @Test
    public void testInvalidCouponCheck_ParamError() {
        InvalidCouponReq request = new InvalidCouponReq();
        request.setBizPlatform(-1);
        Result<InvalidCouponCheckResp> result = dubboCouponInvalidService.invalidCouponCheck(request);
        assertEquals("业务领域非法", result.getMessage());
        request.setBizPlatform(BizPlatformEnum.RETAIL.getCode());

        result = dubboCouponInvalidService.invalidCouponCheck(request);
        assertEquals("暂不支持作废非汽车售后服务领域的优惠券", result.getMessage());

        request.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());
        result = dubboCouponInvalidService.invalidCouponCheck(request);
        assertEquals("vid不能为空", result.getMessage());
        request.setVid("1");

        request.setCouponIdList(Lists.newArrayList(1L, 1L));
        result = dubboCouponInvalidService.invalidCouponCheck(request);
        assertEquals("存在重复couponId", result.getMessage());

        request.setCouponIdList(Lists.newArrayList(1L));
        result = dubboCouponInvalidService.invalidCouponCheck(request);
        assertEquals("无可用优惠券", result.getMessage());

        request.setVid("LKBQNT18WW33VYXL0");
        request.setCouponIdList(Lists.newArrayList(1L, 1011143524L));
        result = dubboCouponInvalidService.invalidCouponCheck(request);
        assertEquals("获取优惠券数量不一致", result.getMessage());
    }
}
