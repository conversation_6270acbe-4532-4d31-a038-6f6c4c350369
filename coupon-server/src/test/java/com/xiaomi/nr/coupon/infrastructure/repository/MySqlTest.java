package com.xiaomi.nr.coupon.infrastructure.repository;


import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave.UserCouponListMapper;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponBootstrap.class)
@ActiveProfiles("dev")
public class MySqlTest {


    @Autowired
    private UserCouponListMapper userCouponListMapper;

    @Test
    public void userMapperTest(){
        long nowTime = TimeUtil.getNowUnixSecond();
        //用于返近90天以内的过期的券
        long userId = 3150000058L;
        List<Integer> bizPlatformList = Lists.newArrayList(BizPlatformEnum.RETAIL.getCode());
        System.out.println(GsonUtil.toJson(userCouponListMapper.getCouponByUnused(userId, nowTime ,"other", bizPlatformList)));

    }

}
