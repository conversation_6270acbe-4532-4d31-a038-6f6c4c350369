package com.xiaomi.nr.coupon.domain.coupon;

import com.xiaomi.nr.coupon.api.dto.couponassign.CouponFetchCheckDTO;
import com.xiaomi.nr.coupon.api.dto.couponassign.CouponFetchCheckRequest;
import com.xiaomi.nr.coupon.api.dto.couponassign.CouponFetchCheckResponse;
import com.xiaomi.nr.coupon.api.service.CouponAssignService;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.youpin.infra.rpc.Result;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;

/**
 * @Description:
 * @Date: 2022.05.
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponBootstrap.class)
@ActiveProfiles("dev")
public class AssignCouponServiceTest {

    @Autowired
    private CouponAssignService couponAssignService;


    @Test
    public void testFetchCheck() {
        CouponFetchCheckRequest request = new CouponFetchCheckRequest();
        request.setUid(2257477783L);
        request.setConfigIdList(Arrays.asList(30249L, 30245L, 30243L, 30240L, 30239L, 30236L, 30250L));
        request.setSceneCode("04223AE7F2ABC3F5999EBB51142C3C6C");
        request.setAppId("1011");
        request.setWithValidCoupon(true);
        Result<CouponFetchCheckResponse> res = couponAssignService.couponFetchCheck(request);
        CouponFetchCheckDTO couponFetchCheckDTO = res.getData().getCouponFetchResult().get(30254L);
        System.out.println(couponFetchCheckDTO.getInvalidReason());
        System.out.println(couponFetchCheckDTO.getValidCouponList());
        System.out.println(couponFetchCheckDTO.isFetchAble());
        System.out.println(couponFetchCheckDTO.isFetched());
    }
}
