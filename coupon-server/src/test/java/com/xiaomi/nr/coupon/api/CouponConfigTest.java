package com.xiaomi.nr.coupon.api;

import com.xiaomi.nr.coupon.api.dto.couponconfig.*;
import com.xiaomi.nr.coupon.api.service.CouponConfigService;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.domain.couponconfig.RelationGoods;
import com.xiaomi.nr.coupon.domain.couponconfig.ValidCouponConfigService;
import com.xiaomi.nr.coupon.domain.couponconfig.model.GoodScope;
import com.xiaomi.nr.coupon.infrastructure.repository.localcache.LocalCacheCoupon;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.CouponConfigRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.po.ConfigCacheItemPo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import groovy.transform.ASTTest;
import org.assertj.core.util.Lists;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.*;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(
        classes = {CouponBootstrap.class, CouponConfigTest.class}
)
public class CouponConfigTest {

    @Autowired
    private CouponConfigRedisDao couponConfigRedisDao;

    @Resource
    private LocalCacheCoupon localcacheCoupon;

    @Autowired
    private RelationGoods relationGoods;

    @Autowired
    private ValidCouponConfigService validCouponConfigService;

    @Resource
    private CouponConfigService couponConfigService;

    @BeforeClass
    public static void beforeClass() throws Exception {

    }

    /**
     * 收尾工作，当前测试文件只执行一次
     */
    @AfterClass
    public static void afterClass() {
        /**
         * 收尾工作，例如停止redis和mysql
         */
        //CommonMock.getInstance().stop();
    }

    @Test
    public void testRedisCouponConfig() throws BizError {
        List<Long> notFinds = new ArrayList<>();
        notFinds.add(12446L);
        notFinds.add(12447L);
        notFinds.add(12449L);
        notFinds.add(12455L);

        Map<Long, ConfigCacheItemPo> caches = couponConfigRedisDao.get(notFinds);

        System.out.println("##############");
        System.out.println(caches.size());
        System.out.println("######################");

        System.out.println(caches);
    }


    @Test
    public void testGetConfigList(){
        try {
            // {"sku":[20852,20853],"goods":[2183600013,2183600011],"packages":[]}
            //{"sku":[23553,22019,17027,14725,16909,8206,16144,6163,16147,19861,16278,9367,16920,1049,14361,17563,17565,16798,18591,18592,18593,18721,18594,18595,18596,18597,18598,8998,5159,23339,22190,8240,20016,7344,20017,7345,20018,20019,15797,21173,15928,15929,19769,21178,19515,9276,12994,15172,22341,17352,2505,18377,14027,16589,16590,17231,13648,14417,15572,18004,19928,13785,14683,19163,23517,15326,15327,26719,12000,19168,18408,18152,18409,20970,17132,17134,18414,12272,15088,27888,18930,17139,14068,18932,18037,20469,15351,18936,14588,18046,16895,23935],"goods":[2171100009,2181600011,2170700004,2170700005,2182300059,2202600001,2182300058,2182300057,2182300056,2184100005,2184100003,2184100001,2163900015,2182700035,2181000034,2182700036,2134900001,2181000033,2173700016,2220700028,2182000044,2173300010,2142400020,2163000004,2164900001,2174800008,2182000034,2173300006,2175200009,2194300005,2171800003,2181300018,2181300017,2184800010,2181300023,2220700008,2191300009,2191600023,2220700005,2220700004,2182100010,2191600026,2172600002,2220900062,2181300029,2182900002,2182100015,2221000030,2215000004,2171300013,2215000014,2220900013,2215000009,2173800001,2170900020,2174100035,2173700038,2215000016,2215000017,2180100009,2164100007,2173700043,2180100010,2184300010,2160800023,2220800003,2214700001,2181900013,2181900012,2181900014,2183400002,2181900011,2171600004,2213200001,2181100015,2180400007,2175000017,2172700018,2180400004,2172700019,2180400002,2171600026,2172700021,2180400003,2181100025,2173100029,2181100027,2184600010,2180400010,2180400011,2180400008,2180400009],"packages":[1211200003,1204900004,1192100024,1215200008,1213100022,1213100023,1213100020,1213100021,1213200003,1213200002,1204900014]}
            GoodsRelationCouponConfigResponse response = relationGoods.getCouponConfigList(20852, "sku");
            System.out.println(response);
        } catch (BizError bizError) {
            bizError.printStackTrace();
        }
    }

    @Test
    public void testGetCouponConfigGoodsRel(){
        try {
            // {"sku":[20852,20853],"goods":[2183600013,2183600011],"packages":[]}
            //{"sku":[23553,22019,17027,14725,16909,8206,16144,6163,16147,19861,16278,9367,16920,1049,14361,17563,17565,16798,18591,18592,18593,18721,18594,18595,18596,18597,18598,8998,5159,23339,22190,8240,20016,7344,20017,7345,20018,20019,15797,21173,15928,15929,19769,21178,19515,9276,12994,15172,22341,17352,2505,18377,14027,16589,16590,17231,13648,14417,15572,18004,19928,13785,14683,19163,23517,15326,15327,26719,12000,19168,18408,18152,18409,20970,17132,17134,18414,12272,15088,27888,18930,17139,14068,18932,18037,20469,15351,18936,14588,18046,16895,23935],"goods":[2171100009,2181600011,2170700004,2170700005,2182300059,2202600001,2182300058,2182300057,2182300056,2184100005,2184100003,2184100001,2163900015,2182700035,2181000034,2182700036,2134900001,2181000033,2173700016,2220700028,2182000044,2173300010,2142400020,2163000004,2164900001,2174800008,2182000034,2173300006,2175200009,2194300005,2171800003,2181300018,2181300017,2184800010,2181300023,2220700008,2191300009,2191600023,2220700005,2220700004,2182100010,2191600026,2172600002,2220900062,2181300029,2182900002,2182100015,2221000030,2215000004,2171300013,2215000014,2220900013,2215000009,2173800001,2170900020,2174100035,2173700038,2215000016,2215000017,2180100009,2164100007,2173700043,2180100010,2184300010,2160800023,2220800003,2214700001,2181900013,2181900012,2181900014,2183400002,2181900011,2171600004,2213200001,2181100015,2180400007,2175000017,2172700018,2180400004,2172700019,2180400002,2171600026,2172700021,2180400003,2181100025,2173100029,2181100027,2184600010,2180400010,2180400011,2180400008,2180400009],"packages":[1211200003,1204900004,1192100024,1215200008,1213100022,1213100023,1213100020,1213100021,1213200003,1213200002,1204900014]}
            Map<Long, List<GoodsItem>> response = relationGoods.getCouponConfigGoodsRel(Lists.newArrayList(30326L), true);
            System.out.println(response);
        } catch (BizError bizError) {
            bizError.printStackTrace();
        }
    }

    @Test
    public void testGetCouponTypeInfo(){
        String sendChannel = "test";
        GoodsItem goodsItem = new GoodsItem();
        goodsItem.setId(20852L);
        goodsItem.setLevel("sku");
        List<GoodsItem > goodsItemList = Lists.newArrayList(goodsItem);
        try {
            List<CouponTypeInfoListResponse> result = validCouponConfigService.getCouponTypeInfo(sendChannel,goodsItemList);
            System.out.println(result);
        } catch (BizError bizError) {
            bizError.printStackTrace();
        }
    }

    @Test
    public void testGetCouponConfigSurplus(){
        try {
            CouponConfigSurplusRequest request = new CouponConfigSurplusRequest();
            List<Integer> configIds = new ArrayList<>();
            configIds.add(0);
            configIds.add(12819);
            configIds.add(14790);
            configIds.add(30049);
            request.setConfigIds(configIds);
            Result<Map<Integer, CouponConfigSurplusDto>> response = couponConfigService.getCouponConfigSurplus(request);
            System.out.println(response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void getValidConfigListV2Test(){

        ValidConfigListRequest request = new ValidConfigListRequest();
        GoodsItem goodsItem = new GoodsItem();
        goodsItem.setId(23042L);
        goodsItem.setLevel("sku");

        GoodsItem goodsItem1 = new GoodsItem();
        goodsItem1.setId(12556L);
        goodsItem1.setLevel("sku");

        GoodsItem goodsItem2 = new GoodsItem();
        goodsItem2.setId(13873L);
        goodsItem2.setLevel("sku");

        GoodsItem goodsItem3 = new GoodsItem();
        goodsItem3.setId(20850L);
        goodsItem3.setLevel("sku");

        GoodsItem goodsItem4= new GoodsItem();
        goodsItem4.setId(22738L);
        goodsItem4.setLevel("sku");

        GoodsItem goodsItem5= new GoodsItem();
        goodsItem5.setId(21394L);
        goodsItem5.setLevel("sku");

        GoodsItem goodsItem6 = new GoodsItem();
        goodsItem6.setId(21395L);
        goodsItem6.setLevel("sku");

        GoodsItem goodsItem7 = new GoodsItem();
        goodsItem7.setId(21396L);
        goodsItem7.setLevel("sku");

        GoodsItem goodsItem8 = new GoodsItem();
        goodsItem8.setId(22740L);
        goodsItem8.setLevel("sku");

        GoodsItem goodsItem9 = new GoodsItem();
        goodsItem9.setId(22748L);
        goodsItem9.setLevel("sku");

        GoodsItem goodsItem10 = new GoodsItem();
        goodsItem10.setId(21396L);
        goodsItem10.setLevel("sku");

        List<GoodsItem> list = new ArrayList<>(Collections.singletonList(goodsItem));
        list.add(goodsItem1);
        list.add(goodsItem2);
        list.add(goodsItem3);
        list.add(goodsItem4);
        list.add(goodsItem5);
        list.add(goodsItem6);
        list.add(goodsItem7);
        list.add(goodsItem8);
        list.add(goodsItem9);
        list.add(goodsItem10);


        request.setGoodsItemList(list);
        request.setSceneCode("7C0938DFA6023303195221E669AFDE24");
        request.setStartFetchTime(1655451064L);
        request.setEndFetchTime(1655451064L);
        System.out.println(GsonUtil.toJson(couponConfigService.getValidConfigListV2(request)));

       // Assert.assertNotNull(couponConfigService.getValidConfigListV2(request));

    }


    @Test
    public void validConfigListTest(){

        CouponTypeInfoListRequest request = new CouponTypeInfoListRequest();
        List<GoodsItem> goodsItemList = new LinkedList<>();
        GoodsItem goods1 = new GoodsItem();
        goods1.setId(19360L);
        goods1.setLevel("sku");

        GoodsItem goods2 = new GoodsItem();
        goods2.setId(1204900001L);
        goods2.setLevel("package");

        goodsItemList.add(goods1);
        goodsItemList.add(goods2);


        request.setGoodsItemList(goodsItemList);
        request.setSendChannel("store_manager");

        Assert.assertNotNull(couponConfigService.getValidConfigList(request));
    }



}
