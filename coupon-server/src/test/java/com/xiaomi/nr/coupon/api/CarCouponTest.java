package com.xiaomi.nr.coupon.api;

import com.xiaomi.nr.coupon.api.dto.couponassign.SingleAssignRequest;
import com.xiaomi.nr.coupon.api.dto.couponassign.SingleAssignResponse;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.api.service.CouponAssignService;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(
        classes = {CouponBootstrap.class, CarCouponTest.class}
)
@Slf4j
public class CarCouponTest {
    @Autowired
    private CouponAssignService couponAssignService;

    @BeforeClass
    public static void beforeClass() throws Exception {

    }

    /**
     * 收尾工作，当前测试文件只执行一次
     */
    @AfterClass
    public static void afterClass() {
        /**
         * 收尾工作，例如停止redis和mysql
         */
        //CommonMock.getInstance().stop();
    }

    @Test
    public void testVidAssignNew() {
        try {
            SingleAssignRequest req = new SingleAssignRequest();
            req.setRequestId("test_20240308_0013");
            req.setAssignMode(1);
            req.setConfigId(138939L);
            req.setUserId(3150000058L);
            req.setVid("MOCKD000000000003");
            req.setAppId("XM2107");
            req.setToken("12121213sdfasdfasdf");
            req.setSceneCode("623BAEA4303533E60F4C8D9A37B446F3");
            req.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());


            Result<SingleAssignResponse> res = couponAssignService.single(req);
            System.out.println(res);
        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testAssignServicePackage() {

        SingleAssignRequest req = new SingleAssignRequest();
        req.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());
        req.setRequestId("cxp_test_20240528_1548");
        req.setVid("MOCKD000000000003");
        req.setSceneCode("08B818C8A63BD1849391A0430CDDCBA8");
        req.setConfigId(150496L);
        req.setStartUseTime(1716206072L);
        req.setEndUseTime(1724815698L);
        req.setAppId("XM2226");
        req.setToken("fe7769b6048b1d244616c88215d94f09");

        Result<SingleAssignResponse> res = couponAssignService.single(req);
        System.out.println("res is " + GsonUtil.toJson(res));


    }

}
