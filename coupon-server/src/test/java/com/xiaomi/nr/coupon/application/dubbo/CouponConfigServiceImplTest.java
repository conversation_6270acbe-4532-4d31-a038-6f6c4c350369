package com.xiaomi.nr.coupon.application.dubbo;

import com.xiaomi.nr.coupon.api.dto.couponconfig.CouponConfigInfoRequest;
import com.xiaomi.nr.coupon.api.dto.couponconfig.CouponTypeInfoDto;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/14 17:20
 */
@ActiveProfiles("dev")
@SpringBootTest(classes = CouponBootstrap.class)
@Slf4j
public class CouponConfigServiceImplTest {
    @Autowired
    private CouponConfigServiceImpl couponConfigService;

    @Test
    public void getCouponConfigInfoListTest() {
        log.info("============================================================");

        CouponConfigInfoRequest request = new CouponConfigInfoRequest();
        request.setConfigIdList(Lists.newArrayList(139965L));
        request.setWithProducts(false);
        request.setAppId("XM2115");
        request.setToken("d2074ec4ea714125855a0da511ed77e0");

        Result<List<CouponTypeInfoDto>> result = couponConfigService.getCouponConfigInfoList(request);

        log.info("getCouponConfigInfoListTest result = {}", result);

        log.info("============================================================");
    }
}