package com.xiaomi.nr.coupon.v2.application.scheduler;

import com.xiaomi.nr.coupon.application.scheduler.CouponQuartzScheduler;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.CouponConfigMapper;
import com.xiaomi.nr.coupon.v2.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.locks.ReentrantLock;

import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * @author: zhangliwei6
 * @date: 2025/1/16 20:18
 * @description:
 */
@Slf4j
@Order(-1)
public class CouponQuartzSchedulerTest extends BaseTest {

    @Autowired
    private CouponQuartzScheduler scheduler;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private CouponConfigMapper couponConfigMapper;

    /**
     *  loadCouponCacheToCache准备nr_coupon_config表有效券和无效券，即end_use_time-1天之前和之后的数据，最后写入couponConfigCache中
     *  loadCouponSceneCache准备nr_coupon_scene表数据及关联表nr_coupon_scene_permission的数据，最后写入couponSceneCache中
     *  loadServiceSceneCache准备nr_coupon_service_scene表数据，最后写入serviceSceneCache中
     */
    @Test
    public void test_run() {
        try {
            scheduler.run(null);
        } catch (Exception e) {
            log.error("ApplicationRunner.run error", e);
            Assertions.fail(e.getMessage());
        }
    }

    /**
     *  loadCouponGoodCacheToCache准备nr_coupon_config表数据、couponConfigCache本地缓存数据和redis中nr:coupon:newGoods:{configId}数据
     *  但没有存储到任何地方
     */
    @Test
    public void test_loadCouponGoodCacheToCache() {
        // mock getValidConfigIdByUseTime，因H2中timestamp类型和"0"比较会报错
        CouponConfigMapper mockCouponConfigMapper = PowerMockito.mock(CouponConfigMapper.class);
        when(mockCouponConfigMapper.getValidConfigIdByUseTime(anyLong(), anyString())).thenReturn(Lists.newArrayList(194502, 194507));
        Whitebox.setInternalState(couponConfigRepository, "couponConfigMapper", mockCouponConfigMapper);
        scheduler.loadCouponGoodCacheToCache();
        // mock完要还原，否则其他地方用到的也是mock过的bean
        Whitebox.setInternalState(couponConfigRepository, "couponConfigMapper", couponConfigMapper);
    }

    // @Test
    public void test_loadCouponCacheToCache_lockFail() {
        ReentrantLock mockLock = PowerMockito.mock(ReentrantLock.class);
        Whitebox.setInternalState(scheduler, "configPoolUpdateLock", mockLock);
        Whitebox.setInternalState(scheduler, "couponSceneLock", mockLock);
        Whitebox.setInternalState(scheduler, "serviceSceneLock", mockLock);
        when(mockLock.tryLock()).thenReturn(false);
        try {
            when(mockLock.tryLock(anyLong(), any())).thenReturn(false);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
}
