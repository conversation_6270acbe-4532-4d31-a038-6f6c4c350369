package com.xiaomi.nr.coupon.v2.application.scheduler;

import com.xiaomi.nr.coupon.application.scheduler.LettuceConnectionValidScheduler;
import com.xiaomi.nr.coupon.v2.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @author: zhangliwei6
 * @date: 2025/1/22 19:18
 * @description:
 */
@Slf4j
public class LettuceConnectionValidSchedulerTest extends BaseTest {

    @Autowired
    private LettuceConnectionValidScheduler scheduler;

    @Test
    public void test() {
        scheduler.task();
    }
}
