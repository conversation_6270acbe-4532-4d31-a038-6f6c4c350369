package com.xiaomi.nr.coupon.domain;

import com.xiaomi.nr.coupon.api.dto.coupon.CheckoutCouponListRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.CheckoutCouponListResponse;
import com.xiaomi.nr.coupon.api.dto.coupon.checkout.GoodsInfo;
import com.xiaomi.nr.coupon.api.dto.coupon.info.CouponInfoItemDto;
import com.xiaomi.nr.coupon.api.dto.coupon.info.CouponInfoRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.info.CouponInfoResponse;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.api.service.CouponService;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import zander.com.google.common.collect.Lists;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponBootstrap.class)
@ActiveProfiles("dev")
public class couponInfoTest {

    @Resource
    private CouponService couponService;

    @Test
    public void getCouponInfo(){
        CouponInfoRequest request = new CouponInfoRequest();
        request.setUserId(3150000058L);
        List<Long> list = new ArrayList<>();
        list.add(1009116562L);
        list.add(1009116167L);
        list.add(1009116627L);
        list.add(1111L);
        list.add(1009114656L);
        request.setCouponIds(list);

        Result<CouponInfoResponse> res = couponService.couponInfo(request);
        for(Map.Entry<Long, CouponInfoItemDto> item : res.getData().getData().entrySet()) {
            System.out.println(item.getKey() + " => " + item.getValue());
        }
    }

    @Test
    public void getCarCouponInfo(){
        CouponInfoRequest request = new CouponInfoRequest();
        request.setVid("LKBQ1VRE5EMMP4AA2");
        request.setCouponIds(Lists.newArrayList(1010742202L));
        request.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());

        Result<CouponInfoResponse> res = couponService.couponInfo(request);
        System.out.println("result:" + GsonUtil.toJson(res));
    }

    @Test
    public void checkoutCouponList() {
        CheckoutCouponListRequest req = new CheckoutCouponListRequest();
        req.setUserId(3150397990L);
        req.setClientId(180100041075L);
        req.setOrgCode("JM002");
        req.setCityId(0L);
        req.setShoppingMode(0L);
        List<GoodsInfo> goodsInfos  = new ArrayList<>();
        GoodsInfo goodsInfo = new GoodsInfo();
        goodsInfo.setId(19333L);
        goodsInfo.setLevel("sku");
        goodsInfos.add(goodsInfo);
        req.setSkuPackageList(goodsInfos);
        Result<CheckoutCouponListResponse> res = couponService.checkoutCouponList(req);

    }
}
