package com.xiaomi.nr.coupon.infrastructure.rpc;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.AssignCouponTmpRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.AssignRequestItem;
import com.xiaomi.nr.coupon.admin.api.dto.mission.CouponMissionListRequest;
import com.xiaomi.nr.coupon.admin.api.service.DubboCouponService;
import com.xiaomi.nr.coupon.admin.api.service.DubboMissionService;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.util.GsonUtil;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponBootstrap.class)
@ActiveProfiles("dev")
public class couponAdminTest {


    @Reference(interfaceClass = DubboMissionService.class,group = "dev",version = "1.0")
    private DubboMissionService missionService;

    /**
     * 测试根据渠道或券发放任务ID获取券发放任务信息
     */
    @Test
    public void getCouponMissionListTest(){

        CouponMissionListRequest request = new CouponMissionListRequest();
        request.setMissionId(0L);
        request.setSendChannel("store_manager");
        request.setLastMissionId(0L);
        request.setPageSize(50);
        request.setAppId("XM2106");
        String token = "appId=XM2106&lastMissionId=0&missionId=0&pageSize=50&sendChannel=store_manager&secret=98dd1182c35863981729501c2b226317";
        String tokenMd5 = DigestUtils.md5Hex(token);
        request.setToken(tokenMd5);
        System.out.println(tokenMd5);
        System.out.println(GsonUtil.toJson(missionService.getCouponMissionList(request)));


        /*String tokrn = DigestUtils.md5Hex("appId=XM2106&configIdList=[29025]&secret=efa60423495635475319428c9810c194");
        System.out.println(tokenMd5);*/
    }


    @Reference(interfaceClass = DubboCouponService.class,group = "staging",version = "1.0")
    private DubboCouponService couponService;

    @Test
    public void assignCouponList(){

        AssignCouponTmpRequest request = new AssignCouponTmpRequest();
        request.setAppId("XM2106");

        List<AssignRequestItem> assignRequestItemList = new ArrayList<>();
        AssignRequestItem item1 = new AssignRequestItem();
        item1.setMissionId(12201L);
        item1.setUserId(31512356778L);
        AssignRequestItem item2 = new AssignRequestItem();
        item2.setMissionId(12201L);
        item2.setUserId(31512356789L);
        //1009116444 , 1009116444
        AssignRequestItem item3 = new AssignRequestItem();
        item3.setMissionId(234673567L);
        item3.setUserId(31512356789L);
        assignRequestItemList.add(item1);
        assignRequestItemList.add(item2);
        assignRequestItemList.add(item3);
        request.setAssignRequestItemList(assignRequestItemList);

        String str = "appId=XM2106&assignRequestItemList="+String.valueOf(assignRequestItemList)+"&secret=98dd1182c35863981729501c2b226317";

        String token = DigestUtils.md5Hex(str);
        request.setToken(token);
        System.out.println(GsonUtil.toJson(request));
    }



}
