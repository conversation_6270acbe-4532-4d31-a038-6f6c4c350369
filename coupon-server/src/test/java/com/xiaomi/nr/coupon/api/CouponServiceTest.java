package com.xiaomi.nr.coupon.api;


import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.api.dto.coupon.*;
import com.xiaomi.nr.coupon.api.dto.coupon.checkout.GoodsInfo;
import com.xiaomi.nr.coupon.api.dto.couponcode.GetCouponCodeInfoRequest;
import com.xiaomi.nr.coupon.api.dto.couponcode.GetCouponCodeInfoResponse;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.api.service.CouponProductService;
import com.xiaomi.nr.coupon.api.service.CouponService;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.storeinfo.StoreInfoRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.storeinfo.po.OrgInfo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.StringUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {CouponBootstrap.class, CouponServiceTest.class})
public class CouponServiceTest {

    private static final Logger log = LoggerFactory.getLogger(CouponServiceTest.class);
    @Autowired
    private CouponService couponService;

    @Autowired
    private StoreInfoRedisDao storeInfoRedisDao;

    @Autowired
    private CouponProductService couponProductService;

    @Test
    public void getProductCouponTest(){
        ProductCouponRequest request = new ProductCouponRequest();
        List<ProductInfo> productInfoList = new ArrayList<>();

        ProductInfo goods = new ProductInfo();
        goods.setMarketPrice(199900L);
        goods.setSalePrice(199900L);
        goods.setSku(23301L);
        goods.setSource(1);
        goods.setBusinessType(0);
        goods.setPackageId(0L);
        productInfoList.add(goods);

        request.setClientId(180100041075L);
        request.setUserId(3150043985L);
        //request.setOrgCode("MI0101");
        request.setGoodsInfoList(productInfoList);
        request.setCoinCouponTypeIdList(Arrays.asList("12716"));
        request.setAppId("1008");
        request.setToken(StringUtil.genNewToken("1008","8oqgCJxqvr48Pvqq"));
        Result<ProductCouponResponse> result =  couponService.getProductCoupon(request);

        Assert.assertNotNull(result);
        System.out.println(GsonUtil.toJson(result));
        if(result.getCode() != 0){
            Assert.fail("code不为0");
        }
    }


    @Test
    public void couponUsePush() {
        CouponUsePushRequest request =new CouponUsePushRequest();
        request.setUserId(3150059058L);
        request.setCouponId(1009332074L);
        request.setStatus(1);
        request.setModifyTime(System.currentTimeMillis());
        Result<Void> result = couponService.couponUsePush(request);
        System.out.println(GsonUtil.toJson(result));
    }



    @Test
    public void checkoutCouponList() {
        CheckoutCouponListRequest request = new CheckoutCouponListRequest();
        request.setUserId(22012345L);
        request.setClientId(180100031058L);
//        request.setOrgCode("JM001");
//        request.setCityId();
//        request.setShoppingMode();
        GoodsInfo g1 =new GoodsInfo();
        g1.setLevel("sku");
        g1.setId(2047L);
        request.setSkuPackageList(Lists.newArrayList(g1));
        request.setAppId("XM2106");
        request.setToken("");
        Result<CheckoutCouponListResponse> r =couponService.checkoutCouponList(request);
        System.out.println("========="+GsonUtil.toJson(r));

    }

    @Test
    public void getSimpleGoodsFetchUsable() {
        List<SimpleGoodsItem> goodsList = new ArrayList<>();
        SimpleGoodsItem g1 = new SimpleGoodsItem();
        g1.setLevel("sku");
        g1.setSkuPackageId(60389L);
        goodsList.add(g1);

        SimpleGoodsItem g2 = new SimpleGoodsItem();
        g2.setLevel("sku");
        g2.setSkuPackageId(4999L);
        goodsList.add(g2);

        SimpleGoodsFetchUsableRequest request = new SimpleGoodsFetchUsableRequest();
        request.setUserId(3150000058L);
        request.setOrgCode("MI0101");
        request.setGoodsList(goodsList);

        List<Long> configIds = new ArrayList<>();
        Collections.addAll(configIds, 63294L,63294L,63294L);
        request.setConfigIdList(configIds);

        Result<SimpleGoodsFetchUsableResponse> resp = couponService.getSimpleGoodsFetchUsable(request);
        System.out.println("========="+GsonUtil.toJson(resp));
    }

    @Test
    public void getSimpleGoodsFetchUsableNotPass() {
        List<SimpleGoodsItem> goodsList = new ArrayList<>();
        SimpleGoodsItem g1 = new SimpleGoodsItem();
        g1.setLevel("sku");
        g1.setSkuPackageId(8706L);
        goodsList.add(g1);

        SimpleGoodsItem g2 = new SimpleGoodsItem();
        g2.setLevel("sku");
        g2.setSkuPackageId(7999L);
        goodsList.add(g2);

        SimpleGoodsFetchUsableRequest request = new SimpleGoodsFetchUsableRequest();
        request.setUserId(3150410272L);
        request.setOrgCode("MI0101");
        request.setGoodsList(goodsList);

        List<Long> configIds = new ArrayList<>();
        Collections.addAll(configIds, 69414L,69412L,69412L,69413L,69417L,69418L,69419L,69420L,69421L,69415L,69416L,69412L);
        request.setConfigIdList(configIds);

        Result<SimpleGoodsFetchUsableResponse> resp = couponService.getSimpleGoodsFetchUsable(request);
        System.out.println("========="+GsonUtil.toJson(resp));
    }

    @Test
    public void getSimpleGoodsUserUsable() throws BizError {
        List<SimpleGoodsItem> goodsList = new ArrayList<>();
        SimpleGoodsItem g1 = new SimpleGoodsItem();
        g1.setLevel("sku");
        g1.setSkuPackageId(60389L);
        goodsList.add(g1);

        SimpleGoodsItem g2 = new SimpleGoodsItem();
        g2.setLevel("sku");
        g2.setSkuPackageId(4999L);
        goodsList.add(g2);

        SimpleGoodsUserUsableRequest request = new SimpleGoodsUserUsableRequest();
        request.setUserId(3150000058L);
        request.setOrgCode("JM70119");
        request.setGoodsList(goodsList);

        List<Long> configIds = new ArrayList<>();
        Collections.addAll(configIds, 63294L,63294L,63294L);
        request.setConfigIdList(configIds);

        Result<SimpleGoodsUserUsableResponse> resp = couponService.getSimpleGoodsUserUsable(request);
        System.out.println("========="+GsonUtil.toJson(resp));
    }

    @Test
    public void getOrgInfo() {
        String orgCode = "MI0101";
        OrgInfo orgInfo = storeInfoRedisDao.getOrgInfo(orgCode);
        System.out.println("========="+GsonUtil.toJson(orgInfo));
    }

    @Test
    public void getProductUsableCoupon() {
        List<GoodsItemDto> goodsList = new ArrayList<>();
        GoodsItemDto g1 = new GoodsItemDto();
        g1.setLevel("sku");
        g1.setId(60389L);
        g1.setMarketPrice(800000L);
        g1.setSalePrice(700000L);
        g1.setBusinessType(1);
        g1.setSaleMode("common");
        g1.setVirtual(false);
        g1.setFinalStartTime(null);
        g1.setFinalEndTime(null);
        goodsList.add(g1);

        GoodsItemDto g2 = new GoodsItemDto();
        g2.setLevel("sku");
        g2.setId(4999L);
        g2.setMarketPrice(800000L);
        g2.setSalePrice(700000L);
        g2.setBusinessType(1);
        g2.setSaleMode("common");
        g2.setVirtual(false);
        g2.setFinalStartTime(null);
        g2.setFinalEndTime(null);
        goodsList.add(g2);

        GoodsItemDto g3 = new GoodsItemDto();
        g3.setLevel("sku");
        g3.setId(4999L);
        g3.setMarketPrice(800000L);
        g3.setSalePrice(700000L);
        g3.setBusinessType(1);
        g3.setSaleMode("booking");
        g3.setVirtual(false);
        g3.setFinalStartTime(1691596800L);
        g3.setFinalEndTime(1693324800L);
        goodsList.add(g3);

        GoodsItemDto g4 = new GoodsItemDto();
        g4.setLevel("sku");
        g4.setId(4999L);
        g4.setMarketPrice(800000L);
        g4.setSalePrice(700000L);
        g4.setBusinessType(1);
        g4.setSaleMode("prepay");
        g4.setVirtual(false);
        g4.setFinalStartTime(null);
        g4.setFinalEndTime(null);
        goodsList.add(g4);

        GoodsItemDto g5 = new GoodsItemDto();
        g5.setLevel("sku");
        g5.setId(4999L);
        g5.setMarketPrice(800000L);
        g5.setSalePrice(700000L);
        g5.setBusinessType(1);
        g5.setSaleMode("common");
        g5.setVirtual(true);
        g5.setFinalStartTime(null);
        g5.setFinalEndTime(null);
        goodsList.add(g5);

        GoodsItemDto g6 = new GoodsItemDto();
        g6.setLevel("sku");
        g6.setId(4999L);
        g6.setMarketPrice(800000L);
        g6.setSalePrice(700000L);
        g6.setBusinessType(2);
        g6.setSaleMode("common");
        g6.setVirtual(false);
        g6.setFinalStartTime(null);
        g6.setFinalEndTime(null);
        goodsList.add(g6);

        GoodsItemDto g7 = new GoodsItemDto();
        g7.setLevel("sku");
        g7.setId(16448L);
        g7.setMarketPrice(800000L);
        g7.setSalePrice(700000L);
        g7.setBusinessType(1);
        g7.setSaleMode("common");
        g7.setVirtual(false);
        g7.setFinalStartTime(null);
        g7.setFinalEndTime(null);
        goodsList.add(g7);

        GoodsItemDto g8 = new GoodsItemDto();
        g8.setLevel("sku");
        g8.setId(16446L);
        g8.setMarketPrice(800000L);
        g8.setSalePrice(700000L);
        g8.setBusinessType(1);
        g8.setSaleMode("common");
        g8.setVirtual(false);
        g8.setFinalStartTime(null);
        g8.setFinalEndTime(null);
        goodsList.add(g8);

        ProductUsableCouponRequest request = new ProductUsableCouponRequest();
        request.setUserId(3150000058L);
        request.setOrgCode("JM70119");
        request.setGoodsList(goodsList);

        List<ProductConfigItemDto> configs = new ArrayList<>();

        ProductConfigItemDto configItem1 = new ProductConfigItemDto();
        configItem1.setConfigId(63294L);
        configs.add(configItem1);

        ProductConfigItemDto configItem2 = new ProductConfigItemDto();
        configItem2.setConfigId(32826L);
        configs.add(configItem2);

        request.setConfigList(configs);


        Result<ProductUsableCouponResponse> resp = couponProductService.getProductUsableCoupon(request);
        System.out.println("========="+GsonUtil.toJson(resp));
    }

    @Test
    public void getCouponCodeInfoTest() {
        GetCouponCodeInfoRequest request = new GetCouponCodeInfoRequest();
        request.setCouponCode("MK337CGR3JSTMNZZ");
        request.setOrderId(1246421003440402L);
        request.setSendType("xiguaMarket");
        request.setUseMode(1);
        request.setStat("used");


        Result<GetCouponCodeInfoResponse> response = couponService.getCouponCodeInfo(request);

        log.info("getCouponCodeInfoTest response = {}", GsonUtil.toJson(response));
    }

    @Test
    public void getCarShopProductUsableCoupon() {
        List<GoodsItemDto> goodsList = new ArrayList<>();
        GoodsItemDto g1 = new GoodsItemDto();
        g1.setLevel("ssu");
        g1.setId(2182100034L);
        goodsList.add(g1);

        ProductUsableCouponRequest request = new ProductUsableCouponRequest();
        request.setUserId(3150426595L);
        request.setGoodsList(goodsList);
        request.setBizPlatform(BizPlatformEnum.CAR_SHOP.getCode());
        request.setUseChannel(6);
        request.setNeedFetchedCoupon(true);

        Result<ProductUsableCouponResponse> resp = couponProductService.getProductUsableCoupon(request);
        System.out.println("========="+GsonUtil.toJson(resp));
    }

}
