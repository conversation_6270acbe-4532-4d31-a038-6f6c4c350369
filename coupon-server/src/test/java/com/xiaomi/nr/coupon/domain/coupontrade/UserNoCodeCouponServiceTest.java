package com.xiaomi.nr.coupon.domain.coupontrade;

import com.xiaomi.nr.coupon.api.dto.trade.ConsumeCouponRequest;
import com.xiaomi.nr.coupon.api.dto.trade.ConsumeCouponResponse;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponBootstrap.class)
@ActiveProfiles("dev")
public class UserNoCodeCouponServiceTest {

    @Autowired
    private UserNoCodeCouponService userNoCodeCouponService;

    @Test
    public void testCommit() throws BizError {
        ConsumeCouponRequest request = new ConsumeCouponRequest();

        request.setBizPlatform(4);
        request.setUserId(3150000058L);
        request.setOrderId(1231L);


        ConsumeCouponResponse result = userNoCodeCouponService.consumeUserCoupon(request);
        log.info("result:{}", GsonUtil.toJson(result));
    }

    @Test
    public void testRoll() throws BizError {
        ConsumeCouponRequest request = new ConsumeCouponRequest();

        request.setBizPlatform(4);
        request.setUserId(3150041197L);
        request.setVid("123");
        request.setOrderId(1001L);

        ConsumeCouponResponse result = userNoCodeCouponService.consumeUserCoupon(request);
        log.info("result:{}", GsonUtil.toJson(result));
    }
}
