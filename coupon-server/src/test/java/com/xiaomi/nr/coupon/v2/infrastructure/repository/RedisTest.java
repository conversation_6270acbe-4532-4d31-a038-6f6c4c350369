package com.xiaomi.nr.coupon.v2.infrastructure.repository;

import com.xiaomi.mit.unittest.redis.SetupRedis;
import com.xiaomi.nr.coupon.v2.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.remoting.util.GsonUtil;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Map;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * @author: zhangliwei6
 * @date: 2025/1/15 14:50
 * @description:
 */
@Slf4j
@SetupRedis(value = "/redis/string-setup.json", templateName = "stringPulseTypeRedisTemplate")
public class RedisTest extends BaseTest {

    @Autowired
    @Qualifier("stringPulseTypeRedisTemplate")
    private StringRedisTemplate redisTemplate;

    @Test
    public void testRedis() {
        redisTemplate.opsForValue().set("1", "a");
        Assert.assertEquals("a", redisTemplate.opsForValue().get("1"));
    }

    @Test
    @SetupRedis(value = "/redis/array-setup.json", templateName = "stringPulseTypeRedisTemplate")
    public void testArray() {
        Set<String> result = redisTemplate.opsForSet().members("test:array");
        log.info("testArray result: {}", GsonUtil.getGson().toJson(result));
        assertThat(result.size()).isEqualTo(2);
    }

    @Test
    @SetupRedis(value = "/redis/hash-setup.json", templateName = "stringPulseTypeRedisTemplate")
    public void testHash() {
        Map result = redisTemplate.opsForHash().entries("test:hash");
        log.info("testHash result: {}", GsonUtil.getGson().toJson(result));
        assertThat(result.get("version")).isEqualTo("1");
    }
}
