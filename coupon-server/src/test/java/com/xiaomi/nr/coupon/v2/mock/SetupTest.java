package com.xiaomi.nr.coupon.v2.mock;

import com.xiaomi.nr.coupon.constant.CacheKeyConstant;
import com.xiaomi.nr.coupon.enums.couponconfig.SendChannelEnum;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.enums.store.StoreTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.CouponConfigMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.auth.po.AppAuthInfo;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponactivity.po.ActivityCacheCouponItem;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponactivity.po.ActivityCacheInfo;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponactivity.po.ActivityCacheItem;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.storeinfo.po.OrgInfo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.StringUtil;
import com.xiaomi.nr.coupon.v2.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

/**
 * @author: zhangliwei6
 * @date: 2025/1/24 14:31
 * @description:
 */
@Slf4j
@Order(Integer.MIN_VALUE)
public class SetupTest extends BaseTest {

    @Autowired
    @Qualifier("stringPulseTypeRedisTemplate")
    private RedisTemplate redisTemplate;

    @Autowired
    @Qualifier("stringNewCouponRedisTemplate")
    private RedisTemplate stringNewCouponRedisTemplate;

    @Autowired
    @Qualifier("numberNewCouponRedisTemplate")
    private RedisTemplate numberNewCouponRedisTemplate;

    @Autowired
    private CouponConfigMapper couponConfigMapper;

    /**
     * 券配置商品cache key
     */
    private static final String KEY_COUPON_NEW_GOODS_CACHE = "nr:coupon:newGoods:{configId}";

    /**
     * 券配置cache key（180天以内创建且过期不超过3天）
     */
    private static final String KEY_COUPON_INFO_CACHE = "nr:coupon:info:{configId}";

    private static final String KEY_COUPON_INFO_CACHE_OLD = "nr_coupon_config_info_cache_{configId}";

    /**
     * 用户领券计数缓存
     */
    private static final String KEY_USER_COUPON_SEND_COUNT = "nr:user:count:{userId}";

    /**
     * VID领券计数缓存
     */
    private static final String KEY_VID_COUPON_SEND_COUNT = "nr:vid:count:{vid}";

    /**
     * 检查某个券已领取总数量
     */
    private static final String KEY_COUPON_SEND_COUNT = "nr:coupon:sendCount:{configId}";

    /**
     * 商品可用券关系缓存key
     */
    private static final String GOODS_COUPON_CONFIG_KEY = "nr:coupon:reverse:{level}_{id}";

    private static final String KEY_COUPON_MISSION_CACHE = "nr:coupon:mission:{id}";

    private static final String NEW_KEY_COUPON_MISSIONID_CACHE = "nr:coupon:valid:mission:{id}";

    /**
     * 新的AppAuth缓存信息key
     */
    private static final String APPID_NEW_REDIS_KEY = "minos:app:auth:{appId}";

    private static final String KEY_COUPON_ACTIVITY_CONFIG_CACHE = "pulse_getcoupon_event_new_v2";

    /**
     * 券批次基本信息key拼接
     *
     * @param configId
     * @return
     */
    private static String getConfigInfoKey(Integer configId) {
        return StringUtil.formatContent(KEY_COUPON_INFO_CACHE, String.valueOf(configId));
    }

    private static String getOldConfigInfoKey(Integer configId) {
        return StringUtil.formatContent(KEY_COUPON_INFO_CACHE_OLD, String.valueOf(configId));
    }

    /**
     * 券批次商品key拼接
     *
     * @param configId
     * @return
     */
    private static String getConfigGoodKey(Integer configId) {
        return StringUtil.formatContent(KEY_COUPON_NEW_GOODS_CACHE, String.valueOf(configId));
    }

    private static String getGoodsCouponConfigKey(String level, Long good) {
        return StringUtil.formatContent(GOODS_COUPON_CONFIG_KEY, level, String.valueOf(good));
    }

    private static String getCouponMissionKey(Long id) {
        return StringUtil.formatContent(KEY_COUPON_MISSION_CACHE, String.valueOf(id));
    }

    private static String getNewCouponMissionKey(Long id) {
        return StringUtil.formatContent(NEW_KEY_COUPON_MISSIONID_CACHE, String.valueOf(id));
    }

    private static String getAppAuthKey(String appId) {
        return StringUtil.formatContent(APPID_NEW_REDIS_KEY, appId);
    }

    @Test
    public void prepareData() {
        // 准备测试数据，写入redis或本地内存
        setUpCouponGoods();
        setUpCouponMission();
        setUpCouponConfigFromDB();
        setUpStoreAndInexclude();
        setUpAppAuth();
        setUpActivityCacheInfo();

        Object res1 = redisTemplate.opsForValue().get(getConfigInfoKey(194714));
        log.info("data prepared, configId=194714 res = {}", res1);

        setUpNewCouponConfig();
        setUpCouponNewGoods();
        setUpNewUserCouponSendCount();
        setUpNewCouponSendCount();

        Object res = stringNewCouponRedisTemplate.opsForValue().get(getConfigInfoKey(190396));
        log.info("data prepared, configId=190396 res = {}", res);
    }

    private void setUpActivityCacheInfo() {
        ActivityCacheInfo activityCacheInfo = new ActivityCacheInfo();
        activityCacheInfo.setCode(1);
        activityCacheInfo.setMsg("testMsg");
        activityCacheInfo.setCacheCreateTime(System.currentTimeMillis());
        ActivityCacheItem activityCacheItem = new ActivityCacheItem();
        activityCacheItem.setActCode("act1");
        activityCacheItem.setActName("act1");
        activityCacheItem.setStartTime(1737579734L);
        activityCacheItem.setEndTime(1741224946L);
        ActivityCacheCouponItem activityCacheCouponItem = new ActivityCacheCouponItem();
        activityCacheCouponItem.setId(194714L);
        activityCacheCouponItem.setName("testActCoupon");
        activityCacheCouponItem.setSid("sid");
        activityCacheCouponItem.setActivityTag("actTag");
        activityCacheCouponItem.setClientId("testAppId_#other");
        activityCacheCouponItem.setChannel(SendChannelEnum.Others.getRedisValue());
        activityCacheCouponItem.setConfigId(194714L);
        activityCacheCouponItem.setCouponTag("couponTag");
        activityCacheCouponItem.setCrowd("crowd");
        activityCacheItem.setCoupons(Lists.newArrayList(activityCacheCouponItem));
        activityCacheInfo.setData(Lists.newArrayList(activityCacheItem));
        ValueOperations valueOperations = redisTemplate.opsForValue();
        valueOperations.set(KEY_COUPON_ACTIVITY_CONFIG_CACHE, GsonUtil.toJson(activityCacheInfo));
    }

    private void setUpAppAuth() {
        ValueOperations valueOperations = redisTemplate.opsForValue();
        AppAuthInfo appAuthInfo = new AppAuthInfo();
        appAuthInfo.setAppId("testAppId");
        appAuthInfo.setSecret("1");
        appAuthInfo.setChannelList(Lists.newArrayList(SendChannelEnum.StoreManager.getRedisValue(), SendChannelEnum.Others.getRedisValue()));
        valueOperations.set(getAppAuthKey("testAppId"), GsonUtil.toJson(appAuthInfo));
    }

    private void setUpStoreAndInexclude() {
        ValueOperations valueOperations = redisTemplate.opsForValue();
        OrgInfo orgInfo = new OrgInfo();
        orgInfo.setOrgCode("direct");
        orgInfo.setOrgType(StoreTypeEnum.ORG_TYPE_DIRECT.getOrgType());
        valueOperations.set(StringUtil.formatContent(CacheKeyConstant.KEY_STORE_ORGINFO, "direct"), GsonUtil.toJson(orgInfo));
        orgInfo.setOrgCode("spec");
        orgInfo.setOrgType(StoreTypeEnum.ORG_TYPE_SPECIALTY.getOrgType());
        valueOperations.set(StringUtil.formatContent(CacheKeyConstant.KEY_STORE_ORGINFO, "spec"), GsonUtil.toJson(orgInfo));
        orgInfo.setOrgCode("autho");
        orgInfo.setOrgType(StoreTypeEnum.ORG_TYPE_AUTHORIZED.getOrgType());
        valueOperations.set(StringUtil.formatContent(CacheKeyConstant.KEY_STORE_ORGINFO, "autho"), GsonUtil.toJson(orgInfo));
        String globalInexclude = "{\"sku\":[\"19937\"],\"goods\":[],\"commodity\":[\"1164900004\",\"1180300037\",\"1180300038\",\"1180300039\",\"1180300040\",\"1180300041\",\"1180300042\",\"1180300043\",\"1180300044\",\"1180300045\"],\"package\":[\"1170300021\",\"1170300022\",\"1174100038\",\"1174100039\"],\"cat\":[\"158\",\"164\",\"142\",\"156\",\"110\",\"50\",\"63\",\"103\",\"133\",\"144\",\"191\",\"192\"],\"cache_create_time\":1739157783}";
        valueOperations.set(CacheKeyConstant.KEY_GLOBAL_INEXCLUDE, globalInexclude);
        String globalActInexclude = "{\"sku\":[\"9007112\"],\"goods\":[\"2155100010\",\"2173500035\",\"2174600001\"],\"commodity\":[\"1204900005\",\"1152000049\",\"1152000052\",\"1152000054\",\"1152000056\",\"1152000057\",\"1152000058\",\"1152300012\",\"1153100002\",\"1153600014\",\"1154300030\",\"1154300029\",\"1154800009\",\"1154900042\",\"1154900043\",\"1154900044\",\"1154900045\",\"1154900046\",\"1154900047\",\"1154900048\",\"1154900049\",\"1154700017\",\"1150100006\",\"1134900304\",\"1153800053\",\"1153300080\",\"1151400069\",\"1151400068\",\"1150300020\",\"1150300021\",\"1151400056\",\"1144300006\",\"1144000013\",\"1143000006\",\"1143000007\",\"1143400012\",\"1143400013\",\"1143600014\",\"1155100022\",\"1160300025\",\"1160900030\",\"1163200014\",\"1163200013\",\"1163200012\",\"1163200011\",\"1163200010\",\"1163500043\",\"1164900004\",\"1164300014\",\"1172300011\",\"1172300012\",\"1172900027\",\"1175000195\",\"1175000197\",\"1175000198\",\"1175000200\",\"1175000246\",\"1175000255\",\"1175000256\",\"1175000257\",\"1175000258\",\"1175000259\",\"1175000260\",\"1175000261\",\"1175000187\",\"1175000188\",\"1175000191\",\"1175000192\",\"1175000193\",\"1175000194\",\"1175000196\",\"1175000201\",\"1175000202\",\"1175000204\",\"1175000205\",\"1175000206\",\"1175000207\",\"1175000208\",\"1175000209\",\"1175000210\",\"1175000211\",\"1175000212\",\"1175000213\",\"1175000214\",\"1175000215\",\"1175000216\",\"1175000217\",\"1175000218\",\"1175000219\",\"1175000221\",\"1175000222\",\"1175000223\",\"1175000224\",\"1175000225\",\"1175000226\",\"1175000227\",\"1175000229\",\"1175000231\",\"1175000238\",\"1175000240\",\"1175000241\",\"1175000242\",\"1175000243\",\"1175000244\",\"1175000245\",\"1175000247\",\"1175000248\",\"1175000250\",\"1175000251\",\"1175000262\",\"1175000263\",\"1175000264\",\"1175000265\",\"1175100024\",\"1175100025\",\"1175100026\",\"1175100027\",\"1175100028\",\"1175100029\",\"1175100030\"],\"package\":[],\"cat\":[\"192\"],\"cache_create_time\":1739154305}";
        valueOperations.set(CacheKeyConstant.KEY_ACT_GLOBAL_INEXCLUDE, globalActInexclude);
        String globalCouponInexclude = "{\"sku\":[],\"goods\":[],\"commodity\":[\"1175000251\",\"1175000250\",\"1175000248\",\"1175000247\",\"1175000246\",\"1175000245\",\"1175000244\",\"1175000243\",\"1175000242\",\"1175000241\",\"1175000240\",\"1175000238\",\"1175000231\",\"1175000229\",\"1175000227\",\"1175000226\",\"1175000225\",\"1175000224\",\"1175100030\",\"1175100029\",\"1175100028\",\"1175100027\",\"1175100026\",\"1175100025\",\"1175100024\",\"1175000265\",\"1175000264\",\"1175000263\",\"1175000262\",\"1175000261\",\"1175000260\",\"1175000259\",\"1175000258\",\"1175000257\",\"1175000256\",\"1175000255\",\"1164900004\",\"1175000187\",\"1175000188\",\"1175000191\",\"1175000192\",\"1175000193\",\"1175000194\",\"1175000195\",\"1175000196\",\"1175000197\",\"1175000198\",\"1175000200\",\"1175000201\",\"1175000202\",\"1175000204\",\"1175000205\",\"1175000223\",\"1175000222\",\"1175000221\",\"1175000219\",\"1175000218\",\"1175000217\",\"1175000216\",\"1175000215\",\"1175000214\",\"1175000213\",\"1175000212\",\"1175000211\",\"1175000210\",\"1175000209\",\"1175000208\",\"1175000207\",\"1175000206\"],\"package\":[\"1174100039\",\"1174100038\",\"1170300022\",\"1170300021\",\"1194400002\"],\"cat\":[\"158\",\"164\",\"142\",\"156\",\"110\",\"50\",\"63\",\"103\",\"133\",\"144\",\"191\",\"192\"],\"cache_create_time\":1739157843}";
        valueOperations.set(CacheKeyConstant.KEY_COUPON_GLOBAL_INEXCLUDE, globalCouponInexclude);
    }

    private void setUpCouponConfigFromDB() {
        ValueOperations operations = stringNewCouponRedisTemplate.opsForValue();
        couponConfigMapper.getByIds(Lists.newArrayList(194714, 194715, 194712, 194505, 194507, 194502)).forEach(couponConfigPO -> {
            operations.set(getConfigInfoKey(couponConfigPO.getId()), GsonUtil.toJson(couponConfigPO));
            String couponCacheItemPo = "{\"id\":194712,\"name\":\"【全渠道电池】彩虹电池0.01元带回家\",\"globalStartTime\":1646647200,\"globalEndTime\":1903795199,\"status\":\"approved\",\"modeType\":\"no_code\",\"useType\":\"deductible\",\"isShare\":true,\"isPostFree\":true,\"isCheckPrice\":true,\"isCheckPackage\":false,\"useChannelType\":\"online_offline\",\"rangeDesc\":\"使用时需支付0.01元，默认不包邮，具体以商品实际运费为准。\",\"sendChannel\":\"store_manager\",\"addTime\":1646643800,\"modifyTime\":1646643694,\"quotaType\":\"count\",\"quotaCount\":1,\"quotaMoney\":0,\"reduceMoney\":0,\"reduceDiscount\":0,\"reduceMaxPrice\":0,\"deductTargetGoods\":[{\"id\":11855,\"level\":\"sku\"}],\"deductType\":\"one_cent\",\"clients\":[\"180100041114\",\"180100041090\",\"180100041088\",\"180100041081\"],\"goodsInclude\":{\"sku\":{},\"goods\":{},\"packages\":{},\"groups\":{}},\"useChannel\":[\"mi_shop\",\"mi_authorized\",\"mi_home\"],\"cacheCreateTime\":\"2025-02-17 19:49:48\"}\n";
            operations.set(getOldConfigInfoKey(couponConfigPO.getId()), couponCacheItemPo);
        });
    }

    private void setUpCouponGoods() {
        String goodInfo1 = "{\"id\":194714,\"s\":[22540,22541,22542,14350,22543,22544,22545,22546,6163,22547,22548,14357,22549,22550,18455,22551],\"gi\":[2181100032,2181100033,2144600003,2182300161,2182300160,2144600004,2153300090],\"pi\":[1193300001,1161200052,1212200003],\"ssu\":[600000981,600000986,600016460,600016461],\"ut\":\"Jun 2, 2022 10:58:19 AM\"}";

        String goodInfo2 = "{\"id\":194712,\"s\":[22540,22541,22542,14350,22543,22544,22545,22546,6163,22547,22548,14357,22549,22550,18455,22551],\"gi\":[2181100032,2181100033,2144600003,2182300161,2182300160,2144600004,2153300090],\"pi\":[1193300001,1161200052,1212200003],\"ssu\":[600000981,600000986,600016460,600016461],\"ut\":\"Jun 2, 2022 10:58:19 AM\"}";

        String goodInfo3 = "{\"id\":194715,\"s\":[22540,22541,22542,14350,22543,22544,22545,22546,6163,22547,22548,14357,22549,22550,18455,22551],\"gi\":[2181100032,2181100033,2144600003,2182300161,2182300160,2144600004,2153300090],\"pi\":[1193300001,1161200052,1212200003],\"ssu\":[600000981,600000986,600016460,600016461],\"ut\":\"Jun 2, 2022 10:58:19 AM\"}";

        ValueOperations<String, String> operations = redisTemplate.opsForValue();

        operations.set(getConfigGoodKey(194714), goodInfo1);
        operations.set(getConfigGoodKey(194712), goodInfo2);
        operations.set(getConfigGoodKey(194715), goodInfo3);

        operations.set(getGoodsCouponConfigKey(GoodsLevelEnum.Sku.getValue(), 22540L), "194714");
        operations.set(getGoodsCouponConfigKey(GoodsLevelEnum.Package.getValue(), 1193300001L), "194714");
        operations.set(getGoodsCouponConfigKey(GoodsLevelEnum.Goods.getValue(), 2181100032L), "194714");
        operations.set(getGoodsCouponConfigKey(GoodsLevelEnum.Ssu.getValue(), 600000981L), "194714");

        operations.set(getGoodsCouponConfigKey(GoodsLevelEnum.Sku.getValue(), 22542L), "194712");
        operations.set(getGoodsCouponConfigKey(GoodsLevelEnum.Package.getValue(), 1161200052L), "194712");
        operations.set(getGoodsCouponConfigKey(GoodsLevelEnum.Goods.getValue(), 2144600003L), "194712");
        operations.set(getGoodsCouponConfigKey(GoodsLevelEnum.Ssu.getValue(), 600016460L), "194712");

        operations.set(getGoodsCouponConfigKey(GoodsLevelEnum.Sku.getValue(), 22541L), "194715");
        operations.set(getGoodsCouponConfigKey(GoodsLevelEnum.Package.getValue(), 1212200003L), "194715");
        operations.set(getGoodsCouponConfigKey(GoodsLevelEnum.Goods.getValue(), 2181100033L), "194715");
        operations.set(getGoodsCouponConfigKey(GoodsLevelEnum.Ssu.getValue(), 600000986L), "194715");
    }

    private void setUpCouponMission() {
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        operations.set(getNewCouponMissionKey(194714L), "194714");
        operations.set(getNewCouponMissionKey(194715L), "194715");
        operations.set(getNewCouponMissionKey(194505L), "194505");

        String missionCacheItemPO1 = "{\"id\":193686,\"name\":\"testMission1\",\"status\":\"approved\",\"missionType\":\"interface\",\"timeType\":\"days\",\"couponStartTime\":1737129600,\"couponEndTime\":1745942399,\"days\":60,\"hours\":0,\"sendStartTime\":1737129600,\"sendEndTime\":1740758399,\"maxSendNum\":1000,\"groupSendNum\":1000,\"groupIds\":[],\"departmentId\":\"XM2106:testAppId\",\"addTime\":1737183436,\"cacheCreateTime\":1739778483,\"couponConfigId\":194714,\"adminId\":0,\"adminName\":\"p-chenweiming7\",\"version\":2}";
        String missionCacheItemPO2 = "{\"id\":198793,\"name\":\"testMission2\",\"status\":\"approved\",\"missionType\":\"interface\",\"timeType\":\"section\",\"couponStartTime\":1739289600,\"couponEndTime\":1741795199,\"days\":0,\"hours\":0,\"sendStartTime\":1739289600,\"sendEndTime\":1741795199,\"maxSendNum\":10,\"groupSendNum\":10,\"groupIds\":[],\"departmentId\":\"XM2106:testAppId\",\"addTime\":1739326862,\"cacheCreateTime\":1739779743,\"couponConfigId\":194712,\"adminId\":0,\"adminName\":\"p-caozihan\",\"version\":1}";
        String missionCacheItemPO3 = "{\"id\":194505,\"name\":\"testMission3\",\"status\":\"approved\",\"missionType\":\"interface\",\"timeType\":\"days\",\"couponStartTime\":1730390400,\"couponEndTime\":1733007599,\"days\":0,\"hours\":7,\"sendStartTime\":1730390400,\"sendEndTime\":1732982399,\"maxSendNum\":3,\"groupSendNum\":3,\"groupIds\":[],\"departmentId\":\"XM2106:testAppId\",\"addTime\":1730354812,\"cacheCreateTime\":1732982378,\"couponConfigId\":178067,\"adminId\":0,\"adminName\":\"caoxiaopeng1\",\"version\":2}";
        operations.set(getCouponMissionKey(193686L), missionCacheItemPO1);
        operations.set(getCouponMissionKey(198793L), missionCacheItemPO2);
        operations.set(getCouponMissionKey(194505L), missionCacheItemPO3);
    }

    private void setUpNewCouponConfig() {
        String config_190396 = "{\"id\":190396,\"n\":\"600013449优惠券\",\"s\":1,\"cd\":\"不适用于秒杀、拼团、套装、众筹、第三方产品、虚拟产品等，涉及产品以优惠券实际使用情况为准。\",\"budget_apply_no\":\"\",\"line_num\":0,\"budget_create_time\":\"\",\"br_apply_no\":\"3e4dbb5161e94ce5bb81d339a85b8ee8\",\"ct\":1,\"si\":-1,\"pt\":4,\"ss\":\"136A560EF374DC3F6945EB3325AE28CD\",\"sft\":1735833600,\"eft\":1740758399,\"utt\":1,\"sut\":1735833600,\"eut\":1740758399,\"ud\":0,\"uc\":\"1,2,3,4\",\"up\":\"{\\\"1\\\":{\\\"all\\\":true}}\",\"us\":\"{\\\"2\\\":{\\\"all\\\":true,\\\"limitIds\\\":[]},\\\"3\\\":{\\\"all\\\":true,\\\"limitIds\\\":[]},\\\"4\\\":{\\\"all\\\":true,\\\"limitIds\\\":[]}}\",\"bt\":2,\"bp\":0,\"bc\":1,\"pv\":1000,\"mr\":0,\"st\":1,\"ac\":1000,\"fl\":100,\"ep\":\"{\\\"postFree\\\":2,\\\"share\\\":2,\\\"area\\\":2,\\\"proMember\\\":2,\\\"specialStore\\\":2,\\\"publicPromotion\\\":2,\\\"checkoutStage\\\":2,\\\"displayDate\\\":1,\\\"annualType\\\":0}\",\"ai\":\"\",\"sc\":\"other\",\"tpf\":0,\"ut\":1735885265000,\"sct\":0,\"flt\":1,\"tlt\":1,\"pp\":2}";
        String config_193463 = "{\"id\":193463,\"n\":\"test_0117\",\"s\":1,\"cd\":\"不适用于秒杀、拼团、套装、众筹、第三方产品、虚拟产品等，涉及产品以优惠券实际使用情况为准。\",\"budget_apply_no\":\"\",\"line_num\":0,\"budget_create_time\":\"\",\"br_apply_no\":\"acf4ce4dcca44d5e98d82cbd98acb143\",\"ct\":1,\"si\":-1,\"pt\":1,\"ss\":\"136A560EF374DC3F6945EB3325AE28CD\",\"sft\":1737043200,\"eft\":1740758399,\"utt\":1,\"sut\":1737043200,\"eut\":1740758399,\"ud\":0,\"uc\":\"1,2,3,4\",\"up\":\"{\\\"1\\\":{\\\"all\\\":true}}\",\"us\":\"{\\\"2\\\":{\\\"all\\\":true,\\\"limitIds\\\":[]},\\\"3\\\":{\\\"all\\\":true,\\\"limitIds\\\":[]},\\\"4\\\":{\\\"all\\\":true,\\\"limitIds\\\":[]}}\",\"bt\":1,\"bp\":1000,\"bc\":0,\"pv\":200,\"mr\":0,\"st\":1,\"ac\":100,\"fl\":10,\"ep\":\"{\\\"postFree\\\":1,\\\"share\\\":2,\\\"area\\\":2,\\\"proMember\\\":2,\\\"specialStore\\\":2,\\\"publicPromotion\\\":2,\\\"checkoutStage\\\":2,\\\"displayDate\\\":1,\\\"annualType\\\":0}\",\"ai\":\"\",\"sc\":\"other\",\"tpf\":0,\"ut\":1737100169000,\"sct\":0,\"flt\":1,\"tlt\":1,\"pp\":2}";
        String config_197569 = "{\"id\":197569,\"n\":\"test_couponcode\",\"s\":1,\"cd\":\"test\",\"budget_apply_no\":\"\",\"line_num\":0,\"budget_create_time\":\"\",\"br_apply_no\":\"eaf1bbd145c04d209e664bda6cb76138\",\"ct\":1,\"si\":-1,\"pt\":4,\"ss\":\"A306A1AD9AB1C2925F7DBEA051858C09\",\"sft\":1738771200,\"eft\":1774972799,\"utt\":1,\"sut\":1738771200,\"eut\":1774972799,\"ud\":0,\"uc\":\"1,2,3,4\",\"up\":\"{\\\"1\\\":{\\\"all\\\":true}}\",\"us\":\"{\\\"2\\\":{\\\"all\\\":true,\\\"limitIds\\\":[]},\\\"3\\\":{\\\"all\\\":true,\\\"limitIds\\\":[]},\\\"4\\\":{\\\"all\\\":true,\\\"limitIds\\\":[]}}\",\"bt\":2,\"bp\":0,\"bc\":1,\"pv\":1000,\"mr\":0,\"st\":1,\"ac\":1000,\"fl\":0,\"ep\":\"{\\\"postFree\\\":2,\\\"share\\\":2,\\\"area\\\":2,\\\"proMember\\\":2,\\\"specialStore\\\":2,\\\"publicPromotion\\\":2,\\\"annualType\\\":0,\\\"checkoutStage\\\":2,\\\"displayDate\\\":1}\",\"ai\":\"\",\"sc\":\"other\",\"tpf\":0,\"ut\":1738827078000,\"sct\":0,\"flt\":1,\"tlt\":1,\"pp\":2}";
        String config_197772 = "{\"id\":197772,\"n\":\"test_0207\",\"s\":1,\"cd\":\"test\",\"budget_apply_no\":\"BR202403200004\",\"line_num\":9013,\"budget_create_time\":\"2024-03-20 14:07:20\",\"br_apply_no\":\"ddbf315e5c7d49b9aadb00d1eded399b\",\"ct\":1,\"si\":-1,\"pt\":4,\"ss\":\"11BFB15C0E2BA4D03DFF93D95C843D0E\",\"sft\":1738857600,\"eft\":1806508799,\"utt\":1,\"sut\":1738857600,\"eut\":1806508799,\"ud\":0,\"uc\":\"2\",\"up\":\"{}\",\"us\":\"{\\\"2\\\":{\\\"all\\\":true,\\\"limitIds\\\":[]}}\",\"bt\":2,\"bp\":0,\"bc\":1,\"pv\":100,\"mr\":0,\"st\":1,\"ac\":1000,\"fl\":10,\"ep\":\"{\\\"postFree\\\":2,\\\"share\\\":2,\\\"area\\\":2,\\\"proMember\\\":2,\\\"specialStore\\\":2,\\\"publicPromotion\\\":2,\\\"annualType\\\":0,\\\"checkoutStage\\\":1,\\\"displayDate\\\":1}\",\"ai\":\"\",\"sc\":\"other\",\"tpf\":3,\"ut\":1738926758000,\"sct\":0,\"flt\":1,\"tlt\":1,\"pp\":2}";
        String config_197773 = "{\"id\":197773,\"n\":\"test_jichubaoyang_0207\",\"s\":1,\"cd\":\"test\",\"budget_apply_no\":\"\",\"line_num\":0,\"budget_create_time\":\"\",\"br_apply_no\":\"06c354782cd940a295fd0fb73c23e467\",\"ct\":11,\"si\":-1,\"pt\":3,\"ss\":\"5D058D0B5C6FDC1BDD5AF5A1961DDE3B\",\"sft\":1738857600,\"eft\":1806508799,\"utt\":1,\"sut\":1738857600,\"eut\":1806508799,\"ud\":0,\"uc\":\"2\",\"up\":\"{}\",\"us\":\"{\\\"2\\\":{\\\"all\\\":true,\\\"limitIds\\\":[]}}\",\"bt\":2,\"bp\":0,\"bc\":1,\"pv\":0,\"mr\":0,\"st\":1,\"ac\":100,\"fl\":10,\"ep\":\"{\\\"postFree\\\":2,\\\"share\\\":2,\\\"area\\\":2,\\\"proMember\\\":2,\\\"specialStore\\\":2,\\\"publicPromotion\\\":2,\\\"annualType\\\":0}\",\"ai\":\"\",\"sc\":\"other\",\"tpf\":4,\"ut\":1738982064476,\"sct\":1,\"flt\":1,\"tlt\":1,\"pp\":2}";
        String config_197976 = "{\"id\":197976,\"n\":\"test_carShop_0208\",\"s\":1,\"cd\":\"test\",\"budget_apply_no\":\"\",\"line_num\":0,\"budget_create_time\":\"\",\"br_apply_no\":\"da6ab9dcae8a4d0181f08d7a406fd8e0\",\"ct\":1,\"si\":-1,\"pt\":4,\"ss\":\"1DC2FBE1B76AE8E10CBD23381492A66A\",\"sft\":1738944000,\"eft\":1806508799,\"utt\":1,\"sut\":1738944000,\"eut\":1806508799,\"ud\":0,\"uc\":\"6\",\"up\":\"{}\",\"us\":\"{\\\"6\\\":{\\\"all\\\":true}}\",\"bt\":2,\"bp\":0,\"bc\":1,\"pv\":1000,\"mr\":0,\"st\":1,\"ac\":1000,\"fl\":10,\"ep\":\"{\\\"postFree\\\":1,\\\"share\\\":2,\\\"area\\\":2,\\\"proMember\\\":2,\\\"specialStore\\\":2,\\\"publicPromotion\\\":2,\\\"annualType\\\":0,\\\"checkoutStage\\\":2,\\\"displayDate\\\":1}\",\"ai\":\"\",\"sc\":\"other\",\"tpf\":5,\"ut\":1738980431038,\"sct\":0,\"flt\":1,\"tlt\":1,\"pp\":2}";
        String config_191426 = "{\"id\":191426,\"n\":\"test_liwenlong\",\"s\":1,\"cd\":\"不适用于秒杀、拼团、套装、众筹、第三方产品、虚拟产品等，涉及产品以优惠券实际使用情况为准。\",\"budget_apply_no\":\"\",\"line_num\":0,\"budget_create_time\":\"\",\"br_apply_no\":\"310b32c6c01f45d382e43c49fa0b2731\",\"ct\":1,\"si\":-1,\"pt\":4,\"ss\":\"136A560EF374DC3F6945EB3325AE28CD\",\"sft\":1736265600,\"eft\":1740758399,\"utt\":1,\"sut\":1736265600,\"eut\":1740758399,\"ud\":0,\"uc\":\"1,2,3,4\",\"up\":\"{\\\"1\\\":{\\\"all\\\":true}}\",\"us\":\"{\\\"2\\\":{\\\"all\\\":true,\\\"limitIds\\\":[]},\\\"3\\\":{\\\"all\\\":true,\\\"limitIds\\\":[]},\\\"4\\\":{\\\"all\\\":true,\\\"limitIds\\\":[]}}\",\"bt\":2,\"bp\":0,\"bc\":1,\"pv\":1000,\"mr\":0,\"st\":1,\"ac\":100,\"fl\":10,\"ep\":\"{\\\"postFree\\\":1,\\\"share\\\":1,\\\"area\\\":2,\\\"proMember\\\":2,\\\"specialStore\\\":2,\\\"publicPromotion\\\":2,\\\"checkoutStage\\\":2,\\\"displayDate\\\":1,\\\"annualType\\\":0}\",\"ai\":\"\",\"sc\":\"other\",\"tpf\":0,\"ut\":1736335989000,\"sct\":0,\"flt\":1,\"tlt\":1,\"pp\":2}";

        ValueOperations operations = stringNewCouponRedisTemplate.opsForValue();
        operations.set(getConfigInfoKey(190396), config_190396);
        operations.set(getConfigInfoKey(193463), config_193463);
        operations.set(getConfigInfoKey(197569), config_197569);
        operations.set(getConfigInfoKey(197772), config_197772);
        operations.set(getConfigInfoKey(197773), config_197773);
        operations.set(getConfigInfoKey(197976), config_197976);
        operations.set(getConfigInfoKey(191426), config_191426);

    }

    private void setUpCouponNewGoods() {
        String goods_190396 = "{\"id\":190396,\"s\":[],\"gi\":[],\"pi\":[],\"ssu\":[],\"ext\":{},\"ut\":\"Feb 5, 2025 2:00:41 PM\"}";
        String goods_193463 = "{\"id\":193463,\"s\":[26671],\"gi\":[2130100001],\"pi\":[],\"ssu\":[],\"ext\":{},\"ut\":\"Feb 5, 2025 2:00:42 PM\"}";
        String goods_197569 = "{\"id\":197569,\"s\":[4706],\"gi\":[2130100002],\"pi\":[],\"ssu\":[600013293],\"ext\":{},\"ut\":\"Feb 7, 2025 2:14:03 PM\"}";
        String goods_197772 = "{\"id\":197772,\"s\":[],\"gi\":[],\"pi\":[],\"ssu\":[600000451,600000504,600000488],\"ext\":{},\"ut\":\"Feb 7, 2025 7:12:24 PM\"}";
        String goods_197773 = "{\"id\":197773,\"s\":[],\"gi\":[],\"pi\":[],\"ssu\":[600000981,600016459],\"ext\":{\"600000981\":{\"t\":\"part\",\"c\":1},\"600016459\":{\"t\":\"labor\",\"c\":1}},\"ut\":\"Feb 7, 2025 7:20:58 PM\"}";
        String goods_197976 = "{\"id\":197976,\"ssu\":[2130100001,600013293],\"ut\":\"Feb 8, 2025 10:06:59 AM\"}";
        String goods_191426 = "{\"id\":191426,\"s\":[26671],\"gi\":[2130100001],\"pi\":[],\"ssu\":[],\"ext\":{},\"ut\":\"Feb 8, 2025 2:00:38 PM\"}";

        ValueOperations operations = stringNewCouponRedisTemplate.opsForValue();
        operations.set(getConfigGoodKey(190396), goods_190396);
        operations.set(getConfigGoodKey(193463), goods_193463);
        operations.set(getConfigGoodKey(197569), goods_197569);
        operations.set(getConfigGoodKey(197772), goods_197772);
        operations.set(getConfigGoodKey(197773), goods_197773);
        operations.set(getConfigGoodKey(197976), goods_197976);
        operations.set(getConfigGoodKey(191426), goods_191426);

    }


    private void setUpNewUserCouponSendCount() {
        // 用户领券计数缓存
        Long userId = 3150420867L;
        HashOperations<String, String, Integer> redisHashOps = numberNewCouponRedisTemplate.opsForHash();
        redisHashOps.put(StringUtil.formatContent(KEY_USER_COUPON_SEND_COUNT, String.valueOf(userId)), "191426", 2);
        redisHashOps.put(StringUtil.formatContent(KEY_USER_COUPON_SEND_COUNT, String.valueOf(userId)), "191841", 2);
        redisHashOps.put(StringUtil.formatContent(KEY_USER_COUPON_SEND_COUNT, String.valueOf(userId)), "197772", 5);
        redisHashOps.put(StringUtil.formatContent(KEY_USER_COUPON_SEND_COUNT, String.valueOf(userId)), "197976", 5);
    }

    private void setUpVidCouponSendCount() {
        HashOperations<String, String, Integer> redisHashOps = numberNewCouponRedisTemplate.opsForHash();
        redisHashOps.put(StringUtil.formatContent(KEY_VID_COUPON_SEND_COUNT, "LKBQWDL7MWSKL4NX0"), "197773", 3);
    }

    private void setUpNewCouponSendCount() {
        // 券已领取总数量
        ValueOperations operations = stringNewCouponRedisTemplate.opsForValue();
        operations.set(StringUtil.formatContent(KEY_COUPON_SEND_COUNT, String.valueOf(190396)), "15");
        operations.set(StringUtil.formatContent(KEY_COUPON_SEND_COUNT, String.valueOf(197772)), "5");
        operations.set(StringUtil.formatContent(KEY_COUPON_SEND_COUNT, String.valueOf(197773)), "3");
        operations.set(StringUtil.formatContent(KEY_COUPON_SEND_COUNT, String.valueOf(197976)), "5");
        operations.set(StringUtil.formatContent(KEY_COUPON_SEND_COUNT, String.valueOf(191426)), "2");

    }

}
