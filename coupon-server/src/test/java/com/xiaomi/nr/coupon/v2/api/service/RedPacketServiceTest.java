package com.xiaomi.nr.coupon.v2.api.service;

import com.xiaomi.nr.coupon.api.dto.redpacket.UserValidMoneyRequest;
import com.xiaomi.nr.coupon.api.dto.redpacket.UserValidMoneyResponse;
import com.xiaomi.nr.coupon.api.service.RedPacketService;
import com.xiaomi.nr.coupon.v2.BaseTest;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * @author: zhangliwei6
 * @date: 2025/2/14 9:57
 * @description:
 */
@Slf4j
public class RedPacketServiceTest extends BaseTest {

    @Autowired
    private RedPacketService redPacketService;

     @Test
    public void testUserValidMoney_Success() {
        UserValidMoneyRequest request = new UserValidMoneyRequest();
        request.setUserId(3150437067L);
        Result<UserValidMoneyResponse> result = redPacketService.userValidMoney(request);
        assertEquals(10, result.getData().getMoney());
    }

    @Test
    public void testUserValidMoney_ParamError() {
        Result<UserValidMoneyResponse> result = redPacketService.userValidMoney(null);
        assertEquals(0L, result.getData().getMoney());

        UserValidMoneyRequest request = new UserValidMoneyRequest();
        request.setUserId(-1L);
        result = redPacketService.userValidMoney(request);
        assertEquals(0L, result.getData().getMoney());
    }
}
