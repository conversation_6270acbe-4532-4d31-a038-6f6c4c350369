package com.xiaomi.nr.coupon.v2.infrastructure.repository;

import com.xiaomi.mit.unittest.db.SetupDB;
import com.xiaomi.nr.coupon.api.dto.trade.CouponLockItem;
import com.xiaomi.nr.coupon.infrastructure.repository.CarCouponRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CouponOptMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.CouponServiceSceneMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.po.ServiceScenePo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.v2.BaseTest;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * @author: zhangliwei6
 * @date: 2025/1/14 10:23
 * @description:
 */
@Slf4j
@SetupDB(value = "/db/mock-nr_coupon_admin.sql",  dataSourceName = "xmPulseCouponDatasource")
public class MysqlTest extends BaseTest {

    @Autowired
    private CarCouponRepository carCouponRepository;

    @Autowired
    private CouponServiceSceneMapper couponServiceSceneMapper;

    @Autowired
    private CouponOptMapper couponOptMapper;

    @Test
    public void testLockCoupon() {
        // 准备测试数据
        String vid = "testVid";
        long userId = 123L;
        long orderId = 456L;
        Integer offline = 1;

        CouponLockItem item1 = new CouponLockItem();
        item1.setCouponCode("1");
        item1.setCouponId(1L);
        item1.setReduceExpress(BigDecimal.ZERO);
        item1.setReplaceMoney(BigDecimal.valueOf(1.1D));
        CouponLockItem item2 = new CouponLockItem();
        item1.setCouponCode("2");
        item1.setCouponId(2L);
        item1.setReduceExpress(new BigDecimal("2"));
        item1.setReplaceMoney(BigDecimal.valueOf(2.2D));
        List<CouponLockItem> couponItems = Arrays.asList(item1, item2);
        Assert.assertThrows(BizError.class,
                () -> carCouponRepository.lockCoupon(vid, userId, orderId, couponItems, offline));
    }

    @Test
    public void testFirstRead() {
        List<CouponPo> couponInfos = carCouponRepository.getCouponInfos("LNBQRSFJKL9JVEXV1", Lists.newArrayList(30329706704L));
        log.info("result-{}", GsonUtil.toJson(couponInfos));
        Assert.assertNotNull(couponInfos);
    }

    @Test
    public void test_unittestTool1() {
        List<ServiceScenePo> allCouponServiceScene = couponServiceSceneMapper.getAllCouponServiceScene();
        Assertions.assertTrue(allCouponServiceScene.stream().anyMatch(serviceScenePo -> serviceScenePo.getId() == 11));
    }

    @Test
    @SetupDB(value = "/db/mock-xm_pulse.sql", dataSourceName = "xmPulseWriteDatasource")
    public void test_unittestTool2() {
        Assertions.assertNotNull(couponOptMapper.getByCouponOpt(1L, 1L, 1L));
    }
}
