package com.xiaomi.nr.coupon.v2.api.service;

import com.xiaomi.nr.coupon.api.dto.coupon.checkout.GoodsInfo;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.api.dto.enums.GoodsExtTagEnum;
import com.xiaomi.nr.coupon.api.dto.trade.*;
import com.xiaomi.nr.coupon.api.service.CouponTradeDubboService;
import com.xiaomi.nr.coupon.enums.coupon.ShoppingModeEnum;
import com.xiaomi.nr.coupon.enums.coupon.SubmitTypeEnum;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.enums.store.StoreTypeEnum;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.v2.BaseTest;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

import static org.junit.Assert.assertEquals;

@Slf4j
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class CouponTradeDubboServiceTest extends BaseTest {

    @Autowired
    private CouponTradeDubboService couponTradeDubboService;

    @Test
    @Order(1)
    public void testLockUserCoupon() {
        LockCouponRequest request = new LockCouponRequest();
        request.setUserId(3150272288L);
        request.setClientId(180100041114L);
        request.setOrderId(5250103948741860L);
        request.setOffline(0);
        request.setOrgCode("direct");
        request.setUsedCoupon(1011029394L);
        request.setSubmitType(SubmitTypeEnum.Normal.getCode());
        CouponLockItem couponLockItem = new CouponLockItem();
        couponLockItem.setCouponId(1011143165L);
        couponLockItem.setCouponCode("M3KKRKZJK7K9P5KU");
        couponLockItem.setReplaceMoney(new BigDecimal("5.1"));
        couponLockItem.setReduceExpress(new BigDecimal("0.0"));
        request.setCouponItems(Lists.newArrayList(couponLockItem));
        Result<LockCouponResponse> result = couponTradeDubboService.lockUserCoupon(request);
        log.info("lockUserCoupon result-{}", GsonUtil.toJson(result));
        assertEquals(GeneralCodes.OK.getCode(), result.getCode());
    }

    @Test
    public void testLockUserCoupon_paramError() {
        LockCouponRequest request = new LockCouponRequest();

        Result<LockCouponResponse> result = couponTradeDubboService.lockUserCoupon(request);
        assertEquals("用户信息不正确", result.getMessage());
        request.setUserId(1L);

        result = couponTradeDubboService.lockUserCoupon(request);
        assertEquals("订单信息不正确", result.getMessage());
        request.setOffline(1);
        request.setOrderId(1L);

        request.setBizPlatform(null);
        result = couponTradeDubboService.lockUserCoupon(request);
        assertEquals("业务领域不能为空", result.getMessage());
        request.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());

        result = couponTradeDubboService.lockUserCoupon(request);
        assertEquals("VID不能为空", result.getMessage());
        request.setVid("1");

        request.setSubmitType(SubmitTypeEnum.Normal.getCode());
        result = couponTradeDubboService.lockUserCoupon(request);
        assertEquals("下单使用优惠券信息不正确", result.getMessage());

        CouponLockItem couponLockItem = new CouponLockItem();
        request.setCouponItems(Lists.newArrayList(couponLockItem));
        result = couponTradeDubboService.lockUserCoupon(request);
        assertEquals("优惠券信息不正确", result.getMessage());

        couponLockItem.setCouponId(1L);
        result = couponTradeDubboService.lockUserCoupon(request);
        assertEquals("优惠券信息不正确", result.getMessage());
    }

    @Test
    @Order(2)
    public void testConsumeUserCoupon() {
        ConsumeCouponRequest request = new ConsumeCouponRequest();
        request.setUserId(3150272288L);
        request.setClientId(180100041114L);
        request.setOrderId(5250103948741860L);
        request.setOffline(0);
        request.setUsedCoupon(1011029394L);
        request.setSubmitType(SubmitTypeEnum.Normal.getCode());
        request.setCouponCode("M3KKRKZJK7K9P5KU");
        request.setCouponIds(Lists.newArrayList(1011143165L));
        Result<ConsumeCouponResponse> result = couponTradeDubboService.consumeUserCoupon(request);
        log.info("consumeUserCoupon result-{}", GsonUtil.toJson(result));
        assertEquals(GeneralCodes.OK.getCode(), result.getCode());
    }

    @Test
    @Order(3)
    public void testRollbackUserCoupon() {
        RollbackCouponRequest request = new RollbackCouponRequest();
        request.setUserId(3150272288L);
        request.setClientId(180100041114L);
        request.setOrderId(5250103948741860L);
        request.setOffline(0);
        request.setUsedCoupon(1011029394L);
        request.setSubmitType(SubmitTypeEnum.Normal.getCode());
        request.setCouponCode("M3KKRKZJK7K9P5KU");
        request.setCouponIds(Lists.newArrayList(1011143165L));
        Result<RollbackCouponResponse> result = couponTradeDubboService.rollbackUserCoupon(request);
        log.info("rollbackUserCoupon result-{}", GsonUtil.toJson(result));
        assertEquals(GeneralCodes.OK.getCode(), result.getCode());
    }

    @Test
    public void testGetCheckoutCouponListV2_noCode() {
        GetCheckoutCouponListV2Request request = new GetCheckoutCouponListV2Request();
        request.setAppId("XM2229");
        request.setToken("1");
        request.setBizPlatform(Lists.newArrayList(BizPlatformEnum.CAR_AFTER_SALE.getCode()));
        request.setUserId(3150430536L);
        request.setClientId(180100041114L);
        request.setVid("LKBQNT18WW33VYXL0");
        request.setCouponCode("");
        request.setOrgCode("direct");
        request.setOrgType(StoreTypeEnum.ORG_TYPE_DIRECT.getOrgType());
        request.setCityId(1L);
        request.setShoppingMode(ShoppingModeEnum.DEFAULT.getType());
        request.setShipmentId(-1);

        request.setUsedCoupon(1L);
        request.setSubmitType(SubmitTypeEnum.Normal.getCode());
        request.setPromotionTypeList(Lists.newArrayList(1,2,3,4));

        GoodsInfo goodsInfo1 = new GoodsInfo();
        goodsInfo1.setId(22542L);
        goodsInfo1.setLevel(GoodsLevelEnum.Sku.getValue());
        goodsInfo1.setTag(GoodsExtTagEnum.LABOR.getTag());
        goodsInfo1.setCount(1);
        GoodsInfo goodsInfo2 = new GoodsInfo();
        goodsInfo2.setId(1161200052L);
        goodsInfo2.setLevel(GoodsLevelEnum.Package.getValue());
        goodsInfo2.setTag(GoodsExtTagEnum.PART.getTag());
        goodsInfo2.setCount(1);
        GoodsInfo goodsInfo3 = new GoodsInfo();
        goodsInfo3.setId(600016460L);
        goodsInfo3.setLevel(GoodsLevelEnum.Ssu.getValue());
        goodsInfo3.setTag(GoodsExtTagEnum.LABOR.getTag());
        goodsInfo3.setCount(1);
        request.setSkuPackageList(Lists.newArrayList(goodsInfo1, goodsInfo2, goodsInfo3));

        Result<GetCheckoutCouponListV2Response> result = couponTradeDubboService.getCheckoutCouponListV2(request);
        log.info("getCheckoutCouponListV2_noCode result-{}", GsonUtil.toJson(result));
        assertEquals(GeneralCodes.OK.getCode(), result.getCode());
    }

    @Test
    public void testGetCheckoutCouponListV2_userCode() {
        GetCheckoutCouponListV2Request request = new GetCheckoutCouponListV2Request();
        request.setAppId("1");
        request.setToken("1");
        request.setBizPlatform(Lists.newArrayList(BizPlatformEnum.RETAIL.getCode()));
        request.setUserId(3150272288L);
        request.setClientId(180100041114L);
        request.setCouponCode("M3KKRKZJK7K9P5KU");
        request.setOrgCode("direct");
        request.setOrgType(StoreTypeEnum.ORG_TYPE_DIRECT.getOrgType());
        request.setCityId(1L);
        request.setShoppingMode(ShoppingModeEnum.DEFAULT.getType());
        request.setShipmentId(-1);

        request.setUsedCoupon(1L);
        request.setSubmitType(SubmitTypeEnum.Normal.getCode());
        request.setPromotionTypeList(Lists.newArrayList(1,2,3,4));

        GoodsInfo goodsInfo1 = new GoodsInfo();
        goodsInfo1.setId(22540L);
        goodsInfo1.setLevel(GoodsLevelEnum.Sku.getValue());
        goodsInfo1.setTag(GoodsExtTagEnum.LABOR.getTag());
        goodsInfo1.setCount(1);
        GoodsInfo goodsInfo2 = new GoodsInfo();
        goodsInfo2.setId(1193300001L);
        goodsInfo2.setLevel(GoodsLevelEnum.Package.getValue());
        goodsInfo2.setTag(GoodsExtTagEnum.PART.getTag());
        goodsInfo2.setCount(1);
        GoodsInfo goodsInfo3 = new GoodsInfo();
        goodsInfo3.setId(600000981L);
        goodsInfo3.setLevel(GoodsLevelEnum.Ssu.getValue());
        goodsInfo3.setTag(GoodsExtTagEnum.LABOR.getTag());
        goodsInfo3.setCount(1);
        request.setSkuPackageList(Lists.newArrayList(goodsInfo1, goodsInfo2, goodsInfo3));

        Result<GetCheckoutCouponListV2Response> result = couponTradeDubboService.getCheckoutCouponListV2(request);
        log.info("getCheckoutCouponListV2_userCode result-{}", GsonUtil.toJson(result));
        assertEquals(GeneralCodes.OK.getCode(), result.getCode());
    }

    @Test
    public void testGetCheckoutCouponListV2_userRep() {
        GetCheckoutCouponListV2Request request = new GetCheckoutCouponListV2Request();
        request.setAppId("1");
        request.setToken("1");
        request.setBizPlatform(Lists.newArrayList(BizPlatformEnum.CAR_SHOP.getCode()));
        request.setUserId(3150448260L);
        request.setClientId(180100041075L);
        request.setOrgCode("direct");
        request.setOrgType(StoreTypeEnum.ORG_TYPE_DIRECT.getOrgType());
        request.setCityId(1L);
        request.setShoppingMode(ShoppingModeEnum.DEFAULT.getType());
        request.setShipmentId(-1);

        request.setUsedCoupon(1011089216L);
        request.setSubmitType(SubmitTypeEnum.REPLACE.getCode());
        request.setPromotionTypeList(Lists.newArrayList(1,2,3,4));

        GoodsInfo goodsInfo1 = new GoodsInfo();
        goodsInfo1.setId(22541L);
        goodsInfo1.setLevel(GoodsLevelEnum.Sku.getValue());
        goodsInfo1.setTag(GoodsExtTagEnum.LABOR.getTag());
        goodsInfo1.setCount(1);
        GoodsInfo goodsInfo2 = new GoodsInfo();
        goodsInfo2.setId(1212200003L);
        goodsInfo2.setLevel(GoodsLevelEnum.Package.getValue());
        goodsInfo2.setTag(GoodsExtTagEnum.PART.getTag());
        goodsInfo2.setCount(1);
        GoodsInfo goodsInfo3 = new GoodsInfo();
        goodsInfo3.setId(600000986L);
        goodsInfo3.setLevel(GoodsLevelEnum.Ssu.getValue());
        goodsInfo3.setTag(GoodsExtTagEnum.LABOR.getTag());
        goodsInfo3.setCount(1);
        request.setSkuPackageList(Lists.newArrayList(goodsInfo1, goodsInfo2, goodsInfo3));

        Result<GetCheckoutCouponListV2Response> result = couponTradeDubboService.getCheckoutCouponListV2(request);
        log.info("getCheckoutCouponListV2_userRep result-{}", GsonUtil.toJson(result));
        assertEquals(GeneralCodes.OK.getCode(), result.getCode());
    }

    @Test
    public void testGetCheckoutCouponListV2_paramError() {
        GetCheckoutCouponListV2Request request = new GetCheckoutCouponListV2Request();

        Result<GetCheckoutCouponListV2Response> result = couponTradeDubboService.getCheckoutCouponListV2(request);
        assertEquals("小米ID不能为空", result.getMessage());
        request.setUserId(1L);

        request.setBizPlatform(null);
        result = couponTradeDubboService.getCheckoutCouponListV2(request);
        assertEquals("业务领域不符合要求", result.getMessage());
        request.setBizPlatform(Lists.newArrayList(BizPlatformEnum.CAR_AFTER_SALE.getCode()));

        result = couponTradeDubboService.getCheckoutCouponListV2(request);
        assertEquals("必须传入VID", result.getMessage());
        request.setVid("1");

        result = couponTradeDubboService.getCheckoutCouponListV2(request);
        assertEquals("VID不符合要求", result.getMessage());
        request.setVid("12345678901234567");

        request.setBizPlatform(Lists.newArrayList(BizPlatformEnum.RETAIL.getCode()));
        result = couponTradeDubboService.getCheckoutCouponListV2(request);
        assertEquals("应用ID不能为空", result.getMessage());
        request.setClientId(1L);

        request.setShipmentId(null);
        result = couponTradeDubboService.getCheckoutCouponListV2(request);
        assertEquals("履约方式不能为空", result.getMessage());
        request.setShipmentId(1);

        request.setCouponIds(Lists.newArrayList(-1L));
        request.setCouponCode(" ");
        result = couponTradeDubboService.getCheckoutCouponListV2(request);
        assertEquals("优惠券ID和优惠码不能同时使用", result.getMessage());
        request.setCouponCode(null);

        result = couponTradeDubboService.getCheckoutCouponListV2(request);
        assertEquals("优惠券ID存在不合法的值", result.getMessage());
        request.setCouponIds(null);

        request.setCouponCode(" ");
        result = couponTradeDubboService.getCheckoutCouponListV2(request);
        assertEquals("优惠码存不合法的值", result.getMessage());
        request.setCouponCode("1");

        request.setPromotionTypeList(Lists.newArrayList(0));
        result = couponTradeDubboService.getCheckoutCouponListV2(request);
        assertEquals("优惠类型非法", result.getMessage());
    }
} 