package com.xiaomi.nr.coupon.mock;

import com.xiaomi.nr.coupon.mock.mysql.DataSourceMock;
import com.xiaomi.nr.coupon.mock.redis.RedisConnectionMock;
import com.xiaomi.youpin.utest.envmock.MockAction;
import com.xiaomi.youpin.utest.envmock.MockOption;

public class OptionHandler extends MockOption {

    @Override
    public MockAction doMysqlMock() {
        return new DataSourceMock();
    }

    @Override
    public MockAction doRedisMock() {
        return new RedisConnectionMock();
    }

    @Override
    public MockAction doServiceBeanMock() {
        return null;
    }

    @Override
    public MockAction doExtendMock() {
        return null;
    }

    @Override
    public boolean doZkMock() {
        return true;
    }

    @Override
    public boolean doTalosMock() {
        return true;
    }

    @Override
    public boolean doEsMock() {
        return true;
    }

    @Override
    public boolean doDubboMock() {
        return true;
    }
}
