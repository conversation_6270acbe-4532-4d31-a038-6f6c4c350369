package com.xiaomi.nr.coupon.v2.api.service;

import com.xiaomi.goods.gis.api.BatchedInfoService;
import com.xiaomi.goods.gis.api.GoodsInfoNewService;
import com.xiaomi.goods.gis.dto.batched.BatchedMultiInfoDTO;
import com.xiaomi.goods.gis.dto.batched.BatchedMultiInfoResponse;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsMultiInfoDTO;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsMultiInfoResponse;
import com.xiaomi.mit.api.error.ErrorCodes;
import com.xiaomi.nr.coupon.api.dto.couponconfig.*;
import com.xiaomi.nr.coupon.api.service.CouponConfigService;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.rpc.gis.GoodsInfoProxyService;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.v2.BaseTest;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * @author: zhangliwei6
 * @date: 2025/1/16 20:18
 * @description:
 */
@Slf4j
public class CouponConfigServiceTest extends BaseTest {

    @Autowired
    private CouponConfigService couponConfigService;

    @Autowired
    @Qualifier("stringNewCouponRedisTemplate")
    private StringRedisTemplate redisTemplate;

    @Autowired
    private GoodsInfoProxyService goodsInfoProxyService;

    @Test
    public void test_getGoodsList() {
        // mock api
        GoodsInfoNewService mockGoodsInfoNewService = PowerMockito.mock(GoodsInfoNewService.class);
        when(mockGoodsInfoNewService.getGoodsMultiInfo(any())).thenReturn(Result.success(mockGoodsMultiInfoResponse()));
        BatchedInfoService mockBatchedInfoService = PowerMockito.mock(BatchedInfoService.class);
        when(mockBatchedInfoService.getBatchedMultiInfo(any())).thenReturn(Result.success(mockBatchedMultiInfoResp()));
        Whitebox.setInternalState(goodsInfoProxyService, "goodsInfoNewService", mockGoodsInfoNewService);
        Whitebox.setInternalState(goodsInfoProxyService, "batchedInfoService", mockBatchedInfoService);

        CouponConfigRelationGoodsRequest request = new CouponConfigRelationGoodsRequest();
        request.setTypeId(194714L);
        Result<CouponConfigRelationGoodsResponse> result = couponConfigService.getGoodsList(request);
        log.info("getGoodsList result-{}", GsonUtil.toJson(result));
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());
    }

    private BatchedMultiInfoResponse mockBatchedMultiInfoResp() {
        BatchedMultiInfoResponse mockResp = new BatchedMultiInfoResponse();
        Map<Long, BatchedMultiInfoDTO> mockBatchedMap = new HashMap<>();
        // packageId -> productId
        BatchedMultiInfoDTO batchedMultiInfoDTO = new BatchedMultiInfoDTO();
        batchedMultiInfoDTO.setProductId(1L);
        mockBatchedMap.put(1193300001L, batchedMultiInfoDTO);
        mockBatchedMap.put(1161200052L, batchedMultiInfoDTO);
        mockBatchedMap.put(1212200003L, batchedMultiInfoDTO);
        mockResp.setBatchedMap(mockBatchedMap);
        return mockResp;
    }

    private static GoodsMultiInfoResponse mockGoodsMultiInfoResponse() {
        GoodsMultiInfoResponse mockResp = new GoodsMultiInfoResponse();
        Map<Long, GoodsMultiInfoDTO> mockGoodsMap = new HashMap<>();
        // goodsId -> productId
        GoodsMultiInfoDTO goodsMultiInfoDTO = new GoodsMultiInfoDTO();
        goodsMultiInfoDTO.setProductId(1L);
        mockGoodsMap.put(2181100032L, goodsMultiInfoDTO);
        mockGoodsMap.put(2181100033L, goodsMultiInfoDTO);
        mockGoodsMap.put(2144600003L, goodsMultiInfoDTO);
        mockGoodsMap.put(2182300161L, goodsMultiInfoDTO);
        mockGoodsMap.put(2182300160L, goodsMultiInfoDTO);
        mockGoodsMap.put(2144600004L, goodsMultiInfoDTO);
        mockGoodsMap.put(2153300090L, goodsMultiInfoDTO);
        mockResp.setGoodsMap(mockGoodsMap);
        return mockResp;
    }

    @Test
    public void test_getConfigList() {
        GoodsRelationCouponConfigRequest request = new GoodsRelationCouponConfigRequest();
        request.setId(22540L);
        request.setLevel(GoodsLevelEnum.Sku.getValue());
        Result<GoodsRelationCouponConfigResponse> result = couponConfigService.getConfigList(request);
        log.info("getConfigList result-{}", GsonUtil.toJson(result));
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());
    }

    @Test
    public void test_getGoodsCouponConfigRel() {
        GoodsCouponConfigRelRequest request = new GoodsCouponConfigRelRequest();
        request.setConfigIds(Lists.newArrayList(194714L, 194715L));
        request.setWithFuture(true);
        List<GoodsItem> goodsItems = new ArrayList<>();
        request.setGoodsItems(goodsItems);

        GoodsItem goodsItem = new GoodsItem();
        goodsItem.setId(22540L);
        goodsItem.setLevel(GoodsLevelEnum.Sku.getValue());
        goodsItems.add(goodsItem);
        GoodsItem goodsItem1 = new GoodsItem();
        goodsItem1.setId(1193300001L);
        goodsItem1.setLevel(GoodsLevelEnum.Package.getValue());
        goodsItems.add(goodsItem1);
        GoodsItem goodsItem2 = new GoodsItem();
        goodsItem2.setId(2181100032L);
        goodsItem2.setLevel(GoodsLevelEnum.Goods.getValue());
        goodsItems.add(goodsItem2);
        GoodsItem goodsItem3 = new GoodsItem();
        goodsItem3.setId(600000981L);
        goodsItem3.setLevel(GoodsLevelEnum.Ssu.getValue());
        goodsItems.add(goodsItem3);

        Result<GoodsCouponConfigRelResponse> result = couponConfigService.getGoodsCouponConfigRel(request);
        log.info("getGoodsCouponConfigRel result-{}", GsonUtil.toJson(result));
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());
    }

    @Test
    public void test_getCouponConfigGoodsRel() {
        CouponConfigGoodsRelRequest request = new CouponConfigGoodsRelRequest();
        request.setConfigIds(Lists.newArrayList(194714L, 194715L));
        request.setWithSku(true);
        Result<CouponConfigGoodsRelResponse> result = couponConfigService.getCouponConfigGoodsRel(request);
        log.info("getCouponConfigGoodsRel result-{}", GsonUtil.toJson(result));
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());
    }

    @Test
    public void test_getValidConfigList_paramError() {
        CouponTypeInfoListRequest request = new CouponTypeInfoListRequest();
        Result<List<CouponTypeInfoListResponse>> result = couponConfigService.getValidConfigList(request);
        Assertions.assertEquals(GeneralCodes.ParamError.getCode(), result.getCode());
        Assertions.assertEquals("参数sendChannel券发放渠道不能为空", result.getMessage());
        request.setSendChannel("1");
        result = couponConfigService.getValidConfigList(request);
        Assertions.assertEquals(GeneralCodes.ParamError.getCode(), result.getCode());
        Assertions.assertEquals("参数SKU/套装id不能为空", result.getMessage());
        GoodsItem goodsItem = new GoodsItem();
        request.setGoodsItemList(Lists.newArrayList(goodsItem));
        result = couponConfigService.getValidConfigList(request);
        Assertions.assertEquals(ErrCode.COUPON.getCode(), result.getCode());
        Assertions.assertTrue(result.getMessage().contains("所传SKU/套装,id不合法, id="));
        goodsItem.setId(1L);
        result = couponConfigService.getValidConfigList(request);
        Assertions.assertEquals(ErrCode.COUPON.getCode(), result.getCode());
        Assertions.assertTrue(result.getMessage().contains("所传SKU/套装,层级level不合法, level="));
    }

    @Test
    public void test_getValidConfigList() {
        CouponTypeInfoListRequest request = new CouponTypeInfoListRequest();
        request.setSendChannel("test");
        GoodsItem goodsItem1 = new GoodsItem();
        goodsItem1.setId(22540L);
        goodsItem1.setLevel(GoodsLevelEnum.Sku.getValue());
        GoodsItem goodsItem2 = new GoodsItem();
        goodsItem2.setId(1212200003L);
        goodsItem2.setLevel(GoodsLevelEnum.Package.getValue());
        GoodsItem goodsItem3 = new GoodsItem();
        goodsItem3.setId(2144600003L);
        goodsItem3.setLevel(GoodsLevelEnum.Goods.getValue());
        request.setGoodsItemList(Lists.newArrayList(goodsItem1, goodsItem2, goodsItem3));
        Result<List<CouponTypeInfoListResponse>> result = couponConfigService.getValidConfigList(request);
        log.info("getValidConfigList result-{}", GsonUtil.toJson(result));
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());
    }

    @Test
    public void test_getCouponConfigInfoList() {
        CouponConfigInfoRequest request = new CouponConfigInfoRequest();
        request.setConfigIdList(Collections.singletonList(194714L));
        request.setWithProducts(true);

        Result<List<CouponTypeInfoDto>> result = couponConfigService.getCouponConfigInfoList(request);
        log.info("getCouponConfigInfoList result-{}", GsonUtil.toJson(result));
        CouponTypeInfoDto couponInfo = result.getData().get(0);
        assertEquals(194714L, couponInfo.getConfigId().longValue());
        assertEquals("乾坤满件折减券-卡券自动化测试专用-jpj", couponInfo.getConfigName());
    }

    @Test
    public void test_getCouponConfigSurplus() {
        redisTemplate.opsForValue().set("nr:coupon:sendCount:194714", "1");

        CouponConfigSurplusRequest request = new CouponConfigSurplusRequest();
        request.setConfigIds(Collections.singletonList(194714));

        Result<Map<Integer, CouponConfigSurplusDto>> result = couponConfigService.getCouponConfigSurplus(request);
        log.info("getCouponConfigSurplus result-{}", GsonUtil.toJson(result));
        assertNotNull(result);
        assertEquals(result.getCode(), ErrorCodes.OK.getCode());
        assertNotNull(result.getData());
        
        CouponConfigSurplusDto surplus = result.getData().get(194714);
        assertNotNull(surplus);
        assertEquals(1, surplus.getSendCount());
    }

    @Test
    public void test_getValidConfigListV2() {
        ValidConfigListRequest request = new ValidConfigListRequest();
        List<GoodsItem> goodsItems = new ArrayList<>();
        GoodsItem item = new GoodsItem();
        item.setId(22540L);
        item.setLevel(GoodsLevelEnum.Sku.getValue());
        goodsItems.add(item);
        request.setGoodsItemList(goodsItems);

        request.setSceneCode("DCCC8C085E79839661D9B93E9A996EC8");
        request.setStartFetchTime(1736611200L);
        request.setEndFetchTime(1740758399L);

        Result<ValidConfigListResponse> result = couponConfigService.getValidConfigListV2(request);
        log.info("getValidConfigListV2 result-{}", GsonUtil.toJson(result));

        ValidConfigListResponse response = result.getData();
        assertNotNull(response.getCouponConfigMap());
    }
} 