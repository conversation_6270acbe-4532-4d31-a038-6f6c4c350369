package com.xiaomi.nr.coupon.api;

import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.infrastructure.rpc.sid.SidProxy;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(
        classes = {CouponBootstrap.class, SidTest.class}
)
public class SidTest {
    @Autowired
    private SidProxy sidProxy;

    @BeforeClass
    public static void beforeClass() throws Exception {

    }

    /**
     * 收尾工作，当前测试文件只执行一次
     */
    @AfterClass
    public static void afterClass() {
        /**
         * 收尾工作，例如停止redis和mysql
         */
        //CommonMock.getInstance().stop();
    }

    @Test
    public void testGetSid() {
        try {
            List<Long> ids = sidProxy.get(0L, 10);
            System.out.println("@@@@@@@@@@@@@@@@@@@@@@@: " + ids);
        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            e.printStackTrace();
        }
    }



}
