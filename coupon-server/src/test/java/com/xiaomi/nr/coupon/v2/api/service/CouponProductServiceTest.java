package com.xiaomi.nr.coupon.v2.api.service;

import com.xiaomi.nr.coupon.api.dto.coupon.GoodsItemDto;
import com.xiaomi.nr.coupon.api.dto.coupon.ProductConfigItemDto;
import com.xiaomi.nr.coupon.api.dto.coupon.ProductUsableCouponRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.ProductUsableCouponResponse;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.api.service.CouponProductService;
import com.xiaomi.nr.coupon.enums.couponconfig.UseChannelEnum;
import com.xiaomi.nr.coupon.enums.goods.BusinessTypeEnum;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.enums.goods.SaleModeEnum;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.v2.BaseTest;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * @author: zhangliwei6
 * @date: 2025/2/7 9:31
 * @description:
 */
@Slf4j
public class CouponProductServiceTest extends BaseTest {

    @Autowired
    private CouponProductService couponProductService;

    @Test
    public void testGetProductUsableCoupon_RetailSuccess() {
        ProductUsableCouponRequest request = new ProductUsableCouponRequest();
        request.setUserId(3150272288L);
        request.setOrgCode("direct");
        request.setUseChannel(UseChannelEnum.MiShop.getCode());
        request.setPromotionTypeList(Lists.newArrayList(1,2,3,4));
        request.setBizPlatform(BizPlatformEnum.RETAIL.getCode());
        request.setClientId(180100041114L);

        ProductConfigItemDto productConfigItemDto = new ProductConfigItemDto();
        productConfigItemDto.setConfigId(194714L);
        request.setConfigList(Lists.newArrayList(productConfigItemDto));

        GoodsItemDto goodsItem1 = new GoodsItemDto();
        goodsItem1.setId(22540L);
        goodsItem1.setLevel(GoodsLevelEnum.Sku.getValue());
        goodsItem1.setSalePrice(1122L);
        goodsItem1.setMarketPrice(2222L);
        goodsItem1.setVirtual(false);
        goodsItem1.setSaleMode(SaleModeEnum.STANDARD.getValue());
        goodsItem1.setBusinessType(BusinessTypeEnum.CN_ORDER.getValue());
        goodsItem1.setFinalStartTime(1737579734L);
        goodsItem1.setFinalEndTime(1741224946L);
        GoodsItemDto goodsItem2 = new GoodsItemDto();
        goodsItem2.setId(1193300001L);
        goodsItem2.setLevel(GoodsLevelEnum.Package.getValue());
        goodsItem2.setSalePrice(2233L);
        goodsItem2.setMarketPrice(4433L);
        goodsItem2.setVirtual(false);
        goodsItem2.setSaleMode(SaleModeEnum.BOOKING.getValue());
        goodsItem2.setBusinessType(BusinessTypeEnum.CN_FAMILYS.getValue());
        goodsItem2.setFinalStartTime(1737579744L);
        goodsItem2.setFinalEndTime(1741224947L);
        GoodsItemDto goodsItem3 = new GoodsItemDto();
        goodsItem3.setId(600000981L);
        goodsItem3.setLevel(GoodsLevelEnum.Ssu.getValue());
        goodsItem3.setSalePrice(3344L);
        goodsItem3.setMarketPrice(4444L);
        goodsItem3.setVirtual(false);
        goodsItem3.setSaleMode(SaleModeEnum.PRESALES.getValue());
        goodsItem3.setBusinessType(BusinessTypeEnum.CN_YPORDER.getValue());
        goodsItem3.setFinalStartTime(1737579754L);
        goodsItem3.setFinalEndTime(1741224948L);
        request.setGoodsList(Lists.newArrayList(goodsItem1, goodsItem2, goodsItem3));

        Result<ProductUsableCouponResponse> result = couponProductService.getProductUsableCoupon(request);
        log.info("getProductUsableCoupon retail result-{}", GsonUtil.toJson(result));
        assertNotNull(result.getData());
    }

    @Test
    public void testGetProductUsableCoupon_CarShopSuccess() {
        ProductUsableCouponRequest request = new ProductUsableCouponRequest();
        request.setUserId(3150448260L);
        request.setOrgCode("spec");
        request.setUseChannel(UseChannelEnum.MiHome.getCode());
        request.setPromotionTypeList(Lists.newArrayList(1,2,3,4));
        request.setBizPlatform(BizPlatformEnum.CAR_SHOP.getCode());
        request.setClientId(180100041075L);

        ProductConfigItemDto productConfigItemDto = new ProductConfigItemDto();
        productConfigItemDto.setConfigId(194715L);
        request.setConfigList(Lists.newArrayList(productConfigItemDto));

        GoodsItemDto goodsItem3 = new GoodsItemDto();
        goodsItem3.setId(600000986L);
        goodsItem3.setLevel(GoodsLevelEnum.Ssu.getValue());
        goodsItem3.setSalePrice(3399L);
        goodsItem3.setMarketPrice(4499L);
        goodsItem3.setVirtual(false);
        goodsItem3.setSaleMode(SaleModeEnum.PRESALES.getValue());
        goodsItem3.setBusinessType(BusinessTypeEnum.CN_YPORDER.getValue());
        goodsItem3.setFinalStartTime(1737579854L);
        goodsItem3.setFinalEndTime(1741224958L);
        request.setGoodsList(Lists.newArrayList(goodsItem3));

        Result<ProductUsableCouponResponse> result = couponProductService.getProductUsableCoupon(request);
        log.info("getProductUsableCoupon carShop result-{}", GsonUtil.toJson(result));
        assertNotNull(result);
    }

    @Test
    public void testGetProductUsableCoupon_ParamError() {
        // 业务领域非法
        ProductUsableCouponRequest request = new ProductUsableCouponRequest();
        request.setBizPlatform(999);
        Result<ProductUsableCouponResponse> result = couponProductService.getProductUsableCoupon(request);
        assertEquals("业务领域非法", result.getMessage());
        request.setBizPlatform(BizPlatformEnum.RETAIL.getCode());

        // 不设置clientId
        result = couponProductService.getProductUsableCoupon(request);
        assertEquals("clientId不符合要求", result.getMessage());
        request.setClientId(123L);

        // 找不到业务领域
        request.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());
        result = couponProductService.getProductUsableCoupon(request);
        assertEquals("暂不支持当前业务领域", result.getMessage());
        request.setBizPlatform(BizPlatformEnum.RETAIL.getCode());

        request.setClientId(123L);
        request.setNeedFetchedCoupon(false);
        request.setNeedFetchableCoupon(false);
        result = couponProductService.getProductUsableCoupon(request);
        assertEquals("可领券和可用券至少查询一种类型", result.getMessage());
        request.setNeedFetchableCoupon(true);

        GoodsItemDto goodsItemDto = new GoodsItemDto();
        request.setGoodsList(Lists.newArrayList(goodsItemDto));
        result = couponProductService.getProductUsableCoupon(request);
        assertEquals("所传商品sku或套装ID不能都为空", result.getMessage());
        goodsItemDto.setId(1L);

        result = couponProductService.getProductUsableCoupon(request);
        assertEquals("所传商品品级不能都为空", result.getMessage());
        goodsItemDto.setLevel(GoodsLevelEnum.Goods.getValue());

        goodsItemDto.setId(-1L);
        result = couponProductService.getProductUsableCoupon(request);
        assertEquals("所传商品id需大于0", result.getMessage());
        goodsItemDto.setId(1L);

        request.setPromotionTypeList(Lists.newArrayList(-1));
        result = couponProductService.getProductUsableCoupon(request);
        assertEquals("优惠类型非法", result.getMessage());
        request.setPromotionTypeList(Lists.newArrayList(2));

        // retail特有校验
        result = couponProductService.getProductUsableCoupon(request);
        assertEquals("所传商品品级只能为sku或package或ssu", result.getMessage());
        goodsItemDto.setLevel(GoodsLevelEnum.Sku.getValue());

        result = couponProductService.getProductUsableCoupon(request);
        assertEquals("所传商品销售价不能为空", result.getMessage());
        goodsItemDto.setSalePrice(1L);

        result = couponProductService.getProductUsableCoupon(request);
        assertEquals("所传商品市场价不能为空", result.getMessage());
        goodsItemDto.setMarketPrice(1L);

        result = couponProductService.getProductUsableCoupon(request);
        assertEquals("所传是否为虚拟商品不能为空", result.getMessage());
        goodsItemDto.setVirtual(false);

        result = couponProductService.getProductUsableCoupon(request);
        assertEquals("所传商品销售模式不能为空", result.getMessage());
        goodsItemDto.setSaleMode(SaleModeEnum.BOOKING.getValue());

        result = couponProductService.getProductUsableCoupon(request);
        assertEquals("所传商品类型必须大于0", result.getMessage());
        goodsItemDto.setBusinessType(BusinessTypeEnum.CN_ORDER.getValue());

        result = couponProductService.getProductUsableCoupon(request);
        assertEquals("定金预售模式必须传有效的尾款支付时间", result.getMessage());
        goodsItemDto.setFinalStartTime(1737579854L);
        goodsItemDto.setFinalEndTime(1741224946L);

        // car_shop特有校验
        request.setBizPlatform(BizPlatformEnum.CAR_SHOP.getCode());
        result = couponProductService.getProductUsableCoupon(request);
        assertEquals("渠道非法", result.getMessage());
        request.setUseChannel(UseChannelEnum.MiShop.getCode());

        result = couponProductService.getProductUsableCoupon(request);
        assertEquals("所传商品品级只能为ssu", result.getMessage());
    }
}
