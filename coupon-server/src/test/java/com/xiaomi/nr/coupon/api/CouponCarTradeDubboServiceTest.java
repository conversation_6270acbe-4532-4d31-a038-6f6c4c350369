package com.xiaomi.nr.coupon.api;

import com.xiaomi.nr.coupon.api.dto.coupon.checkout.GoodsInfo;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.api.dto.enums.GoodsExtTagEnum;
import com.xiaomi.nr.coupon.api.dto.trade.*;
import com.xiaomi.nr.coupon.api.service.CouponTradeDubboService;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.mock.OptionHandler;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.utest.envmock.MockHelper;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(
        classes = {CouponBootstrap.class, CouponCarTradeDubboServiceTest.class}
)
@Slf4j
public class CouponCarTradeDubboServiceTest {

    @Autowired
    @Qualifier("stringNewCouponRedisTemplate")
    private StringRedisTemplate redisTemplate;

    @Autowired
    private CouponTradeDubboService couponTradeDubboService;

    private static long startTime;


    @BeforeClass
    public static void beforeClass() throws Exception {
        startTime = System.currentTimeMillis();
    }

    @AfterClass
    public static void afterClass() {
        /**
         * 收尾工作，例如停止redis和mysql
         */
        MockHelper.stop();
        System.out.println("allTime:" + (System.currentTimeMillis() - startTime) + "ms");
    }


    /**
     * 结算无码券测试
     */
    @Test
    public void checkoutNoCode() {
        List<Long> couponIds = new ArrayList<>();
        couponIds.add(1010628107L);
        couponIds.add(1010633338L);
        try {
            GetCheckoutCouponListV2Request req = new GetCheckoutCouponListV2Request();
            req.setUserId(3150000058L);
            req.setVid("MOCKD000000000001");
            req.setClientId(180100031052L);
            req.setOrgCode("");
            req.setCityId(0L);
            req.setShoppingMode(0);
            req.setCouponIds(couponIds);
            req.setSubmitType(0);
            req.setBizPlatform(Lists.newArrayList(BizPlatformEnum.CAR_AFTER_SALE.getCode()));
            List<GoodsInfo> goodsInfos = new ArrayList<>();


            //可用
            GoodsInfo goodsInfo0 = new GoodsInfo();
            goodsInfo0.setId(600000977L);
            goodsInfo0.setLevel("ssu");
            goodsInfo0.setTag(GoodsExtTagEnum.PART.getTag());
            goodsInfo0.setCount(8);
            goodsInfos.add(goodsInfo0);

            //可用
            GoodsInfo goodsInfo1 = new GoodsInfo();
            goodsInfo1.setId(600005239L);
            goodsInfo1.setLevel("ssu");
            goodsInfo1.setTag(GoodsExtTagEnum.LABOR.getTag());
            goodsInfo1.setCount(8);
            goodsInfos.add(goodsInfo1);

            //不可用
            GoodsInfo goodsInfo2 = new GoodsInfo();
            goodsInfo2.setId(10001L);
            goodsInfo2.setLevel("ssu");
            goodsInfo2.setTag(GoodsExtTagEnum.LABOR.getTag());
            goodsInfo2.setCount(8);
            goodsInfos.add(goodsInfo2);

            //可用
            GoodsInfo goodsInfo3 = new GoodsInfo();
            goodsInfo3.setId(600005238L);
            goodsInfo3.setLevel("ssu");
            goodsInfo3.setTag(GoodsExtTagEnum.LABOR.getTag());
            goodsInfo3.setCount(8);
            goodsInfos.add(goodsInfo3);

            //可用
            GoodsInfo goodsInfo4 = new GoodsInfo();
            goodsInfo4.setId(600000981L);
            goodsInfo4.setLevel("ssu");
            goodsInfo4.setTag(GoodsExtTagEnum.PART.getTag());
            goodsInfo4.setCount(8);
            goodsInfos.add(goodsInfo4);

            //不可用
            GoodsInfo goodsInfo5 = new GoodsInfo();
            goodsInfo5.setId(10002L);
            goodsInfo5.setLevel("ssu");
            goodsInfo5.setTag(GoodsExtTagEnum.PART.getTag());
            goodsInfo5.setCount(8);
            goodsInfos.add(goodsInfo5);


            req.setSkuPackageList(goodsInfos);

            System.out.println(GsonUtil.toJson(req));
            Result<GetCheckoutCouponListV2Response> res = couponTradeDubboService.getCheckoutCouponListV2(req);
            System.out.println("###################################################");
            System.out.println(GsonUtil.toJson(res));
            System.out.println("###################################################");
        } catch (Exception e) {
            System.out.println("@@@@@@@@@@@@@@@@@@@@@@@: " + e.getMessage());
            Assert.fail(e.getMessage());
        }
    }


    @Test
    public void lockUserCoupon() {
        //尽量每次只更换订单号
        Long orderId = 1000001000001001L;
        Long userId = 3150000058L;
        String vid = "MOCKD000000000001";
        Long couponId = 1010628107L;

        try {
            // 初次调用
            LockCouponResponse response = lockCouponMethod(userId, vid, orderId, couponId);
            Assert.assertEquals(false, response.isIdempotent());

            // 幂等调用
            LockCouponResponse response2 = lockCouponMethod(userId, vid, orderId, couponId);
            Assert.assertEquals(true, response2.isIdempotent());

        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            Assert.fail(e.getMessage());
        }
    }


    @Test
    public void consumeUserCoupon() {
        //尽量每次只更换订单号
        Long orderId = 202405292007L;
        Long userId = 3150000058L;
        String vid = "MOCKD000000000004";
        Long couponId = 1010644758L;

        try {
            lockCouponMethod(userId, vid, orderId, couponId);

            // 初次调用
            ConsumeCouponResponse response = consumeCouponMethod(userId, vid, orderId, couponId);
            Assert.assertEquals(false, response.isIdempotent());

            // 幂等调用
            ConsumeCouponResponse response2 = consumeCouponMethod(userId, vid, orderId, couponId);
            Assert.assertEquals(true, response2.isIdempotent());

        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            Assert.fail(e.getMessage());
        }
    }

    @Test
    public void rollbackUserCoupon() {
        //尽量每次只更换订单号

        // ***************券码锁定后回滚*************** start
        Long orderId = 1000001000001003L;
        Long userId = 3150000058L;
        String vid = "MOCKD000000000001";
        Long couponId = 1010628107L;

        try {
            // 锁券码
            lockCouponMethod(userId, vid, orderId, couponId);

            // 初次调用
            RollbackCouponResponse result = rollbackCouponMethod(userId, vid, orderId, couponId);
            Assert.assertEquals(false, result.isIdempotent());

            // 幂等调用
            RollbackCouponResponse result2 = rollbackCouponMethod(userId, vid, orderId, couponId);
            Assert.assertEquals(true, result2.isIdempotent());

        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            Assert.fail(e.getMessage());
        }
        // ***************券码锁定后回滚*************** end

        try {
            // 后续单测执行太快触发日志唯一建
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            System.out.println("########################Thread: " + e.getMessage());
        }

        // ***************券码核销后回滚*************** start
        //尽量每次只更换订单号
        orderId = 1000001000001004L;

        try {
            // 锁券码
            lockCouponMethod(userId, vid, orderId, couponId);

            // 核销券码
            consumeCouponMethod(userId, vid, orderId, couponId);

            // 初次调用
            RollbackCouponResponse result = rollbackCouponMethod(userId, vid, orderId, couponId);
            Assert.assertEquals(false, result.isIdempotent());

            // 幂等调用
            RollbackCouponResponse result2 = rollbackCouponMethod(userId, vid, orderId, couponId);
            Assert.assertEquals(true, result2.isIdempotent());

        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            Assert.fail(e.getMessage());
        }
        // ***************券码核销后回滚*************** end
    }

    /**
     * 锁券公共方法
     *
     * @param orderId
     * @return
     */
    private LockCouponResponse lockCouponMethod(Long userId, String vid, Long orderId, Long couponId) {
        List<CouponLockItem> couponItems = Lists.newArrayList();
        CouponLockItem couponLockItem = new CouponLockItem();
        couponLockItem.setCouponId(couponId);
        couponLockItem.setReplaceMoney(new BigDecimal("5.1"));
        couponLockItem.setReduceExpress(new BigDecimal("1.8"));
        couponItems.add(couponLockItem);

        LockCouponRequest request = new LockCouponRequest();
        request.setOrderId(orderId);
        request.setCouponItems(couponItems);
        request.setClientId(180100031052L);
        request.setOffline(0);
        request.setUserId(userId);
        request.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());
        request.setVid(vid);
        request.setSubmitType(0);
        return couponTradeDubboService.lockUserCoupon(request).getData();
    }

    /**
     * 核销券公共方法
     *
     * @param orderId
     * @return
     */
    private ConsumeCouponResponse consumeCouponMethod(Long userId, String vid, Long orderId, Long couponId) {
        ConsumeCouponRequest request = new ConsumeCouponRequest();
        request.setUserId(userId);
        request.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());
        request.setVid(vid);
        request.setCouponIds(Lists.newArrayList(couponId));
        request.setOrderId(orderId);
        request.setOffline(0);
        request.setSubmitType(0);
        return couponTradeDubboService.consumeUserCoupon(request).getData();
    }

    private RollbackCouponResponse rollbackCouponMethod(Long userId, String vid, Long orderId, Long couponId) {
        RollbackCouponRequest request = new RollbackCouponRequest();
        request.setUserId(userId);
        request.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());
        request.setVid(vid);
        request.setOrderId(orderId);
        request.setCouponIds(Lists.newArrayList(couponId));
        request.setSubmitType(0);
        request.setOffline(0);
        return couponTradeDubboService.rollbackUserCoupon(request).getData();
    }

    @Test
    public void lockAfterSaleCouponTest() {
        log.info("================================================================================");

        LockCouponRequest request = new LockCouponRequest();
        request.setClientId(0L);
        request.setOrderId(89999999L);
        request.setOffline(0);
        request.setSubmitType(0);
        request.setUsedCoupon(0L);
        request.setAppId("XM2108");
        request.setToken("110c4f4c5a5b779af99c7b4a677dc850");
        request.setBizPlatform(4);
        request.setUserId(123456789L);
        request.setVid("LKBQ6WVUKFXAJZV40");

        List<CouponLockItem> couponItems = Lists.newArrayList();
        CouponLockItem couponLockItem1 = new CouponLockItem();
        couponLockItem1.setCouponId(1010663122L);
        couponLockItem1.setReplaceMoney(BigDecimal.valueOf(5.00));
        couponLockItem1.setReduceExpress(BigDecimal.valueOf(0.00));
        couponItems.add(couponLockItem1);

        CouponLockItem couponLockItem2 = new CouponLockItem();
        couponLockItem2.setCouponId(1010742085L);
        couponLockItem2.setReplaceMoney(BigDecimal.valueOf(5.00));
        couponLockItem2.setReduceExpress(BigDecimal.valueOf(0.00));
        couponItems.add(couponLockItem2);

        request.setCouponItems(couponItems);

        Result<LockCouponResponse> result = couponTradeDubboService.lockUserCoupon(request);

        log.info("lockAfterSaleCouponTest result = {}", GsonUtil.toJson(result));

        log.info("================================================================================");
    }
    
    @Test
    public void consumeAfterSaleCouponTest() {
        log.info("================================================================================");

        ConsumeCouponRequest request = new ConsumeCouponRequest();
        request.setUserId(123456789);
        request.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());
        request.setVid("LKBQ6WVUKFXAJZV40");
        request.setCouponIds(Lists.newArrayList(1010742083L));
        request.setOrderId(89999999999L);
        request.setOffline(0);
        request.setSubmitType(0);
        request.setUsedCoupon(0L);
        request.setClientId(0L);

        Result<ConsumeCouponResponse> result = couponTradeDubboService.consumeUserCoupon(request);

        log.info("consumeAfterSaleCouponTest result = {}", GsonUtil.toJson(result));

        log.info("================================================================================");
    }

    @Test
    public void returnAfterSaleCouponTest() {
        log.info("================================================================================");


        RollbackCouponRequest request = new RollbackCouponRequest();
        request.setVid("LKBQ6WVUKFXAJZV40");
        request.setCouponIds(Lists.newArrayList(1010742083L));
        request.setOrderId(899999999L);
        request.setSubmitType(0);
        request.setClientId(0L);
        request.setOffline(0);
        request.setBizPlatform(4);
        request.setUserId(123456789);
        request.setUsedCoupon(0L);
        couponTradeDubboService.rollbackUserCoupon(request);

        log.info("================================================================================");
    }

}
