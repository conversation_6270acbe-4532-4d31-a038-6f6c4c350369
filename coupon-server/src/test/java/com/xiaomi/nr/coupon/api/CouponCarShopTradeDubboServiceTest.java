package com.xiaomi.nr.coupon.api;

import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.api.dto.trade.GetCheckoutCouponListV2Request;
import com.xiaomi.nr.coupon.api.dto.trade.GetCheckoutCouponListV2Response;
import com.xiaomi.nr.coupon.api.service.CouponTradeDubboService;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.utest.envmock.MockHelper;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(
        classes = {CouponBootstrap.class, CouponCarShopTradeDubboServiceTest.class}
)
@Slf4j
public class CouponCarShopTradeDubboServiceTest {

    @Autowired
    private CouponTradeDubboService couponTradeDubboService;

    private static long startTime;


    @BeforeClass
    public static void beforeClass() throws Exception {
        startTime = System.currentTimeMillis();
    }

    @AfterClass
    public static void afterClass() {
        /**
         * 收尾工作，例如停止redis和mysql
         */
        MockHelper.stop();
        System.out.println("allTime:" + (System.currentTimeMillis() - startTime) + "ms");
    }


    /**
     * 结算无码券测试
     */
    @Test
    public void checkoutNoCode() {
        try {
            GetCheckoutCouponListV2Request req = new GetCheckoutCouponListV2Request();
            req.setUserId(3150428235L);
            req.setBizPlatform(Lists.newArrayList(BizPlatformEnum.CAR_SHOP.getCode()));

            System.out.println(GsonUtil.toJson(req));
            Result<GetCheckoutCouponListV2Response> res = couponTradeDubboService.getCheckoutCouponListV2(req);
            System.out.println("###################################################");
            System.out.println(GsonUtil.toJson(res));
            System.out.println("###################################################");
        } catch (Exception e) {
            System.out.println("@@@@@@@@@@@@@@@@@@@@@@@: " + e.getMessage());
            Assert.fail(e.getMessage());
        }
    }

}
