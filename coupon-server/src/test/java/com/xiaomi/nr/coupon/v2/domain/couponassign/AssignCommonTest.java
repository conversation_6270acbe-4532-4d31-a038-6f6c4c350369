package com.xiaomi.nr.coupon.v2.domain.couponassign;

import com.xiaomi.mit.unittest.utils.ObjectFillUtil;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.couponassign.AssignCommon;
import com.xiaomi.nr.coupon.domain.couponassign.model.SingleAssignRequestDo;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigInfo;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponSceneItem;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CarCouponMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CouponMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.rpc.sid.SidProxy;
import com.xiaomi.nr.coupon.v2.BaseTest;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * @author: zhangliwei6
 * @date: 2025/1/17 16:37
 * @description:
 */
@Slf4j
public class AssignCommonTest extends BaseTest {

    @Autowired
    private CouponConfigRepository configCache;

    @Autowired
    private CouponMapper couponMapper;

    @Autowired
    private CarCouponMapper carCouponMapper;

//    @MockBean
//    private SidProxy sidProxy;

    @Autowired
    private AssignCommon assignCommon;

    @Test
    public void testIsCarVidCoupon_Success() {
        // 测试成功场景
        boolean result = assignCommon.isCarVidCoupon(BizPlatformEnum.CAR_AFTER_SALE.getCode());
        assertTrue(result);
    }

    @Test
    public void testGetConfigInfo_Success() throws BizError {
        // 准备测试数据
        Long id = 1L;
        CouponConfigItem couponConfigItem = new CouponConfigItem();
        couponConfigItem.setConfigId(id.intValue());
        couponConfigItem.setCouponConfigInfo(new CouponConfigInfo());

        // todo cache和redis中设置缓存

        // 验证结果
        BizError bizError = assertThrows(BizError.class, () -> assignCommon.getConfigInfo(id));
        assertEquals("无法获取到有效的券配置信息", bizError.getMsg());
    }

    @Test
    public void testGetSceneInfo_Success() throws BizError {
        // 准备测试数据
        String code = "testSceneCode";
        CouponSceneItem couponSceneItem = new CouponSceneItem();
        couponSceneItem.setSceneCode(code);

        // todo 插入coupon_scene，再refreshrefreshCouponSceneCache

        // 验证结果
        BizError bizError = assertThrows(BizError.class, () -> assignCommon.getSceneInfo(code));
        assertEquals("无法获取投放场景信息", bizError.getMsg());
    }

    @Test
    public void testGetIdemData_Success_ByVid() {
        // 准备测试数据
        SingleAssignRequestDo request = new SingleAssignRequestDo();
        request.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());
        request.setVid("testVid");
        request.setRequestId("testRequestId");
        request.setConfigId(1L);
        String sendChannel = "testChannel";

        CouponPo couponPo = new CouponPo();
        ObjectFillUtil.fillObject(couponPo);
        couponPo.setId(1L);
        couponPo.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());
        couponPo.setVid("testVid");
        couponPo.setRequestId("testRequestId");
        couponPo.setSendChannel("testChannel");
        couponPo.setActivityId("1");
        couponPo.setTypeId(1L);
        couponPo.setUseTime(100L);
        couponPo.setExpireTime(10000L);
        couponPo.setIsPass(1);
        couponPo.setAddTime(1000000L);
        couponPo.setAdminId(1L);
        couponPo.setInvalidTime(100000000L);
        couponPo.setOffline(1);
        couponPo.setTimesLimit(1);
        carCouponMapper.insert(couponPo);

        // 调用方法
        CouponPo result = assignCommon.getIdemData(request, sendChannel);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getId());
    }

    @Test
    public void testGetIdemData_Success_ByUserId() {
        // 准备测试数据
        SingleAssignRequestDo request = new SingleAssignRequestDo();
        request.setBizPlatform(BizPlatformEnum.CAR.getCode());
        request.setUserId(1L);
        request.setRequestId("testRequestId");
        request.setConfigId(1L);
        String sendChannel = "testChannel";

        CouponPo couponPo = new CouponPo();
        couponPo.setId(1L);
        couponPo.setBizPlatform(BizPlatformEnum.CAR.getCode());
        couponPo.setVid("testVid");
        couponPo.setUserId(1L);
        couponPo.setOffline(0);
        couponPo.setSendType("marketing");
        couponPo.setRequestId("testRequestId");
        couponPo.setActivityId("1");
        couponPo.setReduceExpress(BigDecimal.ZERO);
        couponPo.setParentId(1L);
        couponPo.setSendChannel("testChannel");
        couponPo.setExtendInfo("");
        couponMapper.insert(couponPo);

        // 调用方法
        CouponPo result = assignCommon.getIdemData(request, sendChannel);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getId());
    }

    @Test
    public void testGetCouponIds_Success() throws Exception {
        // 准备测试数据
        Long userId = 123L;
        int count = 2;
        List<Long> ids = Arrays.asList(1L, 2L);

        // 配置mock对象
        SidProxy mockSidProxy = PowerMockito.mock(SidProxy.class);
        when(mockSidProxy.get(userId, count)).thenReturn(ids);
        Whitebox.setInternalState(assignCommon, "sidProxy", mockSidProxy);

        // 调用方法
        List<Long> result = assignCommon.getCouponIds(userId, count);

        // 验证结果
        assertNotNull(result);
        assertEquals(count, result.size());
        assertEquals(ids, result);
    }
}
