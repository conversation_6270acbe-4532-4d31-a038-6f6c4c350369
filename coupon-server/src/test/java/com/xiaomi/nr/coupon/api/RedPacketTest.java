package com.xiaomi.nr.coupon.api;

import com.xiaomi.nr.coupon.api.dto.redpacket.UserValidMoneyRequest;
import com.xiaomi.nr.coupon.api.dto.redpacket.UserValidMoneyResponse;
import com.xiaomi.nr.coupon.api.service.RedPacketService;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(
        classes = {CouponBootstrap.class, RedPacketTest.class}
)
@Slf4j
public class RedPacketTest {
    @Autowired
    private RedPacketService redPacketService;

    @BeforeClass
    public static void beforeClass() throws Exception {}

    /**
     * 收尾工作，当前测试文件只执行一次
     */
    @AfterClass
    public static void afterClass() {
        /**
         * 收尾工作，例如停止redis和mysql
         */
        //CommonMock.getInstance().stop();
    }


    @Test
    public void testGetValidMoney() {
        long runStartTime = TimeUtil.getNowUnixMillis();
        try {
            long userId = 3150000058L;
            UserValidMoneyRequest request = new UserValidMoneyRequest();
            request.setUserId(userId);
            Result<UserValidMoneyResponse> result = redPacketService.userValidMoney(request);
            log.info("@@@@@@@@@@@@@@@@@@, costTime={}, result={}", TimeUtil.sinceMillis(runStartTime), result);
        } catch (Exception e) {
            log.error("##################, costTime={}, err={}", TimeUtil.sinceMillis(runStartTime), e.getMessage());
            e.printStackTrace();
        }
    }

}
