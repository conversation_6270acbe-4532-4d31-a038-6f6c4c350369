package com.xiaomi.nr.coupon.domain.coupontrade;

import com.xiaomi.nr.coupon.api.dto.trade.ConsumeCouponRequest;
import com.xiaomi.nr.coupon.api.dto.trade.ConsumeCouponResponse;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.domain.common.UserCouponPush;
import com.xiaomi.nr.coupon.domain.common.model.CouponUsePushContext;
import com.xiaomi.nr.coupon.enums.coupon.CouponPushStatusEnum;
import com.xiaomi.nr.coupon.enums.coupon.OrderStatusEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;


/**
 * @Description:
 * @Date: 2022.05.
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponBootstrap.class)
@ActiveProfiles("dev")
@EnableAspectJAutoProxy
public class UserCouponServiceTest {

    //@InjectMocks
    @Autowired
    private UserNoCodeCouponService userNoCodeCouponService;

    @Mock
    private TradeCheckFactory tradeCheckFactory;

    @Mock
    private TradeCheckAbstract tradeCheckAbstract;

    @Mock
    private UserCouponPush userCouponPush;



    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }


    @Test
    void testEmptyCouponIds() {
        ConsumeCouponRequest request = new ConsumeCouponRequest();
        request.setUserId(124402692L);
        request.setCouponIds(Arrays.asList(1000056880L));
        request.setOrderId(0L);

        BizError exception = assertThrows(BizError.class, () -> {
            userNoCodeCouponService.consumeUserCoupon(request);
        });

        assertEquals(GeneralCodes.ParamError.getCode(), exception.getCode());
        assertEquals("未查到有效的优惠券", exception.getMessage());
    }

    // @Test
    void testIdempotentCheckPass() throws BizError {
        ConsumeCouponRequest request = new ConsumeCouponRequest();
        request.setUserId(124402692L);
        request.setCouponIds(Arrays.asList(1000056880L));
        request.setOrderId(0L);
        request.setBizPlatform(1);

        when(tradeCheckFactory.getProvider(anyInt())).thenReturn(tradeCheckAbstract);
        when(tradeCheckAbstract.getCouponPoList(anyLong(), anyString(), anyList())).thenReturn(Arrays.asList(new CouponPo()));
        when(tradeCheckAbstract.couponListConsumeCheck(anyList(), anyLong(), anyString())).thenReturn(true);

        ConsumeCouponResponse response = userNoCodeCouponService.consumeUserCoupon(request);

        assertTrue(response.isIdempotent());
    }


    //@Test
    void testConsumeUserCoupon_SuccessfulConsume() throws Exception {
        ConsumeCouponRequest request = new ConsumeCouponRequest();
        request.setUserId(1L);
        request.setCouponIds(Arrays.asList(1L, 2L));
        request.setOrderId(100L);
        request.setBizPlatform(1);

        when(tradeCheckFactory.getProvider(anyInt())).thenReturn(tradeCheckAbstract);
        when(tradeCheckAbstract.getCouponPoList(anyLong(), anyString(), anyList())).thenReturn(Arrays.asList(new CouponPo()));
        when(tradeCheckAbstract.couponListConsumeCheck(anyList(), anyLong(), anyString())).thenReturn(false);
        doNothing().when(tradeCheckAbstract).consumeCoupon(any(), anyList());

        ConsumeCouponResponse response = userNoCodeCouponService.consumeUserCoupon(request);

        assertFalse(response.isIdempotent());
        verify(userCouponPush, times(1)).couponUsePush(any(CouponUsePushContext.class));
    }
    //@Test
    void testConsumeUserCoupon_ConsumeThrowsBizError() throws BizError {
        ConsumeCouponRequest request = new ConsumeCouponRequest();
        request.setUserId(1L);
        request.setCouponIds(Arrays.asList(1L, 2L));
        request.setOrderId(100L);
        request.setBizPlatform(1);

        when(tradeCheckFactory.getProvider(anyInt())).thenReturn(tradeCheckAbstract);
        when(tradeCheckAbstract.getCouponPoList(anyLong(), anyString(), anyList())).thenReturn(Arrays.asList(new CouponPo()));
        when(tradeCheckAbstract.couponListConsumeCheck(anyList(), anyLong(), anyString())).thenReturn(false);
        //doThrow(new BizError(GeneralCodes.InternalError, "核销失败")).when(tradeCheckAbstract).consumeCoupon(any(), anyList());

        BizError exception = assertThrows(BizError.class, () -> {
            userNoCodeCouponService.consumeUserCoupon(request);
        });

        assertEquals(GeneralCodes.InternalError.getCode(), exception.getCode());
        assertEquals("核销失败", exception.getMessage());
    }
}
