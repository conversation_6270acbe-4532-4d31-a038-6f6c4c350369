package com.xiaomi.nr.coupon.api;

import com.xiaomi.nr.coupon.api.service.CouponScheduleService;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2024/5/21 15:45
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(
        classes = {CouponBootstrap.class, CouponScheduleServiceTest.class}
)
@Slf4j
public class CouponScheduleServiceTest {
    @Autowired
    private CouponScheduleService couponScheduleService;

    @Test
    public void expireRepairTairCouponTest() {
        log.info("============================================================");

        couponScheduleService.expireRepairTairCoupon("");

        log.info("============================================================");
    }
}