package com.xiaomi.nr.coupon.api;

import com.xiaomi.nr.coupon.api.dto.trade.*;
import com.xiaomi.nr.coupon.api.service.CouponTradeDubboService;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.mock.OptionHandler;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.utest.envmock.MockHelper;
import org.assertj.core.util.Lists;
import org.junit.*;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.List;

@ActiveProfiles("ut")
@RunWith(SpringRunner.class)
@SpringBootTest(
        classes = {CouponBootstrap.class, CouponCodeTest.class}
)
public class CouponTradeNoCodeServiceTest {

    @Autowired
    private CouponTradeDubboService couponTradeDubboService;

    private static long startTime;


    @BeforeClass
    public static void beforeClass() throws Exception {
        startTime = System.currentTimeMillis();
        MockHelper.start("coupon_use.sql", new OptionHandler());
    }

    @AfterClass
    public static void afterClass() {
        /**
         * 收尾工作，例如停止redis和mysql
         */
        MockHelper.stop();
        System.out.println("allTime:" + (System.currentTimeMillis() - startTime) + "ms");
    }


    @Before
    public void before() {
        try {

        } catch (Exception e) {
            e.printStackTrace();
            Assert.fail(e.getMessage());
        }
    }

    @Test
    public void lockUserCoupon() {

        List<CouponLockItem> couponItems = Lists.newArrayList();
        CouponLockItem couponLockItem = new CouponLockItem();
        couponLockItem.setCouponId(1638986L);
        couponLockItem.setReplaceMoney(new BigDecimal("5.1"));
        couponLockItem.setReduceExpress(new BigDecimal("0.0"));
        couponItems.add(couponLockItem);

        LockCouponRequest request =new LockCouponRequest();
        request.setUserId(2270998577L);
        request.setOrderId(6221201562660994L);
        request.setCouponItems(couponItems);
        request.setClientId(1234535456L);
        request.setOffline(0);

        request.setUsedCoupon(1638986L);
        request.setSubmitType(1);

        try {
            // 单张券
            Result<LockCouponResponse> responseResult = couponTradeDubboService.lockUserCoupon(request);
            //Assert.assertEquals(false, responseResult.getData().isIdempotent());
        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            Assert.fail(e.getMessage());
        }

/*
        List<CouponLockItem> couponItem2 = Lists.newArrayList();

        CouponLockItem couponLockItem2 = new CouponLockItem();
        couponLockItem2.setCouponId(1638985L);
        couponLockItem2.setReplaceMoney(new BigDecimal("5.1"));
        couponLockItem2.setReduceExpress(new BigDecimal("0.0"));
        couponItem2.add(couponLockItem2);

        CouponLockItem couponLockItem3 = new CouponLockItem();
        couponLockItem3.setCouponId(1638984L);
        couponLockItem3.setReplaceMoney(new BigDecimal("0.0"));
        couponLockItem3.setReduceExpress(new BigDecimal("10.0"));
        couponItem2.add(couponLockItem3);

        request.setCouponItems(couponItem2);
        request.setOrderId(6221201562660995L);

        try {
            // 多张券 初次调用
            Result<LockCouponResponse> responseResult = couponTradeDubboService.lockUserCoupon(request);
            Assert.assertEquals(false, responseResult.getData().isIdempotent());

            // 多张券 幂等调用
            Result<LockCouponResponse> responseResult2 = couponTradeDubboService.lockUserCoupon(request);
            Assert.assertEquals(true, responseResult2.getData().isIdempotent());
        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            Assert.fail(e.getMessage());
        }*/

    }

    @Test
    public void consumeUserCoupon() {

        long orderId = 6221201562660994L;

        // ***************券锁定*************** start
        List<CouponLockItem> couponItems = Lists.newArrayList();
        CouponLockItem couponLockItem = new CouponLockItem();
        couponLockItem.setCouponId(1638983L);
        couponLockItem.setReplaceMoney(new BigDecimal("5.1"));
        couponLockItem.setReduceExpress(new BigDecimal("0.0"));
        couponItems.add(couponLockItem);

        LockCouponRequest lockCouponRequest =new LockCouponRequest();
        lockCouponRequest.setUserId(2270998577L);
        lockCouponRequest.setOrderId(orderId);
        lockCouponRequest.setCouponItems(couponItems);
        lockCouponRequest.setClientId(1234535456L);
        lockCouponRequest.setOffline(0);

        lockCouponRequest.setUsedCoupon(1638986L);
        lockCouponRequest.setSubmitType(1);

        try {
            // 单张券
            Result<LockCouponResponse> responseResult = couponTradeDubboService.lockUserCoupon(lockCouponRequest);
            //Assert.assertEquals(false, responseResult.getData().isIdempotent());
        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            Assert.fail(e.getMessage());
        }

        // ***************券锁定*************** end

        // ***************券核销*************** start

        ConsumeCouponRequest request =new ConsumeCouponRequest();
        request.setUserId(2270998577L);
        request.setOrderId(orderId);
        request.setCouponIds(Lists.newArrayList(1638983L));
        request.setOffline(0);
        request.setUsedCoupon(1638986L);
        request.setSubmitType(1);

        try {
            // 初次调用
            Result<ConsumeCouponResponse> responseResult = couponTradeDubboService.consumeUserCoupon(request);
            Assert.assertEquals(false, responseResult.getData().isIdempotent());

            // 幂等调用
            Result<ConsumeCouponResponse> responseResult2 = couponTradeDubboService.consumeUserCoupon(request);
            Assert.assertEquals(true, responseResult2.getData().isIdempotent());
        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            Assert.fail(e.getMessage());
        }
        // ***************券核销*************** end

    }


    @Test
    public void rollbackUserCoupon() {

       /* lockUserCoupon();
*/
        RollbackCouponRequest request =new RollbackCouponRequest();
        request.setUserId(2270998577L);
        request.setOrderId(6221201562660994L);
        request.setCouponIds(Lists.newArrayList(1638983L));
        request.setOffline(0);
        request.setUsedCoupon(1638986L);
        request.setSubmitType(1);

        /*try {
            // 初次调用
            Result<RollbackCouponResponse> responseResult = couponTradeDubboService.rollbackUserCoupon(request);
            Assert.assertEquals(false, responseResult.getData().isIdempotent());

            // 幂等调用
            Result<RollbackCouponResponse> responseResult2 = couponTradeDubboService.rollbackUserCoupon(request);
            Assert.assertEquals(true, responseResult2.getData().isIdempotent());
        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            Assert.fail(e.getMessage());
        }*/

        consumeUserCoupon();

        /*request.setOrderId(6221201562660996L);
        request.setCouponIds(Lists.newArrayList(1638984L,1638985L));
*/
        try {
            // 初次调用
            Result<RollbackCouponResponse> responseResult = couponTradeDubboService.rollbackUserCoupon(request);
            Assert.assertEquals(false, responseResult.getData().isIdempotent());

            // 幂等调用
            Result<RollbackCouponResponse> responseResult2 = couponTradeDubboService.rollbackUserCoupon(request);
            Assert.assertEquals(true, responseResult2.getData().isIdempotent());
        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            Assert.fail(e.getMessage());
        }

    }



}
