package com.xiaomi.nr.coupon.api;

import com.xiaomi.nr.coupon.api.dto.coupon.info.CouponInfoRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.info.CouponInfoResponse;
import com.xiaomi.nr.coupon.api.dto.couponcode.SingleExchangeRequest;
import com.xiaomi.nr.coupon.api.dto.couponcode.SingleExchangeResponse;
import com.xiaomi.nr.coupon.api.service.CouponAssignService;
import com.xiaomi.nr.coupon.api.service.CouponService;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponcode.po.CouponCodePo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponcodeslave.CouponCodeReadMapper;
import com.xiaomi.nr.coupon.mock.OptionHandler;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.utest.envmock.MockHelper;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.*;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;


@ActiveProfiles("ut")
@RunWith(SpringRunner.class)
@SpringBootTest(
        classes = {CouponBootstrap.class, CouponCodeTest.class}
)
@Slf4j
public class CouponCodeTest {

    @Autowired
    private CouponAssignService couponAssignService;

    @Autowired
    private CouponService couponService;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    @Qualifier("stringNewCouponRedisTemplate")
    private StringRedisTemplate redisTemplate;

    private static long startTime;


    @BeforeClass
    public static void beforeClass() throws Exception {
        startTime = System.currentTimeMillis();
        MockHelper.start("coupon_code_exchange.sql", new OptionHandler());
    }

    @AfterClass
    public static void afterClass() {
        /**
         * 收尾工作，例如停止redis和mysql
         */
        MockHelper.stop();
        System.out.println("allTime:" + (System.currentTimeMillis() - startTime) + "ms");
    }


    @Before
    public void before() {
        try {
            freshCouponGoodInfo();

            couponConfigRepository.loadValidCouponConfigToCache();

        } catch (Exception e) {
            e.printStackTrace();
            Assert.fail(e.getMessage());
        }
    }


    private void freshCouponGoodInfo() {
        String goodInfo = "{\"id\":30318,\"s\":{\"6959\":6271},\"gi\":{\"2221000022\":6271},\"pi\":{},\"ut\":\"Jun 2, 2022 10:58:19 AM\"}";

        ValueOperations<String, String> operations = redisTemplate.opsForValue();

        operations.set("nr:coupon:goods:30318", goodInfo);
    }


    @Test
    public void testExchange() {

        Long uid = 3150058767L;

        SingleExchangeRequest request = new SingleExchangeRequest();
        request.setUserId(uid);
        request.setCouponCode("CUR5DUZR7XMSLVXB");
        request.setAppId("XM2107");

        try {
            Result<SingleExchangeResponse> res = couponAssignService.singleExchange(request);
            Assert.assertEquals(true, res.getCode() == 0);
            Assert.assertEquals(true, res.getData().getCouponId() > 0);

            CouponInfoRequest requestCouponInfo = new CouponInfoRequest();
            requestCouponInfo.setUserId(uid);
            requestCouponInfo.setCouponIds(Lists.newArrayList(res.getData().getCouponId()));
            Result<CouponInfoResponse> result = couponService.couponInfo(requestCouponInfo);

            Assert.assertEquals(true, res.getCode() == 0);
            Assert.assertEquals(true, result.getData().getData().size() == 1);

            Result<SingleExchangeResponse> res2 = couponAssignService.singleExchange(request);
            Assert.assertEquals(true, res2.getData().getIsIdempotent());

        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            Assert.fail(e.getMessage());
        }
    }


    @Test
    public void testExchange2() {
        Long uid = 3150000058L;
        SingleExchangeRequest request = new SingleExchangeRequest();
        request.setUserId(uid);
        request.setCouponCode("MDVVVPUAVXPMY7F9");
        request.setAppId("XM2107");
        request.setOrgCode("MI0101");

        try {
            Result<SingleExchangeResponse> res = couponAssignService.singleExchange(request);
            Assert.assertEquals(true, res.getCode() == 0);
            Assert.assertEquals(true, res.getData().getCouponId() > 0);
        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            Assert.fail(e.getMessage());
        }
    }

    @Autowired
    private CouponCodeReadMapper couponCodeReadMapper;

    @Test
    public void testExchangeGetCodeInfo() {
        List<CouponCodePo>  data = couponCodeReadMapper.getCouponCode("834786432bedb94f61ac803eab99fa6c");
        System.out.println(data);
    }
}
