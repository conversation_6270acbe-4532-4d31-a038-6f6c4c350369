package com.xiaomi.nr.coupon.application.dubbo;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponCheckResp;
import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponReq;
import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponResp;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * <AUTHOR>
 * @date 2024/5/14 17:20
 */
@ActiveProfiles("dev")
@SpringBootTest(classes = CouponBootstrap.class)
@Slf4j
public class DubboCouponInvalidServiceImplTest {

    @Autowired
    private DubboCouponInvalidServiceImpl dubboCouponInvalidService;

    @Test
    public void testInvalidCouponCheck() {
        InvalidCouponReq req = new InvalidCouponReq();
        req.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());
        req.setVid("LKBQ78CMWXCZSFMH7");
        req.setCouponIdList(Lists.newArrayList(1010638970L,1010638971L,1010664330L,1010644850L,1010638972L,1010664331L));

        Result<InvalidCouponCheckResp> res = dubboCouponInvalidService.invalidCouponCheck(req);

        System.out.println("res is " + GsonUtil.toJson(res));

    }

    @Test
    public void testInvalidCoupon() {
        InvalidCouponReq req = new InvalidCouponReq();
        req.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());
        req.setVid("MOCKD000000000003");
        req.setCouponIdList(Lists.newArrayList(1010644714L));

        Result<InvalidCouponResp> res = dubboCouponInvalidService.invalidCoupon(req);

        System.out.println("res is " + GsonUtil.toJson(res));
    }
}