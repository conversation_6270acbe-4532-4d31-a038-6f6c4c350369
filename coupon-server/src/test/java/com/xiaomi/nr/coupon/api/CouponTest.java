package com.xiaomi.nr.coupon.api;

import com.xiaomi.nr.coupon.api.dto.PageBeanStream;
import com.xiaomi.nr.coupon.api.dto.coupon.*;
import com.xiaomi.nr.coupon.api.dto.coupon.checkout.GoodsInfo;
import com.xiaomi.nr.coupon.api.dto.coupon.list.UserCouponListDto;
import com.xiaomi.nr.coupon.api.dto.coupon.list.UserCouponListRequest;
import com.xiaomi.nr.coupon.api.dto.couponassign.SingleAssignRequest;
import com.xiaomi.nr.coupon.api.dto.couponassign.SingleAssignResponse;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.api.dto.trade.GetCheckoutCouponListV2Request;
import com.xiaomi.nr.coupon.api.dto.trade.GetCheckoutCouponListV2Response;
import com.xiaomi.nr.coupon.api.service.CouponAssignService;
import com.xiaomi.nr.coupon.api.service.CouponService;
import com.xiaomi.nr.coupon.api.service.CouponTradeDubboService;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.domain.common.Common;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CouponMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave.UserCouponCountMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.MissionCacheDao;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(
        classes = {CouponBootstrap.class, CouponTest.class}
)
@Slf4j
public class CouponTest {
    @Autowired
    private UserCouponCountMapper userCouponCountMapper;

    @Autowired
    private CouponService couponService;

    @Autowired
    private CouponMapper couponMapper;

    @Autowired
    private MissionCacheDao missionCacheDao;

    @Autowired
    private Common common;

    @Autowired
    private CouponAssignService couponAssignService;

    @Autowired
    private CouponTradeDubboService couponTradeDubboService;


    @BeforeClass
    public static void beforeClass() throws Exception {

    }

    /**
     * 收尾工作，当前测试文件只执行一次
     */
    @AfterClass
    public static void afterClass() {
        /**
         * 收尾工作，例如停止redis和mysql
         */
        //CommonMock.getInstance().stop();
    }

    @Test
    public void testAssign() {
        AssignRequest request = new AssignRequest();
        request.setAppId("XM2107");
        request.setUserId(31500587661L);
        request.setRequestId("couponSingleAssign_9");
        request.setSendChannel("test");
        request.setOrgCode("");
        AssignMissionItemDto item = new AssignMissionItemDto();
        item.setMissionId(12459L);
        List<AssignMissionItemDto> list = new LinkedList<>();
        list.add(item);
        request.setItems(list);
        request.setShareUserId(31500587661L);
        request.setToken("12121213sdfasdfasdf");
        Result<AssignResponse> res = couponService.assign(request);
        System.out.println(res);
    }


    @Test
    public void testAssignNew() {
        try {
            SingleAssignRequest req = new SingleAssignRequest();
            req.setRequestId("couponSingleAssign_9");
            req.setAssignMode(1);
            req.setConfigId(30144L);
            req.setUserId(31500587661L);
            req.setAppId("XM2107");
            req.setToken("12121213sdfasdfasdf");
            req.setSceneCode("DCCC8C085E79839661D9B93E9A996EC8");


            Result<SingleAssignResponse> res = couponAssignService.single(req);
            System.out.println(res);
        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            e.printStackTrace();
        }
    }


    @Test
    public void testGetValidCountDB() {
        try {
            long userId = 3150000058L;
            long nowTime = TimeUtil.getNowUnixSecond();
            List<Integer> bizPlatform = Lists.newArrayList(BizPlatformEnum.RETAIL.getCode());
            Integer count = userCouponCountMapper.getValidCount(userId, bizPlatform, nowTime);
            System.out.println("@@@@@@@@@@@@@@@@@@@@@@@: " + count);
        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testGetValidCount() {
        long runStartTime = TimeUtil.getNowUnixMillis();
        try {
            long userId = 3150000058L;
            UserCouponValidCountRequest request = new UserCouponValidCountRequest();
            request.setUserId(userId);
            request.setUseChannel("mi_home,mi_authorized");
            Result<UserCouponValidCountResponse> result = couponService.userCouponValidCount(request);
            log.info("@@@@@@@@@@@@@@@@@@, costTime={}, result={}", TimeUtil.sinceMillis(runStartTime), result);
        } catch (Exception e) {
            log.error("##################, costTime={}, err={}", TimeUtil.sinceMillis(runStartTime), e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testCarCouponList() {
        try {
            // 汽车服务券
            UserCouponListRequest request = new UserCouponListRequest();
            request.setStatus("unused");
            request.setVid("MOCKD000000000003");
            request.setBizPlatform(Lists.newArrayList(BizPlatformEnum.CAR_AFTER_SALE.getCode()));
            Result<PageBeanStream<UserCouponListDto>> result = couponService.userCouponList(request);

            System.out.println("@@@@@@@@@@@@@@@@@@@@@@@: " + result);
        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testUserCouponList() {

        long userId = 3150428235L;

        UserCouponListRequest request = new UserCouponListRequest();
        request.setUserId(userId);
        request.setStatus("all");

        request.setBizPlatform(Lists.newArrayList(BizPlatformEnum.CAR_SHOP.getCode()));

        Result<PageBeanStream<UserCouponListDto>> result = couponService.userCouponList(request);
        System.out.println("@@@@@@@@@@@@@@@@@@@@@@@: " + GsonUtil.toJson(result));
    }

    @Test
    public void testGetIdemData() {
        try {
            long userId = 3150000058L;
            String sendChannel = "";
            String requestId = "";
            List<CouponPo> idemData = couponMapper.getIdemData(userId, sendChannel, requestId, 0L);
            System.out.println("@@@@@@@@@@@@@@@@@@@@@@@: " + idemData);
        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            e.printStackTrace();
        }
    }


    @Test
    public void testRedisIncrSendCountAndCheck() {
        try {
            List<Long> missionIds = new ArrayList<>();
            missionIds.add(1000L);
            missionIds.add(1001L);
            missionIds.add(1002L);

            List<Long> maxNums = new ArrayList<>();
            maxNums.add(5L);
            maxNums.add(4L);
            maxNums.add(4L);

            missionCacheDao.incrSendCountAndCheck(missionIds, maxNums);
            System.out.println("@@@@@@@@@@@@@@@@@@@@@@@1: ");
        } catch (Exception e) {
            System.out.println("########################2: " + e);
            e.printStackTrace();
        }
    }

    @Test
    public void testCheckAppIdChannel() {
        try {
            Boolean res = common.checkAppIdChannel("XM2106", "store_manager");
            System.out.println("@@@@@@@@@@@@@@@1@@@@@@@@: " + res);

            res = common.checkAppIdChannel("XM2106", "");
            System.out.println("@@@@@@@@@@@@@@@2@@@@@@@@: " + res);
        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            e.printStackTrace();
        }
    }


    @Test
    public void testCheckoutChecker() {
        List<String> codes = new ArrayList<>();
        codes.add("MJ222G3M2LVXRGV6");
        codes.add("MJ222G3M2LVXRGV5");
        codes.add("MK333H5J3EEH4UPV");

        List<GoodsInfo> skuPackages = new ArrayList<>();

        GoodsInfo g1 = new GoodsInfo();
        g1.setId(22559L);
        g1.setLevel("sku");
        skuPackages.add(g1);

        GoodsInfo g2 = new GoodsInfo();
        g1.setId(23968L);
        g1.setLevel("sku");
        skuPackages.add(g2);

        GoodsInfo g3 = new GoodsInfo();
        g1.setId(32000L);
        g1.setLevel("sku");
        skuPackages.add(g3);

        CheckoutCheckerRequest req = new CheckoutCheckerRequest();
        req.setUserId(3150000058L);
        req.setCouponCode(codes);
        req.setClientId(180100041075L);
        req.setOrgCode("MI0101");
        req.setAppId("XM2114");
        req.setToken("bfe0f70cf91d34dfa807c3f19462cd7a");
        req.setSkuPackageList(skuPackages);
        try{
            Result<CheckoutCheckerResponse> res = couponService.checkoutChecker(req);
            System.out.println("@@@@@@@@@@@@@@@1@@@@@@@@");
            System.out.println(GsonUtil.toJson(res));
            System.out.println("@@@@@@@@@@@@@@@2@@@@@@@@");
        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 结算无码券测试
     */
    @Test
    public void checkoutNoCode() {
        Long orderId = 6221201562660996L;
        List<Long> couponIds = new ArrayList<>();
        couponIds.add(1010422340L);
/*        couponIds.add(1010002501L);
        couponIds.add(1010002502L);*/
        try {
            GetCheckoutCouponListV2Request req =new GetCheckoutCouponListV2Request();
            req.setUserId(1149315955L);
            req.setClientId(0L);
            req.setOrgCode("");
            req.setCityId(0L);
            req.setShoppingMode(0);
            req.setBizPlatform(Lists.newArrayList(3));
            List<GoodsInfo> goodsInfos  = new ArrayList<>();
            GoodsInfo goodsInfo = new GoodsInfo();
            goodsInfo.setId(600002292L);
            goodsInfo.setLevel("ssu");
            goodsInfos.add(goodsInfo);
            req.setSkuPackageList(goodsInfos);

            req.setCouponIds(couponIds);

            System.out.println(GsonUtil.toJson(req));

            Result<GetCheckoutCouponListV2Response> res = couponTradeDubboService.getCheckoutCouponListV2(req);
            System.out.println("###################################################");
            System.out.println(GsonUtil.toJson(res));
            System.out.println("###################################################");
        } catch (Exception e) {
            System.out.println("@@@@@@@@@@@@@@@@@@@@@@@: " + e.getMessage());
            Assert.fail(e.getMessage());
        }
    }

    /**
     * 结算有码券测试
     */
    @Test
    public void checkoutCode() {
        try {
            GetCheckoutCouponListV2Request req =new GetCheckoutCouponListV2Request();
            req.setUserId(3150000058L);
            req.setClientId(180100031013L);
            req.setOrgCode("JM002");
            req.setCityId(0L);
            req.setShoppingMode(0);
            List<GoodsInfo> goodsInfos  = new ArrayList<>();
            GoodsInfo goodsInfo = new GoodsInfo();
            goodsInfo.setId(21396L);
            goodsInfo.setLevel("sku");
            goodsInfos.add(goodsInfo);
            req.setSkuPackageList(goodsInfos);

            req.setCouponCode("MSAABAH9A3NNBLY9");

            Result<GetCheckoutCouponListV2Response> res = couponTradeDubboService.getCheckoutCouponListV2(req);
            System.out.println("###################################################");
            System.out.println(GsonUtil.toJson(res));
            System.out.println("###################################################");
        } catch (Exception e) {
            System.out.println("@@@@@@@@@@@@@@@@@@@@@@@: " + e.getMessage());
            Assert.fail(e.getMessage());
        }
    }

    /**
     * 价保单已使用券测试
     */
    @Test
    public void priceProtectUsedCoupon() {
        List<Long> couponIds = new ArrayList<>();
        couponIds.add(1009931140L);
        couponIds.add(1009931138L);
        couponIds.add(1009931137L);
        couponIds.add(1009931136L);
        couponIds.add(1009931139L);
        couponIds.add(1009930643L);
        couponIds.add(1009930642L);
        couponIds.add(1009930322L);
        couponIds.add(1009930321L);
        couponIds.add(1009930244L);
        couponIds.add(1009855655L);
        couponIds.add(1009116500L);
        couponIds.add(1009116268L);
        couponIds.add(1009116270L);

        try {
            GetUsedCouponRequest req =new GetUsedCouponRequest();
            req.setUserId(3150272468L);
            req.setClientId(180100041075L);
            req.setOrgCode("JM002");
            List<GoodsInfo> goodsInfos  = new ArrayList<>();
            GoodsInfo goodsInfo = new GoodsInfo();
            goodsInfo.setId(21396L);
            goodsInfo.setLevel("sku");
            goodsInfos.add(goodsInfo);

            GoodsInfo goodsInfo2 = new GoodsInfo();
            goodsInfo.setId(16147L);
            goodsInfo.setLevel("sku");
            goodsInfos.add(goodsInfo2);

            GoodsInfo goodsInfo3 = new GoodsInfo();
            goodsInfo.setId(10308L);
            goodsInfo.setLevel("sku");
            goodsInfos.add(goodsInfo3);

            req.setSkuPackageList(goodsInfos);

            req.setCouponIds(couponIds);

            Result<GetUsedCouponResponse> res = couponService.getUsedCoupons(req);
            System.out.println("###################################################");
            System.out.println(GsonUtil.toJson(res));
            System.out.println("###################################################");
        } catch (Exception e) {
            System.out.println("@@@@@@@@@@@@@@@@@@@@@@@: " + e.getMessage());
            Assert.fail(e.getMessage());
        }
    }

}
