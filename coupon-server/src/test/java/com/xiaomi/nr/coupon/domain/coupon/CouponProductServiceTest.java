package com.xiaomi.nr.coupon.domain.coupon;

import com.xiaomi.nr.coupon.api.dto.coupon.GoodsItemDto;
import com.xiaomi.nr.coupon.api.dto.coupon.ProductUsableCouponRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.ProductUsableCouponResponse;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.api.service.CouponProductService;
import com.xiaomi.nr.coupon.application.dubbo.CouponProductServiceImpl;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.enums.coupon.CouponUseChannelEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.AvailableChannelEnum;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024-11-20 20:03
*/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponBootstrap.class)
@ActiveProfiles("dev")
public class CouponProductServiceTest {

    @Autowired
    private CouponProductService couponProductService;

    @Test
    public void getProductUsableCouponTest() {
        ProductUsableCouponRequest request = new ProductUsableCouponRequest();
        request.setUserId(3150075543L);
        request.setBizPlatform(BizPlatformEnum.CAR_SHOP.getCode());
        request.setNeedFetchedCoupon(false);
        request.setUseChannel(CouponUseChannelEnum.CAR_SHOP.getId());
        GoodsItemDto dto = new GoodsItemDto();
        dto.setId(600013314L);
        dto.setLevel("ssu");
        request.setGoodsList(Arrays.asList(dto));
        Result<ProductUsableCouponResponse> result = couponProductService.getProductUsableCoupon(request);
        log.info("result:{}", GsonUtil.toJson(result));
    }


}
