package com.xiaomi.nr.coupon.v2.infrastructure.repository;

import com.xiaomi.nr.coupon.v2.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;

/**
 * @author: zhangliwei6
 * @date: 2025/1/16 14:35
 * @description:
 */
@Slf4j
public class RocketMQTest extends BaseTest {

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Test
    public void testSend() {
        rocketMQTemplate.send("test_topic", new Message<Object>() {
            @Override
            public Object getPayload() {
                return "123";
            }

            @Override
            public MessageHeaders getHeaders() {
                return null;
            }
        });
        rocketMQTemplate.send(new Message<Object>() {
            @Override
            public Object getPayload() {
                return "456";
            }

            @Override
            public MessageHeaders getHeaders() {
                return null;
            }
        });
        rocketMQTemplate.syncSendOrderly("test_topic", new Message<Object>() {
            @Override
            public Object getPayload() {
                return "789";
            }

            @Override
            public MessageHeaders getHeaders() {
                return null;
            }
        }, "test_key");
    }
}
