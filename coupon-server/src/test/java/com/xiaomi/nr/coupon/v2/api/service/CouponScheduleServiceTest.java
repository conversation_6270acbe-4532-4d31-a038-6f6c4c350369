package com.xiaomi.nr.coupon.v2.api.service;

import com.xiaomi.nr.coupon.api.service.CouponScheduleService;
import com.xiaomi.nr.coupon.infrastructure.repository.CarCouponRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CarCouponMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.v2.BaseTest;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.reflect.Whitebox;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * @author: zhangliwei6
 * @date: 2025/2/7 9:45
 * @description:
 */
public class CouponScheduleServiceTest extends BaseTest {

    @Autowired
    private CouponScheduleService couponScheduleService;

    @Autowired
    private CarCouponRepository carCouponRepository;

    @Autowired
    private CarCouponMapper carCouponMapper;

    @Test
    public void testExpireRepairTairCoupon_Success() {
        CarCouponMapper mockCarCouponMapper = PowerMockito.mock(CarCouponMapper.class);
        CouponPo couponPo = new CouponPo();
        couponPo.setId(1011143524L);
        when(mockCarCouponMapper.getExpireRepairTairCoupon(anyLong())).thenReturn(Lists.newArrayList(couponPo));
        Object realCarCouponRepository = AopProxyUtils.getSingletonTarget(carCouponRepository);
        Whitebox.setInternalState(realCarCouponRepository, "carCouponMapper", mockCarCouponMapper);
        Result<Void> result = couponScheduleService.expireRepairTairCoupon("test");
        assertEquals(GeneralCodes.OK.getCode(), result.getCode());
        Whitebox.setInternalState(realCarCouponRepository, "carCouponMapper", carCouponMapper);
    }
}
