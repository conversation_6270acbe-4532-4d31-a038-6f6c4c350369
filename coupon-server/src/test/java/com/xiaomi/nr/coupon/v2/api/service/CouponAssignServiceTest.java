package com.xiaomi.nr.coupon.v2.api.service;

import com.xiaomi.nr.coupon.api.dto.couponassign.CouponFetchCheckRequest;
import com.xiaomi.nr.coupon.api.dto.couponassign.CouponFetchCheckResponse;
import com.xiaomi.nr.coupon.api.dto.couponassign.FillSingleAssignRequest;
import com.xiaomi.nr.coupon.api.dto.couponassign.SingleAssignRequest;
import com.xiaomi.nr.coupon.api.dto.couponassign.SingleAssignResponse;
import com.xiaomi.nr.coupon.api.dto.couponcode.SingleExchangeRequest;
import com.xiaomi.nr.coupon.api.dto.couponcode.SingleExchangeResponse;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.api.service.CouponAssignService;
import com.xiaomi.nr.coupon.domain.couponassign.AssignCommon;
import com.xiaomi.nr.coupon.domain.couponassign.SingleAssigner;
import com.xiaomi.nr.coupon.domain.couponcode.ExchangeCommon;
import com.xiaomi.nr.coupon.domain.couponcode.SingleExchanger;
import com.xiaomi.nr.coupon.domain.fetchcheck.CouponFetchCheckService;
import com.xiaomi.nr.coupon.infrastructure.repository.nacosconfig.NacosSwitchConfig;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponassign.CouponAssignRedisDao;
import com.xiaomi.nr.coupon.infrastructure.rpc.sid.SidProxy;
import com.xiaomi.nr.coupon.util.AesUtil;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.v2.BaseTest;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;

/**
 * <AUTHOR>
 * @date 2025/2/5 10:20
 */

@Slf4j
@Order(Integer.MAX_VALUE / 2 + 1)
public class CouponAssignServiceTest extends BaseTest {
    @Autowired
    private CouponAssignService couponAssignService;

    private static final String AES_PASSWORD = "M6EED09BCC6539AD";

    private static SidProxy mockSidProxy = PowerMockito.mock(SidProxy.class);

    /**
     * 发单张券，3C场景
     */
    @Test
    public void test_single_3C() throws Exception {

        SingleAssignRequest req = new SingleAssignRequest();
        req.setRequestId(UUID.randomUUID().toString().replaceAll("-", ""));
        req.setSceneCode("136A560EF374DC3F6945EB3325AE28CD");
        req.setConfigId(190396L);
        req.setUserId(3150420867L);
        req.setAppId("XM2107");

        Mockito.when(mockSidProxy.get(anyLong(), anyInt())).thenReturn(Lists.newArrayList(100001L));

        // 获取 SingleAssigner 对象
        SingleAssigner singleAssigner = (SingleAssigner) getFieldValue(couponAssignService, "singleAssigner");

        // 获取 AssignCommon 对象
        AssignCommon assignCommon = (AssignCommon) getFieldValue(singleAssigner, "assignCommon");

        // 将 mock 的 SidProxy 对象注入到 AssignCommon 中
        setFieldValue(assignCommon, "sidProxy", mockSidProxy);

        Result<SingleAssignResponse> resp = couponAssignService.single(req);

        log.info("test_single_3C resp = {}", GsonUtil.toJson(resp));

    }

    /**
     * 发单张券，汽车销售场景
     */
    @Test
    public void test_single_car() throws Exception {
        SingleAssignRequest req = new SingleAssignRequest();
        req.setRequestId(UUID.randomUUID().toString().replaceAll("-", ""));
        req.setSceneCode("11BFB15C0E2BA4D03DFF93D95C843D0E");
        req.setConfigId(197772L);
        req.setUserId(3150420867L);
        req.setAppId("XM2114");
        req.setBizPlatform(BizPlatformEnum.CAR.getCode());

        Mockito.when(mockSidProxy.get(anyLong(), anyInt())).thenReturn(Lists.newArrayList(100002L));

        // 获取 SingleAssigner 对象
        SingleAssigner singleAssigner = (SingleAssigner) getFieldValue(couponAssignService, "singleAssigner");

        // 获取 AssignCommon 对象
        AssignCommon assignCommon = (AssignCommon) getFieldValue(singleAssigner, "assignCommon");

        // 将 mock 的 SidProxy 对象注入到 AssignCommon 中
        setFieldValue(assignCommon, "sidProxy", mockSidProxy);

        Result<SingleAssignResponse> resp = couponAssignService.single(req);

        log.info("test_single_car resp = {}", GsonUtil.toJson(resp));
    }

    /**
     * 发单张券，汽车售后场景
     */
    @Test
    public void test_single_carAfterSale() throws Exception {
        SingleAssignRequest req = new SingleAssignRequest();
        req.setRequestId(UUID.randomUUID().toString().replaceAll("-", ""));
        req.setSceneCode("5D058D0B5C6FDC1BDD5AF5A1961DDE3B");
        req.setConfigId(197773L);
        // req.setUserId(3150420867L);
        req.setVid("LKBQWDL7MWSKL4NX0");
        req.setAppId("XM2114");
        req.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());
        req.setUserId(3150420867L);

        Mockito.when(mockSidProxy.get(anyLong(), anyInt())).thenReturn(Lists.newArrayList(100003L));

        // 获取 SingleAssigner 对象
        SingleAssigner singleAssigner = (SingleAssigner) getFieldValue(couponAssignService, "singleAssigner");

        // 获取 AssignCommon 对象
        AssignCommon assignCommon = (AssignCommon) getFieldValue(singleAssigner, "assignCommon");

        // 将 mock 的 SidProxy 对象注入到 AssignCommon 中
        setFieldValue(assignCommon, "sidProxy", mockSidProxy);

        Result<SingleAssignResponse> resp = couponAssignService.single(req);

        log.info("test_single_carAfterSale resp = {}", GsonUtil.toJson(resp));
    }

    /**
     * 发单张券，车商城场景
     */
    @Test
    public void test_single_carShop() throws Exception {
        SingleAssignRequest req = new SingleAssignRequest();
        req.setRequestId(UUID.randomUUID().toString().replaceAll("-", ""));
        req.setSceneCode("1DC2FBE1B76AE8E10CBD23381492A66A");
        req.setConfigId(197976L);
        req.setAppId("XM2114");
        req.setBizPlatform(BizPlatformEnum.CAR_SHOP.getCode());
        req.setUserId(3150420867L);

        Mockito.when(mockSidProxy.get(anyLong(), anyInt())).thenReturn(Lists.newArrayList(100007L));

        // 获取 SingleAssigner 对象
        SingleAssigner singleAssigner = (SingleAssigner) getFieldValue(couponAssignService, "singleAssigner");

        // 获取 AssignCommon 对象
        AssignCommon assignCommon = (AssignCommon) getFieldValue(singleAssigner, "assignCommon");

        // 将 mock 的 SidProxy 对象注入到 AssignCommon 中
        setFieldValue(assignCommon, "sidProxy", mockSidProxy);

        Result<SingleAssignResponse> resp = couponAssignService.single(req);

        log.info("test_single_carShop resp = {}", GsonUtil.toJson(resp));
    }

    @Test
    public void test_couponFetchCheck_3C() throws NoSuchFieldException, IllegalAccessException {
        CouponFetchCheckRequest req = new CouponFetchCheckRequest();

        req.setConfigIdList(Collections.singletonList(190396L));
        req.setSceneCode("136A560EF374DC3F6945EB3325AE28CD");
        req.setUid(3150420867L);
        req.setAppId("XM2107");

        // mock NacosSwitchConfig
        NacosSwitchConfig mockNacosSwitchConfig = PowerMockito.mock(NacosSwitchConfig.class);
        mockNacosSwitchConfig.setReadNewCouponCount(true);
        mockNacosSwitchConfig.setOnlineCouponConfigId(31200);
        mockNacosSwitchConfig.setWriteOldUserCouponCount(false);
        mockNacosSwitchConfig.setCarShopGiftAssignNotify(true);

        CouponFetchCheckService couponFetchCheckService = (CouponFetchCheckService) getFieldValue(couponAssignService, "couponFetchCheckService");

        CouponAssignRedisDao assignRedisDao = (CouponAssignRedisDao) getFieldValue(couponFetchCheckService, "assignRedisDao");

        setFieldValue(assignRedisDao, "nacosSwitchConfig", mockNacosSwitchConfig);

        Result<CouponFetchCheckResponse> resp = couponAssignService.couponFetchCheck(req);

        log.info("test_couponFetchCheck_3C resp = {}", GsonUtil.toJson(resp));
    }

    @Test
    public void test_fillSingle_3C() throws Exception {
        FillSingleAssignRequest req = new FillSingleAssignRequest();
        req.setRequestId(UUID.randomUUID().toString().replaceAll("-", ""));
        req.setSceneCode("136A560EF374DC3F6945EB3325AE28CD");
        req.setConfigId(190396L);
        req.setUserId(3150420867L);
        req.setAppId("XM2107");
        req.setTaskId(1978L);

        Mockito.when(mockSidProxy.get(anyLong(), anyInt())).thenReturn(Lists.newArrayList(100004L));

        // 获取 SingleAssigner 对象
        SingleAssigner singleAssigner = (SingleAssigner) getFieldValue(couponAssignService, "singleAssigner");

        // 获取 AssignCommon 对象
        AssignCommon assignCommon = (AssignCommon) getFieldValue(singleAssigner, "assignCommon");

        // 将 mock 的 SidProxy 对象注入到 AssignCommon 中
        setFieldValue(assignCommon, "sidProxy", mockSidProxy);

        Result<SingleAssignResponse> resp = couponAssignService.fillSingle(req);

        log.info("test_fillSingle_3C resp = {}", GsonUtil.toJson(resp));
    }

    @Test
    public void test_singleExchange_3C() throws Exception {
        Long userId = 3150420867L;

        Mockito.when(mockSidProxy.get(anyLong(), anyInt())).thenReturn(Lists.newArrayList(100005L));

        SingleExchanger singleExchanger = (SingleExchanger) getFieldValue(couponAssignService, "singleExchanger");

        ExchangeCommon exchangeCommon = (ExchangeCommon) getFieldValue(singleExchanger, "exchangeCommon");

        setFieldValue(exchangeCommon, "sidProxy", mockSidProxy);

        SingleExchangeRequest req = new SingleExchangeRequest();
        req.setUserId(userId);
        req.setCouponCode(AesUtil.decrypt(AES_PASSWORD, "m/EIxXmmJyhzy7ptP7i+xvos3K5vaAO3tO54hiXbzYs="));
        req.setAppId("XM2107");

        Result<SingleExchangeResponse> resp = couponAssignService.singleExchange(req);

        log.info("test_singleExchange_3C resp = {}", GsonUtil.toJson(resp));
    }

    private Object getFieldValue(Object object, String fieldName) throws NoSuchFieldException, IllegalAccessException {
        java.lang.reflect.Field field = object.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        return field.get(object);
    }

    private void setFieldValue(Object object, String fieldName, Object value) throws NoSuchFieldException, IllegalAccessException {
        java.lang.reflect.Field field = object.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(object, value);
    }

}
