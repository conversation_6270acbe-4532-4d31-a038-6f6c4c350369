package com.xiaomi.nr.coupon.api;

import com.xiaomi.nr.coupon.api.dto.trade.*;
import com.xiaomi.nr.coupon.api.service.CouponTradeDubboService;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.mock.OptionHandler;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.utest.envmock.MockHelper;
import org.assertj.core.util.Lists;
import org.junit.*;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.List;

@ActiveProfiles("ut")
@RunWith(SpringRunner.class)
@SpringBootTest(
        classes = {CouponBootstrap.class, CouponCodeTest.class}
)
public class CouponTradeDubboServiceTest {

    @Autowired
    @Qualifier("stringNewCouponRedisTemplate")
    private StringRedisTemplate redisTemplate;

    @Autowired
    private CouponTradeDubboService couponTradeDubboService;

    private static long startTime;


    @BeforeClass
    public static void beforeClass() throws Exception {
        startTime = System.currentTimeMillis();
        MockHelper.start("coupon_use.sql", new OptionHandler());
    }

    @AfterClass
    public static void afterClass() {
        /**
         * 收尾工作，例如停止redis和mysql
         */
        MockHelper.stop();
        System.out.println("allTime:" + (System.currentTimeMillis() - startTime) + "ms");
    }


    /*@Before
    public void before() {
        try {
            freshCouponGoodInfo();
        } catch (Exception e) {
            e.printStackTrace();
            Assert.fail(e.getMessage());
        }
    }*/

    private void freshCouponGoodInfo() {
        String goodInfo_33002 = "{\"id\":33002,\"s\":[22540,22541,22542,14350,22543,22544,22545,22546,6163,22547,22548,14357,22549,22550,18455,22551],\"gi\":[2181100032,2181100033,2144600003,2182300161,2182300160,2144600004,2153300090],\"pi\":[],\"ut\":\"Jun 2, 2022 10:58:19 AM\"}";

        String goodInfo_33003 = "{\"id\":33003,\"s\":[22540,22541,22542,14350,22543,22544,22545,22546,6163,22547,22548,14357,22549,22550,18455,22551],\"gi\":[2181100032,2181100033,2144600003,2182300161,2182300160,2144600004,2153300090],\"pi\":[],\"ut\":\"Jun 2, 2022 10:58:19 AM\"}";

        String goodInfo_33004 = "{\"id\":33004,\"s\":[22540,22541,22542,14350,22543,22544,22545,22546,6163,22547,22548,14357,22549,22550,18455,22551],\"gi\":[2181100032,2181100033,2144600003,2182300161,2182300160,2144600004,2153300090],\"pi\":[],\"ut\":\"Jun 2, 2022 10:58:19 AM\"}";

        ValueOperations<String, String> operations = redisTemplate.opsForValue();

        operations.set("nr:coupon:newGoods:33002", goodInfo_33002);
        operations.set("nr:coupon:newGoods:33003", goodInfo_33003);
        operations.set("nr:coupon:newGoods:33004", goodInfo_33004);

    }

    @Test
    public void lockUserCouponCode() {
        Long orderId= 6221201562660994L;
        try {
            // 初次调用
            LockCouponResponse response = lockCodeCouponMethod(orderId);
            Assert.assertEquals(false, response.isIdempotent());

            // 幂等调用
            LockCouponResponse response2 = lockCodeCouponMethod(orderId);
            Assert.assertEquals(true, response2.isIdempotent());

        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            Assert.fail(e.getMessage());
        }
    }


    @Test
    public void consumeUserCouponCode() {

        Long orderId = 6221201562660994L;

        try {
            lockCodeCouponMethod(orderId);

            // 初次调用
            ConsumeCouponResponse response = consumeCodeCouponMethod(orderId);
            Assert.assertEquals(false, response.isIdempotent());

            // 幂等调用
            ConsumeCouponResponse response2 = consumeCodeCouponMethod(orderId);
            Assert.assertEquals(true, response2.isIdempotent());

        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            Assert.fail(e.getMessage());
        }
    }

    @Test
    public void rollbackUserCouponCode() {

        // ***************券码锁定后回滚*************** start

        Long orderId = 6221201562660994L;

        RollbackCouponRequest request =new RollbackCouponRequest();
        request.setUserId(2270998577L);
        request.setOrderId(orderId);
        request.setCouponCode("MN66765Y66JLZ4PZ");
        request.setOffline(0);
        try {
            // 锁券码
            lockCodeCouponMethod(orderId);

            // 初次调用
            Result<RollbackCouponResponse> responseResult = couponTradeDubboService.rollbackUserCoupon(request);
            Assert.assertEquals(false, responseResult.getData().isIdempotent());

            // 幂等调用
            Result<RollbackCouponResponse> responseResult2 = couponTradeDubboService.rollbackUserCoupon(request);
            Assert.assertEquals(true, responseResult2.getData().isIdempotent());
        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            Assert.fail(e.getMessage());
        }
        // ***************券码锁定后回滚*************** end

        try {
            // 后续单测执行太快触发日志唯一建
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            System.out.println("########################Thread: " + e.getMessage());
        }

        // ***************券码核销后回滚*************** start
        orderId = 6221201562660995L;

        request.setOrderId(orderId);

        try {
            // 锁券码
            lockCodeCouponMethod(orderId);

            // 核销券码
            consumeCodeCouponMethod(orderId);

            // 初次调用
            Result<RollbackCouponResponse> responseResult = couponTradeDubboService.rollbackUserCoupon(request);
            Assert.assertEquals(false, responseResult.getData().isIdempotent());

            // 幂等调用
            Result<RollbackCouponResponse> responseResult2 = couponTradeDubboService.rollbackUserCoupon(request);
            Assert.assertEquals(true, responseResult2.getData().isIdempotent());
        } catch (Exception e) {
            System.out.println("########################2: " + e.getMessage());
            Assert.fail(e.getMessage());
        }
        // ***************券码核销后回滚*************** end
    }

    /**
     * 锁券码公共方法
     * @param orderId
     * @return
     */
    private LockCouponResponse lockCodeCouponMethod(Long orderId) {

        List<CouponLockItem> couponItems = Lists.newArrayList();
        CouponLockItem couponLockItem = new CouponLockItem();
        couponLockItem.setCouponCode("MN66765Y66JLZ4PZ");
        couponLockItem.setReplaceMoney(new BigDecimal("5.1"));
        couponLockItem.setReduceExpress(new BigDecimal("0.0"));
        couponItems.add(couponLockItem);

        LockCouponRequest request =new LockCouponRequest();
        request.setUserId(2270998577L);
        request.setOrderId(orderId);
        request.setCouponItems(couponItems);
        request.setClientId(1234535456L);
        request.setOffline(0);

        return couponTradeDubboService.lockUserCoupon(request).getData();
    }

    /**
     * 核销券码公共方法
     * @param orderId
     * @return
     */
    private ConsumeCouponResponse consumeCodeCouponMethod(Long orderId) {
        ConsumeCouponRequest request =new ConsumeCouponRequest();
        request.setUserId(2270998577L);
        request.setOrderId(orderId);
        request.setCouponCode("MN66765Y66JLZ4PZ");
        request.setOffline(0);
        return couponTradeDubboService.consumeUserCoupon(request).getData();
    }

}
