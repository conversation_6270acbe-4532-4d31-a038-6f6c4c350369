package com.xiaomi.nr.coupon.domain;

import com.xiaomi.nr.coupon.api.dto.coupon.AssignMissionItemDto;
import com.xiaomi.nr.coupon.api.dto.coupon.AssignRequest;
import com.xiaomi.nr.coupon.api.dto.couponconfig.GoodsItem;
import com.xiaomi.nr.coupon.api.service.CouponService;
import com.xiaomi.nr.coupon.bootstrap.CouponBootstrap;
import com.xiaomi.nr.coupon.domain.couponconfig.RelationGoods;
import com.xiaomi.nr.coupon.domain.couponconfig.impl.ValidCouponConfigServiceImpl;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CouponMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.CouponConfigRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.GoodsConfigRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.MissionCacheDao;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponBootstrap.class)
@ActiveProfiles("dev")
public class validCouponConfigTest {
    @Resource
    private CouponConfigRedisDao configRedisDao;

    @Autowired
    private MissionCacheDao missionCacheRedisDao;


    @Autowired
    private ValidCouponConfigServiceImpl validCouponConfig;

    @Resource
    private GoodsConfigRedisDao goodsConfigRedisDao;

    @Test
    public void redisDaoTest() throws BizError {

        System.out.println(GsonUtil.toJson(configRedisDao.get(12422L)));


        System.out.println("&&&&&&&&&&&&&&&&& 1 &&&&&&&&&&&&&&&&");

/*
        List<MissionMapType> list = missionCacheRedisDao.getMissionIdMap(Lists.newArrayList(12470L));
        for(MissionMapType missionMapType : list){
            if(missionMapType.getCouponConfigId() == 12470){
                System.out.println("&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&");
            }
        }
*/

        System.out.println("&&&&&&&&&&&&&&&&& 2 &&&&&&&&&&&&&&&&");


        //根据券配置id获取券配置信息(单个)
        System.out.println(GsonUtil.toJson(configRedisDao.get(12422L)));

        System.out.println("&&&&&&&&&&&&&&&&& 3 &&&&&&&&&&&&&&&&");

        //根据券配置id获取券配置信息(批量)  12467,12466,12467
        List<Long> listCouponConfigIdList = new LinkedList<>();
        listCouponConfigIdList.add(12483L);
        System.out.println(GsonUtil.toJson(configRedisDao.get(listCouponConfigIdList)));

        System.out.println("&&&&&&&&&&&&&&&&& 4 &&&&&&&&&&&&&&&&");

        //根据sku/套装id获取券配置id
        System.out.println(GsonUtil.toJson(goodsConfigRedisDao.get(14683L,"sku")));

        System.out.println("&&&&&&&&&&&&&&&&& 5 &&&&&&&&&&&&&&&&");

        //获取有效券发放任务id和券配置id对应关系列表
        /*System.out.println(missionCacheRedisDao.getMissionIdMap());*/

        System.out.println("&&&&&&&&&&&&&&&&& 6 &&&&&&&&&&&&&&&&");

        //根据券发放任务id，获取券发放任务信息(批量)
        List<Long> listMissionIdList = new LinkedList<>();
        listMissionIdList.add(12231L);
        listMissionIdList.add(12230L);
        listMissionIdList.add(12232L);
        System.out.println(GsonUtil.toJson(missionCacheRedisDao.get(listMissionIdList)));

        System.out.println("&&&&&&&&&&&&&&&&& ７ &&&&&&&&&&&&&&&&");
    }



    @Test
    public void getCouponConfigTest() throws BizError {

        //根据券配置id获取券配置信息(批量)
       /* List<Long> list = new LinkedList<>();
        list.add(12491L);
        list.add(11943L);
        list.add(12547L);
        System.out.println(GsonUtil.toJson(validCouponConfig.getCouponTypeInfoById(list)));

*/
        //根据sku/套装id获取有效券配置信息
        List<GoodsItem> goodsItemList = new LinkedList<>();
        GoodsItem goodsItem = new GoodsItem();
        goodsItem.setId(20853L);
        goodsItem.setLevel("sku");
        goodsItemList.add(goodsItem);
        System.out.println(GsonUtil.toJson(validCouponConfig.getCouponTypeInfo("store_manager",goodsItemList)));

    }

    @Test
    public void getDistinctCouponConfigTest() throws BizError {

        //根据券配置id获取券配置信息(批量)
        List<Long> list = new LinkedList<>();
        list.add(12524L);
      /*  System.out.println(validCouponConfig.getCouponTypeInfoById(list));

        list = new LinkedList<>();
        list.add(12511L);
        System.out.println(validCouponConfig.getCouponTypeInfoById(list));
*/

        //根据sku/套装id获取有效券配置信息
        List<GoodsItem> goodsItemList = new LinkedList<>();
        GoodsItem goodsItem = new GoodsItem();
        goodsItem.setId(22007L);
        goodsItem.setLevel("sku");
        goodsItemList.add(goodsItem);
        System.out.println(validCouponConfig.getCouponTypeInfo("other", goodsItemList));
    }

    @Autowired
    private RelationGoods relationGoods;

    @Test
    public void getGoodsListTest() throws BizError {
        Long typeId = 12547L;
        System.out.println(GsonUtil.toJson(relationGoods.getGoodsList(typeId)));
    }


    @Resource
    private CouponService couponService;

    @Autowired
    private CouponMapper couponMapper;

    /**
     * 测试发券接口
     */
    @Test
    public void testAssign(){

        AssignRequest request = new AssignRequest();
        request.setAppId("XM2106");
        request.setUserId(2305955087L);
        request.setRequestId("R100000056");
        request.setSendChannel("store_manager");
        request.setOrgCode("XM0101");
        AssignMissionItemDto item = new AssignMissionItemDto();
        item.setMissionId(12190L);
        item.setOrderId(510000000000001L);
        List<AssignMissionItemDto> list = new LinkedList<>();
        list.add(item);
        request.setItems(list);
        request.setShareUserId(2305955087L);
        String token = DigestUtils.md5Hex("appId=XM2106&" +
                "items="+String.valueOf(list)+
                "&orgCode=XM0101&" +
                "requestId=R100000040&" +
                "sendChannel=store_manager&" +
                "shareUserId=3150000059&" +
                "userId=2305955087&" +
                "secret=98dd1182c35863981729501c2b226317");
        request.setToken(token);
        couponService.assign(request);
        System.out.println(GsonUtil.toJson(couponService.assign(request)));

    }

    /**
     * 测试发券接口2
     */
    @Test
    public void testAssign2(){
        AssignRequest request = new AssignRequest();
        request.setAppId("XM2106");
        request.setUserId(3150340320L);
        request.setRequestId("2000000326360886");
        request.setSendChannel("store_manager");
        request.setOrgCode("XM0101");
        AssignMissionItemDto item = new AssignMissionItemDto();
        item.setMissionId(20026L);
        List<AssignMissionItemDto> list = new LinkedList<>();
        list.add(item);
        request.setItems(list);
        request.setShareUserId(2202897204L);
        String token = DigestUtils.md5Hex("appId=XM2106&secret=98dd1182c35863981729501c2b226317");
        request.setToken(token);
        System.out.println(GsonUtil.toJson(couponService.assign(request)));
    }

}
