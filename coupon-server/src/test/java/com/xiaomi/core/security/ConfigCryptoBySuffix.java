package com.xiaomi.core.security;

import com.xiaomi.mit.common.codec.Coder;
import com.xiaomi.mit.common.codec.Coders;
import com.xiaomi.mit.common.security.MitSecurityException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 因gitlab pipeline中执行单测连不上keycenter，且keycenter的启动不好mock，所以只能重写该类，在92行直接返回原值不进行解密
 * <AUTHOR>
 * @date 2021-10-21
 */
public class ConfigCryptoBySuffix {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigCryptoBySuffix.class);

    private static final String KC_SID = "@kc-sid";
    private static final String KC_TYPE = "@kc-type";
    private static final String BYTES = "bytes";

    private static final Pattern LIST_KEY_REGEX = Pattern.compile("\\[\\d+]$");

    public boolean isKcKey(String name) {
        return StringUtils.endsWith(name, KC_SID) || StringUtils.endsWith(name, KC_TYPE);
    }

    String getKcKey(String name, String key) {
        Validate.notNull(name);

        Matcher m = LIST_KEY_REGEX.matcher(name);
        String clean;
        if (m.find()) {
            clean = name.substring(0, m.start());
        } else {
            clean = name;
        }
        return clean + key;
    }

    public String getKcSidKey(String name) {
        return getKcKey(name, KC_SID);
    }

    private String getKcTypeKey(String name) {
        return getKcKey(name, KC_TYPE);
    }

    public Object extractWrappedValue(Function<String, Object> getValueFunc, String name) {
        Object value = getValueFunc.apply(name);
        return tryDecryptValue(getValueFunc, name, value);
    }

    Object tryDecryptValue(Function<String, Object> getValueFunc, String name, Object value) {
        if (value == null) {
            return null;
        }

        // hide keycenter specific config from user.
        if (isKcKey(name)) {
            return null;
        }

        String sidKey = getKcSidKey(name);
        Object sid = getValueFunc.apply(sidKey);
        if (sid == null) {
            // no @kc-sid specified, so it's not keycenter encrypted value.
            return value;
        }

        // sid must be a String.
        Validate.isInstanceOf(CharSequence.class, sid,
                "%s value is of type %s, while String is expected: %s", sidKey, sid.getClass(), sid);

        Validate.isInstanceOf(CharSequence.class, value,
                "%s value is of type %s, while String is expected: %s", name, value.getClass(), value);

        boolean isForBytes = BYTES.equals(getValueFunc.apply(getKcTypeKey(name)));
        Coder<String, String> coder;
        if (isForBytes) {
            // Base64 encode plain bytes
            coder = Coders.BASE64.reverse().andThen(SimpleKeycenter.bytesToString(sid.toString()));
        } else {
            coder = SimpleKeycenter.stringToString(sid.toString());
        }
        try {
            String plain = value.toString();
            LOGGER.info("decrypt config success with sid:{}, {}={}", sid, name, value);
            return plain;
        } catch (Exception e) {
            throw new MitSecurityException("fail decrypt config: '" +
                    name + "'='" + maskMiddle(value.toString()) + "' with sid:" + sid, e);
        }
    }

    String maskMiddle(String text) {
        if (StringUtils.isBlank(text)) {
            return text;
        }

        // first char & last char always are revealed.
        int len = StringUtils.length(text);
        if (len <= 2) {
            return text;
        }

        // if length > 8, reveal 1/4 of chars.
        int left = 1;
        int right = 1;
        if (len > 8) {
            int reveal = len / 4;
            left = (reveal + 1) / 2; // ceil(reveal / 2)
            right = reveal - left;
        }
        int mask = len - left - right;

        return StringUtils.left(text, left) +
                StringUtils.repeat('*', mask) +
                StringUtils.right(text, right);
    }
}
