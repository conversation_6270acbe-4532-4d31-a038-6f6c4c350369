<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="5 seconds">

    <property resource="application.properties"></property>
    <property resource="config/application-${spring.profiles.active}.properties"></property>

    <property name="MAX_HISTORY" value="30"/>

    <!-- 所有日志, 写入文件 -->
    <appender name="fileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/coupon/server.log</file>
        <encoder>
            <pattern>%d|%-5level|%thread|%X{trace_id}|%logger{40}|%L|%msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/coupon/server.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <MaxHistory>${MAX_HISTORY}</MaxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>500MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>
    <appender name="asyncAppender" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 为0-不丢失日志，默认的。10:如果队列的90%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>10</discardingThreshold>
        <queueSize>1500</queueSize>
        <appender-ref ref="fileAppender"/>
    </appender>

    <!-- 错误日志, 写入文件 -->
    <appender name="errorAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/coupon/error.log</file>
        <encoder>
            <pattern>%d|%-5level|%thread|%X{trace_id}|%logger{40}|%L|%msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/coupon/error.%d{yyyy-MM-dd}.log.%i</fileNamePattern>
            <MaxHistory>${MAX_HISTORY}</MaxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <appender name="asyncErrorAppender" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>20</discardingThreshold>
        <queueSize>1500</queueSize>
        <appender-ref ref="errorAppender"/>
    </appender>

    <!-- 控制台输出 -->
    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d|%-5level|%thread|%X{_trace_id_}|%logger{40}|%L|%msg%n</pattern>
        </encoder>
    </appender>

    <logger name="org.springframework" level="ERROR"/>
    <logger name="ch.qos.logback" level="ERROR"/>
    <logger name="com.xiaomi.data.push.service.state" level="ERROR"/>
    <logger name="org.reflections.Reflections" level="ERROR"/>

    <root level="INFO">
        <appender-ref ref="stdout"/>
        <appender-ref ref="asyncAppender"/>
        <appender-ref ref="asyncErrorAppender"/>
    </root>
</configuration>
