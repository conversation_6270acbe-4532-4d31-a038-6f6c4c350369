#server
app.name=coupon
server.debug=true
server.connection-timeout=1000
spring.profiles.active=@profile_name@



rocketmq.producer.sendMessageTimeout=300000
rocketmq.producer.access-key=AKB7FI7MHQH2BBQQDW

rocketmq.couponUsePush.topic=nr_coupon_use_push
rocketmq.couponUsePush-producer.group=nr_coupon_use_push_group
rocketmq.carCouponUsePush.topic=nr_car_coupon_use_message
rocketmq.carCouponUsePush-producer.group=nr_car_coupon_use_message_producer_group
rocketmq.couponAssignNotify.topic=nr_coupon_assign_notify
rocketmq.couponAssignNotify-producer.group=nr_coupon_assign_notify_producer_group


#nacos线上地址：nacos.systech.b2c.srv:80 ，测试地址:  nacos.test.b2c.srv:80
app.nacos.address=${nacos.config.addrs}
#sentinel dashboard 配置sentinel dashboard的访问地址：staging http://sentinel.test.be.mi.com  线上 http://sentinel.be.mi.com
spring.cloud.sentinel.transport.dashboard={sentinel.dashboard.host}
spring.application.name=${app.name}
#项目注册进senbtinel是否懒加载(即当服务启动时就可以在控制台看到，不需要请求经过配置)
spring.cloud.sentinel.eager=false
#开启集群模式，限流时使用
spring.cloud.sentinel.cluster_enable=true
#集群模式下配置所在的nacos地址
spring.cloud.sentinel.nacos-server-addr=${nacos.config.addrs}
#sentinel客户端日志输出目录
spring.cloud.sentinel.log.dir=${log.path}/sentinel-client
# sentinel datasource nacos
#流控规则
spring.cloud.sentinel.datasource.ds1.nacos.server-addr=${app.nacos.address}
spring.cloud.sentinel.datasource.ds1.nacos.dataId=${app.name}-flow-rules
spring.cloud.sentinel.datasource.ds1.nacos.groupId=DEFAULT_GROUP
spring.cloud.sentinel.datasource.ds1.nacos.rule-type=FLOW
#降级规则
spring.cloud.sentinel.datasource.ds2.nacos.server-addr=${app.nacos.address}
spring.cloud.sentinel.datasource.ds2.nacos.dataId=${app.name}-degrade-rules
spring.cloud.sentinel.datasource.ds2.nacos.groupId=DEFAULT_GROUP
spring.cloud.sentinel.datasource.ds2.nacos.rule-type=DEGRADE


