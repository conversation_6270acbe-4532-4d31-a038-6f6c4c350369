#server
app.name=coupon
server.type=online
server.port=8080
server.debug=true
server.connection-timeout=1000

dubbo.group=car_online
dubbo.group.rpc=online
dubbo.protocol.id=dubbo
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.registry.address=nacos://nacos.systech.b2c.srv:80
nacos.config.addrs=nacos.systech.b2c.srv:80

youpin.log.group=online
log.path=/home/<USER>/log

talos.topic=cnzone_newretail_pro_retail_online
talos.sendpoint=http://cnbj4-talos.api.xiaomi.net
talos.access.key=AK7YWFGLFOC7HOEFZP
talos.access.secret=GDB12A7+7A1f2o6GP0tdL0J0Wn/J1pdjETZh6TsrQR9NbJMPPh4oNehAK1XrynREPCcYElivxJzUMENjmiH4aR3E24SzARgQHOQXM1XrQeacqcaYfa5rwhgUR/NAnbgr1sXtW7oylx7fQncIiGoA
talos.access.secret@kc-sid=mi_newretail_risk.g

spring.xmpulsecoupon-datasource.name=nr_coupon_admin
spring.xmpulsecoupon-datasource.username=nr_coupon_admin_wn
spring.xmpulsecoupon-datasource.url=***************************************************************************************
spring.xmpulsecoupon-datasource.password=GDDptVR4zt0aF1A++3H2WqkfvtpjPjxqIThh8SOvghEt46d3gFir0BOAr3x73pCrhEQYEpOcAumC7EhjsWyGr2a5amVxARgQZMMBBW2iShqgo2dogULs0RgUvkUze6J93JRb3P7SweLxJ4HgFtAA
spring.xmpulsecoupon-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmpulsecoupon-datasource.connectionProperties=
spring.xmpulsecoupon-datasource.sql-script-encoding=UTF-8
spring.xmpulsecoupon-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmpulsecoupon-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmpulsecoupon-datasource.initial-size=5
spring.xmpulsecoupon-datasource.max-active=100
spring.xmpulsecoupon-datasource.min-idle=3
spring.xmpulsecoupon-datasource.max-wait=60000
spring.xmpulsecoupon-datasource.remove-abandoned=true
spring.xmpulsecoupon-datasource.remove-abandoned-timeout=180
spring.xmpulsecoupon-datasource.time-between-eviction-runs-millis=60000
spring.xmpulsecoupon-datasource.min-evictable-idle-time-millis=300000
spring.xmpulsecoupon-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmpulsecoupon-datasource.test-while-idle=true
spring.xmpulsecoupon-datasource.test-on-borrow=false
spring.xmpulsecoupon-datasource.test-on-return=false
spring.xmpulsecoupon-datasource.pool-prepared-statements=true
spring.xmpulsecoupon-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmpulsecoupon-datasource.filters=stat,wall,slf4j

spring.xmpulse-datasource.name=xm_pulse
spring.xmpulse-datasource.username=misho_pulse_wn
spring.xmpulse-datasource.url=*******************************************************************************************
spring.xmpulse-datasource.password=GDD9c+qoq5MeZDnksB4LCim/rGV4Ip3Y8LtSb2doZDicKk6z9FgM5gsjgVrd8YpQWbUYEqZlqB5bCUzKuEGVhC2fnQyYARgQkZgeQ8yzRUybz7skkbDnyRgU3SL4N/QXgu4dgSrLHhB5phsT32gA
spring.xmpulse-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmpulse-datasource.connectionProperties=
spring.xmpulse-datasource.sql-script-encoding=UTF-8
spring.xmpulse-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmpulse-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmpulse-datasource.initial-size=20
spring.xmpulse-datasource.max-active=200
spring.xmpulse-datasource.min-idle=3
spring.xmpulse-datasource.max-wait=60000
spring.xmpulse-datasource.remove-abandoned=true
spring.xmpulse-datasource.remove-abandoned-timeout=180
spring.xmpulse-datasource.time-between-eviction-runs-millis=60000
spring.xmpulse-datasource.min-evictable-idle-time-millis=300000
spring.xmpulse-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmpulse-datasource.test-while-idle=true
spring.xmpulse-datasource.test-on-borrow=false
spring.xmpulse-datasource.test-on-return=false
spring.xmpulse-datasource.pool-prepared-statements=true
spring.xmpulse-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmpulse-datasource.filters=stat,wall,slf4j


spring.xmpulse-slave-datasource.name=xm_pulse_r
spring.xmpulse-slave-datasource.username=misho_pulse_rn
spring.xmpulse-slave-datasource.url=*******************************************************************************************
spring.xmpulse-slave-datasource.password=GDChaTAoNA9Lryde+zp7dRjAKuH/7rqRCxibZ3HdENt9XotzlT9hX25B3Lr/KEqE23cYEsCUgJsG3Eqaj4wVPjxibmNcARgQvMN10ZxkTcKqG5vlsdVo1RgU3LRxhGiuArkqstm+jOFsCTwSv4UA
spring.xmpulse-slave-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmpulse-slave-datasource.connectionProperties=
spring.xmpulse-slave-datasource.sql-script-encoding=UTF-8
spring.xmpulse-slave-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmpulse-slave-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmpulse-slave-datasource.initial-size=30
spring.xmpulse-slave-datasource.max-active=200
spring.xmpulse-slave-datasource.min-idle=5
spring.xmpulse-slave-datasource.max-wait=60000
spring.xmpulse-slave-datasource.remove-abandoned=true
spring.xmpulse-slave-datasource.remove-abandoned-timeout=180
spring.xmpulse-slave-datasource.time-between-eviction-runs-millis=60000
spring.xmpulse-slave-datasource.min-evictable-idle-time-millis=300000
spring.xmpulse-slave-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmpulse-slave-datasource.test-while-idle=true
spring.xmpulse-slave-datasource.test-on-borrow=false
spring.xmpulse-slave-datasource.test-on-return=false
spring.xmpulse-slave-datasource.pool-prepared-statements=true
spring.xmpulse-slave-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmpulse-slave-datasource.filters=stat,wall,slf4j


spring.pulse.redis.host=cache01.b2c.srv
spring.pulse.redis.port=5100
spring.pulse.redis.password=GDBn1n7oqGA8xwk+8PawKL0ECx0dpWbaznf2VIFFAyFTgNHshVuRzZFWz3ptmehGq34YEgv9dmcCvkg5i0riWR5OsgBmARgQigzZ42VFQUCBqDHV2SoVTxgUbqda6nZWrC+9cSoLeLrxk069iwsA
spring.pulse.redis.password@kc-sid=mi_newretail_risk.g
spring.pulse.redis.timeout=1000
spring.pulse.redis.database=0
spring.pulse.redis.lettuce.pool.max-idle=200
spring.pulse.redis.lettuce.pool.min-idle=1
spring.pulse.redis.lettuce.pool.shutdown-timeout=5000
spring.pulse.redis.lettuce-pool.max-total=200
spring.pulse.redis.lettuce-pool.timeout=3000
spring.pulse.redis.lettuce.pool.max-wait-millis=3000


spring.newcoupon.redis.host=ares.b2c.cache.b2c.srv
spring.newcoupon.redis.port=5100
spring.newcoupon.redis.password=GDAYWmRcsm81H5h/FATZ6QFOF9layURejFpLWp9WthMXkK30X0j/6OqRnV5nBWIQTb8YEspC+0uPdENLrd+PIdzhXvP9ARgQ4sOlANygQ4a5Tky8whVm8hgUBc6zwfQb9LLk/CAHAarE8tV68OcA
spring.newcoupon.redis.password@kc-sid=mi_newretail_risk.g
spring.newcoupon.redis.timeout=1000
spring.newcoupon.redis.database=0
spring.newcoupon.redis.lettuce.pool.max-idle=200
spring.newcoupon.redis.lettuce.pool.min-idle=1
spring.newcoupon.redis.lettuce.pool.shutdown-timeout=5000
spring.newcoupon.redis.lettuce-pool.max-total=300
spring.newcoupon.redis.lettuce-pool.timeout=2000
spring.newcoupon.redis.lettuce.pool.max-wait-millis=2000


spring.karos.misc.redis.host=ares.b2c.cache.b2c.srv
spring.karos.misc.redis.port=5100
spring.karos.misc.redis.password=GDD1VkjQgoy2FWAWEYewNg4p8T56S6O7dhOJ553pwCoLuAD30ytv/tF5xhgLcB9DBh0YEg3hUpu0akIcnpRGImrnhj6IARgQYb6NCUd/RCCYJzTrDQAUKBgUNiYcuAOMhvk7jGXi2pxQE9YJdFYA
spring.karos.misc.redis.password@kc-sid=mi_newretail_risk.g
spring.karos.misc.redis.timeout=1000
spring.karos.misc.redis.database=0
spring.karos.misc.redis.lettuce.pool.max-idle=200
spring.karos.misc.redis.lettuce.pool.min-idle=1
spring.karos.misc.redis.lettuce.pool.shutdown-timeout=5000
spring.karos.misc.redis.lettuce-pool.max-total=100
spring.karos.misc.redis.lettuce-pool.timeout=3000
spring.karos.misc.redis.lettuce.pool.max-wait-millis=3000


spring.misc.redis.host=cache01.b2c.srv
spring.misc.redis.port=5201
spring.misc.redis.password=GCCAliLXyKWuHdXNmca4SufHuMwIa8OB/opD/X4WX10qRxgSo2guutYsRXiEKE6lvwuVcVABGBCkli/hPMdMIKUQk4q4x9cVGBS5GWvMQ5EsNvYJsqrrKgkC2gioiwA=
spring.misc.redis.password@kc-sid=mi_newretail_risk.g
spring.misc.redis.timeout=3000
spring.misc.redis.database=0
spring.misc.redis.lettuce.pool.max-idle=8
spring.misc.redis.lettuce.pool.min-idle=1
spring.misc.redis.lettuce.pool.shutdown-timeout=10000
spring.misc.redis.lettuce-pool.max-total=200
spring.misc.redis.lettuce-pool.timeout=3000
spring.misc.redis.lettuce.pool.max-wait-millis=3000

keycenter.sid=micom_ecom.g

rpc.etcd.host=http://soa01.etcd.b2c.srv:4001

rocketmq.name-server=shopapi-cnbj1-rocketmq.namesrv.api.xiaomi.net:9876
rocketmq.consumer.access-key=AKB7FI7MHQH2BBQQDW
rocketmq.consumer.secret-key=GDDkYfMQVOrVCFeONCqSHxSk87Ghg7nfzOfW222cXajLAO5jK9/rEZkA2j+XaONQxHEYEtFxRF75nkHNisUYInrJSzFrARgQu+2GV9mKST66kjbGyTPs2xgUVTkpqO3oTqgeTeWDIcJGuHoJTVsA
rocketmq.consumer.secret-key@kc-sid=mi_newretail_risk.g
rocketmq.push-consumer.access-key=AKB7FI7MHQH2BBQQDW
rocketmq.push-consumer.secret-key=GDDkYfMQVOrVCFeONCqSHxSk87Ghg7nfzOfW222cXajLAO5jK9/rEZkA2j+XaONQxHEYEtFxRF75nkHNisUYInrJSzFrARgQu+2GV9mKST66kjbGyTPs2xgUVTkpqO3oTqgeTeWDIcJGuHoJTVsA
rocketmq.push-consumer.secret-key@kc-sid=mi_newretail_risk.g
rocketmq.consumer.order.online.topic=nr_message_bus
rocketmq.consumer.order.offline.topic=nr_message_bus_offline
rocketmq.orderCouponCount-consumer.group.online=nr_coupon_orderCount
rocketmq.orderCouponCount-consumer.group.offline=nr_coupon_orderCount_offline
rocketmq.order.coupon.tag=OrderPaid


rocketmq.couponUsePush.name-server=cnbj1-rocketmq.namesrv.api.xiaomi.net:9876
rocketmq.carCouponUsePush.name-server=shopapi-cnbj1-rocketmq.namesrv.api.xiaomi.net:9876
rocketmq.couponAssignNotify.name-server=shopapi-cnbj1-rocketmq.namesrv.api.xiaomi.net:9876

rocketmq.producer.secret-key=GDBZkAby+Oc9CnXzNDwgbMI9A2RefWhMaGjGnERLhbiusEB50q1sEkxUCeBD0kI6LBgYEo6FA/AR9U/HibGzFp2NOw/9ARgQCnEm1V1hSaOdPtPaOsR8TBgUwF2DqesPmI3C9a2QXwCtZmIJ+eAA
rocketmq.producer.secret-key@kc-sid=mi_newretail_risk.g

coupon.config.cache.biz.platform=3,4,5