#server
app.name=coupon
server.type=staging
server.port=8080
server.debug=true
server.connection-timeout=1000

dubbo.group=staging
dubbo.group.rpc=staging
dubbo.protocol.id=dubbo
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.registry.address=nacos://nacos.test.b2c.srv:80
nacos.config.addrs=nacos.test.b2c.srv:80

youpin.log.group=staging
log.path=/home/<USER>/log

talos.topic=cnzone_newretail_common_test
talos.sendpoint=http://staging-cnbj2-talos.api.xiaomi.net
talos.access.key=AKTAFN57IDDEY7VBBP
talos.access.secret=GDA8I/MpcbAosZv0H2jtdG9GEVXnXrAYS0ovVeaAYs61dMdz4CIUTiiJkkLi0AmzDwcYEiXVYiQ7vEmetLSQvOPjnP58/xgQzCjOhVoFQxy/u1c86wpYExgUvzpxIsNmLvtYJmLd7q8liEEtt7QA
talos.access.secret@kc-sid=mi_newretail_risk.g

spring.xmpulsecoupon-datasource.name=xm_pulse_natl
spring.xmpulsecoupon-datasource.username=pulse_natl_w
spring.xmpulsecoupon-datasource.url=*********************************************************************************************
spring.xmpulsecoupon-datasource.password=GCCtd6+Qony2GnCf/nA+/9Z9UebivkIaegnLFDqBa5D/LRgSIR4Bk+cwTGKtgq60atyNTJH/GBAkVwtlI4tBer+ErmMm5DFXGBSCtD1k1yO408OV0q8Br9hRx2zUnwA=
spring.xmpulsecoupon-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmpulsecoupon-datasource.connectionProperties=
spring.xmpulsecoupon-datasource.sql-script-encoding=UTF-8
spring.xmpulsecoupon-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmpulsecoupon-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmpulsecoupon-datasource.initial-size=5
spring.xmpulsecoupon-datasource.max-active=100
spring.xmpulsecoupon-datasource.min-idle=3
spring.xmpulsecoupon-datasource.max-wait=60000
spring.xmpulsecoupon-datasource.remove-abandoned=true
spring.xmpulsecoupon-datasource.remove-abandoned-timeout=180
spring.xmpulsecoupon-datasource.time-between-eviction-runs-millis=60000
spring.xmpulsecoupon-datasource.min-evictable-idle-time-millis=300000
spring.xmpulsecoupon-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmpulsecoupon-datasource.test-while-idle=true
spring.xmpulsecoupon-datasource.test-on-borrow=false
spring.xmpulsecoupon-datasource.test-on-return=false
spring.xmpulsecoupon-datasource.pool-prepared-statements=true
spring.xmpulsecoupon-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmpulsecoupon-datasource.filters=stat,wall,slf4j


spring.xmpulse-datasource.name=xm_pulse
spring.xmpulse-datasource.username=pulse_user
spring.xmpulse-datasource.url=**************************************************************************************
spring.xmpulse-datasource.password=GCBIx7HK5oD+qBHS4X60ca/kG1NXNiPB+/LqW3kGOw4ZvxgSIR4Bk+cwTGKtgq60atyNTJH/GBD0btYaRJVDCZUfd3oDKsnxGBTenxYzGhlUFWt5deVSIf4Zc2M4RgA=
spring.xmpulse-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmpulse-datasource.connectionProperties=
spring.xmpulse-datasource.sql-script-encoding=UTF-8
spring.xmpulse-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmpulse-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmpulse-datasource.initial-size=5
spring.xmpulse-datasource.max-active=100
spring.xmpulse-datasource.min-idle=3
spring.xmpulse-datasource.max-wait=60000
spring.xmpulse-datasource.remove-abandoned=true
spring.xmpulse-datasource.remove-abandoned-timeout=180
spring.xmpulse-datasource.time-between-eviction-runs-millis=60000
spring.xmpulse-datasource.min-evictable-idle-time-millis=300000
spring.xmpulse-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmpulse-datasource.test-while-idle=true
spring.xmpulse-datasource.test-on-borrow=false
spring.xmpulse-datasource.test-on-return=false
spring.xmpulse-datasource.pool-prepared-statements=true
spring.xmpulse-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmpulse-datasource.filters=stat,wall,slf4j


spring.xmpulse-slave-datasource.name=xm_pulse
spring.xmpulse-slave-datasource.username=pulse_user
spring.xmpulse-slave-datasource.url=**************************************************************************************
spring.xmpulse-slave-datasource.password=GCD6A0TSuq8thEkpUkYlw1F1HMgzIykl43ysKIOVQlcYvxgS25IjxUsIRmGthA/cC0YWr3b/GBDl9lUSDDdBaYnJeaZj6NxAGBSsy9Ao7Q0D1yrCjOumRp879Qk+YQA=
spring.xmpulse-slave-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmpulse-slave-datasource.connectionProperties=
spring.xmpulse-slave-datasource.sql-script-encoding=UTF-8
spring.xmpulse-slave-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmpulse-slave-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmpulse-slave-datasource.initial-size=5
spring.xmpulse-slave-datasource.max-active=100
spring.xmpulse-slave-datasource.min-idle=3
spring.xmpulse-slave-datasource.max-wait=60000
spring.xmpulse-slave-datasource.remove-abandoned=true
spring.xmpulse-slave-datasource.remove-abandoned-timeout=180
spring.xmpulse-slave-datasource.time-between-eviction-runs-millis=60000
spring.xmpulse-slave-datasource.min-evictable-idle-time-millis=300000
spring.xmpulse-slave-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmpulse-slave-datasource.test-while-idle=true
spring.xmpulse-slave-datasource.test-on-borrow=false
spring.xmpulse-slave-datasource.test-on-return=false
spring.xmpulse-slave-datasource.pool-prepared-statements=true
spring.xmpulse-slave-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmpulse-slave-datasource.filters=stat,wall,slf4j


logging.level.com.xiaomi.nr.coupon.dao.mysqldao.couponconfig=DEBUG


spring.pulse.redis.host=wcc.cache01.test.b2c.srv
spring.pulse.redis.port=22122
spring.pulse.redis.password=GDBusWOGdkqO5bSGxPxE++tM0GCP50tQ0wghjcWYmhORGYpw9MU6ZcVZLKQc+PVIDWoYEngKLDIVWUXZnhCszYLBWH8h/xgQ7xpi5ArcQa+9M26AjRTS6BgU8ETCO+VZwZzUaNzLAjZybtDmM5gA
spring.pulse.redis.password@kc-sid=mi_newretail_risk.g
spring.pulse.redis.timeout=1000
spring.pulse.redis.database=0
spring.pulse.redis.lettuce.pool.max-idle=32
spring.pulse.redis.lettuce.pool.min-idle=1
spring.pulse.redis.lettuce.pool.shutdown-timeout=5000
spring.pulse.redis.lettuce-pool.max-total=200
spring.pulse.redis.lettuce-pool.timeout=3000
spring.pulse.redis.lettuce.pool.max-wait-millis=3000


spring.newcoupon.redis.host=ares.test.common.cache.srv
spring.newcoupon.redis.port=22127
spring.newcoupon.redis.password=GDCfI3t0QtX2L+3i44Vo3FlpHpn5X4iN3p3tN0h+Jz2owWbK1EYoOnNqkquEQ7hW2jwYEjAi37a9vEy9vUvBRJ4AZGEq/xgQpvpa6h3BQH62O0VRYnrW5xgUX+/8/MotgHLNy/P0J81YenoP+aIA
spring.newcoupon.redis.password@kc-sid=mi_newretail_risk.g
spring.newcoupon.redis.timeout=1000
spring.newcoupon.redis.database=0
spring.newcoupon.redis.lettuce.pool.max-idle=32
spring.newcoupon.redis.lettuce.pool.min-idle=1
spring.newcoupon.redis.lettuce.pool.shutdown-timeout=5000
spring.newcoupon.redis.lettuce-pool.max-total=200
spring.newcoupon.redis.lettuce-pool.timeout=3000
spring.newcoupon.redis.lettuce.pool.max-wait-millis=3000


spring.karos.misc.redis.host=wcc.cache01.test.b2c.srv
spring.karos.misc.redis.port=22122
spring.karos.misc.redis.password=GBBkA/wbKFvisNzuGAlAdQfQGBIjtHUDXgRD0K5PINSnrTF0V/8YENLRW5RCJEJrqhjxvXnaj+YYFDzlISDlT73osgjIRn6o/rSkjnpmAA==
spring.karos.misc.redis.password@kc-sid=mi_newretail_risk.g
spring.karos.misc.redis.timeout=1000
spring.karos.misc.redis.database=0
spring.karos.misc.redis.lettuce.pool.max-idle=32
spring.karos.misc.redis.lettuce.pool.min-idle=1
spring.karos.misc.redis.lettuce.pool.shutdown-timeout=5000
spring.karos.misc.redis.lettuce-pool.max-total=200
spring.karos.misc.redis.lettuce-pool.timeout=3000
spring.karos.misc.redis.lettuce.pool.max-wait-millis=3000


spring.misc.redis.host=wcc.cache01.test.b2c.srv
spring.misc.redis.port=22122
spring.misc.redis.password=GBAV4BrcSImxcamL6NmOSb8aGBIbLveawStDs7DljXz3rFubDv8YEAaY6601SE4wiJGHXVL2dD0YFNbqbrvEt8907Lu4/RWnwzFWS2PRAA==
spring.misc.redis.password@kc-sid=mi_newretail_risk.g
spring.misc.redis.timeout=3000
spring.misc.redis.database=0
spring.misc.redis.lettuce.pool.max-idle=8
spring.misc.redis.lettuce.pool.min-idle=1
spring.misc.redis.lettuce.pool.shutdown-timeout=10000
spring.misc.redis.lettuce-pool.max-total=200
spring.misc.redis.lettuce-pool.timeout=3000
spring.misc.redis.lettuce.pool.max-wait-millis=3000


keycenter.sid=keycenter-test

rpc.etcd.host=http://etcd.test.mi.com

rocketmq.name-server=staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876
rocketmq.consumer.access-key=AKB7FI7MHQH2BBQQDW
rocketmq.consumer.secret-key=GDB4g6+6gBe1C83rQRzWaUYqGhJbJuet2Y+F1W32vOgtFF5Bh5I8hzFLNFcUx5Ch49AYEhnAZIZ3202AiqgLa/+iFr+X/xgQ8kNkLrQNRIGQTCP5lvQJSRgUjws80fH3UqpSu4sfi7Ic+/LQrFsA
rocketmq.consumer.secret-key@kc-sid=mi_newretail_risk.g
rocketmq.push-consumer.access-key=AKB7FI7MHQH2BBQQDW
rocketmq.push-consumer.secret-key=GDB4g6+6gBe1C83rQRzWaUYqGhJbJuet2Y+F1W32vOgtFF5Bh5I8hzFLNFcUx5Ch49AYEhnAZIZ3202AiqgLa/+iFr+X/xgQ8kNkLrQNRIGQTCP5lvQJSRgUjws80fH3UqpSu4sfi7Ic+/LQrFsA
rocketmq.push-consumer.secret-key@kc-sid=mi_newretail_risk.g
rocketmq.consumer.order.online.topic=nr_message_bus
rocketmq.consumer.order.offline.topic=nr_message_bus_offline
rocketmq.orderCouponCount-consumer.group.online=nr_coupon_orderCount
rocketmq.orderCouponCount-consumer.group.offline=nr_coupon_orderCount_offline
rocketmq.order.coupon.tag=OrderPaid


rocketmq.couponUsePush.name-server=staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876
rocketmq.carCouponUsePush.name-server=staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876
rocketmq.couponAssignNotify.name-server=staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876

rocketmq.producer.secret-key=GDBAJMXVJZ2FysWQR9WlxsmmXZhsF1RHVgN+3o6jy7+s96bdexVz5BY/OYT7q4EoGWsYEm2RdXe9VELVnGgd9tbQvCmP/xgQwxVBlSQQSnqXI5IxLnapkBgUuryhq/ELaIPrG0W+yBadiMm2BEoA
rocketmq.producer.secret-key@kc-sid=mi_newretail_risk.g


sentinel.dashboard.host=http://sentinel.test.be.mi.com

coupon.config.cache.biz.platform=0,3,4,5


