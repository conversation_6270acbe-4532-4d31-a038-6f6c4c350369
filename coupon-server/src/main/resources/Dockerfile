FROM miserver
MAINTAINER <EMAIL>
RUN mkdir -p /home/<USER>/coupon/
RUN mkdir -p /home/<USER>/log/coupon/
COPY coupon-server-1.0-SNAPSHOT.jar /home/<USER>/coupon/
ENTRYPOINT ["java","-jar","-Xms512M","-Xmx512M","-XX:+UseG1GC","-XX:+PrintReferenceGC","-XX:+PrintGCDetails","-XX:+PrintGCDateStamps","-XX:+PrintHeapAtGC","-Xloggc:/home/<USER>/log/coupon/gc.log","/home/<USER>/coupon/coupon-server-1.0-SNAPSHOT.jar"]