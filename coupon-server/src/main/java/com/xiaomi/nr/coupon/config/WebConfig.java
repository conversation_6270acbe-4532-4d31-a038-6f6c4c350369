package com.xiaomi.nr.coupon.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.GsonHttpMessageConverter;

/**
 * <AUTHOR>
 * @date 2021/4/8
 */
@Configuration
public class WebConfig {

    @Bean
    public HttpMessageConverters httpMessageConverters() {
        GsonHttpMessageConverter gsonHttpMessageConverter = new GsonHttpMessageConverter();
        Gson gson = new GsonBuilder()
                .serializeNulls()
                .create();
        gsonHttpMessageConverter.setGson(gson);
        HttpMessageConverter<?> converter = gsonHttpMessageConverter;
        return new HttpMessageConverters(converter);
    }
}
