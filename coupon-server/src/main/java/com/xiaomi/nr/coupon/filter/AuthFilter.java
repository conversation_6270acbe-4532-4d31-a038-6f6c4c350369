package com.xiaomi.nr.coupon.filter;

import com.xiaomi.nr.coupon.domain.auth.AuthValidator;
import lombok.extern.slf4j.Slf4j;
import net.sf.cglib.beans.BeanMap;
import org.apache.dubbo.common.Constants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Activate(group = Constants.PROVIDER, order = 3)
public class AuthFilter implements Filter {

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        Object[] arguments = invocation.getArguments();
        List<String> keys = new ArrayList<>();
        Map<String, String> params = new HashMap<>();
        BeanMap attachments = BeanMap.create(arguments[0]);
        for (Object key : attachments.keySet()) {
            keys.add(String.valueOf(key));
            params.put(String.valueOf(key), String.valueOf(attachments.get(key)));
        }

        try {
            AuthValidator.auth(invoker.getInterface().getName(), invocation.getMethodName(), params, keys);
        } catch (Exception e) {
            return new RpcResult(com.xiaomi.youpin.infra.rpc.Result.fromException(e));
        }
        return invoker.invoke(invocation);
    }
}