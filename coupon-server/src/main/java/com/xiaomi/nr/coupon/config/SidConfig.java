package com.xiaomi.nr.coupon.config;

import com.xiaomi.nr.coupon.infrastructure.rpc.sid.sdk.SidWrapper;
import lombok.extern.slf4j.Slf4j;
import org.mi.thrift.rpc.XRpc;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * sid服务配置
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Configuration
public class SidConfig {

    /**
     * host
     */
    @Value("${rpc.etcd.host}")
    private String host;

    /**
     * sid
     *
     * @return sidClient
     */
    @Bean(name = "sidClient")
    public SidWrapper sidClient() throws Exception {
        XRpc rpc = XRpc.getInstance();
        rpc.init(host, "mishop", "nr_coupon");
        return new SidWrapper("sid", (long) 1000);
    }

}
