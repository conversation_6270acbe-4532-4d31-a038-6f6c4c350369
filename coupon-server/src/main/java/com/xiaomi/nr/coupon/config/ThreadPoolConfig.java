package com.xiaomi.nr.coupon.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.RejectedExecutionException;

@Configuration
@Slf4j
@EnableAsync
public class ThreadPoolConfig {

    /**
     * 用于异步执行的线程池
     * */
    @Bean("asyncExecutor")
    public ThreadPoolTaskExecutor asyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(50);
        executor.setMaxPoolSize(200);
        executor.setKeepAliveSeconds(30);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("asyncExecutor-");
        executor.setRejectedExecutionHandler((r, executor1) -> {
            log.error("asyncExecutor reject thread. active thread:{}, queue size:{}", executor1.getActiveCount(), executor1.getQueue().size());
            throw new RejectedExecutionException("Task " + r.toString() + " rejected from asyncExecutor");
        });
        return executor;
    }



}
