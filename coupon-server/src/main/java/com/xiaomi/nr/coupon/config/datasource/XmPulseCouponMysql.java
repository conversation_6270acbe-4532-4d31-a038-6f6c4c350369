package com.xiaomi.nr.coupon.config.datasource;


import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.util.Objects;

/**
 * gaea会自动切换主从
 *
 * <AUTHOR>
 */
@Configuration
@MapperScan(basePackages = {
        "com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig"
}, sqlSessionFactoryRef = "xmPulseCouponSqlSessionFactory")
public class XmPulseCouponMysql {
    @Value("${spring.xmpulsecoupon-datasource.url:#{null}}")
    private String dataSourceUrl;

    @Primary
    @Bean(name = "xmPulseCouponDatasource")
    @ConfigurationProperties("spring.xmpulsecoupon-datasource")
    public DataSource mysqlDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Primary
    @Bean(name = "xmPulseCouponSqlSessionFactory")
    public SqlSessionFactory mysqlSqlSessionFactory(@Qualifier("xmPulseCouponDatasource") DataSource dataSource)
            throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        sessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"));
        Objects.requireNonNull(sessionFactory.getObject()).getConfiguration().setMapUnderscoreToCamelCase(true);
        //sessionFactory.getObject().getConfiguration().addInterceptor(new CatMybatisInterceptor(dataSourceUrl));
        return sessionFactory.getObject();
    }

    @Primary
    @Bean(name = "xmPulseCouponTransactionManager")
    public PlatformTransactionManager prodTransactionManager(@Qualifier("xmPulseCouponDatasource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
