package com.xiaomi.nr.coupon.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xiaomi.nr.coupon.config.support.LettucePoolConfig;
import com.xiaomi.nr.coupon.config.support.RedisNodeConfig;
import io.lettuce.core.ClientOptions;
import io.lettuce.core.SocketOptions;
import io.lettuce.core.TimeoutOptions;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import io.lettuce.core.protocol.ProtocolVersion;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;

/**
 * <AUTHOR>
 */
@Configuration
public class RedisConfig {

    /**
     * 老pulse系统缓存配置
     */
    @Bean("pulseRedisConfig")
    @ConfigurationProperties("spring.pulse.redis")
    public RedisNodeConfig pulseRedisConfig() {
        return new RedisNodeConfig();
    }

    /**
     * 老pulse系统缓存模板
     */
    @Bean(name = "stringPulseTypeRedisTemplate")
    public StringRedisTemplate stringPulseRedisTemplate(@Qualifier("pulseRedisConfig") RedisNodeConfig config) {
        return createStringRedisTemplate(config);
    }

    /**
     * 新的券配置缓存redis信息
     */
    @Bean("newCouponRedisConfig")
    @ConfigurationProperties("spring.newcoupon.redis")
    public RedisNodeConfig newCouponRedisConfig() {
        return new RedisNodeConfig();
    }

    /**
     * 新的券配置缓存模板  - String
     */
    @Bean(name = "stringNewCouponRedisTemplate")
    public StringRedisTemplate stringNewCouponRedisTemplate(@Qualifier("newCouponConnectionFactory") LettuceConnectionFactory connectionFactory) {
        return createStringRedisTemplate(connectionFactory, new StringRedisSerializer());
    }

    /**
     * 新的券配置缓存模板  - number
     */
    @Bean(name = "numberNewCouponRedisTemplate")
    public StringRedisTemplate numberNewCouponRedisTemplate(@Qualifier("newCouponConnectionFactory") LettuceConnectionFactory connectionFactory) {
        Jackson2JsonRedisSerializer serializer = new Jackson2JsonRedisSerializer<>(Number.class);
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(JsonParser.Feature.ALLOW_NUMERIC_LEADING_ZEROS, true);
        serializer.setObjectMapper(mapper);
        return createStringRedisTemplate(connectionFactory, serializer);
    }


    @Bean(name = "newCouponConnectionFactory")
    public LettuceConnectionFactory newCouponRedisConnectionFactory(@Qualifier("newCouponRedisConfig") RedisNodeConfig config) {
        return createLettuceConnectionFactory(config);
    }

    private StringRedisTemplate createStringRedisTemplate(
            LettuceConnectionFactory connectionFactory, RedisSerializer serializer) {
        StringRedisTemplate redis = new StringRedisTemplate();
        redis.setConnectionFactory(connectionFactory);
        redis.setKeySerializer(new StringRedisSerializer());
        redis.setValueSerializer(serializer);
        redis.setHashKeySerializer(new StringRedisSerializer());
        redis.setHashValueSerializer(serializer);
        redis.afterPropertiesSet();
        return redis;
    }



    @Bean("karoMiscRedisConfig")
    @ConfigurationProperties("spring.karos.misc.redis")
    public RedisNodeConfig karosRedisRedisConfig() {
        return new RedisNodeConfig();
    }

    /**
     * appAuth缓存信息
     */
    @Bean(name = "stringKarosRedisTemplate")
    public StringRedisTemplate stringKarosRedisTemplate(@Qualifier("karoMiscRedisConfig") RedisNodeConfig config) {
        return createStringRedisTemplate(config);
    }

    /**
     * 领券活动缓存配置
     */
    @Bean("miscRedisConfig")
    @ConfigurationProperties("spring.misc.redis")
    public RedisNodeConfig miscRedisConfig() {
        return new RedisNodeConfig();
    }

    /**
     * 领券活动配置缓存模板
     */
    @Bean(name = "stringMiscRedisTemplate")
    public StringRedisTemplate stringMiscRedisTemplate(@Qualifier("miscRedisConfig") RedisNodeConfig config) {
        return getStringRedisTemplate(config);
    }

    /**
     * 基础配置缓存模板 -- 字符串序列化
     * @param config
     * @return
     */
    private StringRedisTemplate getStringRedisTemplate(RedisNodeConfig config) {
        StringRedisTemplate redis = new StringRedisTemplate();
        redis.setConnectionFactory(createLettuceConnectionFactory(config));
        redis.setKeySerializer(new StringRedisSerializer());
        redis.setValueSerializer(new StringRedisSerializer());
        redis.setHashKeySerializer(new StringRedisSerializer());
        redis.setHashValueSerializer(new StringRedisSerializer());
        redis.afterPropertiesSet();
        return redis;
    }

    private StringRedisTemplate createStringRedisTemplate(RedisNodeConfig config) {
        StringRedisTemplate redis = new StringRedisTemplate();
        redis.setConnectionFactory(createLettuceConnectionFactory(config));
        redis.setKeySerializer(new StringRedisSerializer());
        redis.setValueSerializer(new StringRedisSerializer());
        redis.setHashKeySerializer(new StringRedisSerializer());
        redis.setHashValueSerializer(new StringRedisSerializer());
        redis.afterPropertiesSet();
        return redis;
    }



    private LettuceConnectionFactory createLettuceConnectionFactory(RedisNodeConfig config) {
        LettucePoolConfig poolConfig = config.getLettucePool();
        if (poolConfig == null) {
            throw new IllegalArgumentException("redis LettucePoolConfig is null");
        }

        GenericObjectPoolConfig<?> objectPoolConfig = getPoolConfig(poolConfig);
        RedisStandaloneConfiguration redisConfig = new RedisStandaloneConfiguration(config.getHost(), config.getPort());
        redisConfig.setPassword(RedisPassword.of(config.getPassword()));
        redisConfig.setDatabase(config.getDatabase());

        LettucePoolingClientConfiguration clientConfig = LettucePoolingClientConfiguration.builder()
                .commandTimeout(Duration.ofMillis(poolConfig.getTimeout()))
                .clientOptions(getClientOptions(poolConfig))
                .poolConfig(objectPoolConfig)
                .build();

        final LettuceConnectionFactory lettuceConnectionFactory = new LettuceConnectionFactory(redisConfig, clientConfig);
        lettuceConnectionFactory.afterPropertiesSet();
        return lettuceConnectionFactory;
    }

    private GenericObjectPoolConfig<?> getPoolConfig(LettucePoolConfig poolConfig) {
        GenericObjectPoolConfig<?> objectPoolConfig = new GenericObjectPoolConfig<>();
        objectPoolConfig.setMaxTotal(poolConfig.getMaxTotal());
        objectPoolConfig.setMaxIdle(poolConfig.getMaxIdle());
        objectPoolConfig.setMinIdle(poolConfig.getMinIdle());
        objectPoolConfig.setMaxWaitMillis(poolConfig.getMaxWaitMillis());
        objectPoolConfig.setBlockWhenExhausted(true);
        // 30s 检查一波空闲连接
        objectPoolConfig.setTimeBetweenEvictionRunsMillis(30 * 1000);
        // 每次最多检查200个连接
        objectPoolConfig.setNumTestsPerEvictionRun(200);
        // 空闲连接60s后踢出
        objectPoolConfig.setMinEvictableIdleTimeMillis(60 * 1000);
        objectPoolConfig.setTestWhileIdle(true);
        objectPoolConfig.setTestOnCreate(false);
        objectPoolConfig.setTestOnReturn(false);
        objectPoolConfig.setNumTestsPerEvictionRun(10);
        return objectPoolConfig;
    }

    private ClientOptions getClientOptions(LettucePoolConfig poolConfig) {
        ClusterTopologyRefreshOptions topologyRefreshOptions = ClusterTopologyRefreshOptions.builder()
                .enablePeriodicRefresh(Duration.ofSeconds(60L))
                .enableAdaptiveRefreshTrigger(ClusterTopologyRefreshOptions.RefreshTrigger.MOVED_REDIRECT,
                        ClusterTopologyRefreshOptions.RefreshTrigger.ASK_REDIRECT,
                        ClusterTopologyRefreshOptions.RefreshTrigger.PERSISTENT_RECONNECTS,
                        ClusterTopologyRefreshOptions.RefreshTrigger.UNKNOWN_NODE,
                        ClusterTopologyRefreshOptions.RefreshTrigger.UNCOVERED_SLOT)
                .adaptiveRefreshTriggersTimeout(Duration.ofSeconds(30L))
                .refreshTriggersReconnectAttempts(5)
                .build();

        SocketOptions socketOptions = SocketOptions.builder()
                .connectTimeout(Duration.ofMillis(poolConfig.getTimeout()))
                .keepAlive(true)
                .tcpNoDelay(true)
                .build();

        return ClusterClientOptions.builder()
                .pingBeforeActivateConnection(true)
                .cancelCommandsOnReconnectFailure(true)
                .protocolVersion(ProtocolVersion.RESP2)
                .socketOptions(socketOptions)
                .timeoutOptions(TimeoutOptions.enabled(Duration.ofMillis(poolConfig.getTimeout())))
                .topologyRefreshOptions(topologyRefreshOptions)
                .maxRedirects(5)
                .build();
    }




}
