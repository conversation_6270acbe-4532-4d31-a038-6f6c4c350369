package com.xiaomi.nr.coupon.config;

import com.xiaomi.nr.infra.aop.clustertask.ClusterTaskWrapScheduler;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ConcurrentTaskScheduler;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;

/**
 * <AUTHOR>
 */
@Configuration
@EnableScheduling
public class SchedulerConfig implements SchedulingConfigurer {
    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        ScheduledExecutorService localExecutor = new ScheduledThreadPoolExecutor(1);
        TaskScheduler taskScheduler = new ConcurrentTaskScheduler(localExecutor);
        taskRegistrar.setTaskScheduler(new ClusterTaskWrapScheduler(taskScheduler));
    }
}