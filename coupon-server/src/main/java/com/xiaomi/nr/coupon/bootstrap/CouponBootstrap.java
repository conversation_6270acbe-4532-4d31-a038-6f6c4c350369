package com.xiaomi.nr.coupon.bootstrap;

import com.xiaomi.mone.dubbo.docs.EnableDubboApiDocs;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 */
@EnableAutoConfiguration
@ComponentScan(basePackages = {"com.xiaomi.nr.coupon", "com.xiaomi.youpin", "com.xiaomi.dubbo.validation"})
@DubboComponentScan(basePackages = "com.xiaomi.nr.coupon")
@EnableScheduling
@EnableDubboApiDocs
public class CouponBootstrap {
    private static final Logger logger = LoggerFactory.getLogger(CouponBootstrap.class);

    public static void main(String... args) {
        try {
            SpringApplication.run(CouponBootstrap.class, args);
        } catch (Throwable throwable) {
            logger.error(throwable.getMessage(), throwable);
            System.exit(-1);
        }
    }
}