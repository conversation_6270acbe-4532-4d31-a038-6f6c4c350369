<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>coupon</artifactId>
        <groupId>com.xiaomi.nr</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>coupon-server</artifactId>

    <properties>
        <com.xiaomi.youpin.cat.plugins.version>3.0.3-youpin-SNAPSHOT</com.xiaomi.youpin.cat.plugins.version>
        <jmockit.version>1.36</jmockit.version>
    </properties>

    <dependencies>
        <dependency>
            <artifactId>coupon-service</artifactId>
            <groupId>com.xiaomi.nr</groupId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <artifactId>coupon-admin-api</artifactId>
            <groupId>com.xiaomi.nr</groupId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>

        <!--阿里巴巴nacos-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo-registry-nacos</artifactId>
            <version>1.2.1-mone-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.spring</groupId>
            <artifactId>spring-context-support</artifactId>
            <version>1.0.2</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-spring-context</artifactId>
            <version>0.3.6-mone-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-context</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-context-support</artifactId>
                    <groupId>com.alibaba.spring</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
            <version>1.2.1-mone-v6-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.5</version>
        </dependency>

        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
            <version>3.0.8</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.youpin</groupId>
            <artifactId>utest-utils</artifactId>
            <version>1.1-SNAPSHOT</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.github.kstyrc</groupId>
                    <artifactId>embedded-redis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- keycenter -->
        <dependency>
            <groupId>com.xiaomi.mit</groupId>
            <artifactId>mit-starter</artifactId>
            <version>1.1.8</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi</groupId>
            <artifactId>keycenter-agent-client</artifactId>
            <version>3.5.3</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.mit</groupId>
            <artifactId>unittest-tool</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.junit</groupId>
                <artifactId>junit-bom</artifactId>
                <version>5.11.4</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.xiaomi.nr.coupon.bootstrap.CouponBootstrap</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.12</version>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <propertyName>jacocoArgLine</propertyName>
                        </configuration>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <excludes>
                        <exclude>**/com/xiaomi/nr/coupon/api/dto/**/*.class</exclude>
                        <exclude>**/com/xiaomi/nr/coupon/domain/**/model/*.class</exclude>
                        <exclude>**/com/xiaomi/nr/coupon/infrastructure/**/po/*.class</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.5.2</version>
                <configuration>
                    <argLine>-javaagent:"${settings.localRepository}"/org/jmockit/jmockit/${jmockit.version}/jmockit-${jmockit.version}.jar @{jacocoArgLine}</argLine>
                    <includes>
                        <include>**/xiaomi/nr/coupon/v2/**/*.java</include>
                        <include>**/xiaomi/core/security/ConfigCryptoBySuffix.java</include>
                    </includes>
                </configuration>
            </plugin>
        </plugins>
    </build>


    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <profile_name>dev</profile_name>
                <zookeeper_host>staging</zookeeper_host>
                <app_nacos>nacos.test.b2c.srv:80</app_nacos>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>

        <profile>
            <id>ut</id>
            <properties>
                <profile_name>ut</profile_name>
                <zookeeper_host>staging</zookeeper_host>
                <app_nacos>nacos.test.b2c.srv:80</app_nacos>
            </properties>
        </profile>

        <profile>
            <id>staging</id>
            <properties>
                <profile_name>staging</profile_name>
                <zookeeper_host>staging</zookeeper_host>
                <app_nacos>nacos.test.b2c.srv:80</app_nacos>
            </properties>
        </profile>

        <profile>
            <id>preview</id>
            <properties>
                <profile_name>preview</profile_name>
                <zookeeper_host>c4</zookeeper_host>
                <app_nacos>nacos.systech.b2c.srv:80</app_nacos>
            </properties>
        </profile>

        <profile>
            <id>online</id>
            <properties>
                <profile_name>online</profile_name>
                <zookeeper_host>c4</zookeeper_host>
                <app_nacos>nacos.systech.b2c.srv:80</app_nacos>
            </properties>
        </profile>

        <profile>
            <id>trade</id>
            <properties>
                <profile_name>trade</profile_name>
                <zookeeper_host>c4</zookeeper_host>
                <app_nacos>nacos.systech.b2c.srv:80</app_nacos>
            </properties>
        </profile>

        <profile>
            <id>carOnline</id>
            <properties>
                <profile_name>carOnline</profile_name>
                <zookeeper_host>c4</zookeeper_host>
                <app_nacos>nacos.systech.b2c.srv:80</app_nacos>
            </properties>
        </profile>
    </profiles>
</project>