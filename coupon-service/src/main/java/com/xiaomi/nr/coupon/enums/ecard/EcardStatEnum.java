package com.xiaomi.nr.coupon.enums.ecard;

/**
 * 礼品卡状态　枚举
 *
 * <AUTHOR>
 */

public enum EcardStatEnum {
    /**
     * 预开卡
     */
    PreCreate(0, "预开卡"),

    /**
     * 已开卡
     */
    Create(1, "开卡"),

    /**
     * 开卡作废
     */
    CreateCancel(2, "开卡作废"),

    /**
     * 已激活
     */
    Active(3, "激活"),

    /**
     * 激活作废
     */
    ActiveCancel(4, "激活作废"),

    /**
     * 已绑定
     */
    Bind(5, "绑定"),

    /**
     * 绑定作废
     */
    BindCancel(6, "绑定作废"),

    /**
     * 已冻结
     */
    Freezed(7, "冻结");

    private final Integer value;
    private final String name;

    EcardStatEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static EcardStatEnum findByValue(Integer value) {
        EcardStatEnum[] values = EcardStatEnum.values();
        for (EcardStatEnum ecardStatEnum : values) {
            if (value.equals(ecardStatEnum.value)) {
                return ecardStatEnum;
            }
        }
        return null;
    }
}
