package com.xiaomi.nr.coupon.domain.coupontrade.tradecheck;

import com.xiaomi.nr.coupon.api.dto.coupon.CheckoutCheckerRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.CheckoutCouponListRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.CouponBaseInfo;
import com.xiaomi.nr.coupon.api.dto.coupon.checkout.*;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.constant.CommonConstant;
import com.xiaomi.nr.coupon.constant.UseChannelClientRel;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponConvert;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponFormatCommon;
import com.xiaomi.nr.coupon.domain.coupon.model.CheckoutCouponModel;
import com.xiaomi.nr.coupon.domain.coupon.model.ErrContext;
import com.xiaomi.nr.coupon.domain.couponcode.ExchangeChecker;
import com.xiaomi.nr.coupon.domain.couponconfig.model.*;
import com.xiaomi.nr.coupon.enums.coupon.CouponUseChannelEnum;
import com.xiaomi.nr.coupon.enums.coupon.RegionTypeEnum;
import com.xiaomi.nr.coupon.enums.coupon.ShoppingModeEnum;
import com.xiaomi.nr.coupon.enums.couponcode.StatusEnum;
import com.xiaomi.nr.coupon.enums.couponcode.UseModeEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.CouponTypeEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.ModeTypeEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.PromotionType;
import com.xiaomi.nr.coupon.enums.couponconfig.ShipmentIdEnum;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.enums.store.StoreTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.ExtendInfo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponcode.po.CouponCodePo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponcodeslave.CouponCodeReadMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave.CouponInfoMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.storeinfo.StoreInfoRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.storeinfo.po.OrgInfo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.NumberUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 优惠券信息
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CouponInfoCheck {

    @Resource
    private CouponInfoMapper couponInfoMapper;

    @Resource
    private CouponFormatCommon couponFormatCommon;

    @Resource
    private CouponConvert couponConvert;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private StoreInfoRedisDao storeInfoRedisDao;

    @Autowired
    private CouponCodeReadMapper couponCodeReadMapper;

    @Autowired
    private ExchangeChecker exchangeChecker;

    /**
     * 不限履约方式标识
     */
    private final static int SHIPMENT_NON_LIMIT_TAG = -1;

    /**
     * 排序值
     */
    private static Map<Integer, Integer> sortMap = new HashMap<>();

    static {
        sortMap.put(CouponTypeEnum.Goods.getValue(), 1);
        sortMap.put(CouponTypeEnum.PostFee.getValue(), 2);
        sortMap.put(CouponTypeEnum.Subsidy.getValue(), 3);
        sortMap.put(CouponTypeEnum.Deduction.getValue(), 4);
        sortMap.put(CouponTypeEnum.ServiceCoupon.getValue(), 5);
    }

    /**
     * 获取券的排序值，仅用于生成coupon group no使用
     *
     * @param couponType 券类型
     * @return 排序值
     */
    public Integer getSortMapValByCouponType(Integer couponType) {
        return sortMap.get(couponType);
    }

    /**
     * 结算页优惠劵列表
     * todo 抽空优化代码逻辑
     *
     * @param request 请求参数
     * @return 优惠劵列表
     * @throws BizError
     */
    public List<CouponOwnedInfo> getCheckoutCouponList(CheckoutCouponListRequest request) throws BizError {
        List<CouponOwnedInfo> couponOwnedInfos = new ArrayList<>();
        Long userId = request.getUserId();
        long nowTime = TimeUtil.getNowUnixSecond();
        // 获取用户所有未过期且未使用的劵
        List<CouponPo> couponPoList = couponInfoMapper.getAllCouponForUnused(userId, nowTime, Lists.newArrayList(request.getBizPlatform()));
        List<Long> configIds = new ArrayList<>(couponPoList.size());
        couponPoList.forEach(couponPo -> configIds.add(couponPo.getTypeId()));
        // 劵配置
        Map<Long, CouponConfigItem> couponConfigItemMap = couponConfigRepository.getCouponConfigs(configIds);
        // 遍历每张劵处理
        for (CouponPo couponPo : couponPoList) {
            CouponOwnedInfo couponOwnedInfo = new CouponOwnedInfo();
            couponOwnedInfo.setCouponId(couponPo.getId());
            couponOwnedInfo.setSendChannel(couponPo.getSendChannel());
            couponOwnedInfo.setStat(couponPo.getStat());
            Long configId = couponPo.getTypeId();
            couponOwnedInfo.setTypeId(configId);
            CouponConfigItem couponConfig = couponConfigItemMap.get(configId);
            if (couponConfig == null) {
                log.error("coupon config is null. userId:{}, couponId:{}, configId:{}", userId, couponPo.getId(), configId);
                break;
            }
            // 检查劵的有效时间
            Long startTime = Long.valueOf(couponPo.getStartTime());
            Long endTime = Long.valueOf(couponPo.getEndTime());
            couponOwnedInfo.setStartTime(startTime);
            couponOwnedInfo.setEndTime(endTime);
            if (nowTime < startTime || nowTime >= endTime) {
                Map<String, String> exInfo = new HashMap<>();
                exInfo.put("reason_unusable", "优惠券未在有效期内");
                exInfo.put("code_unusable", String.valueOf(ErrCode.USE_COUPON_TIME_INVALID.getCode()));
                exInfo.put("keydata_unusable", TimeUtil.formatSecond(startTime) + "~" + TimeUtil.formatSecond(endTime));
                couponOwnedInfo.setExInfo(exInfo);
                couponOwnedInfo.setAllow(0L);
            } else {
                couponOwnedInfo.setShipmentId(couponConfig.getCouponConfigInfo().getShipmentId());
                couponOwnedInfo.setCouponType(couponConfig.getCouponConfigInfo().getCouponType());
                int couponType = couponConfig.getCouponConfigInfo().getCouponType();
                couponOwnedInfo.setGroupTag(sortMap.get(couponType) + "_" + couponType + "_" + couponConfig.getCouponConfigInfo().getShipmentId());
                String couponOrgCode = getCouponOrgCode(couponPo.getExtendInfo());
                ErrContext errContext = new ErrContext();

                CheckoutCouponModel checkoutModel = couponConvert.convertCheckoutCouponListToModel(request);

                // 检查劵配置的条件
                if (!checkCondition(couponOrgCode, couponConfig, checkoutModel, errContext, ModeTypeEnum.NoCode.getMysqlValue())) {
                    Map<String, String> exInfo = new HashMap<>();
                    exInfo.put("reason_unusable", errContext.getErrorMsg());
                    exInfo.put("code_unusable", String.valueOf(errContext.getErrorCode()));
                    couponOwnedInfo.setExInfo(exInfo);
                } else {
                    couponOwnedInfo.setAllow(1L);
                }
            }
            // 新缓存转老缓存
            CouponOldConfigInfo couponOldConfigInfo = convertToCouponOldConfig(couponConfig, request.getSkuPackageList());
            if (couponOldConfigInfo == null) {
                Map<String, String> exInfo = new HashMap<>();
                exInfo.put("reason_unusable", "优惠劵配置错误");
                exInfo.put("code_unusable", String.valueOf(ErrCode.USE_COUPON_LOSE_EFFICACY.getCode()));
                couponOwnedInfo.setExInfo(exInfo);
                couponOwnedInfo.setAllow(0L);
            } else {
                couponOwnedInfo.setCouponOldConfigInfo(couponOldConfigInfo);
            }
            // 获取区域消费券可使用区域ID
            if (MapUtils.isNotEmpty(couponConfig.getAssignArea())) {
                List<String> limitUseRegion = getCouponUseRegionId(couponConfig.getAssignArea());
                couponOwnedInfo.setLimitUseRegion(limitUseRegion);
            }
            couponOwnedInfos.add(couponOwnedInfo);
        }
        return couponOwnedInfos;
    }

    /**
     * 结算页优惠劵校验
     *
     * @param request 请求参数
     * @return 优惠券信息（目前只支持了明码券）
     * @throws BizError
     */
    public Map<String, CouponOwnedInfo> checkoutCouponChecker(CheckoutCheckerRequest request) throws BizError {
        Map<String, CouponOwnedInfo> couponOwnedInfos = new HashMap<>();
        Long userId = request.getUserId();

        // md5 index => 券码
        Map<String, String> couponIndexList = getCouponIndex(request.getCouponCode());

        // md5 index => 券码信息
        Map<String, CouponCodePo> couponCodeList = getCouponCodeInfo(userId, new ArrayList<>(couponIndexList.keySet()));

        List<Long> configIds = new ArrayList<>(couponCodeList.size());
        couponCodeList.forEach((index, info) -> configIds.add(info.getTypeId()));

        // 劵配置信息
        Map<Long, CouponConfigItem> couponConfigItemMap = couponConfigRepository.getCouponConfigs(configIds);

        // 遍历每个优惠码处理
        for (Map.Entry<String, String> item : couponIndexList.entrySet()) {
            String couponIndex = item.getKey();
            String couponCode = item.getValue();

            CouponOwnedInfo couponOwnedInfo = new CouponOwnedInfo();
            couponOwnedInfo.setAllow(0L);

            // 优惠码的基本校验
            if (!checkCouponCode(couponCode)) {
                log.info("checkout.coupon, 不合法的优惠码, userId:{}, index={}", userId, couponIndex);
                couponOwnedInfo.setExInfo(newExInfo(String.valueOf(ErrCode.COUPON_CODE_ILLEGAL.getCode()), "优惠码不存在"));
                couponOwnedInfos.put(couponCode, couponOwnedInfo);
                continue;
            }

            // 判断是否存在
            if (!couponCodeList.containsKey(couponIndex)) {
                log.info("checkout.coupon, 不存在的优惠码, userId:{}, index={}", userId, couponIndex);
                couponOwnedInfo.setExInfo(newExInfo(String.valueOf(ErrCode.COUPON_CODE_ILLEGAL.getCode()), "优惠码不存在"));
                couponOwnedInfos.put(couponCode, couponOwnedInfo);
                continue;
            }

            CouponCodePo codeInfo = couponCodeList.get(couponIndex);

            // 判断优惠的配置是否存在
            CouponConfigItem couponConfig = couponConfigItemMap.get(codeInfo.getTypeId());
            if (couponConfig == null || couponConfig.getCouponConfigInfo() == null) {
                log.info("checkout.coupon, 无法获取券配置信息, userId:{}, index={}, id:{}, configId:{}", userId, codeInfo.getCouponIndex(), codeInfo.getId(), codeInfo.getTypeId());
                couponOwnedInfo.setExInfo(newExInfo(String.valueOf(ErrCode.COUPON_CODE_INVALID.getCode()), "优惠码无效"));
                couponOwnedInfos.put(couponCode, couponOwnedInfo);
                continue;
            }

            CouponConfigInfo configInfo = couponConfig.getCouponConfigInfo();
            couponOwnedInfo.setCouponId(codeInfo.getId());
            couponOwnedInfo.setStat(codeInfo.getStat());
            couponOwnedInfo.setTypeId(codeInfo.getTypeId());
            couponOwnedInfo.setSendChannel(configInfo.getSendChannel());
            couponOwnedInfo.setShipmentId(configInfo.getShipmentId());
            couponOwnedInfo.setCouponType(configInfo.getCouponType());
            couponOwnedInfo.setStartTime(configInfo.getStartUseTime());
            couponOwnedInfo.setEndTime(configInfo.getEndUseTime());

            // 新缓存转老缓存
            CouponOldConfigInfo couponOldConfigInfo = convertToCouponOldConfig(couponConfig, request.getSkuPackageList());
            if (couponOldConfigInfo == null) {
                couponOwnedInfo.setExInfo(newExInfo(String.valueOf(ErrCode.USE_COUPON_LOSE_EFFICACY.getCode()), "优惠码配置错误"));
                couponOwnedInfos.put(couponCode, couponOwnedInfo);
                continue;
            }
            couponOwnedInfo.setCouponOldConfigInfo(couponOldConfigInfo);

            // 优惠码的状态和时间校验
            try {
                exchangeChecker.conditionChecker(codeInfo, couponConfig, UseModeEnum.CHECKOUT.getValue(), request.getFromPriceProtect());
            } catch (BizError e) {
                log.info("checkout.coupon, {}, userId:{}, index={}, id:{}, configId:{}", e.getMessage(), userId, codeInfo.getCouponIndex(), codeInfo.getId(), codeInfo.getTypeId());
                couponOwnedInfo.setExInfo(newExInfo(String.valueOf(e.getCode()), e.getMessage()));
                couponOwnedInfos.put(couponCode, couponOwnedInfo);
                continue;
            }

            // 获取区域消费券可使用区域ID
            if (MapUtils.isNotEmpty(couponConfig.getAssignArea())) {
                List<String> limitUseRegion = getCouponUseRegionId(couponConfig.getAssignArea());
                couponOwnedInfo.setLimitUseRegion(limitUseRegion);
            }

            CheckoutCouponModel checkoutModel = couponConvert.convertCheckoutCodeCouponToModel(request);

            // 检查劵配置的条件
            ErrContext errContext = new ErrContext();
            if (!checkCondition(request.getOrgCode(), couponConfig, checkoutModel, errContext, ModeTypeEnum.Code.getMysqlValue())) {
                couponOwnedInfo.setExInfo(newExInfo(String.valueOf(errContext.getErrorCode()), errContext.getErrorMsg()));
                couponOwnedInfos.put(couponCode, couponOwnedInfo);
                continue;
            }

            couponOwnedInfo.setAllow(1L);
            couponOwnedInfos.put(couponCode, couponOwnedInfo);
        }
        return couponOwnedInfos;
    }

    /**
     * 生成ext信息map
     *
     * @param code String
     * @param msg  String
     * @return Map<String, String>
     */
    private Map<String, String> newExInfo(String code, String msg) {
        Map<String, String> exInfo = new HashMap<>();
        exInfo.put("reason_unusable", msg);
        exInfo.put("code_unusable", code);
        return exInfo;
    }

    /**
     * 检查明码券
     *
     * @param code String
     * @return boolean
     */
    private boolean checkCouponCode(String code) {
        if (exchangeChecker.couponCodeChecker(code)) {
            return true;
        }

        //暂时还兼容b.d创建的明码券，等过一段时间这个就不用校验了（仅支持通用的优惠码）
        return checkOldCouponCode(code);
    }

    /**
     * 校验有码券的长度和最后一位
     *
     * @param code 码优惠券
     * @return 是否符合
     */
    public static boolean checkOldCouponCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return false;
        }
        int[] icode = new int[code.length()];
        try {
            for (int i = 0; i < code.length(); i++) {
                int val = Integer.parseInt(String.valueOf(code.charAt(i)));
                icode[i] = val;
            }
        } catch (NumberFormatException e) {
            return false;
        }
        if (icode.length != 16) {
            return false;
        }
        int[] salt = new int[]{1, icode[3], 1, 1, icode[3], 1, icode[3], icode[3], 1, 1, 1, 0, 1, 1, icode[3], 1};
        int sum = 0;
        for (int i = 0; i < salt.length; i++) {
            sum += salt[i] * icode[i];
        }
        return sum % 10 == icode[11];
    }

    /**
     * 获取优惠券发放时的门店ID
     *
     * @param extendInfoStr String
     * @return String
     */
    public String getCouponOrgCode(String extendInfoStr) {
        if (StringUtils.isBlank(extendInfoStr)) {
            return "";
        }
        ExtendInfo extendInfo = GsonUtil.fromJson(extendInfoStr, ExtendInfo.class);
        if (extendInfo != null && !StringUtils.isBlank(extendInfo.getOrgCode())) {
            return extendInfo.getOrgCode();
        }
        return "";
    }

    /**
     * 获取明码券md5 index
     *
     * @param codes List<String>
     * @return Map<String, String>
     */
    private Map<String, String> getCouponIndex(List<String> codes) {
        Map<String, String> result = new HashMap<>();
        if (CollectionUtils.isEmpty(codes)) {
            return result;
        }
        codes.forEach(code -> {
            if (StringUtils.isEmpty(code)) {
                return;
            }
            String index = DigestUtils.md5Hex(code.toUpperCase());
            result.put(index, code);
        });
        return result;
    }

    /**
     * 获取优惠码信息
     *
     * @param userId          Long
     * @param couponIndexList List<String>
     * @return CouponCodePo
     * @throws BizError
     */
    private Map<String, CouponCodePo> getCouponCodeInfo(Long userId, List<String> couponIndexList) throws BizError {
        long runStartTime = TimeUtil.getNowUnixMillis();
        Map<String, CouponCodePo> result = new HashMap<>();
        try {
            List<CouponCodePo> data = couponCodeReadMapper.getCouponCodeList(couponIndexList);
            log.info("checkout.coupon, 获取优惠码信息, runTime={}毫秒, userId={}, index={}, data={}", TimeUtil.sinceMillis(runStartTime), userId, couponIndexList, data);
            if (CollectionUtils.isEmpty(data)) {
                return result;
            }
            data.forEach(item -> {
                if (item != null) {
                    result.put(item.getCouponIndex(), item);
                }
            });
        } catch (Exception e) {
            log.error("checkout.coupon, 获取明码券信息失败, runTime={}毫秒, userId={}, index={}, err={}", TimeUtil.sinceMillis(runStartTime), userId, couponIndexList, e.getMessage());
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_INFO_FAIL, "获取优惠码信息失败");
        }
        return result;
    }

    /**
     * 检查劵配置条件
     *
     * @param couponOrgCode  券发放时的门店ID
     * @param couponConfig   劵配置
     * @param request        请求参数
     * @param errContext     　错误上下文
     * @param couponModeType 使用方式（无码券/优惠码）
     * @return 是否满足条件，true-是，false-否
     */
    public boolean checkCondition(String couponOrgCode, CouponConfigItem couponConfig, CheckoutCouponModel request, ErrContext errContext, String couponModeType) {
        // 校验劵类型
        if (!checkType(couponConfig, errContext, couponModeType)) {
            return false;
        }

        //client id
        /*if (!checkClientId(couponConfig, request, errContext, couponModeType)) {
            return false;
        }*/

        // 零售渠道特有校验 todo 抽象出不同的策略
        if (couponConfig.getBizPlatform().equals(BizPlatformEnum.RETAIL.getCode())) {

            // 检查业务领域
            if (!checkBizPlatform(couponConfig, request, errContext, couponModeType)) {
                return false;
            }

            // 校验渠道
            if (!checkChannel(couponConfig, request, errContext, couponModeType)) {
                return false;
            }

            //专店专用
            if (!checkStore(couponConfig, request, errContext, couponOrgCode, couponModeType)) {
                return false;
            }

            // 校验区域
            if (!checkConsumerCoupon(couponConfig, request, errContext)) {
                return false;
            }

            // 履约方式
            if (!checkShipmentId(couponConfig, request, errContext)) {
                return false;
            }
        }
        // 校验商品
        if (!findGoodsInclude(couponConfig, request, errContext, couponModeType)) {
            return false;
        }
        return true;
    }

    /**
     * 校验履约方式
     *
     * @param couponConfig
     * @param errContext
     * @return
     */
    public boolean checkShipmentId(CouponConfigItem couponConfig, CheckoutCouponModel request, ErrContext errContext) {
        // 价保不校验
        if (request.isFromPriceProtect()) {
            return true;
        }
        //商品券和补贴券不校验履约
        if (couponConfig.getCouponConfigInfo().getCouponType().equals(CouponTypeEnum.Goods.getValue()) || couponConfig.getCouponConfigInfo().getCouponType().equals(CouponTypeEnum.Subsidy.getValue())) {
            return true;
        }
        //兼容老参数，为null说明上游没赋值，若券类型为运费券应该不返回
        if (request.getShipmentId() == null && CouponTypeEnum.PostFee.getValue().equals(couponConfig.getCouponConfigInfo().getCouponType())) {
            errContext.setErrorMsg("仅门店闪送订单可用");
            errContext.setErrorCode(ErrCode.USE_COUPON_SHIPMENT_MISMATCH.getCode());
            return false;
        }
        if (!request.getShipmentId().equals(couponConfig.getCouponConfigInfo().getShipmentId())) {
            errContext.setErrorMsg("仅门店闪送订单可用");
            errContext.setErrorCode(ErrCode.USE_COUPON_SHIPMENT_MISMATCH.getCode());
            return false;
        }
        return true;
    }


    /**
     * 校验门店
     *
     * @param couponConfig  券发放时的门店ID
     * @param request       CheckoutCouponModel
     * @param couponOrgCode 券发放时的门店ID
     * @return 是否满足条件，true-是，false-否
     */
    public boolean checkStore(CouponConfigItem couponConfig, CheckoutCouponModel request, ErrContext errContext, String couponOrgCode, String couponModeType) {
        // 价保不校验
        if (request.isFromPriceProtect()) {
            return true;
        }
        String orgCode = request.getOrgCode();
        ExtPropInfo extPropInfo = couponConfig.getExtPropInfo();
        if (!extPropInfo.specialStore()) {
            return true;
        }
        if (couponOrgCode == null || StringUtils.isBlank(couponOrgCode)) {
            return true;
        }
        boolean isCodeMode = ModeTypeEnum.Code.getMysqlValue().equals(couponModeType);
        if (StringUtils.isBlank(orgCode)) {
            errContext.setErrorMsg(isCodeMode ? "优惠码不适用此渠道" : "优惠券不适用此渠道");
            errContext.setErrorCode(ErrCode.USE_COUPON_ORGCODE_UNUSED.getCode());
            return false;
        }
        if (!couponOrgCode.equals(orgCode)) {
            errContext.setErrorMsg(isCodeMode ? "优惠码仅限领取门店使用" : "优惠券仅限领取门店使用");
            errContext.setErrorCode(ErrCode.USE_COUPON_ORGCODE_UNUSED.getCode());
            return false;
        }
        return true;
    }


    /**
     * 检查劵配置类型
     *
     * @param couponConfig 劵配置
     * @param errContext   错误上下文
     * @return 是否满足条件，true-是，false-否
     */
    public boolean checkType(CouponConfigItem couponConfig, ErrContext errContext, String couponModeType) {
        if (couponConfig.getPromotionType().getCode() < PromotionType.ConditionReduce.getCode()
                || couponConfig.getPromotionType().getCode() > PromotionType.Gift.getCode()) {
            errContext.setErrorMsg(ModeTypeEnum.Code.getMysqlValue().equals(couponModeType) ? "优惠码类型错误" : "优惠券类型错误");
            errContext.setErrorCode(ErrCode.USE_COUPON_TYPE_ERROR.getCode());
            return false;
        }
        return true;
    }

    /**
     * 检查劵配置类型
     *
     * @param couponConfig          优惠券配置
     * @param promotionTypeList     优惠类型列表
     * @param errContext            errContext
     * @return  是否满足条件，true-是，false-否
     */
    public boolean checkPromotionType(CouponConfigItem couponConfig, List<Integer> promotionTypeList, ErrContext errContext) {
        if (CollectionUtils.isEmpty(promotionTypeList)) {
            return true;
        }

        if (!promotionTypeList.contains(couponConfig.getPromotionType().getCode())) {
            errContext.setErrorMsg("当前场景不可用");
            errContext.setErrorCode(ErrCode.USE_COUPON_PROMOTION_TYPE_ERROR.getCode());
            return false;
        }

        return true;
    }

    private boolean isMiShopChannelClientId(Long clientId) {
        Set<Long> clientIds = UseChannelClientRel.getUseChannelClientRelation().get(CouponUseChannelEnum.MI_SHOP.getValue()).getClientIds();
        return clientIds.contains(clientId);
    }

    private boolean isMiHomeChannelClientId(Long clientId) {
        Set<Long> clientIds = UseChannelClientRel.getUseChannelClientRelation().get(CouponUseChannelEnum.MI_HOME_ZY.getValue()).getClientIds();
        return clientIds.contains(clientId);
    }

    private boolean isAuthorizedChannelClientId(Long clientId) {
        Set<Long> clientIds = UseChannelClientRel.getUseChannelClientRelation().get(CouponUseChannelEnum.MI_AUTHORIZED.getValue()).getClientIds();
        return clientIds.contains(clientId);
    }

    /**
     * 检查client id
     *
     * @param couponConfig 劵配置
     * @param request      请求参数
     * @param errContext   错误上下文
     * @return 是否满足条件，true-是，false-否
     */
    private boolean checkClientId(CouponConfigItem couponConfig, CheckoutCouponModel request, ErrContext errContext, String couponModeType) {
        Map<Integer, UseChannel> channelMap = couponConfig.getUseChannelStore();
        if (channelMap.containsKey(CouponUseChannelEnum.MI_SHOP.getId()) && isMiShopChannelClientId(request.getClientId())) {
            return true;
        }

        if ((channelMap.containsKey(CouponUseChannelEnum.MI_HOME_ZY.getId()) || channelMap.containsKey(CouponUseChannelEnum.MI_HOME_ZM.getId())) &&
                isMiHomeChannelClientId(request.getClientId())
        ) {
            return true;
        }

        if (channelMap.containsKey(CouponUseChannelEnum.MI_AUTHORIZED.getId()) && isAuthorizedChannelClientId(request.getClientId())) {
            return true;
        }
        errContext.setErrorMsg(ModeTypeEnum.Code.getMysqlValue().equals(couponModeType) ? "优惠码不适用此渠道" : "优惠券不适用此渠道");
        errContext.setErrorCode(ErrCode.USE_COUPON_CHANNEL_MISMATCH.getCode());
        return false;
    }

    /**
     * 校验业务领域
     *
     * @param couponConfig 劵配置
     * @param request      请求参数
     * @param errContext   错误上下文
     * @return 是否满足条件，true-是，false-否
     */
    public boolean checkBizPlatform(CouponConfigItem couponConfig, CheckoutCouponModel request, ErrContext errContext, String couponModeType) {
        boolean isCodeMode = ModeTypeEnum.Code.getMysqlValue().equals(couponModeType);
        if (!request.getBizPlatform().equals(couponConfig.getBizPlatform())) {
            errContext.setErrorMsg(isCodeMode ? "优惠码不适用此业务领域" : "优惠券不适用此业务领域");
            errContext.setErrorCode(ErrCode.USE_COUPON_BIZ_PLATFORM_MISMATCH.getCode());
            return false;
        }
        return true;
    }

    /**
     * 校验渠道
     *
     * @param couponConfig 劵配置
     * @param request      请求参数
     * @param errContext   错误上下文
     * @return 是否满足条件，true-是，false-否
     */
    public boolean checkChannel(CouponConfigItem couponConfig, CheckoutCouponModel request, ErrContext errContext, String couponModeType) {
        // 价保不校验
        if (request.isFromPriceProtect()) {
            return true;
        }
        boolean isCodeMode = ModeTypeEnum.Code.getMysqlValue().equals(couponModeType);
        Map<Integer, UseChannel> channelMap = couponConfig.getUseChannelStore();
        if (StringUtils.isEmpty(request.getOrgCode())) {
            // 线上
            if (!channelMap.containsKey(CouponUseChannelEnum.MI_SHOP.getId())) {
                if (!formatUnAbleReason(couponConfig.getCouponConfigInfo().getCouponType(), request.getShipmentId(), errContext)) {
                    return false;
                }
                errContext.setErrorMsg(isCodeMode ? "优惠码不适用此渠道" : "优惠券不适用此渠道");
                errContext.setErrorCode(ErrCode.USE_COUPON_CHANNEL_MISMATCH.getCode());
                return false;
            }
        } else {
            // 线下
            // 门店信息
            Integer orgType = request.getOrgType();
            if (!StoreTypeEnum.isStoreType(orgType)) {
                OrgInfo orgInfo = storeInfoRedisDao.getOrgInfo(request.getOrgCode());
                orgType = orgInfo.getOrgType();
                log.info("CouponInfo.checkChannel orgType null, get redis orgInfo, orgCode:{}, userId:{}", request.getOrgCode(), request.getUserId());
            }

            if (orgType == null) {
                log.warn("CouponInfo.checkChannel orgInfo and orgType null, orgCode:{}", request.getOrgCode());
                errContext.setErrorMsg(isCodeMode ? "优惠码不适用此门店" : "优惠券不适用此门店");
                errContext.setErrorCode(ErrCode.USE_COUPON_ORGCODE_MISMATCH.getCode());
                return false;
            }

            // 直营店和专卖店属于米家渠道
            if (StoreTypeEnum.isDirect(orgType) || StoreTypeEnum.isSpecialty(orgType)) {
                if (!channelMap.containsKey(CouponUseChannelEnum.MI_HOME_ZY.getId()) && !channelMap.containsKey(CouponUseChannelEnum.MI_HOME_ZM.getId())) {
                    if (!formatUnAbleReason(couponConfig.getCouponConfigInfo().getCouponType(), request.getShipmentId(), errContext)) {
                        return false;
                    }
                    errContext.setErrorMsg(isCodeMode ? "优惠码不适用此渠道" : "优惠券不适用此渠道");
                    errContext.setErrorCode(ErrCode.USE_COUPON_CHANNEL_MISMATCH.getCode());
                    return false;
                } else {
                    UseChannel useChannelZY = channelMap.get(CouponUseChannelEnum.MI_HOME_ZY.getId());
                    UseChannel useChannelZM = channelMap.get(CouponUseChannelEnum.MI_HOME_ZM.getId());
                    if (!Objects.isNull(useChannelZY) && !Objects.isNull(useChannelZM)) {
                        if (useChannelZY.getAll() && useChannelZM.getAll()) {
                            return true;
                        } else if (!useChannelZY.getLimitIds().contains(request.getOrgCode()) && !useChannelZM.getLimitIds().contains(request.getOrgCode())) {
                            errContext.setErrorMsg(isCodeMode ? "优惠码不适用此门店" : "优惠券不适用此门店");
                            errContext.setErrorCode(ErrCode.USE_COUPON_ORGCODE_MISMATCH.getCode());
                            return false;
                        }
                    }
                }
            } else if (StoreTypeEnum.isAuthorized(orgType)) {
                if (!channelMap.containsKey(CouponUseChannelEnum.MI_AUTHORIZED.getId())) {
                    if (!formatUnAbleReason(couponConfig.getCouponConfigInfo().getCouponType(), request.getShipmentId(), errContext)) {
                        return false;
                    }
                    errContext.setErrorMsg(isCodeMode ? "优惠码不适用此渠道" : "优惠券不适用此渠道");
                    errContext.setErrorCode(ErrCode.USE_COUPON_CHANNEL_MISMATCH.getCode());
                    return false;
                } else {
                    UseChannel useChannelAuth = channelMap.get(CouponUseChannelEnum.MI_AUTHORIZED.getId());
                    if (useChannelAuth.getAll()) {
                        return true;
                    } else if (!useChannelAuth.getLimitIds().contains(request.getOrgCode())) {
                        errContext.setErrorMsg(isCodeMode ? "优惠码不适用此门店" : "优惠券不适用此门店");
                        errContext.setErrorCode(ErrCode.USE_COUPON_ORGCODE_MISMATCH.getCode());
                        return false;
                    }
                }
            } else {
                errContext.setErrorMsg(isCodeMode ? "优惠码不适用此渠道" : "优惠券不适用此渠道");
                errContext.setErrorCode(ErrCode.USE_COUPON_CHANNEL_MISMATCH.getCode());
                return false;
            }
        }
        return true;
    }


    /**
     * 适配运费券专属券不可用原因
     *
     * @param couponType 券类型
     * @param shipmentId 履约id
     * @param errContext 错误信息上下文
     * @return bool
     */
    private boolean formatUnAbleReason(Integer couponType, Integer shipmentId, ErrContext errContext) {
        if (Objects.equals(couponType, CouponTypeEnum.PostFee.getValue()) && !Objects.equals(shipmentId, ShipmentIdEnum.Lightning.getValue())) {
            errContext.setErrorMsg("仅小米之家门店闪送可用");
            errContext.setErrorCode(ErrCode.USE_COUPON_SHIPMENT_MISMATCH.getCode());
            return false;
        }
        return true;
    }

    /**
     * 校验区域消费劵
     *
     * @param couponConfig 劵配置
     * @param request      请求参数
     * @param errContext   错误上下文
     * @return 是否满足条件，true-是，false-否
     */
    public boolean checkConsumerCoupon(CouponConfigItem couponConfig, CheckoutCouponModel request, ErrContext errContext) {
        // 价保不校验
        if (request.isFromPriceProtect()) {
            return true;
        }
        // 不限制指定区域可用
        if (MapUtils.isEmpty(couponConfig.getAssignArea())) {
            return true;
        }
        String orgCode = request.getOrgCode();
        boolean orgCodeEmpty = StringUtils.isEmpty(orgCode);
        // 目前只使用了城市id
        Long regionId = request.getCityId();
        //都不传，则不能用区域消费券
        if (request.getCityId() <= 0 && orgCodeEmpty) {
            errContext.setErrorMsg("不满足消费券使用区域");
            errContext.setErrorCode(ErrCode.USE_COUPON_AREA_LIMIT.getCode());
            return false;
        }
        OrgInfo orgInfo = storeInfoRedisDao.getOrgInfo(orgCode);
        if (StringUtils.isNotEmpty(orgCode) && orgInfo == null) {
            log.error("checkConsumerCoupon orgInfo is null. orgCode:{}", orgCode);
            errContext.setErrorMsg("门店信息获取失败");
            errContext.setErrorCode(ErrCode.USE_COUPON_AREA_LIMIT.getCode());
            return false;
        }
        Long shoppingMode = request.getShoppingMode();
        //根据购物模式判断
        if (shoppingMode == ShoppingModeEnum.LOGISTICS.getType().longValue() && regionId <= 0) {
            errContext.setErrorMsg("不满足消费券使用区域");
            errContext.setErrorCode(ErrCode.USE_COUPON_AREA_LIMIT.getCode());
            return false;
        }
        if (shoppingMode == ShoppingModeEnum.SCENE.getType().longValue() && orgCodeEmpty) {
            errContext.setErrorMsg("不满足消费券使用区域");
            errContext.setErrorCode(ErrCode.USE_COUPON_AREA_LIMIT.getCode());
            return false;
        }
        boolean regionEmpty = regionId <= 0 || orgCodeEmpty;
        if (shoppingMode == ShoppingModeEnum.LOGISTICS_SCENE_MIX.getType().longValue() && regionEmpty) {
            errContext.setErrorMsg("不满足消费券使用区域");
            errContext.setErrorCode(ErrCode.USE_COUPON_AREA_LIMIT.getCode());
            return false;
        }
        // 购物模式如果没有传，则要校正
        if (shoppingMode == ShoppingModeEnum.DEFAULT.getType().longValue()) {
            if (regionId > 0 && !orgCodeEmpty) {
                shoppingMode = ShoppingModeEnum.LOGISTICS_SCENE_MIX.getType().longValue();
            } else if (regionId > 0) {
                shoppingMode = ShoppingModeEnum.LOGISTICS.getType().longValue();
            } else if (!orgCodeEmpty) {
                shoppingMode = ShoppingModeEnum.SCENE.getType().longValue();
            }
        }

        for (Map.Entry<Integer, List<Integer>> entry : couponConfig.getAssignArea().entrySet()) {
            Integer regionType = entry.getKey();
            List<Integer> regions = entry.getValue();
            for (Integer id : regions) {
                //物流＋现场购的混合模式，则优先判断都传的情况
                if (shoppingMode == ShoppingModeEnum.LOGISTICS_SCENE_MIX.getType().longValue()) {
                    if (regionId != id.longValue()) {
                        continue;
                    }
                    if (checkRegion(regionType, id, orgInfo)) {
                        return true;
                    }
                }
                if (shoppingMode == ShoppingModeEnum.LOGISTICS.getType().longValue()) {
                    if (regionId == id.longValue()) {
                        return true;
                    }
                }
                if (shoppingMode == ShoppingModeEnum.SCENE.getType().longValue()) {
                    if (checkRegion(regionType, id, orgInfo)) {
                        return true;
                    }
                }
            }
        }
        errContext.setErrorMsg("不满足消费券使用区域");
        errContext.setErrorCode(ErrCode.USE_COUPON_AREA_LIMIT.getCode());
        return false;
    }

    private static boolean checkRegion(Integer regionType, Integer id, OrgInfo orgInfo) {
        // 省
        if (regionType == RegionTypeEnum.REGION_PROVINCE.getType()) {
            if (orgInfo.getArea().contains(RegionTypeEnum.REGION_PROVINCE.getValue() + "_" + id)) {
                return true;
            }
        } else if (regionType == RegionTypeEnum.REGION_CITY.getType()) {
            if (orgInfo.getArea().contains(RegionTypeEnum.REGION_CITY.getValue() + "_" + id)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查劵配置是否包含商品
     *
     * @param couponConfig 劵配置
     * @param request      请求参数
     * @param errContext   错误上下文
     * @return
     */
    public boolean findGoodsInclude(CouponConfigItem couponConfig, CheckoutCouponModel request, ErrContext errContext, String couponModeType) {
        List<GoodsInfo> goodsInfos = request.getSkuPackageList();
        //　门店在结算前获取券列表是不会传商品过来的
        if (CollectionUtils.isEmpty(goodsInfos)) {
            return true;
        }
        GoodScope goodScope = couponConfig.getGoodScope();
        for (GoodsInfo goodsInfo : goodsInfos) {
            if (GoodsLevelEnum.Sku.getValue().equals(goodsInfo.getLevel()) && goodScope.singleCheckValidSku(goodsInfo.getId())) {
                return true;
            }
            if (GoodsLevelEnum.Package.getValue().equals(goodsInfo.getLevel()) && goodScope.singleCheckValidPackage(goodsInfo.getId())) {
                return true;
            }
            if (GoodsLevelEnum.Ssu.getValue().equals(goodsInfo.getLevel()) && goodScope.singleCheckValidSsu(goodsInfo.getId())) {
                return true;
            }
        }
        errContext.setErrorMsg(ModeTypeEnum.Code.getMysqlValue().equals(couponModeType) ? "不含可用优惠码商品" : "不含可用优惠券商品");
        errContext.setErrorCode(ErrCode.USE_COUPON_GOODS_MISMATCH.getCode());
        return false;
    }

    /**
     * 优惠劵配置转换（新->老）
     *
     * @param couponConfig 新优惠劵配置（新）
     * @return 优惠劵配置（老）
     */
    private CouponOldConfigInfo convertToCouponOldConfig(CouponConfigItem couponConfig, List<GoodsInfo> skuPackageList) {
        CouponOldConfigInfo oldConfig = new CouponOldConfigInfo();
        // 基本信息
        TypeBase typeBase = formatBaseType(couponConfig);
        if (typeBase == null) {
            return null;
        }
        // 条件
        Condition condition = formatCondition(couponConfig, skuPackageList);
        // 政策
        Policy policy = fomatPolicy(couponConfig);
        oldConfig.setBasetype(typeBase);
        oldConfig.setCondition(condition);
        oldConfig.setPolicy(policy);
        return oldConfig;
    }

    private TypeBase formatBaseType(CouponConfigItem couponConfig) {
        TypeBase typeBase = new TypeBase();
        typeBase.setId((long) couponConfig.getConfigId());
        if (couponConfig.getPromotionType() == PromotionType.DirectReduce) {
            typeBase.setType(PromotionType.ConditionReduce.getCode());
        } else {
            typeBase.setType(couponConfig.getPromotionType().getCode());
        }
        CouponConfigInfo couponConfigInfo = couponConfig.getCouponConfigInfo();
        if (couponConfigInfo == null) {
            return null;
        }
        // n元劵转抵扣劵配置
        if (couponConfig.getPromotionType() == PromotionType.NyuanBuy) {
            if (couponConfigInfo.getPromotionValue() > 0) {
                typeBase.setDeductType(1);
            }
        }
        typeBase.setName(couponConfigInfo.getName());
        typeBase.setRangeDesc(couponConfigInfo.getCouponDesc());
        if (couponConfig.getExtPropInfo().shareAble()) {
            typeBase.setIsShare(1);
        } else {
            typeBase.setIsShare(2);
        }
        if (couponConfig.getExtPropInfo().postFree()) {
            typeBase.setPostfree(1);
        } else {
            typeBase.setPostfree(2);
        }
        // 满减、立减->现金劵
        if (couponConfig.getPromotionType() == PromotionType.ConditionReduce || couponConfig.getPromotionType() == PromotionType.DirectReduce) {
            typeBase.setShowTitle(NumberUtil.centToYuanV2(couponConfigInfo.getPromotionValue(), 2, RoundingMode.DOWN));
            typeBase.setShowUnit("元");
            typeBase.setTypeCode("cash");
        } else if (couponConfig.getPromotionType() == PromotionType.ConditionDiscount) {
            typeBase.setShowTitle(NumberUtil.convertDiscount(couponConfigInfo.getPromotionValue(), 2, RoundingMode.DOWN));
            typeBase.setShowUnit("折");
            typeBase.setTypeCode("discount");
        } else if (couponConfig.getPromotionType() == PromotionType.NyuanBuy) {
            typeBase.setTypeCode("deductible");
            typeBase.setShowTitle(NumberUtil.centToYuanV2(couponConfigInfo.getPromotionValue(), 2, RoundingMode.DOWN));
        } else {
            log.error("err coupon type.");
        }
        return typeBase;
    }

    private Condition formatCondition(CouponConfigItem couponConfig, List<GoodsInfo> goodsInfos) {
        Condition condition = new Condition();
        //使用渠道
        List<String> channels = new ArrayList<>();
        if (couponConfig.getUseChannelStore().containsKey(CouponUseChannelEnum.MI_SHOP.getId())) {
            List<String> clientIds = Arrays.asList(StringUtils.split(CommonConstant.CLIENT_IDS, ","));
            condition.setClient(clientIds);
            channels.add(CouponUseChannelEnum.MI_SHOP.getValue());
        }
        if (couponConfig.getUseChannelStore().containsKey(CouponUseChannelEnum.MI_HOME_ZY.getId())) {
            channels.add(CouponUseChannelEnum.MI_HOME_ZY.getValue());
        }
        if (couponConfig.getUseChannelStore().containsKey(CouponUseChannelEnum.MI_HOME_ZM.getId())) {
            channels.add(CouponUseChannelEnum.MI_HOME_ZM.getValue());
        }
        if (couponConfig.getUseChannelStore().containsKey(CouponUseChannelEnum.MI_AUTHORIZED.getId())) {
            channels.add(CouponUseChannelEnum.MI_AUTHORIZED.getValue());
        }
        String channelStr = channels.size() == 0 ? "" : channels.stream().distinct().collect(Collectors.joining(","));
        condition.setUseChannel(channelStr);
        CouponConfigInfo couponConfigInfo = couponConfig.getCouponConfigInfo();
        if (couponConfigInfo == null) {
            return condition;
        }
        List<QuotaEle> quotaEles = getQuota(couponConfig);
        condition.setQuota(quotaEles);
        // 包含商品
        List<CompareItem> goods = getCompareItems(couponConfig, goodsInfos);
        condition.setGoodsInclude(goods);
        return condition;
    }

    /**
     * 获取可用商品
     *
     * @param couponConfig
     * @param goodsInfos
     * @return
     */
    private List<CompareItem> getCompareItems(CouponConfigItem couponConfig, List<GoodsInfo> goodsInfos) {
        CompareItem compareItem = new CompareItem();
        List<String> skuList = Lists.newArrayList();
        List<String> packageList = Lists.newArrayList();
        for (GoodsInfo goodsInfo : goodsInfos) {
            if (goodsInfo == null) {
                continue;
            }
            if (GoodsLevelEnum.Sku.getValue().equals(goodsInfo.getLevel()) && couponConfig.getGoodScope().singleCheckValidSku(goodsInfo.getId())) {
                skuList.add(String.valueOf(goodsInfo.getId()));
            }
            if (GoodsLevelEnum.Package.getValue().equals(goodsInfo.getLevel()) && couponConfig.getGoodScope().singleCheckValidPackage(goodsInfo.getId())) {
                packageList.add(String.valueOf(goodsInfo.getId()));
            }
        }
        compareItem.setSku(skuList);
        compareItem.setPackages(packageList);
        return Lists.newArrayList(compareItem);
    }

    private Policy fomatPolicy(CouponConfigItem couponConfig) {
        Policy policy = new Policy();
        CouponConfigInfo couponConfigInfo = couponConfig.getCouponConfigInfo();
        if (couponConfigInfo == null) {
            return policy;
        }
        List<PolicyLevel> policyLevels = new ArrayList<>();
        PolicyLevel policyLevel = new PolicyLevel();
        List<QuotaEle> quotaEles = getQuota(couponConfig);
        policyLevel.setQuota(quotaEles);
        RuleEle ruleEle = new RuleEle();
        switch (couponConfig.getPromotionType()) {
            case ConditionReduce:
            case DirectReduce:
                ruleEle.setReduceMoney(couponConfigInfo.getPromotionValue());
                policy.setType(1);
                break;
            case ConditionDiscount:
                ruleEle.setReduceDiscount(couponConfigInfo.getPromotionValue() / 10);
                ruleEle.setMaxPrice(couponConfigInfo.getMaxReduce().longValue());
                policy.setType(2);
                break;
            case NyuanBuy:
                ruleEle.setNYuanPrice(couponConfigInfo.getPromotionValue());
                Map<String, String> deductGoods = new HashMap<>();
                // sku
                if (CollectionUtils.isNotEmpty(couponConfig.getGoodScope().getSkus())) {
                    deductGoods.putAll(couponConfig.getGoodScope().getSkus().stream().
                            collect(Collectors.toMap(String::valueOf, String::valueOf)));
                }
                // 套装
                if (CollectionUtils.isNotEmpty(couponConfig.getGoodScope().getPackages())) {
                    deductGoods.putAll(couponConfig.getGoodScope().getPackages().stream().
                            collect(Collectors.toMap(String::valueOf, String::valueOf)));
                }
                ruleEle.setDeductedGoods(deductGoods);
                policy.setType(3);
                break;
        }
        if (couponConfig.getExtPropInfo() != null) {
            // 包邮属性
            ruleEle.setPostFree(couponConfig.getExtPropInfo().getPostFree());
        }
        policyLevel.setRule(ruleEle);
        policyLevels.add(policyLevel);
        policy.setPolicies(policyLevels);
        return policy;
    }

    private List<QuotaEle> getQuota(CouponConfigItem couponConfig) {
        List<QuotaEle> quotaEles = new ArrayList<>();
        CouponConfigInfo couponConfigInfo = couponConfig.getCouponConfigInfo();
        if (couponConfigInfo == null) {
            return quotaEles;
        }
        QuotaEle quotaEle = new QuotaEle();
        switch (couponConfig.getPromotionType()) {
            case ConditionReduce:
            case ConditionDiscount:
            case DirectReduce:
                if (couponConfigInfo.getBottomType() == 1) {
                    // 满元
                    quotaEle.setType(0);
                    quotaEle.setMoney(couponConfigInfo.getBottomPrice().longValue());
                } else if (couponConfigInfo.getBottomType() == 2) {
                    // 满件
                    quotaEle.setType(1);
                    quotaEle.setCount(couponConfigInfo.getBottomCount());
                } else if (couponConfigInfo.getBottomType() == 3) {
                    // 每满元
                    quotaEle.setType(3);
                    quotaEle.setMoney(couponConfigInfo.getBottomPrice().longValue());
                } else {
                    // 每满件
                    quotaEle.setType(4);
                    quotaEle.setCount(couponConfigInfo.getBottomCount());
                }
                break;
            case NyuanBuy:
                quotaEle.setType(1);
                quotaEle.setCount(1);
                break;
        }
        quotaEles.add(quotaEle);
        return quotaEles;
    }

    public List<String> getCouponUseRegionId(Map<Integer, List<Integer>> assignAreaConfig) {
        List<String> res = new ArrayList<>();
        for (Map.Entry<Integer, List<Integer>> entry : assignAreaConfig.entrySet()) {
            String regionPrefix;
            if (entry.getKey() == RegionTypeEnum.REGION_PROVINCE.getType()) {
                regionPrefix = RegionTypeEnum.REGION_PROVINCE.getValue();
            } else if (entry.getKey() == RegionTypeEnum.REGION_CITY.getType()) {
                regionPrefix = RegionTypeEnum.REGION_CITY.getValue();
            } else {
                continue;
            }
            for (Integer i : entry.getValue()) {
                res.add(regionPrefix + "_" + i);
            }
        }
        return res;
    }

    /**
     * 非通用校验（无码券）
     *
     * @param couponBaseInfo   券基本信息
     * @param errContext       错误上下文
     * @param usedCouponCheck 是否来自价保
     * @param couponModeType   使用方式（无码券/优惠码）
     * @return boolean
     */
    public boolean specialConditionCheckerNoCode(CouponBaseInfo couponBaseInfo, ErrContext errContext, boolean usedCouponCheck, String couponModeType) {
        //券配置时间检查
        if (!timeCheckerNoCode(couponBaseInfo, errContext, couponModeType)) {
            return false;
        }

        //状态检查
        if (!statusCheckerNoCode(couponBaseInfo, errContext, usedCouponCheck, couponModeType)) {
            return false;
        }
        return true;
    }

    /**
     * 状态检查（无码券）
     *
     * @param couponBaseInfo   券基本信息
     * @param errContext       错误上下文
     * @param usedCouponCheck 是否来自价保
     * @return boolean
     */
    public boolean statusCheckerNoCode(CouponBaseInfo couponBaseInfo, ErrContext errContext, boolean usedCouponCheck, String couponModeType) {
        boolean isCodeMode = ModeTypeEnum.Code.getMysqlValue().equals(couponModeType);
        //如果是价保，则必须为已使用
        if (usedCouponCheck) {
            if (!StatusEnum.USED.getValue().equals(couponBaseInfo.getStatus())) {
                errContext.setErrorCode(ErrCode.USE_COUPON_STAT_ERROR.getCode());
                errContext.setErrorMsg(isCodeMode ? "优惠码状态不符合价保条件" : "优惠券状态不符合价保条件");
                return false;
            }
            return true;
        }

        if (StatusEnum.EXPIRED.getValue().equals(couponBaseInfo.getStatus())) {
            errContext.setErrorCode(ErrCode.USE_COUPON_STAT_ERROR.getCode());
            errContext.setErrorMsg(isCodeMode ? "优惠码已过期" : "优惠券已过期");
            return false;
        }

        if (StatusEnum.USED.getValue().equals(couponBaseInfo.getStatus())) {
            errContext.setErrorCode(ErrCode.USE_COUPON_STAT_ERROR.getCode());
            errContext.setErrorMsg(isCodeMode ? "优惠码已使用" : "优惠券已使用");
            return false;
        }

        if (!StatusEnum.UNUSED.getValue().equals(couponBaseInfo.getStatus())) {
            errContext.setErrorCode(ErrCode.USE_COUPON_STAT_ERROR.getCode());
            errContext.setErrorMsg(isCodeMode ? "优惠码为不可用状态" : "优惠券为不可用状态");
            return false;
        }
        return true;
    }

    /**
     * 时间校验(有效期的校验，无码券)
     *
     * @param couponBaseInfo 券基本信息
     * @param errContext     错误上下文
     * @return boolean
     */
    public boolean timeCheckerNoCode(CouponBaseInfo couponBaseInfo, ErrContext errContext, String couponModeType) {
        boolean isCodeMode = ModeTypeEnum.Code.getMysqlValue().equals(couponModeType);
        long nowTime = TimeUtil.getNowUnixSecond();
        Long startTime = couponBaseInfo.getStartTime();
        Long endTime = couponBaseInfo.getEndTime();
        if (startTime == null || startTime <= 0L) {
            errContext.setErrorCode(ErrCode.USE_COUPON_TIME_INVALID.getCode());
            errContext.setErrorMsg(isCodeMode ? "优惠码无效" : "优惠券无效");
            return false;
        }
        if (endTime == null || endTime <= 0L) {
            errContext.setErrorCode(ErrCode.USE_COUPON_TIME_INVALID.getCode());
            errContext.setErrorMsg(isCodeMode ? "优惠码无效" : "优惠券无效");
            return false;
        }
        if (startTime > nowTime) {
            errContext.setErrorCode(ErrCode.USE_COUPON_TIME_INVALID.getCode());
            errContext.setErrorMsg(isCodeMode ? "优惠码未到可用时间" : "优惠券未到可用时间");
            errContext.setErrorData(TimeUtil.formatSecond(startTime) + "~" + TimeUtil.formatSecond(endTime));
            return false;
        }
        if (endTime <= nowTime) {
            errContext.setErrorCode(ErrCode.USE_COUPON_TIME_INVALID.getCode());
            errContext.setErrorMsg(isCodeMode ? "优惠码已过期" : "优惠券已过期");
            errContext.setErrorData(TimeUtil.formatSecond(startTime) + "~" + TimeUtil.formatSecond(endTime));
            return false;
        }
        return true;
    }
}
