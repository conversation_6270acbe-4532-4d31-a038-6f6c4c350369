package com.xiaomi.nr.coupon.domain.coupontrade;

import com.xiaomi.nr.coupon.api.dto.trade.*;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public abstract class UserCouponService {

    /**
     * 优惠券结算
     */
    public abstract CheckoutCouponResModel checkoutCoupon(CheckoutCouponReqModel request)  throws BizError;

    /**
     * 优惠券锁定
     */
    public abstract LockCouponResponse lockUserCoupon(LockCouponRequest request)  throws BizError;

    /**
     * 优惠券使用
     */
    public abstract ConsumeCouponResponse consumeUserCoupon(ConsumeCouponRequest request)  throws BizError;

    /**
     * 优惠券锁券
     */
    public abstract RollbackCouponResponse rollbackUserCoupon(RollbackCouponRequest request)  throws BizError;

}
