package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 校验优惠类型
 *
 * <AUTHOR>
 * @date 2024/12/12
 */
@Slf4j
@Component
public class UserNoCodePromotionType extends CheckerAbstract {

    @Override
    public boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {

        if(!ctx.isCheckPromotionType()) {
            return true;
        }

        List<Integer> promotionTypeList = ctx.getPromotionTypeList();
        if (CollectionUtils.isEmpty(promotionTypeList)) {
            return true;
        }

        errCtx.setInvalidConfig(true);

        CouponConfigItem config = ctx.getConfigInfoMap().get(respItem.getConfigId());
        if (promotionTypeList.contains(config.getPromotionType().getCode())) {
            return true;
        }

        errCtx.setErrCode(ErrCode.USE_COUPON_PROMOTION_TYPE_ERROR.getCode());
        errCtx.setErrMsg("当前场景不可用");
        return false;
    }

}
