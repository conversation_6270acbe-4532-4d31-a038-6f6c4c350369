package com.xiaomi.nr.coupon.domain.couponconfig.model;

import com.google.gson.annotations.SerializedName;
import com.xiaomi.nr.coupon.api.dto.enums.GoodsExtTagEnum;
import lombok.Data;

/**
 * 商品扩展信息
 *
 * <AUTHOR>
 * @date 2024/1/3
 **/
@Data
public class GoodExt {

    /**
     * tag labor:工时，part:配件
     */
    @SerializedName("t")
    private String tag;

    /**
     * 数量，核销时可用券的工时或配件ssu个数
     */
    @SerializedName("c")
    private Integer count;

    public boolean isLaborSsu() {
        return GoodsExtTagEnum.LABOR.getTag().contains(tag);
    }

    public boolean isPartSsu() {
        return GoodsExtTagEnum.PART.getTag().contains(tag);
    }
}
