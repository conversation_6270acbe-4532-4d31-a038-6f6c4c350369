package com.xiaomi.nr.coupon.infrastructure.repository.mq.model;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @description 汽车权益核销计数消息
 * <AUTHOR>
 * @date 2025-01-08 19:46
*/
@Data
@Builder
public class CarEquityPerformanceMessage implements Serializable {
    private static final long serialVersionUID = 792159686111024694L;
    /**
     * 系统服务名，以现在各方系统的命名为准，全大写，如 WMS、ACTIVITY、COUPON
     */
    private String sourceSvc;

    /**
     * 权益的唯一标识，由服务权益中心提供
     */
    private String equityKey;

    /**
     * 用户ID
     */
    private Long mid;

    /**
     * VID
     */
    private String vid;

    /**
     * 优惠券 id、活动 id、仓储权益 id、道路救援传空字符串
     */
    private String implAbilityId;

    /**
     * 优惠券使用记录 id、仓储使用记录 id、代步车使用记录 id
     */
    private String usageRecordId;

    /**
     * 使用记录对应的订单 id，如使用对应优惠券的对应订单 id，没有则不传
     */
    private Long usageOrderId;

    /**
     * 使用记录对应的超级工单号，ST开头，没有则不传
     */
    private String usageStNo;

    /**
     * 使用权益的开始时间，毫秒级时间戳，没有则不传
     */
    private Long usageStartTime;

    /**
     * 使用权益的结束时间，毫秒级时间戳，没有则不传
     */
    private Long usageEndTime;

    /**
     * 是否删除对应使用记录，使用权益时传 false，逆向（如取消订单）时传 true，不传默认 false
     */
    private Boolean deleted;

    /**
     * 补充信息
     */
    private String ext;
}
