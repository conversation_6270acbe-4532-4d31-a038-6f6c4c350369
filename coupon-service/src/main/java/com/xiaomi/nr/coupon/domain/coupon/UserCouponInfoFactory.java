package com.xiaomi.nr.coupon.domain.coupon;

import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.coupon.impl.CarCouponInfo;
import com.xiaomi.nr.coupon.domain.coupon.impl.UserCouponInfoImpl;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/21
 */
@Component
public class UserCouponInfoFactory {

    @Autowired
    private CarCouponInfo carCouponInfo;

    @Autowired
    private UserCouponInfoImpl userCouponInfo;

    public AbstractUserCouponInfo getUserCouponInfo(Integer bizPlatform) throws BizError {

        BizPlatformEnum bizPlatformEnum = BizPlatformEnum.valueOf(bizPlatform);
        if (Objects.isNull(bizPlatformEnum)) {
            throw ExceptionHelper.create(GeneralCodes.NotFound, String.format("未找到匹配的业务领域：%d", bizPlatform));
        }

        return BizPlatformEnum.CAR_AFTER_SALE.equals(bizPlatformEnum) ? carCouponInfo : userCouponInfo;
    }

}
