package com.xiaomi.nr.coupon.domain.coupon.impl;

import com.xiaomi.nr.coupon.api.dto.coupon.info.CouponInfoRequest;
import com.xiaomi.nr.coupon.domain.coupon.AbstractUserCouponInfo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave.CarCouponInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/21
 */
@Component
@Slf4j
public class CarCouponInfo extends AbstractUserCouponInfo {

    @Resource
    private CarCouponInfoMapper carCouponInfoMapper;

    /**
     * 获取优惠券信息
     *
     * @param req 入参
     * @return 优惠券信息
     */
    @Override
    public List<CouponPo> getCouponInfo(CouponInfoRequest req) {

        String vid = req.getVid();
        List<Long> couponIds = req.getCouponIds();
        return carCouponInfoMapper.batchGetCouponInfo(vid, couponIds);
    }
}
