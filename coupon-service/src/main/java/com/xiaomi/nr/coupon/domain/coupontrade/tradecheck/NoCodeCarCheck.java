package com.xiaomi.nr.coupon.domain.coupontrade.tradecheck;

import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.coupontrade.TradeCheckAbstract;
import com.xiaomi.nr.coupon.domain.coupontrade.convert.CouponBaseInfoConvert;
import com.xiaomi.nr.coupon.domain.coupontrade.entity.TradeCheckContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 销核结算-无码券-整车销售
 *
 * <AUTHOR>
 * @date 2024/1/5
 */
@Component
public class NoCodeCarCheck extends TradeCheckAbstract {

    @Resource
    private CouponBaseInfoConvert couponBaseInfoConvert;

    @Resource
    private CouponInfoCheck couponInfoCheck;

    @Override
    protected Integer getFactoryKey() {
        return BizPlatformEnum.CAR.getCode();
    }

    @Override
    protected boolean initData(TradeCheckContext ctx) throws Exception {
        ctx.setCouponBaseInfo(couponBaseInfoConvert.convertNoCodeCouponPoConfig2BaseInfo(ctx.getCouponPo(), ctx.getConfig()));
        ctx.setCouponGroupNo(couponBaseInfoConvert.genCouponGroupNo(ctx.getConfig()));
        ctx.setCouponOrgCode(couponInfoCheck.getCouponOrgCode(ctx.getCouponPo().getExtendInfo()));
        return true;
    }

    @Override
    protected boolean check(TradeCheckContext ctx) {
        // 公共校验
        if (!super.commonCheck(ctx)) {
            return false;
        }

        // 校验商品
        if (!couponInfoCheck.findGoodsInclude(ctx.getConfig(), ctx.getRequestModel(), ctx.getErrContext(), ctx.getCouponModeType())) {
            return false;
        }
        return true;
    }

    @Override
    protected void checkAfter(TradeCheckContext ctx) {
        ctx.getCheckoutCouponInfo().setValidSsuList(couponBaseInfoConvert.getValidSsuList(ctx.getConfig(), ctx.getRequestModel().getSkuPackageList()));
    }

}
