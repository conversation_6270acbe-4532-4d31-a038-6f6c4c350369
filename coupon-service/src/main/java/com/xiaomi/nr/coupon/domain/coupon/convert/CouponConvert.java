package com.xiaomi.nr.coupon.domain.coupon.convert;

import com.xiaomi.nr.coupon.api.dto.coupon.*;
import com.xiaomi.nr.coupon.api.dto.coupon.info.CouponInfoItemDto;
import com.xiaomi.nr.coupon.api.dto.coupon.info.UseTermDto;
import com.xiaomi.nr.coupon.api.dto.coupon.list.UserCouponListDto;
import com.xiaomi.nr.coupon.api.dto.couponcode.GetCouponCodeInfoRequest;
import com.xiaomi.nr.coupon.api.dto.couponcode.GetCouponCodeInfoResponse;
import com.xiaomi.nr.coupon.api.dto.couponconfig.PromotionVO;
import com.xiaomi.nr.coupon.api.dto.trade.CheckoutCouponReqModel;
import com.xiaomi.nr.coupon.api.dto.trade.GetCheckoutCouponListV2Request;
import com.xiaomi.nr.coupon.constant.CommonConstant;
import com.xiaomi.nr.coupon.constant.CouponConfigConstant;
import com.xiaomi.nr.coupon.constant.CouponConstant;
import com.xiaomi.nr.coupon.constant.CouponTagConst;
import com.xiaomi.nr.coupon.domain.common.FormatCouponUtil;
import com.xiaomi.nr.coupon.domain.coupon.model.CheckoutCouponModel;
import com.xiaomi.nr.coupon.domain.coupon.model.CouponCodeQueryParam;
import com.xiaomi.nr.coupon.domain.coupon.model.CouponInfoModel;
import com.xiaomi.nr.coupon.domain.couponcode.model.SingleExchangeRequestDo;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigInfo;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherGoodsItemDo;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.coupon.CouponUseChannelEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.*;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponcode.po.CouponCodePo;
import com.xiaomi.nr.coupon.util.AesUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.xiaomi.nr.coupon.constant.CouponConstant.SEND_SCENE_CAR_SHOP_GIFT_EXCHANGE;

/**
 * 优惠券信息转换类
 */
@Slf4j
@Service
public class CouponConvert {
    private static final String AES_PASSWORD = "M6EED09BCC6539AD";

    /********************************************* po -> model ***************************************************/

    /**
     * 初始化单张优惠券基础信息, po -> model
     *
     * @param couponPo    用户优惠券基础信息
     * @param configCache 券配置缓存信息
     * @return CouponModel 优惠券Model信息
     */
    public CouponInfoModel convertPoToModel(CouponPo couponPo, CouponConfigItem configCache) throws BizError {
        if (!checkCouponPo(couponPo, configCache)) {
            return null;
        }

        //CouponPo信息
        String originStatus = couponPo.getStat();
        Long endTime = Long.parseLong(couponPo.getEndTime() == null ? CommonConstant.ZERO_STR : couponPo.getEndTime());
        String status = getCouponStatus(originStatus, endTime);

        //couponConfig信息
        CouponConfigInfo couponConfigInfo = configCache.getCouponConfigInfo();
        Integer configStatus = couponConfigInfo.getStatus();
        Integer useType = configCache.getPromotionType().getCode();
        Map<String, String> showMap = FormatCouponUtil.formatCouponShowValue(useType, couponConfigInfo.getPromotionValue());
        List<String> useChannel = new LinkedList<>();
        for (Integer channelCode : configCache.getUseChannelStore().keySet()) {
            useChannel.add(CouponUseChannelEnum.findValueById(channelCode));
        }
        CouponInfoModel couponInfo = new CouponInfoModel();
        couponInfo.setId(couponPo.getId());
        couponInfo.setStatus(status);
        couponInfo.setStatusDesc(CouponStatusEnum.findNameByValue(status));
        couponInfo.setOriginStatus(originStatus);
        couponInfo.setStartTime(Long.parseLong(couponPo.getStartTime() == null ? CommonConstant.ZERO_STR : couponPo.getStartTime()));
        couponInfo.setEndTime(endTime);
        couponInfo.setFromCouponId(couponPo.getParentId());
        couponInfo.setFromOrderId(recoverFromOrderId(couponPo.getFromOrderId()));
        couponInfo.setUseOrderId(couponPo.getOrderId());
        couponInfo.setUseTime(couponPo.getUseTime());
        couponInfo.setAddTime(couponPo.getAddTime());
        couponInfo.setConfigId(couponPo.getTypeId());
        couponInfo.setConfigStatus(configStatus);
        couponInfo.setConfigStatusDesc(ConfigStatusEnum.findNameByValue(configStatus));
        couponInfo.setConfigName(couponConfigInfo.getName());
        couponInfo.setRangeDesc(couponConfigInfo.getCouponDesc());
        couponInfo.setShowValue(showMap.get(CouponConfigConstant.SHOW_VALUE));
        couponInfo.setShowUnit(showMap.get(CouponConfigConstant.SHOW_UNIT));
        couponInfo.setIsPostFree(configCache.getExtPropInfo().postFree());
        couponInfo.setIsCheckPrice(configCache.getExtPropInfo().checkPrice());
        couponInfo.setIsCheckPackage(configCache.getExtPropInfo().checkPackage());
        couponInfo.setIsShare(configCache.getExtPropInfo().shareAble());
        couponInfo.setCheckPrice(configCache.getExtPropInfo().getCheckPrice());
        couponInfo.setPostFree(configCache.getExtPropInfo().getPostFree());
        couponInfo.setUseType(useType);
        couponInfo.setUseChannel(useChannel);
        couponInfo.setSendChannel(couponConfigInfo.getSendScene());
        couponInfo.setDeductType(configCache.getDeductType().getRedisValue());
        couponInfo.setQuotaType(couponConfigInfo.getBottomType());
        couponInfo.setQuotaCount(couponConfigInfo.getBottomCount());
        couponInfo.setQuotaMoney(couponConfigInfo.getBottomPrice());
        couponInfo.setReduceMoney(couponConfigInfo.getPromotionValue());
        couponInfo.setReduceDiscount(couponConfigInfo.getPromotionValue() / 10);
        couponInfo.setReduceMaxPrice(couponConfigInfo.getMaxReduce());
        if (Objects.equals(PromotionType.NyuanBuy.getCode(), useType)) {
            couponInfo.setDeductTargetGoods(configCache.getGoodScope());
        }
        //会员券打标
        if (configCache.getExtPropInfo().getProMember() == 1) {
            couponInfo.setTags(Collections.singletonList(PromotionType.ProMemberCoupon.getValue()));
        }
        couponInfo.setCouponType(couponConfigInfo.getCouponType());
        couponInfo.setShipmentId(couponConfigInfo.getShipmentId());
        couponInfo.setServiceType(couponPo.getServiceType());
        couponInfo.setBizPlatform(couponPo.getBizPlatform());

        PromotionVO promotionVO = getPromotionVO(configCache.getPromotionType(), couponConfigInfo);
        couponInfo.setPromotionVO(promotionVO);

        couponInfo.setSendScene(couponConfigInfo.getSendScene());
        couponInfo.setCheckoutStage(configCache.getExtPropInfo().getCheckoutStage());
        couponInfo.setDisplayDate(configCache.getExtPropInfo().getDisplayDate());

        return couponInfo;
    }

    /**
     * 获取优惠规则VO
     *
     * @param promotionType     优惠类型
     * @param couponConfigInfo  优惠券信息
     * @return  优惠规则VO
     */
    private PromotionVO getPromotionVO(PromotionType promotionType, CouponConfigInfo couponConfigInfo) {
        PromotionVO promotionVO = new PromotionVO();
        promotionVO.setPromotionType(promotionType.getCode());
        promotionVO.setPromotionValue(couponConfigInfo.getPromotionValue());
        promotionVO.setBottomPrice(couponConfigInfo.getBottomPrice());
        promotionVO.setBottomCount(couponConfigInfo.getBottomCount());
        promotionVO.setBottomType(couponConfigInfo.getBottomType());
        promotionVO.setMaxReduce(couponConfigInfo.getMaxReduce());
        return promotionVO;
    }


    /********************************************* model -> dto ***************************************************/

    /**
     * 优惠券基础信息 CouponInfoItemDto (model -> dto)
     *
     * @param couponInfoModel 优惠券基础信息
     * @param supplier        子类私有属性处理
     * @return CouponInfoItem 优惠券信息Dto
     */
    public CouponInfoItemDto convert2CouponInfoDto(CouponInfoModel couponInfoModel, Supplier<? extends CouponInfoItemDto> supplier) {
        if (Objects.isNull(couponInfoModel)) {
            return null;
        }

        String originStatus = couponInfoModel.getOriginStatus();
        Boolean isShare = couponInfoModel.getIsShare();

        //处理子类私有属性
        CouponInfoItemDto couponInfoDto = supplier.get();
        //处理父类公共属性
        couponInfoDto.setCouponId(couponInfoModel.getId());
        couponInfoDto.setShowValue(couponInfoModel.getShowValue());
        couponInfoDto.setStartTime(couponInfoModel.getStartTime());
        couponInfoDto.setEndTime(couponInfoModel.getEndTime());
        couponInfoDto.setAddTime(couponInfoModel.getAddTime());
        couponInfoDto.setUseOrderId(couponInfoModel.getUseOrderId());
        couponInfoDto.setUseTime(couponInfoModel.getUseTime());
        couponInfoDto.setCouponName(couponInfoModel.getConfigName());
        couponInfoDto.setStatus(couponInfoModel.getStatus());
        couponInfoDto.setStatusDesc(couponInfoModel.getStatusDesc());
        couponInfoDto.setOriginStatus(originStatus);
        couponInfoDto.setIsShare(isShare);
        couponInfoDto.setGiftSendStatus(getCouponGiftSend(originStatus, isShare));
        couponInfoDto.setFromCouponId(couponInfoModel.getFromCouponId());
        couponInfoDto.setFromOrderId(couponInfoModel.getFromOrderId());
        couponInfoDto.setCouponType(couponInfoModel.getCouponType());
        couponInfoDto.setUseType(PromotionType.getValueByCode(couponInfoModel.getUseType()));
        couponInfoDto.setShipmentId(couponInfoModel.getShipmentId());
        couponInfoDto.setConfigId(couponInfoModel.getConfigId());
        couponInfoDto.setIsOneGoodsUse(couponInfoModel.getIsOneGoodsUse());
        couponInfoDto.setOneGoodsUseInfoDto(couponInfoModel.getOneGoodsUseInfoDto());
        couponInfoDto.setUseChannelDesc(couponInfoModel.getUseChannelDesc());
        couponInfoDto.setRangeDesc(couponInfoModel.getRangeDesc());
        couponInfoDto.setShowUnit(couponInfoModel.getShowUnit());
        couponInfoDto.setCustomDetail(couponInfoModel.getCustomRuleDetail());
        Integer serviceType = couponInfoModel.getServiceType();
        couponInfoDto.setServiceType(serviceType);
        couponInfoDto.setServiceTypeName(Optional.ofNullable(serviceType).map(CouponServiceTypeEnum::valueOf).map(CouponServiceTypeEnum::getName).orElse(null));
        couponInfoDto.setBizPlatform(couponInfoModel.getBizPlatform());
        couponInfoDto.setPromotionVO(couponInfoModel.getPromotionVO());
        couponInfoDto.setIsDisplayDate(Objects.equals(couponInfoModel.getDisplayDate(), CouponConstant.DISPLAY_DATE));

        return couponInfoDto;
    }


    /**
     * 用户优惠券列表信息 UserCouponListDto (model->dto)
     *
     * @param couponInfoModel 优惠券基础信息(model)
     * @return UserCouponListDto 用户优惠券列表基础信息(dto)
     */
    public UserCouponListDto convert2UserCouponListDto(CouponInfoModel couponInfoModel) {
        if (Objects.isNull(couponInfoModel)) {
            return null;
        }

        //公有属性调用父类公共转换方法处理
        return (UserCouponListDto) convert2CouponInfoDto(couponInfoModel, () -> {
            UserCouponListDto userCouponListDto = new UserCouponListDto();

            //子类特有属性单独赋值
            userCouponListDto.setIsCheckPrice(couponInfoModel.getIsCheckPrice());
            userCouponListDto.setUseChannelGroup(couponInfoModel.getUseChannelGroup());

            // 标签处理
            if (SEND_SCENE_CAR_SHOP_GIFT_EXCHANGE.equals(couponInfoModel.getSendScene())) {
                userCouponListDto.getTags().add(CouponTagConst.CAR_OWNER_GIFT_EXCHANGE);
            }

            return userCouponListDto;
        });
    }


    /**
     * 用户优惠券列表信息 UserCouponListDto (model->dto)
     *
     * @param couponInfoModel 优惠券基础信息(model)
     * @return UserCouponListDto 用户优惠券列表基础信息(dto)
     */
    public CouponList convert2CouponListDto(CouponInfoModel couponInfoModel, CouponEventInfo event) {
        if (Objects.isNull(couponInfoModel)) {
            return null;
        }

        //公有属性调用父类公共转换方法处理
        String showValue = couponInfoModel.getShowValue();
        String originStatus = couponInfoModel.getOriginStatus();
        Boolean isShare = couponInfoModel.getIsShare();
        CouponList couponList = new CouponList();

        couponList.setCouponId(couponInfoModel.getId());
        couponList.setValue(showValue);
        couponList.setBeginTime(couponInfoModel.getStartTime());
        couponList.setEndTime(couponInfoModel.getEndTime());
        couponList.setCouponName(couponInfoModel.getConfigName());
        couponList.setStat(couponInfoModel.getStatus());
        couponList.setStatOrigin(originStatus);
        couponList.setGiftSend(getCouponGiftSend(originStatus, isShare));
        couponList.setParentId(couponInfoModel.getFromCouponId());
        couponList.setIsShare(isShare);
        couponList.setType(PromotionType.getValueByCode(couponInfoModel.getUseType()));
        couponList.setTypeId(couponInfoModel.getConfigId());
        couponList.setOrderId(couponInfoModel.getUseOrderId());
        couponList.setUseTime(couponInfoModel.getUseTime());
        couponList.setIsAvailable(true);
        couponList.setIsOneGoodsUse(couponInfoModel.getIsOneGoodsUse());
        couponList.setAppTag(couponInfoModel.getUseChannelGroup());
        couponList.setAppTagInfo(couponInfoModel.getUseChannelDesc());
        couponList.setShowUnit(couponInfoModel.getShowUnit());
        couponList.setShowTitle(showValue);
        couponList.setCustomRuleIndex(couponInfoModel.getCustomRuleDetail().getRuleIndex());
        couponList.setCustomRuleDetails(Collections.singletonList(couponInfoModel.getCustomRuleDetail()));
        couponList.setAddTime(couponInfoModel.getAddTime());
        couponList.setCheckPrice(couponInfoModel.getCheckPrice());
        couponList.setAdditional(couponInfoModel.getPostFree());
        couponList.setCouponType(couponInfoModel.getCouponType());
        couponList.setShipmentId(couponInfoModel.getShipmentId());
        couponList.setDeductType(DeductTypeEnum.findCodeByValue(couponInfoModel.getDeductType()));
        couponList.setTags(couponInfoModel.getTags());

        //子类特有属性单独赋值
        couponList.setCustomRuleIndex(couponInfoModel.getCustomRuleDetail().getRuleIndex());
        if (Objects.equals(CouponTypeEnum.PostFee.getValue(), couponInfoModel.getCouponType())) {
            String postFreeStr = "仅小米之家门店闪送可用。";
            couponList.setRangeDesc(postFreeStr + couponInfoModel.getRangeDesc());
            couponList.setRangeShortDesc(postFreeStr + couponInfoModel.getRangeDesc());
            couponList.setUsableRange(postFreeStr + couponInfoModel.getRangeDesc());
        } else {
            couponList.setRangeDesc(couponInfoModel.getUseChannelDesc() + "。" + couponInfoModel.getRangeDesc());
            couponList.setRangeShortDesc(couponInfoModel.getUseChannelDesc() + "。" + couponInfoModel.getRangeDesc());
            couponList.setUsableRange(couponInfoModel.getUseChannelDesc() + "。" + couponInfoModel.getRangeDesc());
        }
        Set<String> availableChannel = new HashSet<>();
        if (CollectionUtils.isNotEmpty(couponInfoModel.getUseChannel())) {
            for (String useChannel : couponInfoModel.getUseChannel()) {
                if (StringUtils.equals(UseChannelEnum.MiShop.getValue(), useChannel)) {
                    availableChannel.add("MISHOP");
                    continue;
                }

                if (StringUtils.equals(UseChannelEnum.MiHome.getValue(), useChannel)) {
                    availableChannel.add("MIHOME");
                }
            }
        }
        couponList.setAvailableChannel(availableChannel);

        if (!Objects.isNull(event)) {
            couponList.setBeginTime(event.getStartTime());
            couponList.setEndTime(event.getEndTime());
            couponList.setActTag(event.getActTag());
            couponList.setCouponTag(event.getCouponTag());
        }

        return couponList;
    }


    /********************************************* convert utils ***************************************************/

    /**
     * 转换发券订单号
     *
     * @param fromOrderId String
     * @return String
     */
    private String recoverFromOrderId(String fromOrderId) {
        //表里的默认值为0，接口输出的时候要转一下
        if (CommonConstant.ZERO_STR.equals(fromOrderId)) {
            return CommonConstant.EMPTY_STR;
        }
        return fromOrderId;
    }


    /**
     * 校验券信息和券配置信息是否合法
     *
     * @param couponPo    优惠券信息
     * @param configCache 券配置缓存信息
     * @return bool       true:通过, false:不通过
     */
    private boolean checkCouponPo(CouponPo couponPo, CouponConfigItem configCache) {

        if (Objects.isNull(couponPo)) {
            log.info("coupon.couponConvert.checkCouponPo(), couponPO不合法, couponPo=null");
            return false;
        }

        if (Objects.isNull(configCache)) {
            log.info("coupon.couponConvert.checkCouponPo(), configCache不合法, configCache=null");
            return false;
        }

        if (couponPo.getId() == null || couponPo.getId() < CommonConstant.ONE_LONG) {
            log.info("coupon.CouponCommonServiceImpl.checkCouponPo(), couponId不合法, couponPo={}", couponPo);
            return false;
        }

        if (configCache.getConfigId() < CommonConstant.ONE_LONG) {
            log.info("coupon.CouponCommonServiceImpl.checkCouponPo(), 券配置ID不合法, couponConfigCache={}", configCache);
            return false;
        }
        return true;
    }


    /**
     * 格式化状态字段
     * locked, presented, received 均属于已使用状态;
     * expired && 未使用但结束时间小于当前时间,算是过期状态
     *
     * @param originStatus 优惠券原始状态
     * @param endTime      优惠券结束时间
     * @return String      转换后的优惠券状态
     */
    private String getCouponStatus(String originStatus, Long endTime) {

        //判断优惠券是否是已使用状态
        if (isCouponUsed(originStatus)) {
            return CouponStatusEnum.USED.getValue();
        }

        //判断优惠券是否是过期状态
        if (isCoupopnExpired(originStatus, endTime)) {
            return CouponStatusEnum.EXPIRED.getValue();
        }
        return originStatus;
    }


    /**
     * 判断券是否已使用
     *
     * @param originStatus 优惠券原始状态
     * @return bool        true:已使用，false:非已使用
     */
    private Boolean isCouponUsed(String originStatus) {
        Map<String, Integer> usedStatMap = new HashMap<>();
        usedStatMap.put(CouponStatusEnum.LOCKED.getValue(), CommonConstant.ONE_INT);
        usedStatMap.put(CouponStatusEnum.PRESENTED.getValue(), CommonConstant.ONE_INT);
        usedStatMap.put(CouponStatusEnum.RECEIVED.getValue(), CommonConstant.ONE_INT);

        return usedStatMap.containsKey(originStatus);
    }


    /**
     * 判断券是否过期
     *
     * @param originStatus 优惠券原始状态
     * @param endTime      优惠券结束时间
     * @return bool        true:已过期，false:非已过期
     */
    private Boolean isCoupopnExpired(String originStatus, long endTime) {
        long nowTme = TimeUtil.getNowUnixSecond();
        return StringUtils.equals(CouponStatusEnum.EXPIRED.getValue(), originStatus) ||
                (StringUtils.equals(CouponStatusEnum.UNUSED.getValue(), originStatus) && endTime < nowTme);
    }


    /**
     * 获得券的赠送状态
     *
     * @param stat    优惠券状态
     * @param isShare 是否可分享(true:可分享，false:不可分享)
     * @return int    0:未赠送，1:已赠送，2:不可赠送，3:已领取
     */
    private int getCouponGiftSend(String stat, boolean isShare) {
        //不可赠送
        if (!isShare) {
            return CommonConstant.TWO_INT;
        }
        CouponStatusEnum statEnum = CouponStatusEnum.findByValue(stat);
        if (Objects.isNull(statEnum)) {
            return CommonConstant.ZERO_INT; //未赠送
        }
        switch (Objects.requireNonNull(statEnum)) {
            case PRESENTED:
                return CommonConstant.ONE_INT; //已赠送
            case RECEIVED:
                return CommonConstant.THREE_INT; //已领取
            default:
                return CommonConstant.ZERO_INT; //未赠送
        }
    }


    public CouponInfoModel convertPoToModelItem(CouponConfigItem configCache, CouponEventInfo eventInfo) throws BizError {
        if (Objects.isNull(configCache)) {
            return null;
        }

        CouponConfigInfo couponConfigInfo = configCache.getCouponConfigInfo();
        Integer configStatus = couponConfigInfo.getStatus();
        Integer useType = configCache.getPromotionType().getCode();
        Map<String, String> showMap = FormatCouponUtil.formatCouponShowValue(useType, couponConfigInfo.getPromotionValue());
        List<String> useChannel = new LinkedList<>();
        for (Integer channelCode : configCache.getUseChannelStore().keySet()) {
            useChannel.add(CouponUseChannelEnum.findValueById(channelCode));
        }

        CouponInfoModel couponInfo = new CouponInfoModel();
        couponInfo.setConfigId(Long.parseLong(String.valueOf(configCache.getConfigId())));
        couponInfo.setConfigStatus(configStatus);
        couponInfo.setConfigStatusDesc(ConfigStatusEnum.findNameByValue(configStatus));
        couponInfo.setConfigName(couponConfigInfo.getName());
        couponInfo.setRangeDesc(couponConfigInfo.getCouponDesc());
        couponInfo.setShowValue(showMap.get(CouponConfigConstant.SHOW_VALUE));
        couponInfo.setShowUnit(showMap.get(CouponConfigConstant.SHOW_UNIT));
        couponInfo.setIsPostFree(configCache.getExtPropInfo().postFree());
        couponInfo.setIsCheckPrice(configCache.getExtPropInfo().checkPrice());
        couponInfo.setCheckPrice(configCache.getExtPropInfo().getCheckPrice());
        couponInfo.setPostFree(configCache.getExtPropInfo().getPostFree());
        couponInfo.setIsCheckPackage(configCache.getExtPropInfo().checkPackage());
        couponInfo.setIsShare(configCache.getExtPropInfo().shareAble());
        couponInfo.setUseType(useType);
        couponInfo.setUseChannel(useChannel);
        couponInfo.setSendChannel(couponConfigInfo.getSendScene());
        couponInfo.setDeductType(configCache.getDeductType().getRedisValue());
        couponInfo.setQuotaType(couponConfigInfo.getBottomType());
        couponInfo.setQuotaCount(couponConfigInfo.getBottomCount());
        couponInfo.setQuotaMoney(couponConfigInfo.getBottomPrice());
        couponInfo.setReduceMoney(couponConfigInfo.getPromotionValue());
        couponInfo.setReduceDiscount(couponConfigInfo.getPromotionValue() / 10);
        couponInfo.setReduceMaxPrice(couponConfigInfo.getMaxReduce());
        if (Objects.equals(PromotionType.NyuanBuy.getCode(), useType)) {
            couponInfo.setDeductTargetGoods(configCache.getGoodScope());
        }
        couponInfo.setCouponType(couponConfigInfo.getCouponType());
        couponInfo.setShipmentId(couponConfigInfo.getShipmentId());
        //会员券打标
        if (configCache.getExtPropInfo().getProMember() == 1) {
            couponInfo.setTags(Collections.singletonList(PromotionType.ProMemberCoupon.getValue()));
        }
        if (!Objects.isNull(eventInfo) && !Objects.isNull(eventInfo.getStartTime()) && eventInfo.getStartTime() > CommonConstant.ZERO_LONG) {
            couponInfo.setStartTime(eventInfo.getStartTime());
        } else {
            couponInfo.setStartTime(couponConfigInfo.getStartFetchTime());
        }
        if (!Objects.isNull(eventInfo) && !Objects.isNull(eventInfo.getEndTime()) && eventInfo.getEndTime() > CommonConstant.ZERO_LONG) {
            couponInfo.setEndTime(eventInfo.getEndTime());
        } else {
            couponInfo.setEndTime(couponConfigInfo.getEndFetchTime());
        }

        // 使用有效期
        UseTermDto useTermDto = new UseTermDto();
        useTermDto.setUseTimeType(couponConfigInfo.getUseTimeType());
        useTermDto.setStartUseTime(couponConfigInfo.getStartUseTime());
        useTermDto.setEndUseTime(couponConfigInfo.getEndUseTime());
        useTermDto.setUseDuration(couponConfigInfo.getUseDuration());
        couponInfo.setUseTermDto(useTermDto);

        // 优惠规则
        PromotionVO promotionVO = getPromotionVO(configCache.getPromotionType(), couponConfigInfo);
        couponInfo.setPromotionVO(promotionVO);

        return couponInfo;
    }

    /**
     * 转换结算优惠券列表至结算券model
     *
     * @param request CheckoutCouponListRequest
     * @return CheckoutCouponModel
     */
    public CheckoutCouponModel convertCheckoutCouponListToModel(CheckoutCouponListRequest request) {
        CheckoutCouponModel result = new CheckoutCouponModel();
        result.setFromPriceProtect(false);
        result.setUserId(request.getUserId());
        result.setOrgCode(request.getOrgCode());
        result.setClientId(request.getClientId());
        result.setCityId(request.getCityId());
        result.setShipmentId(request.getShipmentId());
        result.setShoppingMode(request.getShoppingMode());
        result.setSkuPackageList(request.getSkuPackageList());
        result.setOrgType(request.getOrgType());
        result.setBizPlatform(request.getBizPlatform());
        return result;
    }

    /**
     * 转换结算无码券列表至结算券model
     *
     * @param request CheckoutCouponListRequest
     * @return CheckoutCouponModel
     */
    public CheckoutCouponModel convertCheckoutRequestToModel(CheckoutCouponReqModel request) {
        CheckoutCouponModel result = new CheckoutCouponModel();
        result.setFromPriceProtect(request.isUsedCouponCheck());
        result.setBizPlatform(request.getBizPlatform());
        result.setUserId(request.getUserId());
        result.setVid(request.getVid());
        result.setOrgCode(request.getOrgCode());
        result.setClientId(request.getClientId());
        result.setCityId(request.getCityId());
        result.setShipmentId(request.getShipmentId());
        result.setShoppingMode(request.getShoppingMode());
        result.setSkuPackageList(request.getSkuPackageList());
        result.setOrgType(request.getOrgType());
        result.setPromotionTypeList(request.getPromotionTypeList());

        return result;
    }

    /**
     * 转换结算明码券至结算券model
     *
     * @param request CheckoutCouponListRequest
     * @return CheckoutCouponModel
     */
    public CheckoutCouponModel convertCheckoutCodeCouponToModel(CheckoutCheckerRequest request) {
        CheckoutCouponModel result = new CheckoutCouponModel();
        result.setFromPriceProtect(false);
        result.setUserId(request.getUserId());
        result.setOrgCode(request.getOrgCode());
        result.setClientId(request.getClientId());
        result.setCityId(request.getCityId());
        result.setShipmentId(request.getShipmentId());
        result.setShoppingMode(request.getShoppingMode());
        result.setSkuPackageList(request.getSkuPackageList());
        return result;
    }

    /**
     * 转换兑换优惠码参数至结算券model
     *
     * @param request CheckoutCouponListRequest
     * @return CheckoutCouponModel
     */
    public CheckoutCouponModel convertExchangeCodeCouponToModel(SingleExchangeRequestDo request) {
        CheckoutCouponModel result = new CheckoutCouponModel();
        result.setUserId(request.getUserId());
        result.setOrgCode(request.getOrgCode());
        result.setCityId(0L);
        result.setShipmentId(-1);
        result.setShoppingMode(0L);
        result.setSkuPackageList(new ArrayList<>());
        return result;
    }

    /**
     * 转换价保获取已用券信息参数至结算券model
     *
     * @param request GetUsedCouponRequest
     * @return CheckoutCouponReqModel
     */
    public CheckoutCouponReqModel convertGetUsedCouponReq2CheckoutModel(GetUsedCouponRequest request) {
        CheckoutCouponReqModel result = new CheckoutCouponReqModel();
        result.setUsedCouponCheck(true);
        result.setUserId(request.getUserId());
        result.setClientId(request.getClientId());
        result.setCouponIds(request.getCouponIds());
        result.setCouponCode(request.getCouponCode());
        result.setOrgCode(request.getOrgCode());
        result.setOrgType(request.getOrgType());
        result.setSkuPackageList(request.getSkuPackageList());
        result.setBizPlatform(request.getBizPlatform());
        return result;
    }

    /**
     * 转换结算用券信息参数至结算券model
     *
     * @param request GetUsedCouponRequest
     * @return CheckoutCouponReqModel
     */
    public CheckoutCouponReqModel convertCheckoutReq2CheckoutModel(GetCheckoutCouponListV2Request request) {
        CheckoutCouponReqModel result = new CheckoutCouponReqModel();
        result.setUsedCouponCheck(false);
        result.setUserId(request.getUserId());
        result.setBizPlatform(request.getBizPlatform().get(0));
        result.setVid(request.getVid());
        result.setClientId(request.getClientId());
        result.setCouponIds(request.getCouponIds());
        result.setCouponCode(request.getCouponCode());
        result.setOrgCode(request.getOrgCode());
        result.setOrgType(request.getOrgType());
        result.setCityId(request.getCityId());
        result.setShipmentId(request.getShipmentId());
        result.setShoppingMode(Objects.isNull(request.getShoppingMode()) ? 0L : request.getShoppingMode().longValue());
        result.setSkuPackageList(request.getSkuPackageList());
        result.setUsedCoupon(request.getUsedCoupon());
        result.setSubmitType(Optional.ofNullable(request.getSubmitType()).orElse(0));
        result.setPromotionTypeList(request.getPromotionTypeList());
        return result;
    }

    /**
     * convert CouponInfoModel list to UsableConfigItemDto map
     * @param params .
     * @return .
     */
    public Map<Long, UsableConfigItemDto> convertCouponInfoModelToUsableConfigItemDto(List<CouponInfoModel> params) {
        Map<Long, UsableConfigItemDto> result = new HashMap<>();
        for (CouponInfoModel p : params) {
            String rangeDesc = StringUtils.isNotEmpty(p.getRangeDesc()) ? p.getRangeDesc() : "";
            if(StringUtils.isNotEmpty(p.getUseChannelDesc())) {
                rangeDesc = p.getUseChannelDesc() + "。" + rangeDesc;
            }
            //去掉_ONLINE与_OFFLINE
            List<String> useChannelGroups = CollectionUtils.isNotEmpty(p.getUseChannelGroup()) ? p.getUseChannelGroup().stream().map(e -> e.contains("_ONLINE") ? e.replace("_ONLINE", "") : e.replace("_OFFLINE", "")).collect(Collectors.toList()) : Collections.emptyList();
            UsableConfigItemDto item = new UsableConfigItemDto();
            item.setConfigId(p.getConfigId());
            item.setName(p.getConfigName());
            item.setType(p.getCouponType());
            item.setFetchStartTime(p.getStartTime());
            item.setFetchEndTime(p.getEndTime());
            item.setShare(p.getIsShare());
            item.setPostFree(p.getIsPostFree());
            item.setCheckPrice(p.getIsCheckPrice());
            item.setRangeDesc(rangeDesc);
            item.setPromotionType(PromotionType.getValueByCode(p.getUseType()));
            item.setPromotionTypeCode(p.getUseType());
            item.setDeductType(DeductTypeEnum.findCodeByValue(p.getDeductType()));
            item.setDeductValue(p.getShowValue());
            item.setUseChannelGroup(useChannelGroups);
            item.setUseChannelDesc(p.getUseChannelDesc());
            item.setTags(p.getTags());
            item.setCustomRuleDetails(p.getCustomRuleDetail());
            if (CollectionUtils.isNotEmpty(p.getValidGoods())) {
                item.setValidSkuList(p.getValidGoods().stream().filter(e -> GoodsLevelEnum.Sku.getValue().equals(e.getLevel())).map(MatcherGoodsItemDo::getId).collect(Collectors.toList()));
                item.setValidPackageIdList(p.getValidGoods().stream().filter(e -> GoodsLevelEnum.Package.getValue().equals(e.getLevel())).map(MatcherGoodsItemDo::getId).collect(Collectors.toList()));
                item.setValidSsuList(p.getValidGoods().stream().filter(e -> GoodsLevelEnum.Ssu.getValue().equals(e.getLevel())).map(MatcherGoodsItemDo::getId).collect(Collectors.toList()));
            }
            item.setPromotionVO(p.getPromotionVO());
            item.setUseTermDto(p.getUseTermDto());
            result.put(p.getConfigId(), item);
        }
        return result;
    }

    /**
     * CouponCodePo转GetXGCouponCodeInfoResponse
     *
     * @param couponCodePo couponCodePo
     * @return GetXGCouponCodeInfoResponse
     */
    public GetCouponCodeInfoResponse toGetXGCouponCodeInfoResponse(CouponCodePo couponCodePo) throws Exception {
        if (couponCodePo == null) {
            return null;
        }

        GetCouponCodeInfoResponse resp = new GetCouponCodeInfoResponse();
        resp.setId(couponCodePo.getId());
        resp.setCouponCode(AesUtil.decrypt(AES_PASSWORD, couponCodePo.getCouponCode()));
        resp.setCouponIndex(couponCodePo.getCouponIndex());
        resp.setTypeId(couponCodePo.getTypeId());
        resp.setBatchId(couponCodePo.getBatchId());
        resp.setStartTime(couponCodePo.getStartTime());
        resp.setEndTime(couponCodePo.getEndTime());
        resp.setStat(couponCodePo.getStat());
        resp.setUserId(couponCodePo.getUserId());
        resp.setCouponId(couponCodePo.getCouponId());
        resp.setUseMode(couponCodePo.getUseMode());
        resp.setUseTime(couponCodePo.getUseTime());
        resp.setOrgCode(couponCodePo.getOrgCode());
        resp.setOrderId(couponCodePo.getOrderId());
        resp.setReplaceMoney(couponCodePo.getReplaceMoney());
        resp.setReduceExpress(couponCodePo.getReduceExpress());

        return resp;
    }

    /**
     * GetCouponCodeInfoRequest转CouponCodeQueryParam
     *
     * @param request GetCouponCodeInfoRequest
     * @return CouponCodeQueryParam
     */
    public CouponCodeQueryParam toCouponCodeQueryParam(GetCouponCodeInfoRequest request) {
        if (request == null) {
            return null;
        }

        CouponCodeQueryParam queryParam = new CouponCodeQueryParam();
        queryParam.setStat(request.getStat());
        queryParam.setSendType(request.getSendType());
        queryParam.setUseMode(request.getUseMode());
        queryParam.setOrderId(request.getOrderId());
        queryParam.setCouponIndex(DigestUtils.md5DigestAsHex(request.getCouponCode().getBytes()));

        return queryParam;
    }
}
