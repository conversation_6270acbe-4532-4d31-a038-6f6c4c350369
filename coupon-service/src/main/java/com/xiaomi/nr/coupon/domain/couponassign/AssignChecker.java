package com.xiaomi.nr.coupon.domain.couponassign;

import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.couponassign.model.SingleAssignRequestDo;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigInfo;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponSceneItem;
import com.xiaomi.nr.coupon.enums.couponconfig.*;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponassign.CouponAssignRedisDao;
import com.xiaomi.nr.coupon.util.NumberUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 发券检查
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AssignChecker {

    @Resource
    private CouponAssignRedisDao assignRedisDao;

    @Resource
    private AssignCommon assignCommon;

    /**
     * 参数校验
     *
     * @param request SingleAssignRequestDo
     * @throws BizError .
     */
    public void paramsChecker(SingleAssignRequestDo request) throws BizError {

        // 汽车服务券校验vid
        if (assignCommon.isCarVidCoupon(request.getBizPlatform())) {
            if (Strings.isBlank(request.getVid())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "必须传入VID");
            }
            if (request.getVid().length() != 17) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "VID不符合要求");
            }
        }
        // 非汽车服务券校验userId
        else {
            if (Objects.isNull(request.getUserId()) || request.getUserId() <= 0) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "小米ID不符合要求");
            }
        }

        if (Strings.isBlank(request.getAppId())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "appID不符合要求");
        }

        if (Strings.isBlank(request.getRequestId())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "请求ID不符合要求");
        } else if (request.getRequestId().trim().length() > 40) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "请求ID的长度不能超过40个字符");
        }

        if (Objects.isNull(BizPlatformEnum.valueOf(request.getBizPlatform()))) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "业务领域不符合要求");
        }

        if (Strings.isBlank(request.getSceneCode()) || request.getSceneCode().trim().length() != 32) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "投放场景编码不符合要求");
        }

        if (Objects.isNull(SceneAssignModeEnum.findNameByValue(request.getAssignMode()))) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "发放方式不符合要求");
        }

        if (Objects.isNull(request.getConfigId()) || request.getConfigId() <= 0) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "优惠券配置ID不符合发放要求");
        }

        if (isOfflineSceneCode(request.getSceneCode())) {
            if (isStoreManageSceneCode(request.getSceneCode())) {
                if (Objects.isNull(request.getShareUserId()) || request.getShareUserId() <= 0) {
                    throw ExceptionHelper.create(GeneralCodes.ParamError, "分享人小米ID不符合要求");
                }
            }
            if (Strings.isBlank(request.getOrgCode())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "门店ID不能为空");
            }
        }

        if (Objects.nonNull(request.getOrderId()) && request.getOrderId() < 0) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "发放关联的订单号不符合要求");
        }
    }

    /**
     * 是否为线下投放场景
     *
     * @param sceneCode String
     * @return Boolean
     */
    public Boolean isOfflineSceneCode(String sceneCode) {
        List<String> configs = new ArrayList<>();
        configs.add(SceneCodeEnum.StoreOrderGift.getValue());
        configs.add(SceneCodeEnum.StoreManager.getValue());
        configs.add(SceneCodeEnum.DiffBusiness.getValue());
        return configs.contains(sceneCode);
    }

    /**
     * 是否为店长券投放场景
     *
     * @param sceneCode String
     * @return Boolean
     */
    public Boolean isStoreManageSceneCode(String sceneCode) {
        return SceneCodeEnum.StoreManager.getValue().equals(sceneCode);
    }

    /**
     * 发放条件校验
     *
     * @param request    SingleAssignRequestDo
     * @param configInfo CouponConfigItem
     * @param sceneInfo  CouponSceneItem
     * @throws BizError .
     */
    public void conditionChecker(SingleAssignRequestDo request, CouponConfigItem configInfo, CouponSceneItem sceneInfo) throws BizError {
        //基本信息检查
        baseChecker(configInfo);

        //业务领域检查
        bizPlatformChecker(request.getBizPlatform(), configInfo.getBizPlatform());

        //投放场景检查
        sceneCodeChecker(request.getSceneCode(), configInfo.getCouponConfigInfo().getSendScene());

        //发放权限检查
        rightsChecker(request.getAppId(), sceneInfo.getAppIds());

        //券投放方式检查
        sceneSendModeChecker(sceneInfo);

        //券发放方式检查
        sceneAssignModeChecker(sceneInfo);

        //状态检查
        statusChecker(configInfo, sceneInfo);

        //时间检查
        timeChecker(configInfo, request.getStartUseTime(), request.getEndUseTime());
    }

    /**
     * 检查用户已达到最大可得券数量
     *
     * @param userId   long
     * @param configId long
     * @param maxCount int
     * @throws BizError .
     */
    public void userAssignCountChecker(long userId, long configId, int maxCount) throws BizError {
        try {
            int count = assignRedisDao.getUserAssignCount(userId, configId);
            if (count >= maxCount) {
                throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_USER_LIMITED, "用户可得券数量已达限制");
            }
        } catch (BizError e) {
            throw e;
        } catch (Exception e) {
            log.error("coupon.singleAssign, 检查用户已达到最大可得券数量遇到异常, error={}, userId={}, configId={}", e.getMessage(), userId, configId);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "检查用户可得券数量时遇到异常");
        }
    }

    /**
     * 检查vid已达到最大可得券数量
     *
     * @param vid      String
     * @param configId long
     * @param maxCount int
     * @throws BizError .
     */
    public void vidAssignCountChecker(String vid, long configId, int maxCount) throws BizError {
        try {
            int count = assignRedisDao.getVidAssignCount(vid, configId);
            if (count >= maxCount) {
                throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_USER_LIMITED, "VID可得券数量已达限制");
            }
        } catch (BizError e) {
            throw e;
        } catch (Exception e) {
            log.error("coupon.singleAssign, 检查VID已达到最大可得券数量遇到异常, error={}, vid={}, configId={}", e.getMessage(), vid, configId);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "检查VID可得券数量时遇到异常");
        }
    }


    /**
     * 检查券是否已达到最大可发放数量
     *
     * @param configId long
     * @param maxCount int
     * @throws BizError .
     */
    public void couponAssignedCountChecker(long configId, int maxCount) throws BizError {
        try {
            int count = assignRedisDao.getCouponAssignCount(configId);
            if (count >= maxCount) {
                throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_OVER_LIMITED, "发放数量已达限制");
            }
        } catch (BizError e) {
            throw e;
        } catch (Exception e) {
            log.error("coupon.singleAssign, 检查券是否已达到最大可发放数量遇到异常, error={}, configId={}", e.getMessage(), configId);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "检查发放数量时遇到异常");
        }
    }

    /**
     * 券投放方式检查
     *
     * @param config CouponSceneItem
     * @throws BizError .
     */
    private void sceneSendModeChecker(CouponSceneItem config) throws BizError {
        if (!SceneSendModeEnum.Coupon.getValue().equals(config.getSendMode())) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_SENDMODE_MISMATCH, "场景不支持当前投放方式");
        }
    }

    /**
     * 券发放方式检查(支持接口和灌券两种)
     *
     * @param config CouponSceneItem
     * @throws BizError .
     */
    private void sceneAssignModeChecker(CouponSceneItem config) throws BizError {
        if (!config.getAssignMode().contains(SceneAssignModeEnum.Interface.getValue()) && !config.getAssignMode().contains(SceneAssignModeEnum.Fill.getValue())) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_SENDMODE_MISMATCH, "场景不支持当前发放方式");
        }
    }

    /**
     * 状态检查
     *
     * @param config CouponConfigItem
     * @param scene  CouponSceneItem
     * @throws BizError .
     */
    private void statusChecker(CouponConfigItem config, CouponSceneItem scene) throws BizError {
        if (!ConfigStatusEnum.Online.getValue().equals(config.getCouponConfigInfo().getStatus())) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_STATUS_OFFLINE, "券配置已停止发放");
        }

        if (!SceneStatusEnum.Online.getValue().equals(scene.getSceneStatus())) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_SCENE_OFFLINE, "投放场景已停止发放");
        }
    }

    /**
     * 时间校验(券的真实有效时间)
     *
     * @param config CouponConfigItem
     */
    private void timeChecker(CouponConfigItem config, Long reqStartUseTime, Long reqEndUseTime) throws BizError {

        long nowTime = TimeUtil.getNowUnixSecond();

        CouponConfigInfo couponConfigInfo = config.getCouponConfigInfo();
        if(Objects.isNull(couponConfigInfo)) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_LOSE_EFFICACY, "未找到优惠券信息");
        }

        Long startFetchTime = couponConfigInfo.getStartFetchTime();
        Long endFetchTime = couponConfigInfo.getEndFetchTime();
        if (NumberUtil.isNegative(startFetchTime) || NumberUtil.isNegative(endFetchTime) || startFetchTime >= endFetchTime) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_LOSE_EFFICACY, "券配置的发放时间不符合要求");
        }

        long useEndTime = 0L;
        if (UseTimeTypeEnum.Relative.getValue().equals(couponConfigInfo.getUseTimeType())) {
            if (NumberUtil.isNegative(couponConfigInfo.getUseDuration())) {
                throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_LOSE_EFFICACY, "券的有效使用时间不符合要求");
            }
            useEndTime = nowTime + couponConfigInfo.getUseDuration() * 3600;
        } else if (UseTimeTypeEnum.Fixed.getValue().equals(couponConfigInfo.getUseTimeType())) {
            if (NumberUtil.isNegative(couponConfigInfo.getStartUseTime())
                    || NumberUtil.isNegative(couponConfigInfo.getEndUseTime())
                    || couponConfigInfo.getStartUseTime() >= couponConfigInfo.getEndUseTime()
            ) {
                throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_LOSE_EFFICACY, "券的有效使用时间区间不符合要求");
            }
            useEndTime = couponConfigInfo.getEndUseTime();
        } else if(UseTimeTypeEnum.CUSTOM.getValue().equals(couponConfigInfo.getUseTimeType())) {
            if (NumberUtil.isNegative(reqStartUseTime) || NumberUtil.isNegative(reqEndUseTime) || reqStartUseTime >= reqEndUseTime) {
                throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_LOSE_EFFICACY, "券的有效使用时间区间不符合要求");
            }
            useEndTime = reqEndUseTime;
        }

        if (startFetchTime > nowTime) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_NOSTART_TIME, "券可领取时间未开始");
        }

        if (endFetchTime < nowTime) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_OVER_TIME, "券可领取时间已结束");
        }

        if (useEndTime <= nowTime) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_OVER_TIME, "不能发放已过期的券");
        }
    }

    /**
     * 发放权限检查
     *
     * @param appId  String
     * @param appIds Set<String>
     * @throws BizError .
     */
    private void rightsChecker(String appId, Set<String> appIds) throws BizError {
        if (appIds == null || appIds.isEmpty()) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_APPID_NOAUTH, "投放场景无权限使用！");
        }
        if (!appIds.contains(appId)) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_APPID_NOAUTH, "没有权限发放此场景的券");
        }
    }

    /**
     * 业务领域检查
     *
     * @param bizPlatformByReq    Integer
     * @param bizPlatformByConfig Integer
     * @throws BizError .
     */
    private void bizPlatformChecker(Integer bizPlatformByReq, Integer bizPlatformByConfig) throws BizError {
        if (Objects.isNull(bizPlatformByReq) || Objects.isNull(bizPlatformByConfig)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "无效的业务领域");
        }
        if (Objects.isNull(BizPlatformEnum.valueOf(bizPlatformByReq)) || !bizPlatformByReq.equals(bizPlatformByConfig)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "没有权限发放此业务领域的券");
        }
    }

    /**
     * 投放场景检查
     *
     * @param requestSceneCode String
     * @param configSceneCode  String
     * @throws BizError .
     */
    private void sceneCodeChecker(String requestSceneCode, String configSceneCode) throws BizError {
        if (configSceneCode == null || configSceneCode.isEmpty()) {
            throw ExceptionHelper.create(ErrCode.COUPON, "无效的投放场景");
        }
        List<String> scenes = Arrays.asList(configSceneCode.split(","));
        if (!scenes.contains(requestSceneCode)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "没有权限发放此场景的券");
        }
    }

    /**
     * 基本检查
     *
     * @param configInfo CouponConfigItem
     * @throws BizError .
     */
    private void baseChecker(CouponConfigItem configInfo) throws BizError {
        if (Objects.isNull(configInfo) || configInfo.getConfigId() <= 0 || Objects.isNull(configInfo.getCouponConfigInfo())) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_NOT_FOUND, "无法获取到有效的券配置信息");
        }
        //投放渠道
        if (Strings.isBlank(configInfo.getCouponConfigInfo().getSendChannel())) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_LOSE_EFFICACY, "无效的投放渠道标识");
        }
    }
}
