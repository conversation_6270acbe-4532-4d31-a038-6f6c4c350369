package com.xiaomi.nr.coupon.infrastructure.repository;

import com.xiaomi.nr.coupon.api.dto.trade.CouponLockItem;
import com.xiaomi.nr.coupon.enums.coupon.CarCouponLogTypeEnum;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.coupon.OrderStatusEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CarCouponLogMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CarCouponMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CarCouponOptMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CarCouponLogPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponOptPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave.CarCouponListMapper;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 汽车专用券（目前只有汽车售后服务券在用）
 *
 * <AUTHOR>
 * @date 2024/3/6
 */
@Component
@Slf4j
public class CarCouponRepository {

    @Resource
    private CarCouponMapper carCouponMapper;

    @Resource
    private CarCouponOptMapper carCouponOptMapper;

    @Resource
    private CarCouponListMapper carCouponListMapper;

    @Resource
    private CarCouponLogMapper carCouponLogMapper;

    public List<CouponPo> getAllCouponForUnused(String vid, long nowTime, List<Integer> bizPlatformList) {
        return carCouponListMapper.getAllCouponForUnused(vid, nowTime, bizPlatformList);
    }

    /**
     * 获取该vid对应的券
     *
     * @param vid       vid
     * @param couponIds 优惠券ID列表
     * @return Map<Long, CouponPo>
     */
    public List<CouponPo> getCouponInfos(String vid, List<Long> couponIds) {
        return carCouponListMapper.batchGetCouponInfo(vid, couponIds);
    }

    /**
     * 根据券id获取优惠券
     * 核销使用 读主库 防止存库数据延迟异常
     *
     * @param vid
     * @param couponIds
     * @return
     * @throws BizError
     */
    public List<CouponPo> getCouponPoList(String vid, List<Long> couponIds) throws BizError {
        List<CouponPo> couponPos = carCouponMapper.getByCouponIdList(vid, couponIds);
        if (CollectionUtils.isEmpty(couponPos)) {
            throw ExceptionHelper.create(ErrCode.USE_COUPON_NOT_FOUND, "无可用优惠券");
        }
        if (couponPos.size() != couponIds.size()) {
            log.error("getCouponPoList fail. vid:{},couponIds:{},couponCnt:{}", vid, couponIds, couponPos.size());
            throw ExceptionHelper.create(ErrCode.USE_COUPON_NOT_FOUND, "获取优惠券数量不一致");
        }
        return couponPos;
    }

    public CouponOptPo getByCouponOpt(String vid, long orderId, int optType) {
        return carCouponOptMapper.getByCouponOpt(vid, orderId, optType);
    }

    /**
     * 锁定优惠券
     *
     * @param vid
     * @param userId
     * @param orderId
     * @param couponItems
     * @param offline
     * @throws Exception
     */
    @Transactional(value = "xmPulseWriteTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void lockCoupon(String vid, long userId, long orderId, List<CouponLockItem> couponItems, Integer offline) throws BizError {
        CouponOptPo couponOptPo = carCouponOptMapper.getByCouponOpt(vid, orderId, OrderStatusEnum.ROLLBACK.getType());
        if (couponOptPo != null) {
            log.warn("lockCoupon abandon. vid:{}, orderId:{}", vid, orderId);
            return;
        }

        //锁券
        for (CouponLockItem couponItem : couponItems) {
            int affected = carCouponMapper.lockCoupon(couponItem.getCouponId(), vid, CouponStatusEnum.UNUSED.getValue(), CouponStatusEnum.LOCKED.getValue(), orderId, couponItem.getReplaceMoney(), couponItem.getReduceExpress(), offline);
            if (affected <= 0) {
                log.error("lockCoupon fail. vid:{},couponId:{},orderId:{}", vid, couponItem.getCouponId(), orderId);
                throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券已使用");
            }
        }

        carCouponOptMapper.insert(buildCouponOptPo(vid, userId, orderId, OrderStatusEnum.LOCK));
    }

    /**
     * 核销优惠券
     *
     * @param vid
     * @param userId
     * @param couponIds
     * @param orderId
     * @throws Exception
     */
    public void consumeCoupon(String vid, long userId, List<Long> couponIds, long orderId) throws BizError {
        int affected = carCouponMapper.consumeCoupon(couponIds, vid, CouponStatusEnum.LOCKED.getValue(), CouponStatusEnum.USED.getValue(), orderId, TimeUtil.getNowUnixSecond());
        if (affected < couponIds.size()) {
            log.error("consumeCoupon fail. vid:{},couponId:{},orderId:{}", vid, couponIds, orderId);
            throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券已使用");
        }
    }

    /**
     * 退还优惠券
     *
     * @param vid
     * @param userId
     * @param couponIds
     * @param orderId
     * @param offline
     * @param currentStatus
     * @return bool
     * @throws Exception
     */
    public void returnCoupon(String vid, long userId, List<Long> couponIds, long orderId, Integer offline, String currentStatus) throws BizError {
        int affected = carCouponMapper.returnCoupon(couponIds, vid, currentStatus, CouponStatusEnum.UNUSED.getValue(), orderId,
                BigDecimal.valueOf(0.0), BigDecimal.valueOf(0), offline);

        if (affected <= 0) {
            log.warn("returnCoupon userId:{},orderId:{},affected:{},err:{}", userId, orderId, affected, "优惠券已退券或优惠券信息不正确");
            throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券退还失败");
        }
    }

    private CouponOptPo buildCouponOptPo(String vid, long userId, long orderId, OrderStatusEnum optEnum) {
        CouponOptPo couponOptPo = new CouponOptPo();
        couponOptPo.setId(getId(orderId, optEnum.getType()));
        couponOptPo.setVid(vid);
        couponOptPo.setUserId(userId);
        couponOptPo.setOrderId(orderId);
        couponOptPo.setOptType(optEnum.getType());
        couponOptPo.setAddTime(TimeUtil.getNowUnixSecond());
        return couponOptPo;
    }

    private Long getId(Long orderId, Integer optType) {
        return orderId * 10 + optType;
    }

    public int insertCouponOptLog(String vid, long userId, long orderId, OrderStatusEnum optEnum) {
        return carCouponOptMapper.insert(buildCouponOptPo(vid, userId, orderId, optEnum));
    }

    /**
     * 售后服务券-锁定优惠券
     *
     * @param vid
     * @param userId
     * @param orderId
     * @param couponItems
     * @param offline
     * @throws Exception
     */
    public void lockAfterSaleCoupon(String vid, long userId, long orderId, List<CouponLockItem> couponItems, Integer offline) throws BizError {
        //锁券
        for (CouponLockItem couponItem : couponItems) {
            int affected = carCouponMapper.lockCoupon(couponItem.getCouponId(), vid, CouponStatusEnum.UNUSED.getValue(), CouponStatusEnum.LOCKED.getValue(), orderId, couponItem.getReplaceMoney(), couponItem.getReduceExpress(), offline);
            if (affected <= 0) {
                log.error("lockCoupon fail. vid:{},couponId:{},orderId:{}", vid, couponItem.getCouponId(), orderId);
                throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券已使用");
            }
        }
    }

    public CarCouponLogPo getCouponLogPoByLogType(String vid, Long couponId, Long orderId, Integer logType) {
        return carCouponLogMapper.getByLogType(vid, couponId, orderId, logType);
    }

    public void updateCouponUsedTimes(String vid, Long orderId, Long couponId, Integer oldUsedTimes, Integer newUsedTimes, List<String> statList) throws BizError {
        int affected = carCouponMapper.updateCouponUsedTimes(vid, couponId, oldUsedTimes, newUsedTimes, statList);

        if (affected <= 0) {
            log.error("CarCouponRepository.addCouponUsedTimes failed, vid = {}, orderId = {}, couponId = {}, statList = {}", vid, orderId, couponId, statList);
            throw ExceptionHelper.create(ErrCode.USE_COUPON_USED_TIMES_UPDATE_ERROR, "优惠券使用次数修改失败");
        }
    }

    public void insertCarCouponLogPo(String vid, long userId, long orderId, CouponPo couponPo, CarCouponLogTypeEnum logTypeEnum, Integer oldUsedTimes, Integer newUsedTimes) throws BizError {
        CarCouponLogPo carCouponLogPo = buildCarCouponLogPo(vid, userId, orderId, couponPo, logTypeEnum, oldUsedTimes, newUsedTimes);
        int affected = carCouponLogMapper.insert(carCouponLogPo);

        if (affected <= 0) {
            log.error("CarCouponRepository.insertCarCouponLogPo failed, carCouponLogPo = {}", carCouponLogPo);
            throw ExceptionHelper.create(ErrCode.USE_COUPON_LOG_ERROR, "汽车优惠券使用日志记录失败");
        }
    }

    private CarCouponLogPo buildCarCouponLogPo(String vid, long userId, long orderId, CouponPo couponPo, CarCouponLogTypeEnum logTypeEnum, Integer oldUsedTimes, Integer newUsedTimes) {
        CarCouponLogPo carCouponLogPo = new CarCouponLogPo();
        carCouponLogPo.setId(getId(orderId, logTypeEnum.getCode()));
        carCouponLogPo.setCouponId(couponPo.getId());
        carCouponLogPo.setBizPlatform(couponPo.getBizPlatform());
        carCouponLogPo.setVid(vid);
        carCouponLogPo.setUserId(userId);
        carCouponLogPo.setOrderId(String.valueOf(orderId));
        carCouponLogPo.setLogType(logTypeEnum.getCode());
        carCouponLogPo.setChangeTimes(newUsedTimes);
        carCouponLogPo.setAddTime(TimeUtil.getNowUnixSecond());
        String logDesc = String.format("操作类型:%s, 旧已使用次数:%d, 新使用次数:%d", logTypeEnum.getDesc(), oldUsedTimes, newUsedTimes);
        carCouponLogPo.setLogDesc(logDesc);

        return carCouponLogPo;
    }

    /**
     * 获取所有已过期补胎券
     *
     * @param nowTime nowTime
     * @return List<CouponPo>
     */
    public List<CouponPo> getExpireRepairTairCoupon(long nowTime) {
        return carCouponMapper.getExpireRepairTairCoupon(nowTime);
    }

    /**
     * 更新已过期补胎券状态
     *
     * @param couponIds couponIds
     * @return int
     */
    public int updateRepairTairCouponStat(List<Long> couponIds) {
        return carCouponMapper.updateRepairTairCouponStat(couponIds);
    }

    @Transactional(value = "xmPulseWriteTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void invalidCoupon(String vid, List<Long> couponIdList) throws BizError {

        int effectNum = carCouponMapper.invalidCoupon(vid, StringUtils.join(couponIdList, ","));
        if (effectNum < couponIdList.size()) {
            throw ExceptionHelper.create(ErrCode.INVALID_COUPON_FAIL, "券作废失败");
        }
    }

    //
    // public void returnPaintRepairCoupon(String vid, long userId, List<CouponPo> couponPos, long orderId, Integer offline) throws BizError {
    //     for (CouponPo couponPo : couponPos) {
    //         int affected = carCouponMapper.returnCoupon(String.valueOf(couponPo.getId()), vid, couponPo.getStat(), CouponStatusEnum.UNUSED.getValue(), orderId,
    //                 BigDecimal.valueOf(0.0), BigDecimal.valueOf(0), offline);
    //
    //         if (affected <= 0) {
    //             log.warn("returnCoupon userId:{},orderId:{},affected:{},err:{}", userId, orderId, affected, "优惠券已退券或优惠券信息不正确");
    //             throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券退还失败");
    //         }
    //     }
    // }
}
