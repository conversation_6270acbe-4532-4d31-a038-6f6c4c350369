package com.xiaomi.nr.coupon.infrastructure.repository.localcache.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.xiaomi.nr.coupon.enums.couponconfig.SendChannelEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.localcache.LocalCacheCommon;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.auth.Impl.AppAuthRedisDaoImpl;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.auth.po.AppAuthInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.ParametersAreNonnullByDefault;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 本地缓存通用操作类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class LocalCacheCommonImpl implements LocalCacheCommon {

    @Autowired
    private AppAuthRedisDaoImpl appAuthRedisDao;

    public static Cache<String, AppAuthInfo> appAuthCache = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .initialCapacity(100)
            .maximumSize(300)
            .build();


    /**
     * 取单个PP配置信息
     *
     * @param appId  appId
     * @return AppAuthInfo
     */
    @Override
    public AppAuthInfo getSingleAppAuth(String appId){
        AppAuthInfo info = null;
        try {
            if(StringUtils.isEmpty(appId)){
                return null;
            }

            info = appAuthCache.getIfPresent(appId);
            if(Objects.isNull(info)){
                info = appAuthRedisDao.getNewAppAuth(appId);
                log.info("getSingleAppAuth appId:{},info:{}",appId,info);
                appAuthCache.put(appId,info);
            }
        } catch (Exception e) {
            log.error("getSingleAppAuth Exception appId:{}",appId,e);
            throw e;
        }

        return info;
    }

}
