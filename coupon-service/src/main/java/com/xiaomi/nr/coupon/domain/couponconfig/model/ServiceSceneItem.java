package com.xiaomi.nr.coupon.domain.couponconfig.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @description: 优惠券场景聚合类
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @Date 2022/2/26 8:19 下午
 * @Version: 1.0
 **/
@Data
@Accessors(chain = true)
public class ServiceSceneItem implements Serializable {
    /**
     * 投放场景id
     */
    private Integer id;

    /**
     * 抵扣规则: 1-全部抵扣; 2-部分抵扣
     */
    private Integer deductRule;

    /**
     * 互斥规则：1-互斥; 2-叠加
     */
    private Integer mutualRule;

    /**
     * 抵扣规则：是否部分抵扣
     *
     * @return
     */
    public boolean isDeductPart() {
        return this.deductRule.equals(2);
    }
}
