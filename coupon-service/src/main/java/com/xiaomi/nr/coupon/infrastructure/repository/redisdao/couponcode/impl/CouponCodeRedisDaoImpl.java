package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponcode.impl;

import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponcode.CouponCodeRedisDao;
import com.xiaomi.nr.coupon.util.StringUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class CouponCodeRedisDaoImpl implements CouponCodeRedisDao {

    /**
     * 缓存操作对象
     */
    @Autowired
    @Qualifier("stringNewCouponRedisTemplate")
    private StringRedisTemplate redisTemplate;

    /**
     * 用户已领取某个券的总数量
     */
    private static final String KEY_USER_EXCHANGE_CODE_COUNT = "nr:user:ex:{userId}";

    /**
     * 操作redis超过50ms的打日志
     */
    private static final long RUN_COST_TIME_WARN = 50L;

    private static final String INCR_SCRIPT =
            " local sendCount = redis.call('INCRBY', KEYS[1], 1)" +
                    " if sendCount>tonumber(ARGV[1]) then" +
                    " redis.call('DECRBY', KEYS[1], 1)" +
                    " return 'failed'" +
                    " end" +
                    " if sendCount==1 and tonumber(ARGV[2])~=nil and tonumber(ARGV[2])>0 then" +
                    " redis.call('EXPIRE', KEYS[1], ARGV[2])" +
                    " end" +
                    " return 'success'";
    private static final String DECR_SCRIPT = "redis.call('DECRBY', KEYS[1], 1)";
    private static final String SCRIPT_SUCCESS = "success";


    /**
     * 用户已兑换次数加1，并校验是否超过最大次数
     *
     * @param userId   long
     * @param maxCount int
     */
    @Override
    public void incrUserExchangeCount(long userId, int maxCount) throws BizError {
        long runStartTime = TimeUtil.getNowUnixMillis();
        DefaultRedisScript<String> redisScript = new DefaultRedisScript<>(INCR_SCRIPT, String.class);
        List<String> keys = new ArrayList<>();
        List<String> args = new ArrayList<>();
        keys.add(StringUtil.formatContent(KEY_USER_EXCHANGE_CODE_COUNT, String.valueOf(userId)));
        args.add(String.valueOf(maxCount));
        //1分钟有效期
        args.add("60");
        String r = redisTemplate.execute(redisScript, keys, args.toArray());
        long runCostTime = TimeUtil.sinceMillis(runStartTime);
        if (runCostTime > RUN_COST_TIME_WARN) {
            log.info("coupon.singleAssign, 用户已兑换次数加1耗时, runTime={}毫秒, userId={}, count={}", runCostTime, userId, r);
        }
        if (!SCRIPT_SUCCESS.equals(r)) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_USER_EXCHANGE_RATE, "兑换频率过快");
        }
    }

}
