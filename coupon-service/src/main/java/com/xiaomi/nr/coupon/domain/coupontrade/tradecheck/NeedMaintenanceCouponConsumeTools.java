package com.xiaomi.nr.coupon.domain.coupontrade.tradecheck;

import com.xiaomi.nr.coupon.enums.coupon.CarCouponLogTypeEnum;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.CouponServiceTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.CarCouponRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CarCouponLogPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description 按需保养券核销工具类
 * <AUTHOR>
 * @date 2024-12-30 14:17
*/
@Slf4j
@Component
public class NeedMaintenanceCouponConsumeTools extends AfterSaleCouponConsumeTools {

    @Resource
    private CarCouponRepository carCouponRepository;

    @PostConstruct
    public void init() {
        AfterSaleCouponConsumeToolsFactory.register(CouponServiceTypeEnum.NEED_MAINTENANCE, this);
    }

    /**
     * 券核销幂等校验
     *
     * @param couponPos 券列表
     * @param orderId 订单id
     * @param vid vid
     * @param timeNow 当前时间
     * @return 是否幂等
     * @throws BizError 业务异常
     */
    @Override
    public boolean couponListConsumeCheck(List<CouponPo> couponPos, long orderId, String vid, long timeNow) throws BizError {

        if (CollectionUtils.isEmpty(couponPos)) {
            throw ExceptionHelper.create(ErrCode.USE_COUPON_NOT_FOUND, "优惠券不存在");
        }
        // 按需保养券只能使用一张
        CouponPo couponPo = couponPos.get(0);

        long nowTime = TimeUtil.getNowUnixSecond();

        if (Objects.equals(CouponStatusEnum.USED.getValue(), couponPo.getStat())) {
            return true;
        }

        // 状态校验
        if (!Objects.equals(CouponStatusEnum.LOCKED.getValue(), couponPo.getStat())) {
            // 按需保养券核销前状态需为-锁定
            throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券状态不正确");
        }

        if (Objects.isNull(couponPo.getEndTime()) || Objects.isNull(couponPo.getStartTime())) {
            throw ExceptionHelper.create(ErrCode.USE_COUPON_TIME_INVALID, "优惠券有效期异常");
        }

        if (nowTime < Long.parseLong(couponPo.getStartTime()) || nowTime > Long.parseLong(couponPo.getEndTime())) {
            // 不在有效期内
            throw ExceptionHelper.create(ErrCode.USE_COUPON_TIME_INVALID, "优惠券不在有效期内");
        }

        return false;
    }

    /**
     * 优惠券核销
     *
     * @param vid vid
     * @param userId 用户id
     * @param orderId 订单id
     * @param offline 是否线下
     * @param couponPos 券列表
     * @throws BizError 业务异常
     */
    @Override
    public void consumeCoupon(String vid, long userId, long orderId, Integer offline, List<CouponPo> couponPos) throws BizError {
        if (CollectionUtils.isEmpty(couponPos)) {
            return;
        }

        List<Long> couponIds = couponPos.stream().map(CouponPo::getId).collect(Collectors.toList());
        carCouponRepository.consumeCoupon(vid, userId, couponIds, orderId);
    }
}
