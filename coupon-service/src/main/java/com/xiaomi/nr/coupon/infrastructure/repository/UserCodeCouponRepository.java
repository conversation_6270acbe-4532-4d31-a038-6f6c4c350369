package com.xiaomi.nr.coupon.infrastructure.repository;

import com.xiaomi.nr.coupon.api.dto.trade.CouponLockItem;
import com.xiaomi.nr.coupon.domain.coupon.model.CouponCodeQueryParam;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.coupon.OrderStatusEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CodeCouponMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CouponOptMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponOptPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponcode.po.CouponCodePo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponcodeslave.CouponCodeReadMapper;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class UserCodeCouponRepository {

    @Autowired
    private CodeCouponMapper codeCouponMapper;

    @Autowired
    private CouponCodeReadMapper couponCodeReadMapper;

    @Autowired
    private CouponOptMapper couponOptMapper;

    /**
     * 获取可用券码
     * @param md5Code
     * @return
     * @throws BizError
     */
    public CouponCodePo getUsableCouponCode(String md5Code, long orderId) throws BizError {
        List<CouponCodePo> couponCodePos = codeCouponMapper.getCouponCode(md5Code);
        if (CollectionUtils.isEmpty(couponCodePos)) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_INVALID, "优惠码无效");
        }
        CouponCodePo couponCode = null;
        for (CouponCodePo couponCodePo : couponCodePos) {
            if (couponCodePo.getOrderId() != null && orderId == couponCodePo.getOrderId() && CouponStatusEnum.LOCKED.getValue().equals(couponCodePo.getStat())) {
                couponCode = couponCodePo;
                break;
            }
            if (couponCodePo.getStat().equals(CouponStatusEnum.UNUSED.getValue())) {
                couponCode = couponCodePo;
                break;
            }
        }
        if (couponCode == null) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_INVALID, "优惠码无效");
        }
        long timeNow = TimeUtil.getNowUnixSecond();
        if (couponCode.getStartTime() > timeNow || couponCode.getEndTime() < timeNow) {
            throw ExceptionHelper.create(ErrCode.USE_COUPON_TIME_INVALID, "优惠券未在有效期内");
        }
        return couponCode;
    }

    public List<CouponCodePo> getCouponCodePos(String code) {
        return couponCodeReadMapper.getCouponCode(DigestUtils.md5DigestAsHex(code.getBytes()));
    }

    /**
     * 获取已锁定码券
     * @param userId
     * @param orderId
     * @param md5Code
     * @return
     * @throws BizError
     */
    public CouponCodePo getLockedCouponCode(long userId, long orderId, String md5Code) throws BizError {
        CouponCodePo couponCodePo = codeCouponMapper.getCouponCodeByOrderId(md5Code, userId, orderId);
        if (couponCodePo == null) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_INVALID, "优惠码无效");
        }
        return couponCodePo;
    }


    /**
     * 锁定码券
     * @param userId
     * @param orderId
     * @param id
     * @param couponLockItem
     * @param offline
     * @param orgCode
     * @throws Exception
     */
    @Transactional(value = "xmPulseWriteTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void lockCodeCoupon(long userId, long orderId, Long id, CouponLockItem couponLockItem, Integer offline, String orgCode) throws Exception {

        CouponOptPo couponOptPo = couponOptMapper.getByCouponOpt(userId, orderId, OrderStatusEnum.ROLLBACK.getType());
        if (couponOptPo != null) {
            log.warn("lock codeCoupon abandon. orderId:{}", orderId);
            return;
        }
        // 券资源
        int affected = codeCouponMapper.updateStat(id, userId, CouponStatusEnum.UNUSED.getValue(), CouponStatusEnum.LOCKED.getValue(),
                orderId, couponLockItem.getReplaceMoney(), couponLockItem.getReduceExpress(), TimeUtil.getNowUnixSecond(),
                offline, orgCode);
        if (affected <= 0) {
            log.warn("lock codeCoupon fail. couponCodeId:{}, orderId:{}", id, orderId);
            throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "锁定券资源失败");
        }

        couponOptMapper.insert(buildCouponOptPo(userId, orderId, OrderStatusEnum.LOCK));
    }


    /**
     * 核销码券
     * @param userId
     * @param id
     * @param orderId
     * @throws Exception
     */
    @Transactional(value = "xmPulseWriteTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void consumeCodeCoupon(long userId, Long id, long orderId) throws Exception {
        CouponOptPo couponOptPo = couponOptMapper.getByCouponOpt(userId, orderId, OrderStatusEnum.ROLLBACK.getType());
        if (couponOptPo != null) {
            log.warn("consume codeCoupon abandon. orderId:{}", orderId);
            return;
        }
        int affected = codeCouponMapper.consumeCoupon(id, userId, CouponStatusEnum.LOCKED.getValue(), CouponStatusEnum.USED.getValue(), orderId);
        if (affected <= 0) {
            log.warn("consume codeCoupon fail. userId:{},couponCodeId:{},orderId:{}", userId, id, orderId);
            throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券已使用");
        }

        couponOptMapper.insert(buildCouponOptPo(userId, orderId, OrderStatusEnum.SUBMIT));
    }

    /**
     * 退还码券
     * @param userId
     * @param orderId
     * @param id
     * @param offline
     * @param currentStatus
     * @return
     * @throws Exception
     */
    @Transactional(value = "xmPulseWriteTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public boolean returnCodeCoupon(long userId, long orderId, long id, Integer offline, String currentStatus) throws Exception {
        // 回滚资源
        int rollbackCnt = codeCouponMapper.updateStat(id, userId, currentStatus, CouponStatusEnum.UNUSED.getValue(),
                orderId, BigDecimal.valueOf(0.0), BigDecimal.valueOf(0.0), TimeUtil.getNowUnixSecond(), offline, "");

        couponOptMapper.insert(buildCouponOptPo(userId, orderId, OrderStatusEnum.ROLLBACK));

        if (rollbackCnt <= 0) {
            log.error("rollback codeCoupon fail. couponCodeId:{}, orderId:{}", id, orderId);
            return false;
        } else {
            return true;
        }
    }

    /**
     * 封装券下单操作类型
     * @param userId
     * @param orderId
     * @param lock
     * @return
     */
    private CouponOptPo buildCouponOptPo(long userId, long orderId, OrderStatusEnum lock) {
        CouponOptPo couponOptPo = new CouponOptPo();
        couponOptPo.setId(getId(orderId, lock.getType()));
        couponOptPo.setUserId(userId);
        couponOptPo.setOrderId(orderId);
        couponOptPo.setOptType(lock.getType());
        couponOptPo.setAddTime(TimeUtil.getNowUnixSecond());
        return couponOptPo;
    }

    /**
     * 获取操作类型Id
     * @param orderId
     * @param optType
     * @return
     */
    private Long getId(Long orderId, Integer optType){
        return orderId * 10 + optType;
    }

    /**
     * 获取西瓜商超使用的优惠码
     *
     * @param queryParam queryParam
     * @return CouponCodePo
     */
    public CouponCodePo getCouponCodeInfo(CouponCodeQueryParam queryParam) throws BizError {

        List<CouponCodePo> couponCodePos = codeCouponMapper.getCouponCodeInfo(queryParam);
        if (CollectionUtils.isEmpty(couponCodePos)) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_INVALID, "优惠码无效");
        }

        CouponCodePo couponCodePo = null;
        for (CouponCodePo codePo : couponCodePos) {
            if (codePo.getOrderId() != null && Objects.equals(queryParam.getOrderId(), codePo.getOrderId())) {
                couponCodePo = codePo;
                break;
            }
        }

        if (couponCodePo == null) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_INVALID, "优惠码无效");
        }

        return couponCodePo;
    }
}
