package com.xiaomi.nr.coupon.infrastructure.repository.localcache.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;



import com.google.common.collect.Maps;
import com.xiaomi.nr.coupon.infrastructure.repository.localcache.GoodCouponCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 券可用商品缓存
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class GoodCouponCacheImpl implements GoodCouponCache {

    /**
     * 初始容量
     */
    private static final int INITIAL_CAPACITY = 30000;

    /**
     * 最大容量
     */
    private static final long MAX_CAPACITY = 40000;

    /**
     * 写入后的过期时间，默认为30s
     */
    private static final long EXPIRE_AFTER_WRITE_TIME = 180;

    /**
     * pid -> configIds
     */
    private static Cache<String, List<Long>> localCache = Caffeine.newBuilder()
            .initialCapacity(INITIAL_CAPACITY)
            .maximumSize(MAX_CAPACITY)
            .expireAfterWrite(EXPIRE_AFTER_WRITE_TIME, TimeUnit.SECONDS)
            .recordStats()
            .build();


    /**
     * 从localCache里取商品对应优惠券配置ID列表
     *
     * @param id    Long
     * @param level String
     * @return GoodsConfigRelationPo
     */
    @Override
    public List<Long> getGoodsRelationCoupon(Long id, String level) {
        if (id == null || id <= 0 || StringUtils.isEmpty(level)) {
            log.error("localCache.getGoodsRelationCoupon, 参数不符合要求，id={}, level={}", id, level);
            return null;
        }

        String key = String.format("%s_%s", level, id);

        Map<String, List<Long>> caches = batchGet(Lists.newArrayList(key));
        if (MapUtils.isNotEmpty(caches)) {
            return caches.get(key);
        }
        return null;
    }

    /**
     * 单个key获取缓存
     * @param key
     * @return
     */
    @Override
    public List<Long> singleGet(String key) {
        if (StringUtils.isEmpty(key)) {
            log.error("localCache.singleGet, key不符合要求，singleGet={}", key);
            return null;
        }

        Map<String, List<Long>> caches = batchGet(Lists.newArrayList(key));
        if (MapUtils.isNotEmpty(caches)) {
            return caches.get(key);
        }
        return null;
    }

    /**
     * 批量获取缓存
     *
     * @param keys
     * @return
     */
    @Override
    public Map<String, List<Long>> batchGet(List<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return Maps.newHashMap();
        }
        Map<String, List<Long>> cacheMap = localCache.getAllPresent(keys);
        return cacheMap;
    }

    /**
     * 设置缓存
     *
     * @param goodConfigIds
     */
    @Override
    public void batchSet(Map<Long, List<Long>> goodConfigIds, String level) {
        if (MapUtils.isEmpty(goodConfigIds)) {
            return;
        }
        for (Map.Entry<Long,List<Long>> item : goodConfigIds.entrySet()) {
            localCache.put(String.format("%s_%s", level, item), item.getValue());
        }
    }
}
