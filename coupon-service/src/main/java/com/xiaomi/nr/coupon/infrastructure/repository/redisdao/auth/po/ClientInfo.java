package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.auth.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import java.io.Serializable;

/**
 * Client缓存信息
 */
@Data
public class ClientInfo implements Serializable {

    private static final long serialVersionUID = 2061906376755636587L;

    @SerializedName("client_id")
    private Long clientId;

    @SerializedName("name")
    private String name;

    @SerializedName("description")
    private String description;

    @SerializedName("secret_key")
    private String secretKey;

    @SerializedName("area_id")
    private Integer areaId;

    @SerializedName("language")
    private String language;

    @SerializedName("status")
    private Boolean status;

    @SerializedName("toggle")
    private Integer toggle;

    @SerializedName("channel_id")
    private String channelId;

    @SerializedName("g_channel_id")
    private Integer gChannelId;

    @SerializedName("open_iplist")
    private Boolean openIdList;

    @SerializedName("ext")
    private Extend extend;
}
