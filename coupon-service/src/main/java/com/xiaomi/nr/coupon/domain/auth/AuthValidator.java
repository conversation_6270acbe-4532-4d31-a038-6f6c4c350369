package com.xiaomi.nr.coupon.domain.auth;

import com.xiaomi.nr.coupon.infrastructure.repository.localcache.LocalCacheCommon;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.auth.po.AppAuthInfo;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 权限认证
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AuthValidator {

    private static LocalCacheCommon localCacheStatic;

    @Resource
    private LocalCacheCommon localCache;

    @Autowired
    private void set() {
        localCacheStatic = localCache;
    }

    /**
     * 鉴权
     *
     * @param serviceName 接口名
     * @param methodName  方法名
     * @param params      所有参数
     * @param paramNames  有参数名
     */
    public static void auth(String serviceName, String methodName, Map<String, String> params, List<String> paramNames) throws BizError {
        long startTime = TimeUtil.getNowUnixMillis();
        //参数空检查
        if (params.isEmpty() || paramNames.isEmpty()) {
            log.error("未传任何参数, serviceName={}, methodName={}, params={}", serviceName, methodName, params);
            throw ExceptionHelper.create(GeneralCodes.Forbidden, "未传任何参数，请核实");
        }

        String appId = params.get("appId");

        if (serviceName == null || "".equals(serviceName)) {
            log.error("serviceName为空, appId={}, serviceName={}, methodName={}, params={}", appId, serviceName, methodName, params);
            throw ExceptionHelper.create(GeneralCodes.Forbidden, "参数serviceName为空，请核实");
        }

        if (methodName == null || "".equals(methodName)) {
            log.error("methodName为空, appId={}, serviceName={}, methodName={}, params={}", appId, serviceName, methodName, params);
            throw ExceptionHelper.create(GeneralCodes.Forbidden, "参数methodName为空，请核实");
        }

        //检查该接口是否需要验证token和appId，如不需要返回
        if (!isCheckAuthService(serviceName,methodName)) {
            return;
        }

        if (appId == null || "".equals(appId)) {
            log.info("参数appId为空, appId={}, serviceName={}, methodName={}, params={}", appId, serviceName, methodName, params);
            throw ExceptionHelper.create(GeneralCodes.Forbidden, "参数appId为空，请核实");
        }

        //有没有访问权限
        /* 在没有后台配置权限之前，这里就不进行验证接口权限了
        if (!isAccessRights(appId, serviceName, methodName)) {
            log.info("没有此接口的访问权限, appId={}, serviceName={}, methodName={}, params={}", appId, serviceName, methodName, params);
            throw new BaseException(-1, "没有访问权限，请核实");
        }
        */

        //密钥
        String secret = getSecret(appId);
        if (secret == null) {
            log.info("没有权限, appId={}, serviceName={}, methodName={}, params={}", appId, serviceName, methodName, params);
            throw ExceptionHelper.create(GeneralCodes.Forbidden, "并没有访问权限，请核实");
        }

        log.info("auth.runTime={}", TimeUtil.sinceMillis(startTime));

        //token
        String token = params.get("token");
        if (token == null || "".equals(token)) {
            log.info("参数token为空, appId={}, serviceName={}, methodName={}, params={}", appId, serviceName, methodName, params);
            throw ExceptionHelper.create(GeneralCodes.Forbidden, "参数token为空，请核实");
        }

        //校验token
        if (!genNewToken(appId, secret).equals(token)) {
            if (!genToken(appId, secret, params, paramNames).equals(token)) {
                log.info("token验证失败, appId={}, serviceName={}, methodName={}, params={}", appId, serviceName, methodName, params);
                throw ExceptionHelper.create(GeneralCodes.Forbidden,"token验证失败，请核实参数");
            }
        }
    }

    /**
     * 是否为不需要验证token和appId的服务方法
     * 背景是权限上线前接入的服务没传这些字段
     *
     * @param serviceName String
     * @return Boolean
     */
    /*private static Boolean isNotCheckAuthService(String serviceName, String methodName) {
        if (serviceName == null || methodName == null) {
            return false;
        }
        //List<String> list = AppConfigs.getNotCheckAuthServices();
        Set<String> list = AppConfigs.notCheckSet;
        if (list.isEmpty()) {
            return false;
        }
        return list.contains(serviceName+"."+methodName);
    }*/


    /**
     * 是否为需要验证token和appId的服务方法（白名单）
     * @param serviceName 接口全限定名
     * @param methodName  接口名称
     * @return 是否需要校验
     */
    private static boolean isCheckAuthService(String serviceName, String methodName) {
        if (serviceName == null || methodName == null) {
            return false;
        }

        return AppConfigs.needCheckSet.contains(serviceName+"."+methodName);
    }

    /**
     * 返回appId的secret
     *
     * @param appId String
     * @return String
     */
    public static String getSecret(String appId) {
        if (StringUtils.isEmpty(appId)) {
            return null;
        }

       /* Map<String, AppAuthInfo> configMap = localCacheStatic.getAppAuth();
        if(configMap.containsKey(appId)){
            return configMap.get(appId).getSecret();
        }*/

        AppAuthInfo appAuthInfo = localCacheStatic.getSingleAppAuth(appId);
        if(Objects.isNull(appAuthInfo)){
            return null;
        }

        return appAuthInfo.getSecret();
    }

    /**
     * 方法是否有访问权限
     *
     * @param appId       String
     * @param serviceName String
     * @param methodName  String
     * @return boolean
     */
    private static boolean isAccessRights(String appId, String serviceName, String methodName) {
        if (appId == null || "".equals(appId)) {
            return false;
        }

        Map<String, Map<String, List<String>>> permissions = AppConfigs.getPermissions();

        if (permissions == null) {
            return false;
        }

        if (permissions.get(appId) == null || permissions.get(appId).isEmpty()) {
            return false;
        }

        if (permissions.get(appId).get(serviceName) == null || permissions.get(appId).get(serviceName).isEmpty()) {
            return false;
        }

        return permissions.get(appId).get(serviceName).contains(methodName);
    }

    /**
     * 生成token
     *
     * @param appId      String
     * @param secret     String
     * @param params     Map<String, String>
     * @param paramNames List<String>
     * @return String
     */
    private static String genToken(String appId, String secret, Map<String, String> params, List<String> paramNames) {
        //按首字母排序
        Collections.sort(paramNames);

        //拼接生成所有token的字符串
        StringBuilder str = new StringBuilder();
        str.append("appId").append("=").append(appId).append("&");

        for (String key : paramNames) {
            if ("appId".equals(key) || "token".equals(key) || "vid".equals(key)) {
                continue;
            }
            str.append(key).append("=").append(params.get(key)).append("&");
        }

        log.info("auth.token, appId={}, str={}secret={}***", appId, str, secret.substring(0,8));

        str.append("secret=").append(secret);

        //md5加密生成验证token
        return DigestUtils.md5Hex(str.toString());
    }

    /**
     * 生成token（新）
     *
     * @param appId      String
     * @param secret     String
     * @return String
     */
    private static String genNewToken(String appId, String secret) {
        //拼接生成所有token的字符串
        StringBuilder str = new StringBuilder().append("appId").append("=").append(appId).append("&").append("secret=").append(secret);

        log.info("auth.token, appId={}, str={}secret={}***", appId, str, secret.substring(0,8));

        //md5加密生成验证token
        return DigestUtils.md5Hex(str.toString());
    }




}
