package com.xiaomi.nr.coupon.application.dubbo;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.api.dto.coupon.ProductUsableCouponRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.ProductUsableCouponResponse;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.api.service.CouponProductService;
import com.xiaomi.nr.coupon.domain.coupon.AbstractProductCoupon;
import com.xiaomi.nr.coupon.domain.coupon.ProductCouponFactory;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Objects;

/**
 * 优惠券产品站服务
 *
 * <AUTHOR>
 * @date 2023/7/19
 */
@Component
@Slf4j
@ApiModule(value = "优惠券产品站服务", apiInterface = CouponProductService.class)
@Service(group = "${dubbo.group}", version = "1.0", timeout = 3000, delay = 10000)
public class CouponProductServiceImpl implements CouponProductService {

    @Resource
    private ProductCouponFactory productCouponFactory;

    @ApiDoc("获取单品页商品可用券接口")
    @Override
    public Result<ProductUsableCouponResponse> getProductUsableCoupon(@Valid ProductUsableCouponRequest req) {

        try {
            // 1、业务领域校验
            BizPlatformEnum bizPlatform = BizPlatformEnum.valueOf(req.getBizPlatform());
            if(Objects.isNull(bizPlatform)) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "业务领域非法");
            }

            // 3C领域clientId校验
            Long clientId = req.getClientId();
            if (BizPlatformEnum.RETAIL.equals(bizPlatform) && (Objects.isNull(clientId) || clientId <= 0)) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "clientId不符合要求");
            }

            // 2、获取执行器
            AbstractProductCoupon productCoupon = productCouponFactory.getProductCoupon(bizPlatform);
            if (Objects.isNull(productCoupon)) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "暂不支持当前业务领域");
            }

            // 3、获取商品可用券
            ProductUsableCouponResponse resp = productCoupon.getProductUsableCoupon(req);
            log.info("getProductUsableCoupon req:{}, resp:{}", GsonUtil.toJson(req), GsonUtil.toJson(resp));
            return Result.success(resp);

        } catch (BizError e) {
            log.info("getProductUsableCoupon req={}. e ", GsonUtil.toJson(req), e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("getProductUsableCoupon, 获取单品页商品可用券接口异常，req={}. e ", GsonUtil.toJson(req), e);
            return Result.fromException(e, "获取单品页商品可用券接口异常");
        }
    }
}
