package com.xiaomi.nr.coupon.util;

import com.xiaomi.nr.coupon.api.dto.trade.ConsumeCouponRequest;
import com.xiaomi.nr.coupon.api.dto.trade.RollbackCouponRequest;
import com.xiaomi.nr.promotion.warning.adapter.sdk.ext.CarAfterSaleExt;

import java.io.Serializable;

public class PromotionWaringExtHelper implements Serializable {


    public static String getConsumeCouponExt(ConsumeCouponRequest request) {
        CarAfterSaleExt ext = new CarAfterSaleExt();
        ext.setBizPlatform(request.getBizPlatform());
        ext.setVid(request.getVid());
        ext.setUserId(request.getUserId());

        return CarAfterSaleExt.toJson(ext);
    }

    public static String getRollbackCouponExt(RollbackCouponRequest request) {
        CarAfterSaleExt ext = new CarAfterSaleExt();
        ext.setBizPlatform(request.getBizPlatform());
        ext.setVid(request.getVid());
        ext.setUserId(request.getUserId());
        return CarAfterSaleExt.toJson(ext);
    }
}
