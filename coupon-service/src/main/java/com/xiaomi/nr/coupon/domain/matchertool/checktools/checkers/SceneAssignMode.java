package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import cn.hutool.core.map.MapUtil;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponSceneItem;
import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Objects;
import java.util.Set;

/**
 * 检查券发放方式 外部接口/内部灌券
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Component
public class SceneAssignMode extends CheckerAbstract {

    @Override
    public boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {
        if (!ctx.isCheckSceneAssignMode()) {
            return true;
        }

        errCtx.setInvalidConfig(true);

        CouponSceneItem sceneInfo = MapUtil.isNotEmpty(ctx.getSceneInfoMap()) ? ctx.getSceneInfoMap().get(respItem.getSceneCode()) : null;
        Set<Integer> sceneAssignMode = Objects.nonNull(sceneInfo) && Objects.nonNull(sceneInfo.getAssignMode()) ? sceneInfo.getAssignMode() : Collections.emptySet();

        if (!sceneAssignMode.contains(ctx.getAssignMode())) {
            errCtx.setErrCode(ErrCode.ASSIGN_COUPON_ASSIGNMODE_MISMATCH.getCode());
            errCtx.setErrMsg("场景不支持当前发放方式");
            return false;
        }

        return true;
    }
}
