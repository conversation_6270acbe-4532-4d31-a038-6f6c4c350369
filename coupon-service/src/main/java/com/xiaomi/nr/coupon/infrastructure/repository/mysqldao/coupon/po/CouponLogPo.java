package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po;

import lombok.Data;

import java.io.Serializable;

/**
 * 优惠券实体 tb_coupon
 *
 * <AUTHOR>
 */
@Data
public class CouponLogPo implements Serializable {

    private static final long serialVersionUID = 1198609851163750959L;

    /**
     * 优惠券ID
     */
    private Long couponId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 日志类型
     */
    private String type;

    /**
     * 优惠券原始状态
     */
    private String oldStat;

    /**
     * 优惠券最新状态
     */
    private String newStat;

    /**
     * 后台人员编号
     */
    private Long adminId;

    /**
     * 后台人员姓名
     */
    private String adminName;

    /**
     * 券描述
     */
    private String couponDesc;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 线下使用
     */
    private Integer offline;

}
