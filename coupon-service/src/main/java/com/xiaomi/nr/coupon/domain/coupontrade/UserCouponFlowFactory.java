package com.xiaomi.nr.coupon.domain.coupontrade;

import com.xiaomi.nr.coupon.enums.coupon.CouponCategoryEnum;
import com.xiaomi.nr.coupon.enums.coupon.SubmitTypeEnum;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: 券核销流程工厂
 * @author: hejiapeng
 * @Date 2023/1/10 4:16 下午
 * @Version: 1.0
 **/

@Component
public class UserCouponFlowFactory {

    @Autowired
    private ObjectFactory<UserNoCodeCouponService> userNoCodeCouponServiceObjectFactory;

    @Autowired
    private ObjectFactory<UserCodeCouponService> userCodeCouponServiceObjectFactory;

    @Autowired
    private ObjectFactory<UserRepCouponService> userRepCouponServiceObjectFactory;



    public UserCouponService getUserCouponService(String couponCode, Integer submitType) throws BizError{

        CouponCategoryEnum categoryEnum = getCategoryType(couponCode, submitType);

        switch (categoryEnum) {
            case NO_CODE:
                return  userNoCodeCouponServiceObjectFactory.getObject();
            case NEW_CODE:
                return userCodeCouponServiceObjectFactory.getObject();
            case REP_COUPON:
                return userRepCouponServiceObjectFactory.getObject();
            default:
                throw ExceptionHelper.create(GeneralCodes.NotFound, String.format("优惠券核销流程错误", couponCode));
        }
    }

    private CouponCategoryEnum getCategoryType(String couponCode,  Integer submitType) {
        if(submitType == null || SubmitTypeEnum.Normal.getCode() == submitType) {
            if (StringUtils.isEmpty(couponCode)) {
                return CouponCategoryEnum.NO_CODE;
            } else {
                return CouponCategoryEnum.NEW_CODE;
            }
        }
        return CouponCategoryEnum.REP_COUPON;
    }
}
