package com.xiaomi.nr.coupon.domain.coupon.model;

import com.xiaomi.nr.coupon.api.dto.coupon.OneGoodsUseInfoDto;
import com.xiaomi.nr.coupon.api.dto.coupon.info.CustomDetailDto;
import com.xiaomi.nr.coupon.api.dto.coupon.info.UseTermDto;
import com.xiaomi.nr.coupon.api.dto.couponconfig.PromotionVO;
import com.xiaomi.nr.coupon.domain.couponconfig.model.GoodScope;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherGoodsItemDo;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 优惠券信息
 */
@Data
public class CouponInfoModel implements Serializable {

    private static final long serialVersionUID = -9190969231434480111L;

    /**
     * 优惠券ID
     */
    private Long id;

    /**
     * 小米ID
     */
    private Long userId;

    /**
     * 优惠券状态(判断是否已经使用，并赋值对应状态)
     */
    private String status;

    /**
     * 优惠券状态描述
     */
    private String statusDesc;

    /**
     * 优惠券原始状态
     */
    private String originStatus;

    /**
     * 优惠券真实有效开始时间
     */
    private Long startTime;

    /**
     * 优惠券真实有效结束时间
     */
    private Long endTime;

    /**
     * 使用券的订单号
     */
    private Long useOrderId;

    /**
     * 使用券的时间（秒）
     */
    private Long useTime;

    /**
     * 原分享券的ID
     */
    private Long fromCouponId;

    /**
     * 发放券的订单号
     */
    private String fromOrderId;

    /**
     * 优惠券添加时间
     */
    private Long addTime;

    /**
     * 优惠券配置ID
     */
    private Long configId;

    /**
     * 券类型状态
     */
    private Integer configStatus;

    /**
     * 券类型状态描述
     */
    private String configStatusDesc;

    /**
     * 券类型名称
     */
    private String configName;

    /**
     * 优惠券的面值，100：代表满*减100元，8.5：代表满*打8.5折，空：代表是抵扣券的
     */
    private String showValue;

    /**
     * 优惠券的面额单位（现金券的单位为元，折扣券的单位为折，抵扣券的单位为空）
     */
    private String showUnit;

    /**
     * 　模式类型　code：有码券　no_code：无码券
     */
    private String modeType;

    /**
     * 优惠券类型  1: 商品券 2: 运费券
     */
    private Integer couponType;

    /**
     * 使用类型 cash：现金券，discount：折扣券，deductible：抵扣券
     * 1:满减, 2:满折, 3:N元券, 4:立减（新）
     */
    private Integer useType;

    /**
     * 履约方式 -1: 所有方式 139: 门店闪送 (这个跟交易中台统一定义)
     */
    private Integer shipmentId;

    /**
     * 是否可分享　true：可分享 false：不可分享
     */
    private Boolean isShare;

    /**
     * 是否包邮 true：包邮 false：不包邮
     */
    private Boolean isPostFree;

    /**
     * 是否调价商品可用 true：是 false：否
     */
    private Boolean isCheckPrice;

    /**
     * 是否套装部分商品可用券 true：是 false：否
     */
    private Boolean isCheckPackage;

    /**
     * 抵扣类型　zero_cent：0元抵扣 one_cent：1分钱抵扣
     */
    private String deductType;

    /**
     * 配额类型 money：满元 count：满件 money_count：满元且满件 eve_money：每满元 eve_count：每满件
     * 门槛类型 1满元 2满件 3每满元 4每满件(新)
     */
    private Integer quotaType;

    /**
     * 满件配额（单位件）
     */
    private Integer quotaCount;

    /**
     * 满元配额（单位分）
     */
    private Integer quotaMoney;

    /**
     * 满减券的满减金额（单位分）
     */
    private Long reduceMoney;

    /**
     * 折扣券的折扣（9折就是90）
     */
    private Long reduceDiscount;

    /**
     * 折扣券的最大减免金额（单位分）
     */
    private Integer reduceMaxPrice;

    /**
     * 是否是单个商品可用
     */
    private Boolean isOneGoodsUse;

    /**
     * 单个商品可用信息
     */
    private OneGoodsUseInfoDto oneGoodsUseInfoDto;

    /**
     * 优惠券规则
     */
    private CustomDetailDto customRuleDetail;

    /**
     * 优惠券发放渠道，store_manager：店长券，store_order_gift：下单赠券，**：**渠道，空：其他渠道
     */
    private String sendChannel;

    /**
     * 可使用渠道类型 online：仅线上使用 offline：仅线下使用 online_offline：线上线下均可使用
     */
    private String useChannelType;

    /**
     * 优惠券使用渠道（1～N个），mi_shop：小米商城，mi_home：小米之家，mi_authorized：授权店
     */
    private List<String> useChannel;

    /**
     * 优惠券使用渠道描述（仅小米商城/小米授权店/小米之家可用）
     */
    private String useChannelDesc;

    /**
     * 使用渠道分类（MISHOP_ONLINE：小米线上商城, MIHOME_OFFLINE：小米线下门店）
     */
    private List<String> useChannelGroup;

    /**
     * 券使用范围描述
     */
    private String rangeDesc;

    /**
     * 可用的client_id列表
     */
    private List<Long> clients;

    /**
     * 抵扣券可抵扣的货品SKU
     */
    private GoodScope deductTargetGoods;

    private String couponTag;

    private String actTag;

    /**
     * 特价商品是否参与  1表示不参与，2表示参与
     */
    private int checkPrice;
    /**
     * 是否包邮 1-是 2-否
     */
    private int postFree;

    private List<String> tags;

    /**
     * 排序字段
     */
    private Long sortValue = 1L;

    /**
     * 过程数据：针外部传入商品，匹配该券可用sku或套装列表（仅部分流程会用到）
     */
    private List<MatcherGoodsItemDo> validGoods = Collections.emptyList();

    /**
     * 服务场景
     */
    private Integer serviceType;

    /**
     * 业务领域
     */
    private Integer bizPlatform;

    /**
     * 使用有效期
     */
    private UseTermDto useTermDto;

    /**
     * 优惠规则
     */
    private PromotionVO promotionVO;

    /**
     * 投放场景
     */
    private String sendScene;

    /**
     * 结算阶段
     */
    private Integer checkoutStage;

    /**
     * 是否展示有效日期
     */
    private Integer displayDate;

}
