package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import org.springframework.stereotype.Component;

/**
 * 校验券剩余可领数量
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Component
public class ConfigFetchLimit extends CheckerAbstract {

    @Override
    public boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {
        if (!ctx.isCheckConfigFetchLimit()) {
            return true;
        }

        errCtx.setInvalidConfig(true);

        CouponConfigItem config = ctx.getConfigInfoMap().get(respItem.getConfigId());

        if (config.getCouponConfigInfo().getApplyCount() <= ctx.getConfigFetchedCountMap().getOrDefault(config.getConfigId().longValue(), 0)) {
            errCtx.setErrCode(ErrCode.ASSIGN_COUPON_OVER_LIMITED.getCode());
            errCtx.setErrMsg("优惠券已领完");
            return false;
        }

        return true;
    }
}
