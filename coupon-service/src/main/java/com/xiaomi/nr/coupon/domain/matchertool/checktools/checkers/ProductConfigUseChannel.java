package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.UseChannel;
import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/9
 */
@Component
public class ProductConfigUseChannel extends CheckerAbstract {

    /**
     * 校验使用渠道
     *
     * @param ctx      MatcherContextDo
     * @param respItem MatcherRespItemDo
     * @param errCtx   MatcherErrContextDo
     * @return boolean
     */
    @Override
    protected boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {

        if (!ctx.isCheckUseChannel()) {
            return true;
        }

        errCtx.setInvalidConfig(true);

        CouponConfigItem config = ctx.getConfigInfoMap().get(respItem.getConfigId());
        Map<Integer, UseChannel> channelMap = config.getUseChannelStore();

        // 只适合渠道层，无下层limit层
        if (channelMap.containsKey(ctx.getUseChannel())) {
            return true;
        }

        errCtx.setErrCode(ErrCode.USE_COUPON_CHANNEL_MISMATCH.getCode());
        errCtx.setErrMsg("优惠码不适用此渠道");
        return false;

    }
}
