package com.xiaomi.nr.coupon.enums.couponconfig;

/**
 * 可使用渠道类型 枚举
 *
 * <AUTHOR>
 */
public enum UseChannelType {

    /**
     * 仅线上可使用
     */
    Online("online", "1", "仅线上可使用"),

    /**
     * 仅线下可使用
     */
    Offline("offline", "2", "仅线下可使用"),

    /**
     * 线上线下均可使用
     */
    OnlineOffline("online_offline", "3", "线上线下均可使用");

    private final String redisValue;
    private final String mysqlValue;
    private final String name;

    UseChannelType(String redisValue, String mysqlValue, String name) {
        this.redisValue = redisValue;
        this.mysqlValue = mysqlValue;
        this.name = name;
    }

    public String getRedisValue() {
        return this.redisValue;
    }

    public String getMysqlValue() {
        return this.mysqlValue;
    }

    public String getName() {
        return name;
    }

    public static String findNameByRedisValue(String value) {
        UseChannelType[] values = UseChannelType.values();
        for (UseChannelType item : values) {
            if (item.getRedisValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }

    public static String findRedisValueByMysqlValue(String value) {
        UseChannelType[] values = UseChannelType.values();
        for (UseChannelType item : values) {
            if (item.getMysqlValue().equals(value)) {
                return item.getRedisValue();
            }
        }
        return null;
    }

    public static String findMysqlValueByRedisValue(String value) {
        UseChannelType[] values = UseChannelType.values();
        for (UseChannelType item : values) {
            if (item.getRedisValue().equals(value)) {
                return item.getMysqlValue();
            }
        }
        return null;
    }
}

