package com.xiaomi.nr.coupon.constant;

import com.google.common.collect.ImmutableMap;
import com.xiaomi.nr.coupon.enums.couponconfig.CouponTypeEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.PromotionType;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class CouponConstant {
    /**
     * 优惠券日志表里的状态:已发放
     */
    public static final String STATUS_SEND = "send";
    /**
     * 商品层级类型 - sku
     */
    public static final String PRODUCT_LEVEL_SKU = "sku";
    /**
     * 商品层级类型 - package
     */
    public static final String PRODUCT_LEVEL_PACKAGE = "package";

    /**
     * 新券模型开始自增ID的初始值(线上)
     */
    public static final long NEW_COUPON_ID_LIMIT = 50000L;

    /**
     * 新券模型开始自增ID的初始值(线上)
     */
    public static final long NEW_COUPON_ID_LIMIT_TEST = 14776L;

    /**
     * 批量取券缓存数量限制
     */
    public static final int GET_COUPON_CONFIG_CACHE_LIMIT = 30;

    /**
     * 锁定
     */
    public static final String COUPON_LOG_LOCK_DESC = "下单锁定";
    /**
     * 下单使用
     */
    public static final String COUPON_LOG_USED_DESC = "下单使用";
    /**
     * 关单恢复
     */
    public static final String COUPON_LOG_REFUND_DESC = "关单恢复";

    /**
     * 数据库写日志时的admin_id
     */
    public static final Long DB_ADMIN_ID = 0L;


    /**
     * 券优惠类型排序（N元券 > 立减券 > 满减券 > 折扣券）
     */
    public static final Map<Integer, Integer> PROMOTION_TYPE_SORT_MAP = ImmutableMap.of(
            PromotionType.DirectReduce.getCode(), 2,
            PromotionType.ConditionReduce.getCode(), 3,
            PromotionType.ConditionDiscount.getCode(), 4,
            PromotionType.NyuanBuy.getCode(), 1,
            PromotionType.Gift.getCode(), 0
    );

    /**
     * 券类型排序值（超级补贴券 > 商品券 > 运费券）
     */
    public static final Map<Integer, Integer> COUPON_TYPE_SORT_MAP = ImmutableMap.of(
            CouponTypeEnum.Goods.getValue(), 2,
            CouponTypeEnum.PostFee.getValue(), 3,
            CouponTypeEnum.Subsidy.getValue(), 1
    );

    /**
     * 车商城礼品兑换场景
     */
    public static final String SEND_SCENE_CAR_SHOP_GIFT_EXCHANGE = "1DC2FBE1B76AE8E10CBD23381492A66A";

    public static final Integer DISPLAY_DATE = 1;
    public static final Integer NOT_DISPLAY_DATE = 0;

}
