package com.xiaomi.nr.coupon.infrastructure.repository.mq.convert;

import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.xiaomi.nr.coupon.domain.common.model.CouponUsePushContext;
import com.xiaomi.nr.coupon.enums.coupon.CouponPushStatusEnum;
import com.xiaomi.nr.coupon.enums.coupon.OrderStatusEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.CouponServiceTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.mq.model.CarEquityPerformanceMessage;
import com.xiaomi.nr.coupon.infrastructure.repository.mq.model.CouponUseMessage;
import com.xiaomi.nr.coupon.infrastructure.repository.mq.model.CouponUsePushMqHeader;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import org.apache.rocketmq.spring.support.RocketMQHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @description 
 * <AUTHOR>
 * @date 2025-01-09 09:25
*/
@Component
public class CouponUseMessageConvert {

    /**
     * 构建优惠券使用消息
     *
     * @param context 上下文
     * @param couponPo 优惠券
     * @param scene 场景
     * @param profile 环境配置
     * @return 优惠券使用消息
     */
    public Message<?> buildCouponUseMessage(CouponUsePushContext context, CouponPo couponPo, String scene, String profile) {
        Long userId = context.getUserId() ;
        String vid = context.getVid() ;
        Integer status = context.getStatus();
        Long modifyTimeMillis = context.getModifyTimeMillis();
        Long orderId = context.getOrderId();
        Long couponId = couponPo.getId();


        CouponUsePushMqHeader couponUsePushMqHeader = new CouponUsePushMqHeader();
        couponUsePushMqHeader.setScene(scene);
        couponUsePushMqHeader.setProfile(profile);

        CouponUseMessage.Body body = CouponUseMessage.Body.builder()
                .unikey(couponId + "_" + modifyTimeMillis)
                .userId(userId)
                .vid(vid)
                .configId(couponPo.getTypeId())
                .couponId(couponId)
                .status(status)
                .modifyTimeMillis(modifyTimeMillis)
                .modifyTime(modifyTimeMillis / 1000)
                .orderId(orderId)
                .bizPlatform(couponPo.getBizPlatform())
                .build();

        CouponUseMessage message = CouponUseMessage.builder()
                .header(couponUsePushMqHeader)
                .body(body)
                .build();
        return MessageBuilder.withPayload(Objects.requireNonNull(GsonUtil.toJson(message))).setHeader(RocketMQHeaders.KEYS, couponId + "_" + modifyTimeMillis).build();
    }

    /**
     * 构建汽车权益核销计数消息
     *
     * @param couponPo 优惠券
     * @param context  上下文
     * @return 汽车权益核销计数消息
     */
    public Message<?> buildCarEquityPerformanceMessage(CouponUsePushContext context, CouponPo couponPo, String equityKey) {
        CarEquityPerformanceMessage carEquityPerformanceMessage = CarEquityPerformanceMessage.builder()
                .sourceSvc("COUPON")
                .equityKey(equityKey)
                .mid(context.getUserId())
                .vid(context.getVid())
                .implAbilityId(String.valueOf(couponPo.getId()))
                .usageRecordId(String.valueOf(couponPo.getId()))
                .usageOrderId(context.getOrderId())
                .deleted(Objects.equals(context.getStatus(), CouponPushStatusEnum.RETURN.getCode()))
                .build();
        return MessageBuilder.withPayload(Objects.requireNonNull(GsonUtil.toJson(Lists.newArrayList(carEquityPerformanceMessage)))).setHeader(RocketMQHeaders.KEYS, context.getVid()).build();
    }
}
