package com.xiaomi.nr.coupon.domain.couponconfig.impl;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.api.dto.couponconfig.*;
import com.xiaomi.nr.coupon.constant.CommonConstant;
import com.xiaomi.nr.coupon.constant.CouponConstant;
import com.xiaomi.nr.coupon.domain.couponconfig.GoodsCouponConfigService;
import com.xiaomi.nr.coupon.domain.couponconfig.convert.MissionConvert;
import com.xiaomi.nr.coupon.domain.couponconfig.convert.ValidCouponConfigConvert;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigInfo;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.enums.couponconfig.ConfigStatusEnum;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.GoodsCouponRepository;
import com.xiaomi.nr.coupon.util.SystemHelper;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.MissionCacheDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.po.MissionCacheItemPo;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品对应券接口
 */
@Service
@Slf4j
public class GoodsCouponConfigServiceImpl implements GoodsCouponConfigService {

    @Resource
    private SystemHelper systemHelper;

    @Resource
    private MissionConvert missionConvert;

    @Resource
    private MissionCacheDao missionCacheDao;

    @Resource
    private GoodsCouponRepository goodsCouponRepository;

    @Resource
    private CouponConfigRepository couponConfigRepository;

    @Resource
    private ValidCouponConfigConvert validCouponConfigConvert;

    /**
     * 获取商品可用券信息
     * @param request ValidConfigListRequest
     * @return ValidConfigListResponse
     * @throws BizError 业务异常
     */
    @Override
    public ValidConfigListResponse getGoodsValidCoupon(ValidConfigListRequest request) throws BizError {

        ValidConfigListResponse response = new ValidConfigListResponse();
        //获取商品对应的券ID
        Map<String, List<Long>> goodsMap = getGoodsMap(request.getGoodsItemList());
        Map<Long, List<Long>> configIdMap = getValidGoodsToCouponIdMap(goodsMap);
        if(MapUtils.isEmpty(configIdMap)){
            return response;
        }

        //根据券ID批量获取券配置信息
        List<Long> configAllId = configIdMap.values().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());
        Map<Long, CouponConfigItem> couponConfigItemMap = couponConfigRepository.getCouponConfigMap(configAllId);
        if(MapUtils.isEmpty(couponConfigItemMap)){
            return response;
        }

        //过滤无效的券配置信息
        Map<Long, CouponConfigItem> validConfigMap = filterValidConfig(request, couponConfigItemMap);
        if(MapUtils.isEmpty(validConfigMap)){
            return response;
        }

        //取出任务信息
        Map<Long, List<Long>> missionIdMap = new HashMap<>();
        Map<Long, MissionCacheItemPo> missionInfoMap = new HashMap<>();
        getMissionDtoMap(validConfigMap, missionIdMap, missionInfoMap);

        //合并券配置与任务信息
        Map<Long, ValidConfigInfoDto> validConfigInfoDtoMap = mergeCouponMissionMap(validConfigMap, missionIdMap, missionInfoMap);
        if(MapUtils.isEmpty(validConfigInfoDtoMap)){
            return response;
        }

        //组装返回值
        response = buildResponse(goodsMap, configIdMap, validConfigInfoDtoMap);
        return response;
    }



    /**
     * 取出sku和package ID
     * @param goodsItemList 商品信息
     * @return Map<String, List<Long>>
     */
    private Map<String, List<Long>> getGoodsMap(List<GoodsItem> goodsItemList){
        if(CollectionUtils.isEmpty(goodsItemList)){
            return null;
        }

        Map<String, List<Long>> goodsMap = new HashMap<>(CommonConstant.THREE_INT);
        for(GoodsItem goodsItem : goodsItemList){
            if(goodsMap.containsKey(goodsItem.getLevel())){
                goodsMap.get(goodsItem.getLevel()).add(goodsItem.getId());
                continue;
            }

            goodsMap.put(goodsItem.getLevel(), Lists.newArrayList((Collections.singletonList(goodsItem.getId()))));
        }
        return goodsMap;
    }


    /**
     * 获取商品对应的券配置id
     * @param goodsMap 商品信息
     * @return Map<Long, List<Long>>
     */
     private Map<Long, List<Long>> getValidGoodsToCouponIdMap(Map<String, List<Long>> goodsMap){
         if(MapUtils.isEmpty(goodsMap)){
             return null;
         }

         //取缓存获取券配置id
         Map<Long, List<Long>> configIdMap = new HashMap<>();
         for(String level : goodsMap.keySet()){
             configIdMap.putAll(goodsCouponRepository.batchGetGoodConfigs(goodsMap.get(level), level));
         }

        return configIdMap;
     }


    /**
     * 新券任务信息本地计算，旧券的任务取redis缓存
     * @param validConfigMap 可用券信息
     * @return 券和任务信息
     * @throws BizError 业务异常
     */
    private void getMissionDtoMap(Map<Long, CouponConfigItem> validConfigMap, Map<Long, List<Long>> missionIdMap, Map<Long, MissionCacheItemPo> missionInfoMap){
        if(MapUtils.isEmpty(validConfigMap)){
            return;
        }

        Map<Long, MissionCacheItemPo> newMissionInfoMap = new HashMap<>();
        List<Long> oldConfigIds = new ArrayList<>();

        //券id小于50000的都是老券,需要从缓存取任务信息(online)
        long limitCouponId = systemHelper.isTestSystem() ? CouponConstant.NEW_COUPON_ID_LIMIT_TEST : CouponConstant.NEW_COUPON_ID_LIMIT;

        for (Long id : validConfigMap.keySet()){

            if(id < limitCouponId){
                oldConfigIds.add(id);
                continue;
            }

            //新券本地组装任务
            CouponConfigItem couponItem = validConfigMap.get(id);
            if(Objects.isNull(couponItem)){
                continue;
            }

            MissionCacheItemPo missionCacheItemPo = makeMissionCache(couponItem);
            if(Objects.isNull(missionCacheItemPo)){
                continue;
            }

            newMissionInfoMap.put(id, missionCacheItemPo);
            if(!missionIdMap.containsKey(id)){
                missionIdMap.put(id, Lists.newArrayList(Collections.singletonList(id)));
                continue;
            }

            missionIdMap.get(id).add(id);
        }

        //取出老券任务id, 读取任务信息
        Map<Long, MissionCacheItemPo> oldMissionInfoMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(oldConfigIds)){
            Map<Long, List<Long>> oldMissionIdMap = missionCacheDao.getMissionIdMap(oldConfigIds);
            missionIdMap.putAll(oldMissionIdMap);
            List<Long> oldMissionIdList = oldMissionIdMap.values().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());
            oldMissionInfoMap = missionCacheDao.get(oldMissionIdList);
        }

        if(MapUtils.isNotEmpty(newMissionInfoMap)){
            missionInfoMap.putAll(newMissionInfoMap);
        }

        if(MapUtils.isNotEmpty(oldMissionInfoMap)){
            missionInfoMap.putAll(oldMissionInfoMap);
        }

    }


    /**
     * 生成新券模型的任务缓存信息
     * @param couponItem 券配置缓存
     * @return 任务缓存
     */
    private MissionCacheItemPo makeMissionCache(CouponConfigItem couponItem){

        long globalCouponEndTime = Math.max(couponItem.getCouponConfigInfo().getEndFetchTime() + couponItem.getCouponConfigInfo().getUseDuration() * 3600, couponItem.getCouponConfigInfo().getEndUseTime());
        long missionEndTime = couponItem.getCouponConfigInfo().getEndUseTime();

        if (couponItem.getCouponConfigInfo().getUseDuration() > 0) {
            missionEndTime = TimeUtil.getNowUnixSecond() + couponItem.getCouponConfigInfo().getUseDuration() * 3600;
        }

        if (missionEndTime > globalCouponEndTime) {
            log.info("makeMissionCacheList, 发放任务的有效期不在券配置最大有效期内, couponConfigId={}, missionId={}", couponItem.getConfigId(),couponItem.getConfigId());
            return null;
        }

        return missionConvert.makeMissionCache(couponItem);
    }


    /**
     * 组装返回值
     * @param goodsMap   商品信息
     * @param configIdMap 商品对应券id
     * @param configInfoMap 券详细信息
     * @return ValidConfigListResponse
     */
    private ValidConfigListResponse buildResponse(Map<String, List<Long>> goodsMap, Map<Long, List<Long>> configIdMap, Map<Long, ValidConfigInfoDto> configInfoMap){

        Map<Long, Set<Long>> skuToCouponMap = new HashMap<>();
        Map<Long, Set<Long>> packageToCouponMap = new HashMap<>();
        Map<Long, Set<Long>> ssuToCouponMap = new HashMap<>();
        for(Map.Entry<String, List<Long>> item : goodsMap.entrySet()){
            for(Long goodsId : item.getValue()){

                //判断该商品是否有对应的券，没有就跳过
                if(Objects.isNull(configIdMap.get(goodsId))){
                    continue;
                }

                for(Long configId : configIdMap.get(goodsId)){

                    //无效券跳过
                    if(!configInfoMap.containsKey(configId)){
                        continue;
                    }

                    //sku对应券
                    if(StringUtils.equals(GoodsLevelEnum.Sku.getValue(), item.getKey())){
                        putResponseMap(goodsId, configId, skuToCouponMap);
                    }

                    //package对应券
                    if(StringUtils.equals(GoodsLevelEnum.Package.getValue(), item.getKey())){
                        putResponseMap(goodsId, configId, packageToCouponMap);
                    }

                    //package对应券
                    if(StringUtils.equals(GoodsLevelEnum.Ssu.getValue(), item.getKey())){
                        putResponseMap(goodsId, configId, ssuToCouponMap);
                    }
                }
            }
        }

        return new ValidConfigListResponse(skuToCouponMap, packageToCouponMap, ssuToCouponMap, configInfoMap);
    }


    /**
     * map添加元素
     * @param goodsId 商品id
     * @param configId 券配置id
     * @param goodsToCouponMap 商品对应券id
     */
    private void putResponseMap(Long goodsId, Long configId, Map<Long, Set<Long>> goodsToCouponMap){

        if(!goodsToCouponMap.containsKey(goodsId)){
            goodsToCouponMap.put(goodsId, new HashSet<>(Collections.singletonList(configId)));
            return;
        }

        goodsToCouponMap.get(goodsId).add(configId);
    }


    /**
     * 合并券和任务信息
     * @param configMap 券信息
     * @param configToMissionMap 券对应任务
     * @param missionMap 任务信息
     * @throws BizError 业务异常
     */
    private  Map<Long, ValidConfigInfoDto> mergeCouponMissionMap(Map<Long, CouponConfigItem> configMap, Map<Long, List<Long>> configToMissionMap, Map<Long, MissionCacheItemPo> missionMap) throws BizError {

        Map<Long, ValidConfigInfoDto> resultMap = new HashMap<>(configMap.size());
        for(Long configId : configMap.keySet()){

            CouponConfigItem couponConfigItem = configMap.get(configId);
            if (Objects.isNull(couponConfigItem)){
                continue;
            }

            if (!configToMissionMap.containsKey(configId)){
                continue;
            }

            List<MissionCacheItemPo> missionCacheList = new ArrayList<>(configToMissionMap.get(configId).size());
            for(Long missionId : configToMissionMap.get(configId)){
                if (!missionMap.containsKey(missionId)){
                    continue;
                }
                missionCacheList.add(missionMap.get(missionId));
            }

            if (!CollectionUtils.isEmpty(missionCacheList)) {
                missionCacheList = missionCacheList.stream().sorted(Comparator.comparing(MissionCacheItemPo::getSendStartTime)).collect(Collectors.toList());
            }

            ValidConfigInfoDto validConfigInfoDto = validCouponConfigConvert.convertValidConfigInfoDto(couponConfigItem, missionCacheList);
            if(Objects.isNull(validConfigInfoDto) || validConfigInfoDto.getConfigId() < CommonConstant.ZERO_LONG){
                continue;
            }

            resultMap.put(configId, validConfigInfoDto);
        }

        return resultMap;
    }



    /**
     * 根据领取时间&投放场景过滤有效的券配置信息
     * @param request      投放场景、开始领取时间、结束领取时间
     * @param map   券信息
     * @return 券信息
     */
    private Map<Long, CouponConfigItem> filterValidConfig(ValidConfigListRequest request,  Map<Long, CouponConfigItem> map) {
        if(MapUtils.isEmpty(map)){
            return null;
        }

        Map<Long, CouponConfigItem> result = new HashMap<>(map.size());
        long nowTime = TimeUtil.getNowUnixSecond();
        String sceneCode = request.getSceneCode();
        Long startFetchTime = request.getStartFetchTime();
        Long endFetchTime = request.getEndFetchTime();

        for (Long id : map.keySet()) {

            CouponConfigItem item = map.get(id);
            CouponConfigInfo couponConfigInfo = item.getCouponConfigInfo();
            if (couponConfigInfo.getStartFetchTime() == null || couponConfigInfo.getEndFetchTime() == null) {
                continue;
            }

            if(!Objects.isNull(startFetchTime) && startFetchTime > couponConfigInfo.getEndFetchTime()){
                continue;
            }

            if(!Objects.isNull(endFetchTime) && endFetchTime < couponConfigInfo.getStartFetchTime()){
                continue;
            }

            if (ConfigStatusEnum.Online.getValue().intValue() != couponConfigInfo.getStatus()) {
                continue;
            }

            if (couponConfigInfo.getEndFetchTime() <= nowTime) {
                continue;
            }

            if (!StringUtils.equals(sceneCode, couponConfigInfo.getSendScene())) {
                continue;
            }

            result.put(id, item);
        }
        return result;
    }

}
