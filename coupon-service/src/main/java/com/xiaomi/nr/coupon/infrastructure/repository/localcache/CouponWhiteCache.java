package com.xiaomi.nr.coupon.infrastructure.repository.localcache;

import com.github.benmanes.caffeine.cache.stats.CacheStats;

/**
 * 优惠券场景本地缓存
 *
 * <AUTHOR>
 */
public interface CouponWhiteCache {

    /**
     * 根据场景编码获取场景
     *
     * @param userId
     * @return
     */
    boolean checkWhiteListContain(Long userId);


    /**
     * 批量设置优惠券场景到localCache
     *
     * @param userId
     */
    void addWhiteListCache(Long userId);

    /**
     * 获取缓存统计信息
     *
     * @return CacheStats
     */
    CacheStats getCacheStatistics();

}
