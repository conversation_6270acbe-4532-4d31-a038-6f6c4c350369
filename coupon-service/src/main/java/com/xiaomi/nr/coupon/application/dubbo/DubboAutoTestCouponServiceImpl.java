package com.xiaomi.nr.coupon.application.dubbo;

import com.mi.framework.http.HttpClient;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.api.dto.autotest.BatchFetchCouponRequest;
import com.xiaomi.nr.coupon.api.dto.autotest.BatchFetchCouponResponse;
import com.xiaomi.nr.coupon.api.dto.autotest.BatchFetchCouponVO;
import com.xiaomi.nr.coupon.api.dto.autotest.BatchFetchRedpacketMsgResponse;
import com.xiaomi.nr.coupon.api.dto.autotest.BatchFetchRedpacketRequest;
import com.xiaomi.nr.coupon.api.dto.autotest.BatchFetchRedpacketResponse;
import com.xiaomi.nr.coupon.api.dto.autotest.BatchFetchRedpacketVO;
import com.xiaomi.nr.coupon.api.dto.couponassign.SingleAssignRequest;
import com.xiaomi.nr.coupon.api.dto.couponassign.SingleAssignResponse;
import com.xiaomi.nr.coupon.api.service.CouponAssignService;
import com.xiaomi.nr.coupon.api.service.DubboAutoTestCouponService;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.dubbo.config.annotation.Service;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
@Service(group = "${dubbo.group}", version = "1.0", timeout = 3000, delay = 10000)
@ApiModule(value = "自动化测试服务", apiInterface = DubboAutoTestCouponService.class)
public class DubboAutoTestCouponServiceImpl implements DubboAutoTestCouponService {


    @Autowired
    private CouponAssignService couponAssignService;

    @ApiDoc("批量领券")
    @Override
    public Result<BatchFetchCouponResponse> batchFetchCoupon(BatchFetchCouponRequest request) {
        try {
            BatchFetchCouponResponse response = new BatchFetchCouponResponse();
            List<BatchFetchCouponVO> batchFetchCouponVOS = new ArrayList<>();
            for (SingleAssignRequest singleAssignRequest : request.getBatchFetchCouponList()) {
                try {
                    singleAssignRequest.setAppId(request.getAppId());
                    Result<SingleAssignResponse> result = couponAssignService.single(singleAssignRequest);
                    if (result.getData() != null) {
                        SingleAssignResponse singleAssignResponse = result.getData();
                        BatchFetchCouponVO batchFetchCouponVO = new BatchFetchCouponVO();
                        batchFetchCouponVO.setIsIdempotent(singleAssignResponse.getIsIdempotent());
                        batchFetchCouponVO.setRequestId(singleAssignRequest.getRequestId());
                        batchFetchCouponVO.setCouponId(singleAssignResponse.getCouponId());
                        batchFetchCouponVO.setConfigId(singleAssignRequest.getConfigId());
                        batchFetchCouponVO.setUserId(singleAssignRequest.getUserId());
                        batchFetchCouponVOS.add(batchFetchCouponVO);
                    }
                } catch (Exception e) {
                    log.error("DubboAutoTestService batchFetchCoupon error singleAssignRequest:{}", singleAssignRequest, e);
                }
            }
            response.setBatchFetchCouponVOS(batchFetchCouponVOS);
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboAutoTestService batchFetchCoupon error request:{}", request, e);
            return Result.fromException(e, "批量发券失败");
        }
    }

    @ApiDoc("批量发红包")
    @Override
    public Result<BatchFetchRedpacketResponse> batchFetchRedpacket(BatchFetchRedpacketRequest request) {
        String appUrl = "http://minos.shopapi.test.b2c.srv"; //域名
        String appID = "XM2107";
        String appKey = "719b4fc62342bd761a678ccd9a04fa32";//分配的密钥
        String requestID = String.valueOf(System.currentTimeMillis());
        try {
            ObjectMapper mapper = new ObjectMapper();

            //生成body
            Map<String, Object> assignMap = new HashMap<>();
            assignMap.put("mission_id", request.getMissionId());
            List<Map<String, Object>> prepareGroup = new ArrayList<>();
            for (BatchFetchRedpacketVO batchFetchRedpacketVO : request.getPrepareGroup()) {
                Map<String, Object> data = new HashMap<>();
                data.put("user_id", batchFetchRedpacketVO.getUserId());
                data.put("amount", batchFetchRedpacketVO.getAmount());
                data.put("uniq_id", batchFetchRedpacketVO.getUniqId());
                data.put("start_time", batchFetchRedpacketVO.getStartTime());
                data.put("end_time", batchFetchRedpacketVO.getEndTime());
                data.put("request_id", System.currentTimeMillis());
                prepareGroup.add(data);
            }
            assignMap.put("prepare_group", prepareGroup);
            String assignMapJson = mapper.writeValueAsString(assignMap);

            //生成签名
            String signStr = appID + assignMapJson.replace("/", "\\/") + appKey;
            String sign = org.apache.commons.codec.digest.DigestUtils.md5Hex(signStr.getBytes()).toUpperCase();

            //生成head
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("appid", request.getAppId());
            headerMap.put("requestid", requestID);
            headerMap.put("sign", sign);

            //生成data
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("header", headerMap);
            requestMap.put("body", assignMapJson);
            String original = mapper.writeValueAsString(requestMap);
            String data = Base64.encodeBase64String(original.replace("/", "\\/").getBytes());

            //生成请求map
            Map<String, Object> params = new HashMap();
            params.put("data", data);


            BatchFetchRedpacketResponse batchFetchRedpacketResponse = new BatchFetchRedpacketResponse();

            String response = new HttpClient().addParam(params)
                    .retry(false)
                    .post(appUrl + "/pulse/redpacket/action/prepare")
                    .execute();
            log.info("batchFetchRedpacket request:{},response:{}", request, response);
            BatchFetchRedpacketMsgResponse responseMap = GsonUtil.fromJson(response, BatchFetchRedpacketMsgResponse.class);
            if (0 != responseMap.getHeader().getCode()) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, responseMap.getHeader().getDesc());
            }

            return Result.success(responseMap.getBody());
        } catch (BizError bizError) {
            log.error("batchFetchRedpacket bizError request:{}", request, bizError);
            return Result.fromException(bizError, bizError.getMessage());
        } catch (Exception e) {
            log.error("batchFetchRedpacket error request:{}", request, e);
            return Result.fromException(e, "批量发红包失败");
        }
    }


}
