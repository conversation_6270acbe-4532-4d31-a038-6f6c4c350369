/**
 * Autogenerated by Thrift Compiler (0.9.2)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.xiaomi.nr.coupon.infrastructure.rpc.sid.sdk;

import org.mi.thrift.EncodingUtils;
import org.mi.thrift.protocol.TTupleProtocol;
import org.mi.thrift.scheme.IScheme;
import org.mi.thrift.scheme.SchemeFactory;
import org.mi.thrift.scheme.StandardScheme;
import org.mi.thrift.scheme.TupleScheme;

import javax.annotation.Generated;
import java.util.*;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.2)", date = "2021-11-15")
public class idinfo implements org.mi.thrift.TBase<idinfo, idinfo._Fields>, java.io.Serializable, Cloneable, Comparable<idinfo> {
    private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("idinfo");

    private static final org.mi.thrift.protocol.TField ID_FIELD_DESC = new org.mi.thrift.protocol.TField("id", org.mi.thrift.protocol.TType.I64, (short)1);
    private static final org.mi.thrift.protocol.TField TIMESTAMP_FIELD_DESC = new org.mi.thrift.protocol.TField("timestamp", org.mi.thrift.protocol.TType.I64, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new idinfoStandardSchemeFactory());
      schemes.put(TupleScheme.class, new idinfoTupleSchemeFactory());
    }

    public long id; // required
    public long timestamp; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.mi.thrift.TFieldIdEnum {
        ID((short)1, "id"),
        TIMESTAMP((short)2, "timestamp");

        private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

        static {
          for (_Fields field : EnumSet.allOf(_Fields.class)) {
            byName.put(field.getFieldName(), field);
          }
        }

        /**
         * Find the _Fields constant that matches fieldId, or null if its not found.
         */
        public static _Fields findByThriftId(int fieldId) {
            switch(fieldId) {
                case 1: // ID
                  return ID;
                case 2: // TIMESTAMP
                  return TIMESTAMP;
                default:
                  return null;
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, throwing an exception
         * if it is not found.
         */
        public static _Fields findByThriftIdOrThrow(int fieldId) {
          _Fields fields = findByThriftId(fieldId);
          if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
          return fields;
        }

        /**
         * Find the _Fields constant that matches name, or null if its not found.
         */
        public static _Fields findByName(String name) {
          return byName.get(name);
        }

        private final short _thriftId;
        private final String _fieldName;

        _Fields(short thriftId, String fieldName) {
          _thriftId = thriftId;
          _fieldName = fieldName;
        }

        public short getThriftFieldId() {
          return _thriftId;
        }

        public String getFieldName() {
          return _fieldName;
        }
    }

    // isset id assignments
    private static final int __ID_ISSET_ID = 0;
    private static final int __TIMESTAMP_ISSET_ID = 1;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
        Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.mi.thrift.meta_data.FieldMetaData>(_Fields.class);
        tmpMap.put(_Fields.ID, new org.mi.thrift.meta_data.FieldMetaData("id", org.mi.thrift.TFieldRequirementType.DEFAULT, 
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I64)));
        tmpMap.put(_Fields.TIMESTAMP, new org.mi.thrift.meta_data.FieldMetaData("timestamp", org.mi.thrift.TFieldRequirementType.DEFAULT, 
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I64)));
        metaDataMap = Collections.unmodifiableMap(tmpMap);
        org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(idinfo.class, metaDataMap);
    }

    public idinfo() {
    }

    public idinfo(
        long id,
        long timestamp)
    {
        this();
        this.id = id;
        setIdIsSet(true);
        this.timestamp = timestamp;
        setTimestampIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public idinfo(idinfo other) {
        __isset_bitfield = other.__isset_bitfield;
        this.id = other.id;
        this.timestamp = other.timestamp;
    }

    public idinfo deepCopy() {
      return new idinfo(this);
    }

    @Override
    public void clear() {
        setIdIsSet(false);
        this.id = 0;
        setTimestampIsSet(false);
        this.timestamp = 0;
    }

    public long getId() {
        return this.id;
    }

    public idinfo setId(long id) {
        this.id = id;
        setIdIsSet(true);
        return this;
    }

    public void unsetId() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ID_ISSET_ID);
    }

    /** Returns true if field id is set (has been assigned a value) and false otherwise */
    public boolean isSetId() {
        return EncodingUtils.testBit(__isset_bitfield, __ID_ISSET_ID);
    }

    public void setIdIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ID_ISSET_ID, value);
    }

    public long getTimestamp() {
        return this.timestamp;
    }

    public idinfo setTimestamp(long timestamp) {
        this.timestamp = timestamp;
        setTimestampIsSet(true);
        return this;
    }

    public void unsetTimestamp() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TIMESTAMP_ISSET_ID);
    }

    /** Returns true if field timestamp is set (has been assigned a value) and false otherwise */
    public boolean isSetTimestamp() {
        return EncodingUtils.testBit(__isset_bitfield, __TIMESTAMP_ISSET_ID);
    }

    public void setTimestampIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TIMESTAMP_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
        case ID:
            if (value == null) {
              unsetId();
            } else {
              setId((Long)value);
            }
            break;

        case TIMESTAMP:
            if (value == null) {
              unsetTimestamp();
            } else {
              setTimestamp((Long)value);
            }
            break;

      }
    }

    public Object getFieldValue(_Fields field) {
        switch (field) {
        case ID:
            return Long.valueOf(getId());

        case TIMESTAMP:
            return Long.valueOf(getTimestamp());

        }
        throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
        if (field == null) {
          throw new IllegalArgumentException();
        }

        switch (field) {
        case ID:
            return isSetId();
        case TIMESTAMP:
            return isSetTimestamp();
        }
        throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
        if (that == null)
          return false;
        if (that instanceof idinfo)
          return this.equals((idinfo)that);
        return false;
    }

    public boolean equals(idinfo that) {
        if (that == null)
          return false;

        boolean this_present_id = true;
        boolean that_present_id = true;
        if (this_present_id || that_present_id) {
            if (!(this_present_id && that_present_id))
              return false;
            if (this.id != that.id)
              return false;
        }

        boolean this_present_timestamp = true;
        boolean that_present_timestamp = true;
        if (this_present_timestamp || that_present_timestamp) {
            if (!(this_present_timestamp && that_present_timestamp))
              return false;
            if (this.timestamp != that.timestamp)
              return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        List<Object> list = new ArrayList<Object>();

        boolean present_id = true;
        list.add(present_id);
        if (present_id)
          list.add(id);

        boolean present_timestamp = true;
        list.add(present_timestamp);
        if (present_timestamp)
          list.add(timestamp);

        return list.hashCode();
    }

    @Override
    public int compareTo(idinfo other) {
        if (!getClass().equals(other.getClass())) {
          return getClass().getName().compareTo(other.getClass().getName());
        }

        int lastComparison = 0;

        lastComparison = Boolean.valueOf(isSetId()).compareTo(other.isSetId());
        if (lastComparison != 0) {
          return lastComparison;
        }
        if (isSetId()) {
          lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.id, other.id);
          if (lastComparison != 0) {
            return lastComparison;
          }
        }
        lastComparison = Boolean.valueOf(isSetTimestamp()).compareTo(other.isSetTimestamp());
        if (lastComparison != 0) {
          return lastComparison;
        }
        if (isSetTimestamp()) {
          lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.timestamp, other.timestamp);
          if (lastComparison != 0) {
            return lastComparison;
          }
        }
        return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.mi.thrift.protocol.TProtocol iprot) throws org.mi.thrift.TException {
        schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.mi.thrift.protocol.TProtocol oprot) throws org.mi.thrift.TException {
        schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("idinfo(");
        boolean first = true;

        sb.append("id:");
        sb.append(this.id);
        first = false;
        if (!first) sb.append(", ");
        sb.append("timestamp:");
        sb.append(this.timestamp);
        first = false;
        sb.append(")");
        return sb.toString();
    }

    public void validate() throws org.mi.thrift.TException {
        // check for required fields
        // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
      } catch (org.mi.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
      } catch (org.mi.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class idinfoStandardSchemeFactory implements SchemeFactory {
        public idinfoStandardScheme getScheme() {
            return new idinfoStandardScheme();
        }
    }

    private static class idinfoStandardScheme extends StandardScheme<idinfo> {

        public void read(org.mi.thrift.protocol.TProtocol iprot, idinfo struct) throws org.mi.thrift.TException {
            org.mi.thrift.protocol.TField schemeField;
            iprot.readStructBegin();
            while (true)
            {
                schemeField = iprot.readFieldBegin();
                if (schemeField.type == org.mi.thrift.protocol.TType.STOP) { 
                    break;
                }
                switch (schemeField.id) {
                    case 1: // ID
                        if (schemeField.type == org.mi.thrift.protocol.TType.I64) {
                            struct.id = iprot.readI64();
                            struct.setIdIsSet(true);
                        } else { 
                          org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 2: // TIMESTAMP
                        if (schemeField.type == org.mi.thrift.protocol.TType.I64) {
                            struct.timestamp = iprot.readI64();
                            struct.setTimestampIsSet(true);
                        } else { 
                          org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    default:
                      org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                }
                iprot.readFieldEnd();
            }
            iprot.readStructEnd();

            // check for required fields of primitive type, which can't be checked in the validate method
            struct.validate();
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot, idinfo struct) throws org.mi.thrift.TException {
            struct.validate();

            oprot.writeStructBegin(STRUCT_DESC);
            oprot.writeFieldBegin(ID_FIELD_DESC);
            oprot.writeI64(struct.id);
            oprot.writeFieldEnd();
            oprot.writeFieldBegin(TIMESTAMP_FIELD_DESC);
            oprot.writeI64(struct.timestamp);
            oprot.writeFieldEnd();
            oprot.writeFieldStop();
            oprot.writeStructEnd();
        }

    }

    private static class idinfoTupleSchemeFactory implements SchemeFactory {
        public idinfoTupleScheme getScheme() {
            return new idinfoTupleScheme();
        }
    }

    private static class idinfoTupleScheme extends TupleScheme<idinfo> {

        @Override
        public void write(org.mi.thrift.protocol.TProtocol prot, idinfo struct) throws org.mi.thrift.TException {
            TTupleProtocol oprot = (TTupleProtocol) prot;
            BitSet optionals = new BitSet();
            if (struct.isSetId()) {
                optionals.set(0);
            }
            if (struct.isSetTimestamp()) {
                optionals.set(1);
            }
            oprot.writeBitSet(optionals, 2);
            if (struct.isSetId()) {
                oprot.writeI64(struct.id);
            }
            if (struct.isSetTimestamp()) {
                oprot.writeI64(struct.timestamp);
            }
        }

        @Override
        public void read(org.mi.thrift.protocol.TProtocol prot, idinfo struct) throws org.mi.thrift.TException {
            TTupleProtocol iprot = (TTupleProtocol) prot;
            BitSet incoming = iprot.readBitSet(2);
            if (incoming.get(0)) {
                struct.id = iprot.readI64();
                struct.setIdIsSet(true);
            }
            if (incoming.get(1)) {
                struct.timestamp = iprot.readI64();
                struct.setTimestampIsSet(true);
            }
        }
    }

}

