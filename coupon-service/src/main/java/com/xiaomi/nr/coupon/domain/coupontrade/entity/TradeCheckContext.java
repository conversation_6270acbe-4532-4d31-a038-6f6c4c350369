package com.xiaomi.nr.coupon.domain.coupontrade.entity;

import com.xiaomi.nr.coupon.api.dto.coupon.CouponBaseInfo;
import com.xiaomi.nr.coupon.api.dto.trade.CheckoutCouponInfo;
import com.xiaomi.nr.coupon.domain.coupon.model.CheckoutCouponModel;
import com.xiaomi.nr.coupon.domain.coupon.model.ErrContext;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.ServiceSceneItem;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.youpin.infra.rpc.errors.ErrorCode;
import lombok.Data;

import java.util.Objects;

/**
 * 销核结算检查
 *
 * <AUTHOR>
 * @date 2024/1/4
 */
@Data
public class TradeCheckContext {

    /**
     * 车辆vid：指定vid可用时需要校验
     */
    private String requestVid;

    /**
     * 使用方式：当前要处理是一张什么样的券（无码券/优惠码）
     */
    private String couponModeType;

    /**
     * 请求参数model
     */
    private CheckoutCouponModel requestModel;

    /**
     * 券配置信息
     */
    private CouponConfigItem config;

    /**
     * 用户无码券po
     */
    private CouponPo couponPo;

    /**
     * 发券的门店ID
     */
    private String couponOrgCode;

    /**
     * 服务场景配置
     */
    private ServiceSceneItem serviceSceneConfig;

    /**
     * 校验出错一定会写这个信息
     */
    private ErrContext errContext = new ErrContext();

    /**
     * 要返回的券结算相关信息
     */
    private CheckoutCouponInfo checkoutCouponInfo = new CheckoutCouponInfo();

    /**
     * 设置校验失败结果
     *
     * @param err ErrorCode
     * @param msg 具体失败信息
     */
    public void setErrContext(ErrorCode err, String msg) {
        this.errContext.setErrorCode(err.getCode());
        this.errContext.setErrorMsg(msg);
    }

    public void setErrContext(ErrorCode err, String msg, String invalidData) {
        this.errContext.setErrorCode(err.getCode());
        this.errContext.setErrorMsg(msg);
        this.errContext.setErrorData(invalidData);
    }


    public void setFailResult(Integer errCode, String msg, String invalidData) {
        this.checkoutCouponInfo.setValidCode(errCode);
        this.checkoutCouponInfo.setInvalidReason(msg);
        this.checkoutCouponInfo.setInvalidData(invalidData);
    }

    /**
     * 　设置券信息
     *
     * @param info 基本信息
     */
    public void setCouponBaseInfo(CouponBaseInfo info) {
        this.checkoutCouponInfo.setCouponBaseInfo(info);
    }

    /**
     * 设置券叠加分组
     *
     * @param groupNo 分组号
     */
    public void setCouponGroupNo(String groupNo) {
        this.checkoutCouponInfo.setCouponGroupNo(groupNo);
    }

    /**
     * 是否校验通过
     *
     * @return bool
     */
    public boolean isCheckPass() {
        return Objects.nonNull(this.getErrContext()) &&
                (Objects.isNull(this.getErrContext().getErrorCode()) ||
                        this.getErrContext().getErrorCode() == 0);
    }
}
