package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.po.CouponScenePermissionPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.po.CouponScenePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description: 优惠券场景授权mapper
 * @author: heji<PERSON><PERSON>
 * @Date 2022/2/26 9:39 下午
 * @Version: 1.0
 **/
@Mapper
@Component
public interface CouponScenePermissionMapper {

    /**
     * 获取优惠券场景
     *
     */
    @Select("select app_id from nr_coupon_scene_permission where status = 1 and scene_id = #{sceneId}")
    List<String> getCouponScenePermission(@Param("sceneId") Long sceneId);


}
