package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import org.springframework.stereotype.Component;

/**
 * 校验用户已领券数据（针对单个券配置）
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Component
public class UserFetchLimit extends CheckerAbstract {

    @Override
    public boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {
        if (!ctx.isCheckUserFetchLimit()) {
            return true;
        }

        errCtx.setInvalidConfig(true);

        CouponConfigItem config = ctx.getConfigInfoMap().get(respItem.getConfigId());
        if (config.getCouponConfigInfo().getFetchLimit() <= ctx.getUserFetchedCountMap().getOrDefault(config.getConfigId().longValue(), 0)) {
            errCtx.setErrCode(ErrCode.ASSIGN_COUPON_USER_LIMITED.getCode());
            errCtx.setErrMsg("用户已领券数量超过限制");
            return false;
        }
        return true;
    }
}
