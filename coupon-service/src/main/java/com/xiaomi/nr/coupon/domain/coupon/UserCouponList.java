package com.xiaomi.nr.coupon.domain.coupon;

import com.xiaomi.nr.coupon.api.dto.PageBeanStream;
import com.xiaomi.nr.coupon.api.dto.coupon.CouponList;
import com.xiaomi.nr.coupon.api.dto.coupon.list.UserCouponListDto;
import com.xiaomi.nr.coupon.api.dto.coupon.list.UserCouponListRequest;
import com.xiaomi.nr.coupon.constant.CommonConstant;
import com.xiaomi.nr.coupon.constant.CouponConfigConstant;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponConvert;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponFormatCommon;
import com.xiaomi.nr.coupon.domain.coupon.model.CouponInfoModel;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave.UserCouponListMapper;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.utils.profiler.JProfilerUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.profiler.Profiler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户优惠券列表服务
 */
@Slf4j
@Service
public class UserCouponList {

    @Autowired
    private UserCouponListMapper userCouponListMapper;

    @Autowired
    private CouponConvert couponConvert;

    @Autowired
    private CouponFormatCommon couponFormatCommon;

    /**
     * 获取用户优惠券列表
     *
     * @param req         接口查询参数
     * @return PageBean<> 用户优惠券列表分页返回值
     */
    public PageBeanStream<UserCouponListDto> getUserCouponList(UserCouponListRequest req) throws BizError {

        Profiler profiler = JProfilerUtil.createNestedProfiler("getUserCouponList");
        log.info("getUserCouponList. req:{}", GsonUtil.toJson(req));
        initRequest(req);
        JProfilerUtil.startNestedPoint(profiler,"userCouponList-getUserCouponList");

        //获取用户优惠券信息(db -> po)
        long nowTime = TimeUtil.getNowUnixSecond();
        long nearlyDaysTime = nowTime - 90L * 24L * 3600L;
        List<CouponPo> couponPoOriginList = getCouponPoList(req, nowTime, nearlyDaysTime);
        List<CouponPo> couponPoList = vaildExpiredCoupon(couponPoOriginList, nearlyDaysTime);
        JProfilerUtil.startNestedPoint(profiler,"userCouponList-getCouponPoList");

        //格式化优惠券信息(po -> model)
        List<CouponInfoModel> couponInfoModelList = formatCouponList(req.getBizPlatform(), couponPoList);
        JProfilerUtil.startNestedPoint(profiler,"userCouponList-formatCouponList");

        //过滤特定使用渠道的优惠券
        couponInfoModelList = filterUseChannelCoupon(req.getUseChannel(), couponInfoModelList);

        //构建用户优惠券列表返回值(model -> dto)
        List<UserCouponListDto> userCouponListDtoList = convert2UserCouponListDto(couponInfoModelList);

        int poSize = couponPoList.size();
        int pageSize = req.getPageSize();
        int total = userCouponListDtoList.size();
        long lastId = poSize > CommonConstant.ZERO_INT ? couponPoList.get(poSize - CommonConstant.ONE_INT).getId() : CommonConstant.ZERO_LONG;

        JProfilerUtil.stopNestedPoint(profiler);
        //全量返回(排序)
        if (!isPageStatus(req.getStatus())) {
            return new PageBeanStream<>(lastId, Math.max(pageSize, total), true, couponSort(userCouponListDtoList));
        }

        //分页返回
        return new PageBeanStream<>(lastId, pageSize, pageSize > poSize, userCouponListDtoList);
    }

    /**
     * 获取用户优惠券列表（专门兼容pulse_v2里的）
     *
     * @param req         接口查询参数
     * @return PageBean<> 用户优惠券列表分页返回值
     */
    public PageBeanStream<CouponList> getUserCouponListByPulseV2(UserCouponListRequest req) throws BizError {

        Profiler profiler = JProfilerUtil.createNestedProfiler("getUserCouponListByPulseV2");
        initRequest(req);
        JProfilerUtil.startNestedPoint(profiler,"getUserCouponListByPulseV2-getUserCouponList");

        //获取用户优惠券信息(db -> po)
        long nowTime = TimeUtil.getNowUnixSecond();
        long nearlyDaysTime = nowTime - 90L * 24L * 3600L;
        List<CouponPo> couponPoOriginList = getCouponPoList(req, nowTime, nearlyDaysTime);
        List<CouponPo> couponPoList = vaildExpiredCoupon(couponPoOriginList, nearlyDaysTime);
        JProfilerUtil.startNestedPoint(profiler,"getUserCouponListByPulseV2-getCouponPoList");

        //格式化优惠券信息(po -> model)
        List<CouponInfoModel> couponInfoModelList = formatCouponList(req.getBizPlatform(), couponPoList);
        JProfilerUtil.startNestedPoint(profiler,"getUserCouponListByPulseV2-formatCouponList");

        //过滤特定使用渠道的优惠券
        couponInfoModelList = filterUseChannelCoupon(req.getUseChannel(), couponInfoModelList);

        //构建用户优惠券列表返回值(model -> dto)
        List<UserCouponListDto> userCouponListDtoList = convert2UserCouponListDto(couponInfoModelList);

        //构建用户优惠券列表返回值(model -> dto)
        List<CouponList> couponList = convert2UserCouponListByPulseV2Dto(couponInfoModelList);

        int poSize = couponPoList.size();
        int pageSize = req.getPageSize();
        int total = userCouponListDtoList.size();
        long lastId = poSize > CommonConstant.ZERO_INT ? couponPoList.get(poSize - CommonConstant.ONE_INT).getId() : CommonConstant.ZERO_LONG;

        JProfilerUtil.stopNestedPoint(profiler);
        //全量返回(排序)
        if (!isPageStatus(req.getStatus())) {
            return new PageBeanStream<>(lastId, Math.max(pageSize, total), true, couponSortByPulseV2(couponList));
        }

        //分页返回
        return new PageBeanStream<>(lastId, pageSize, pageSize > poSize, couponList);
    }


    /**
     * 初始化请求参数(兼容分页与不分页请求)
     *
     * @param req 请求参数
     * @return 初始化之后的值
     */
    private UserCouponListRequest initRequest(UserCouponListRequest req){
        if(Objects.isNull(req.getPageSize())){
            req.setPageSize(CommonConstant.PAGESIZE_DEFAULT);
        }

        if(req.getPageSize() > CommonConstant.PAGESIZE_MAX){
            req.setPageSize(CommonConstant.PAGESIZE_MAX);
        }

        if(Objects.isNull(req.getLastId())){
            req.setLastId(CommonConstant.ZERO_LONG);
        }
        return req;
    }



    /**
     * 格式化用户优惠券列表
     *
     * @param couponPoList 优惠券列表基础信息(DB数据, 不包含券配置信息)
     * @return List<>      优惠券列表详细信息,包含券配置缓存信息
     */
    private List<CouponInfoModel> formatCouponList(List<Integer> bizPlatform, List<CouponPo> couponPoList) throws BizError {
        if (CollectionUtils.isEmpty(couponPoList)) {
            return Collections.emptyList();
        }

        //调用公共的format方法, 获取优惠券详细信息
        return couponFormatCommon.formatCouponList(bizPlatform, couponPoList);
    }



    /**
     * 过滤特定使用范围的优惠券
     *
     * @param useChannel     使用渠道
     * @param couponInfoList 所有渠道下的优惠券信息
     * @return List<>        指定使用渠道下的用户优惠券信息
     */
    private List<CouponInfoModel> filterUseChannelCoupon(String useChannel, List<CouponInfoModel> couponInfoList){
       if(StringUtils.isEmpty(useChannel) || CollectionUtils.isEmpty(couponInfoList)){
           return couponInfoList;
       }

        String[] useChannels = useChannel.split(",");

        //过滤出包含指定使用渠道的优惠券信息
        List<CouponInfoModel> list = new ArrayList<>();
        for(CouponInfoModel item : couponInfoList){
            for(String channel : useChannels) {
                if (item.getUseChannel().contains(channel)) {
                    list.add(item);
                    break;
                }
            }
        }
        return list;
    }



    /**
     * 构建用户优惠券列表返回值 (model -> dto)
     *
     * @param couponInfoModelList 优惠券列表详细信息
     * @return List<>             用户优惠券列表接口返回值
     */
    private List<UserCouponListDto> convert2UserCouponListDto(List<CouponInfoModel> couponInfoModelList) {
        if (CollectionUtils.isEmpty(couponInfoModelList)) {
            return Collections.emptyList();
        }

        //调用公共转换方法
        List<UserCouponListDto> userCouponListDtoList = new ArrayList<>(couponInfoModelList.size());
        couponInfoModelList.forEach(couponInfoModel -> {
            UserCouponListDto userCouponListDto = couponConvert.convert2UserCouponListDto(couponInfoModel);
            if (!Objects.isNull(userCouponListDto)) {
                userCouponListDtoList.add(userCouponListDto);
            }
        });
        return userCouponListDtoList;
    }

    /**
     * 构建用户优惠券列表返回值 (model -> dto, 兼容pulseV2原接口专用)
     *
     * @param couponInfoModelList 优惠券列表详细信息
     * @return List<>             用户优惠券列表接口返回值
     */
    private List<CouponList> convert2UserCouponListByPulseV2Dto(List<CouponInfoModel> couponInfoModelList) {
        if (CollectionUtils.isEmpty(couponInfoModelList)) {
            return Collections.emptyList();
        }

        //调用公共转换方法
        List<CouponList> couponList = new ArrayList<>(couponInfoModelList.size());
        couponInfoModelList.forEach(couponInfoModel -> {
            CouponList item = couponConvert.convert2CouponListDto(couponInfoModel, null);
            if (!Objects.isNull(item)) {
                couponList.add(item);
            }
        });
        return couponList;
    }


    /**
     * 优惠券列表排序，按力度大小，是否已用排序(目前排序只针对于unused、all状态的查询进行排序)
     *
     * @param couponList 用户优惠券列表
     * @return List<>    按规则排序后的列表
     */
    private List<UserCouponListDto> couponSort(List<UserCouponListDto> couponList) {
        if (CollectionUtils.isEmpty(couponList)) {
            return Collections.emptyList();
        }

        //未使用状态
        List<UserCouponListDto> couponUnUsedList = new ArrayList<>();
        //其他状态
        List<UserCouponListDto> couponUsedList = new ArrayList<>();
        couponList.forEach(coupon -> {
            if (StringUtils.equals(coupon.getStatus(), CouponStatusEnum.UNUSED.getValue())) {
                couponUnUsedList.add(coupon);
            }else {
                couponUsedList.add(coupon);
            }});

        //按value值从大到小排序返回
        List<UserCouponListDto> sortResult = sortList(couponUnUsedList);
        sortResult.addAll(sortList(couponUsedList));
        return sortResult;
    }

    /**
     * 优惠券列表排序，按力度大小，是否已用排序(目前排序只针对于unused、all状态的查询进行排序，兼容pulseV2专用)
     *
     * @param couponList 用户优惠券列表
     * @return List<>    按规则排序后的列表
     */
    private List<CouponList> couponSortByPulseV2(List<CouponList> couponList) {
        if (CollectionUtils.isEmpty(couponList)) {
            return Collections.emptyList();
        }

        //未使用状态
        List<CouponList> couponUnUsedList = new ArrayList<>();
        //其他状态
        List<CouponList> couponUsedList = new ArrayList<>();
        couponList.forEach(coupon -> {
            if (StringUtils.equals(coupon.getStat(), CouponStatusEnum.UNUSED.getValue())) {
                couponUnUsedList.add(coupon);
            }else {
                couponUsedList.add(coupon);
            }});

        //按value值从大到小排序返回
        List<CouponList> sortResult = sortListByPulseV2(couponUnUsedList);
        sortResult.addAll(sortListByPulseV2(couponUsedList));
        return sortResult;
    }


    /**
     * 优惠券列表信息按value倒序排列
     *
     * @param userCouponListDtoList    初始优惠券列表
     * @return List<UserCouponListDto> 倒排后优惠券列表
     */
    private List<UserCouponListDto> sortList(List<UserCouponListDto> userCouponListDtoList) {

        //先按showValue倒序排列，showValue相同时再按addTime倒序排列
        userCouponListDtoList = userCouponListDtoList.stream().sorted((c1,c2) -> {
            String beforeV = c1.getShowValue();
            String afterV = c2.getShowValue();
            if(StringUtils.equals(beforeV,afterV)){
                return -c1.getAddTime().compareTo(c2.getAddTime());
            }
            BigDecimal c1V = new BigDecimal(CommonConstant.EMPTY_STR.equals(beforeV) ? CommonConstant.ZERO_STR : beforeV);
            BigDecimal c2V = new BigDecimal(CommonConstant.EMPTY_STR.equals(afterV) ? CommonConstant.ZERO_STR : afterV);
            return -c1V.compareTo(c2V);

        }).collect(Collectors.toList());

        return userCouponListDtoList;
    }

    /**
     * 优惠券列表信息按value倒序排列 (兼容pulseV2专用)
     *
     * @param userCouponListDtoList    初始优惠券列表
     * @return List<> 倒排后优惠券列表
     */
    private List<CouponList> sortListByPulseV2(List<CouponList> userCouponListDtoList) {

        //先按value倒序排列，value相同时再按addTime倒序排列
        userCouponListDtoList = userCouponListDtoList.stream().sorted((c1,c2) -> {
            String beforeV = c1.getValue();
            String afterV = c2.getValue();
            if(StringUtils.equals(beforeV,afterV)){
                return -c1.getAddTime().compareTo(c2.getAddTime());
            }
            BigDecimal c1V = new BigDecimal(CommonConstant.EMPTY_STR.equals(beforeV) ? CommonConstant.ZERO_STR : beforeV);
            BigDecimal c2V = new BigDecimal(CommonConstant.EMPTY_STR.equals(afterV) ? CommonConstant.ZERO_STR : afterV);
            return -c1V.compareTo(c2V);

        }).collect(Collectors.toList());

        return userCouponListDtoList;
    }


    /**
     * 优惠券状态校验(判断是否分页返回)
     *
     * @param status 优惠券状态
     * @return bool  true:分页返回，false:全量返回
     */
    private boolean isPageStatus(String status) {
        return CouponConfigConstant.COUPON_STATUS_PAGEMAP.containsKey(status);
    }


    /**
     * 过期的券只返回近90天以内的
     *
     * @param couponPoOriginList
     * @param nearlyDaysTime
     * @return
     */
    private List<CouponPo> vaildExpiredCoupon(List<CouponPo> couponPoOriginList, long nearlyDaysTime){
        List<CouponPo> couponPoList = new ArrayList<>();
        if(CollectionUtils.isEmpty(couponPoOriginList)){
            return couponPoList;
        }

        for(CouponPo couponPo : couponPoOriginList){
            if(StringUtils.equals(CouponStatusEnum.EXPIRED.getValue(), couponPo.getStat()) && couponPo.getAddTime() < nearlyDaysTime){
                continue;
            }

            if(Long.parseLong(couponPo.getEndTime()) < nearlyDaysTime){
                continue;
            }
            couponPoList.add(couponPo);
        }

        return couponPoList;
    }



    /**
     * 从DB获取用户优惠券列表初始信息
     *
     * @param req     用户优惠券列表请求参数
     * @return List<> 优惠券列表(DB)
     */
    private List<CouponPo> getCouponPoList(UserCouponListRequest req, long nowTime, long nearlyDaysTime) {

        //基础查询参数
        List<Integer> bizPlatformList = req.getBizPlatform();
        String status = req.getStatus();
        long userId = req.getUserId();
        Long lastId = req.getLastId();
        Integer pageSize = req.getPageSize();
        String sendChannel = req.getSendChannel();
        CouponStatusEnum statEunm = CouponStatusEnum.findByValue(status);
        if (Objects.isNull(statEunm)) {
            log.info("coupon.getUserCouponList.getCouponPoList(), 非法的优惠券状态, 请求参数request={}", req);
            return Collections.emptyList();
        }

        switch (statEunm) {

            //优惠券状态为所有(all): 全量返回
            case ALL:
                return userCouponListMapper.getCouponByAll(userId, sendChannel, nearlyDaysTime, bizPlatformList);

            //优惠券状态为未使用(unused): 全量返回
            case UNUSED:
                return userCouponListMapper.getCouponByUnused(userId, nowTime, sendChannel, bizPlatformList);

            //优惠券状态为已使用(used): 分页返回
            case USED:
                return userCouponListMapper.getCouponByUsedLocked(userId, sendChannel, lastId, pageSize, bizPlatformList);

            //优惠券状态为已分享/已领取(presented/received): 分页返回
            case PRESENTED:
            case RECEIVED:
                return userCouponListMapper.getCouponByStat(userId, status, sendChannel, lastId, pageSize, bizPlatformList);

            //优惠券状态为已使用+已分享+已领取(used+presented+received): 分页返回
            case USED_PRESENTED_RECEIVED:
                return userCouponListMapper.getCouponByUsedPreRec(userId, nowTime, sendChannel, lastId, pageSize, bizPlatformList);

            //优惠券状态为已过期(expired): 分页返回
            case EXPIRED:
                //只返回近90天以内过期的券
                return userCouponListMapper.getCouponByExpired(userId, nearlyDaysTime, nowTime, sendChannel, lastId, pageSize, bizPlatformList);

            default:
                log.info("coupon.UserCouponList.getCouponPoList(), 优惠券状态不合法, 请求参数request={}", req);
                return Collections.emptyList();
        }
    }

    /**
     * 根据uid和券配置id查询可用券id
     * @param uid
     * @param configIdList
     * @return
     */
    public Map<Long, List<Long>> getValidCouponIdListByUidConfigIds(Long uid, List<Long> configIdList) {
        List<CouponPo> couponPoList = userCouponListMapper.getCouponByUidConfigIdList(uid, configIdList);
        Map<Long, List<Long>> couponMap = new HashMap<>();
        if (CollectionUtils.isEmpty(configIdList)) {
            return couponMap;
        }
        //TODO 券有效性的其它校验规则
        Long nowTime = TimeUtil.getNowUnixSecond();
        Map<Long, List<CouponPo>> validCouponMap = couponPoList.stream().filter(couponPo -> couponPo.getStat().equals(CouponStatusEnum.UNUSED.getValue())
        && Long.parseLong(couponPo.getStartTime()) <= nowTime && Long.parseLong(couponPo.getEndTime()) >= nowTime)
                .collect(Collectors.groupingBy(CouponPo::getTypeId));
        for (Long configId : validCouponMap.keySet()) {
            couponMap.put(configId, validCouponMap.get(configId).stream().map(CouponPo::getId).collect(Collectors.toList()));
        }
        return couponMap;
    }

}