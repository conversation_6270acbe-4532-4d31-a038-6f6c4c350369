package com.xiaomi.nr.coupon.domain.coupon.model;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherGoodsItemDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import lombok.Data;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/9/13
 */
@Data
public class ProductUsableCouponContext {

    /**
     * 匹配结果：关键结果，先取此结果，再结合别的信息，做最终输出
     */
    private List<MatcherRespItemDo> keyResp = new ArrayList<>();

    /**
     * 内部数据：券配置信息
     */
    private Map<Long, CouponConfigItem> configInfoMap = new HashMap<>();

    /**
     * 内部数据：用户券信息，用户券ID=>用户券信息
     */
    private Map<Long, CouponPo> userCouponMap = new HashMap<>();

    /**
     * 区配结果：可用商品，取传入商品与券配置可用商品的交集过滤结果，券配置ID=>商品信息列表
     */
    private Map<Long, List<MatcherGoodsItemDo>> validGoods = new HashMap<>();

    /**
     * 更新属性
     *
     * @param matcherContextDo  matcherContextDo
     */
    public void fill(MatcherContextDo matcherContextDo) {
        if (Objects.nonNull(matcherContextDo)) {
            keyResp.addAll(matcherContextDo.getKeyResp());
            configInfoMap.putAll(matcherContextDo.getConfigInfoMap());
            userCouponMap.putAll(matcherContextDo.getUserCouponMap());
            validGoods.putAll(matcherContextDo.getValidGoods());
        }
    }

}
