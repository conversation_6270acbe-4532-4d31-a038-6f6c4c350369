package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface CouponMapper {

    String TB_COUPON_FIELD = "id, user_id, type_id, activity_id, start_time, end_time, days, stat, order_id, use_time, " +
            "expire_time, is_pass, admin_id, admin_name, add_time, send_type, from_order_id, replace_money, invalid_time, " +
            "offline, reduce_express, parent_id, send_channel, request_id, extend_info, biz_platform " ;

    /**
     * 券写入
     *
     * @param coupon CouponPo
     * @return Integer
     */
    @Insert("insert into tb_coupon (" +
            "id, user_id, type_id, activity_id, start_time, end_time, days, stat, order_id, use_time, " +
            "expire_time, is_pass, admin_id, admin_name, add_time, send_type, from_order_id, replace_money, invalid_time, " +
            "offline, reduce_express, parent_id, send_channel, request_id, extend_info, biz_platform" +
            ") values(" +
            "#{coupon.id},#{coupon.userId},#{coupon.typeId},#{coupon.activityId},#{coupon.startTime},#{coupon.endTime}," +
            "#{coupon.days},#{coupon.stat},#{coupon.orderId},#{coupon.useTime},#{coupon.expireTime},#{coupon.isPass}," +
            "#{coupon.adminId},#{coupon.adminName},#{coupon.addTime},#{coupon.sendType},#{coupon.fromOrderId},#{coupon.replaceMoney}," +
            "#{coupon.invalidTime},#{coupon.offline},#{coupon.reduceExpress},#{coupon.parentId},#{coupon.sendChannel},#{coupon.requestId}," +
            "#{coupon.extendInfo},#{coupon.bizPlatform})")
    Integer insert(@Param("coupon") CouponPo coupon);

    /**
     * 获取幂等数据
     *
     * @param userId      Long
     * @param sendChannel String
     * @param requestId   String
     * @param missionId Long
     * @return List<CouponPo>
     */
    @Select("select id, user_id, type_id, activity_id, start_time, end_time, order_id, from_order_id " +
            " from tb_coupon" +
            " where user_id=#{userId} and send_channel=#{sendChannel} and request_id=#{requestId} and activity_id=#{missionId}")
    List<CouponPo> getIdemData(@Param("userId") Long userId, @Param("sendChannel") String sendChannel, @Param("requestId") String requestId, @Param("missionId") Long missionId);


    /**
     * 根据用户Id和券Id获取用户券列表
     * @param uid
     * @param couponIdList
     * @return
     */
    @Select("<script>select " + TB_COUPON_FIELD + " from tb_coupon where user_id=#{uid} and " +
            "id in <foreach collection='couponIdList' item='couponId' index='index' open='(' close=')' separator=','>#{couponId}</foreach>" +
            "</script>")
    List<CouponPo> getByCouponIdList(@Param("uid") long uid, @Param("couponIdList") List<Long> couponIdList);

    /**
     * 锁定用户券
     *
     * @param id            ID
     * @param userId        用户Id
     * @param statOld       原状态
     * @param orderId       订单ID
     * @param statNew       新状态
     * @param replaceMoney  抵扣钱
     * @param reduceExpress 抵扣邮费
     * @param offline       是否线下
     * @return 更新数
     */
    @Update("update tb_coupon " +
            "set stat=#{statNew}, " +
            "order_id=#{orderId}, " +
            "replace_money=#{replaceMoney}, " +
            "reduce_express=#{reduceExpress}, " +
            "offline=#{offline} " +
            "where id=#{id} and user_id=#{userId} and stat=#{statOld}")
    int lockCoupon(@Param("id") Long id, @Param("userId") Long userId, @Param("statOld") String statOld,
                   @Param("statNew") String statNew, @Param("orderId") Long orderId,
                   @Param("replaceMoney") BigDecimal replaceMoney, @Param("reduceExpress") BigDecimal reduceExpress,
                   @Param("offline") Integer offline);


    /**
     * 退还用户券
     *
     * @param couponIds     ID
     * @param userId        用户Id
     * @param statOld       原状态
     * @param orderId       订单ID
     * @param statNew       新状态
     * @param replaceMoney  抵扣钱
     * @param reduceExpress 抵扣邮费
     * @param offline       是否线下
     * @return 更新数
     */
    @Update("update tb_coupon " +
            "set stat=#{statNew}, " +
            "replace_money=#{replaceMoney}, " +
            "reduce_express=#{reduceExpress} " +
            " where id in (${couponIds}) and user_id=#{userId} and order_id=#{orderId} and stat=#{statOld}")
    int returnCoupon(@Param("couponIds") String couponIds, @Param("userId") Long userId, @Param("statOld") String statOld,
                   @Param("statNew") String statNew, @Param("orderId") Long orderId,
                   @Param("replaceMoney") BigDecimal replaceMoney, @Param("reduceExpress") BigDecimal reduceExpress,
                   @Param("offline") Integer offline);

    /**
     * 退还用户券V2
     *
     * @param couponIds     ID
     * @param userId        用户Id
     * @param statOld       原状态
     * @param orderId       订单ID
     * @param statNew       新状态
     * @return 更新数
     */
    @Update("update tb_coupon " +
            "set stat=#{statNew} " +
            " where id in (${couponIds}) and user_id=#{userId} and order_id=#{orderId} and stat=#{statOld}")
    int returnCouponV2(@Param("couponIds") String couponIds, @Param("userId") Long userId, @Param("statOld") String statOld,
                     @Param("statNew") String statNew, @Param("orderId") Long orderId);


    /**
     * 核销用户券
     * @param couponIds
     * @param userId
     * @param statOld
     * @param statNew
     * @param orderId
     * @param useTime
     * @return
     */
    @Update("update tb_coupon " +
            "set stat=#{statNew}, " +
            "use_time=#{useTime} " +
            "where id in (${couponIds}) and user_id=#{userId} and order_id=#{orderId} and stat=#{statOld}")
    int consumeCoupon(@Param("couponIds") String couponIds, @Param("userId") Long userId, @Param("statOld") String statOld,
                   @Param("statNew") String statNew, @Param("orderId") Long orderId,@Param("useTime") Long useTime);




}
