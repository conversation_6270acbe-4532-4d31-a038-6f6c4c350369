package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.po;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 券配置ID列表（缓存）
 *
 * <AUTHOR>
 */
@Data
public class ConfigIdListCachePo implements Serializable {

    private static final long serialVersionUID = -5547358642433794338L;

    /**
     * 券配置ID列表
     */
    private List<Long> configIds;

    /**
     * 缓存生成时间
     */
    private String cacheCreateTime;
}
