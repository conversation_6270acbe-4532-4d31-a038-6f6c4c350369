package com.xiaomi.nr.coupon.domain.matchertool.core;

import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;

/**
 * 校验工具接口
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
public interface MatcherCheckToolInterface {

    /**
     * 校验方法
     *
     * @param ctx      MatcherContextDo
     * @param respItem MatcherRespItemDo
     * @return MatcherErrContextDo
     */
    MatcherErrContextDo check(MatcherContextDo ctx, MatcherRespItemDo respItem);

}
