package com.xiaomi.nr.coupon.infrastructure.repository;

import com.xiaomi.nr.coupon.domain.coupontrade.convert.UserCouponLogConvert;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CodeCouponLogMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CouponLogMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CodeCouponLogPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponLogPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

@Component
@Slf4j
public class UserCouponLogRepository {

    @Autowired
    private CouponLogMapper couponLogMapper;

    @Autowired
    private CodeCouponLogMapper codeCouponLogMapper;

    @Autowired
    private UserCouponLogConvert userCouponLogConvert;


    /**
     * 记录锁定优惠码日志
     * @param userId
     * @param orderId
     * @param couponIndex
     * @param offline
     * @param replaceMoney
     * @param reduceExpress
     */
    public void insertLockCodeCouponLog(Long userId, Long orderId, String couponIndex, Integer offline, BigDecimal replaceMoney, BigDecimal reduceExpress) {
        // 日志
        CodeCouponLogPo codecouponLog = userCouponLogConvert.initLockCodeCouponLog(userId, orderId, couponIndex, offline, replaceMoney, reduceExpress);

        int insertAffected = codeCouponLogMapper.insert(codecouponLog);
        if (insertAffected <= 0) {
            log.error("lockCodeCoupon log insert fail. userId:{},couponIndex:{},orderId:{}", userId, couponIndex, orderId);
        }
    }

    /**
     * 记录核销优惠码日志
     * @param userId
     * @param orderId
     * @param couponIndex
     * @param offline
     */
    public void insertConsumeCodeCouponLog(Long userId, Long orderId, String couponIndex, Integer offline) {
        // 日志
        CodeCouponLogPo codecouponLog = userCouponLogConvert.initConsumerCodeCouponLog(userId, orderId, couponIndex, offline, BigDecimal.valueOf(0.0), BigDecimal.valueOf(0));

        int insertAffected = codeCouponLogMapper.insert(codecouponLog);
        if (insertAffected <= 0) {
            log.error("consumeCodeCoupon log insert fail. userId:{},couponIndex:{},orderId:{}", userId, couponIndex, orderId);
        }
    }

    /**
     * 记录退还优惠码日志
     * @param userId
     * @param orderId
     * @param couponIndex
     * @param offline
     * @param replaceMoney
     * @param reduceExpress
     */
    public void insertRollbackCodeCouponLog(Long userId, Long orderId, String couponIndex, Integer offline, BigDecimal replaceMoney, BigDecimal reduceExpress) {
        // 日志
        CodeCouponLogPo codecouponLog = userCouponLogConvert.initRollbackCodeCouponLog(userId, orderId, couponIndex, offline, replaceMoney.negate(), reduceExpress.negate());

        int insertAffected = codeCouponLogMapper.insert(codecouponLog);
        if (insertAffected <= 0) {
            log.error("rollbackCodeCoupon log insert fail. userId:{},couponIndex:{},orderId:{}", userId, couponIndex, orderId);
        }
    }

    /**
     * 记录使用优惠券日志
     * @param userId
     * @param orderId
     * @param couponPos
     * @param offline
     */
    @Async("asyncExecutor")
    public void insertConsumeCouponLog(long userId, long orderId, List<CouponPo> couponPos, int offline, Long clientId) {
        //添加锁券日志
        List<CouponLogPo> couponLogPos = userCouponLogConvert.buildConsumeCouponLogPos(userId, couponPos, offline, clientId);
        int insertAffected = couponLogMapper.batchInsert(couponLogPos);
        if (insertAffected < couponPos.size()) {
            log.error("consumeCoupon log insert fail. userId:{}, orderId:{}, insertAffected: {}, poSize: {}", userId, orderId, insertAffected, couponPos.size());
        }
    }

    /**
     * 记录回滚优惠券日志
     * @param userId
     * @param orderId
     * @param couponPos
     * @param offline
     */
    @Async("asyncExecutor")
    public void insertRollbackCouponLog(long userId, long orderId, List<CouponPo> couponPos, int offline, Long clientId) {
        //添加锁券日志
        List<CouponLogPo> couponLogPos = userCouponLogConvert.buildRollbackCouponLogPos(userId, couponPos, offline, clientId);
        int insertAffected = couponLogMapper.batchInsert(couponLogPos);
        if (insertAffected < couponPos.size()) {
            log.error("rollbackCoupon log insert fail. userId:{}, orderId:{}, insertAffected: {}, poSize: {}", userId, orderId, insertAffected, couponPos.size());
        }
    }

}
