package com.xiaomi.nr.coupon.domain.coupon;

import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.coupon.impl.CarCouponList;
import com.xiaomi.nr.coupon.domain.coupon.impl.UserCouponListImpl;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/5/20
 */
@Component
public class UserCouponListFactory {

    @Autowired
    private CarCouponList carCouponList;

    @Autowired
    private UserCouponListImpl userCouponList;


    public AbstractUserCouponList getUserCouponList(List<Integer> bizPlatformList) throws BizError {

        // 领域校验放在了参数校验，此处只关注工厂逻辑
        return bizPlatformList.contains(BizPlatformEnum.CAR_AFTER_SALE.getCode()) ? carCouponList : userCouponList;
    }
}
