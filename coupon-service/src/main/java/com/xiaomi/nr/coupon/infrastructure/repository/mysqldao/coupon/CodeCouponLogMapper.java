package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CodeCouponLogPo;
import org.apache.ibatis.annotations.Insert;
import org.springframework.stereotype.Repository;

/**
 * 新券Db日志访问接口
 *
 * <AUTHOR>
 * @date 2021/4/22
 */
@Repository
public interface CodeCouponLogMapper {

    /**
     * 插入日志
     *
     * @param codeCouponLog 日志
     * @return 影响行数
     */
    @Insert(" insert into tb_codecoupon_log" +
            " (coupon_index, log_type, replace_money, `reduce_express`, offline, order_id, user_id, use_desc, admin_id, admin_desc, add_time) " +
            " values (#{couponIndex}, #{logType}, #{replaceMoney}, #{reduceExpress}, #{offline}, #{orderId}, #{userId}, #{useDesc}, #{adminId}, #{adminDesc}, #{addTime})")
    int insert(CodeCouponLogPo codeCouponLog);
}
