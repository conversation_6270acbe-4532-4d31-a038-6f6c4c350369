package com.xiaomi.nr.coupon.domain.matchertool.core;

import com.alibaba.nacos.common.utils.StringUtils;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.*;
import com.xiaomi.nr.coupon.domain.matchertool.entity.nocodefetchable.NoCodeFetchableReqDo;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 3C领域
 *
 * <AUTHOR>
 * @date 2024/10/14
 */
@Slf4j
public abstract class MatcherAbstractFetchable extends MatcherAbstract {

    @Override
    public void specialInitResource(MatcherContextDo ctx) throws BizError {

        List<Long> configIds = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(ctx.getOuterConfigList())) {
            ctx.setOuterConfigList(ctx.getOuterConfigList().stream().filter(e -> Objects.nonNull(e) && e.getConfigId() > 0)
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                            new TreeSet<>(Comparator.comparing(e -> e.getConfigId() + "-" + e.getSceneCode() + "-" + e.getAppId()))
                    ), ArrayList::new)));

            configIds = ctx.getOuterConfigList().stream().map(MatcherConfigItemDo::getConfigId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(configIds)) {
                throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_NOT_FOUND, "无法获取到有效的券配置参数");
            }
        }

        if (CollectionUtils.isNotEmpty(configIds)) {
            Map<Long, CouponConfigItem> configInfoMap = getCouponConfigInfos(configIds);
            if (MapUtils.isEmpty(configInfoMap)) {
                throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_NOT_FOUND, "无法获取到有效的券配置信息");
            }
            ctx.setConfigInfoMap(configInfoMap);
        }

        if (ctx.isCheckConfigFetchLimit()) {
            ctx.setConfigFetchedCountMap(getConfigByFetchedCount(configIds));
        }

        if (ctx.isCheckUserFetchLimit()) {
            ctx.setUserFetchedCountMap(getConfigByUserFetchedCount(ctx.getUserId(), configIds));
        }

        List<String> sceneCodes = CollectionUtils.isNotEmpty(ctx.getOuterConfigList()) ? ctx.getOuterConfigList().stream().map(MatcherConfigItemDo::getSceneCode).filter(Objects::nonNull).distinct().collect(Collectors.toList()) : Collections.emptyList();
        if (CollectionUtils.isNotEmpty(sceneCodes)) {
            ctx.setSceneInfoMap(getSceneInfo(sceneCodes));
        }

    }

    @Override
    public MatcherContextDo newContext(MatcherBaseReqDo req) throws BizError {
        NoCodeFetchableReqDo p = (NoCodeFetchableReqDo) req;
        if (Objects.isNull(p)) {
            throw ExceptionHelper.create(GeneralCodes.InternalError, "可领匹配器入参不符合要求");
        }

        MatcherContextDo ctx = new MatcherContextDo();

        ctx.setUserId(p.getUserId());
        ctx.setBizPlatform(p.getBizPlatform());
        ctx.setClientId(p.getClientId());
        ctx.setOrgCode(p.getOrgCode());
        ctx.setOuterConfigList(p.getConfigList());
        ctx.setOuterGoodsList(p.getGoodsList());
        ctx.setCheckBizPlatform(p.isCheckBizPlatform());
        ctx.setCheckUserChannelAndOrgCode(p.isCheckUserChannelAndOrgCode());
        ctx.setCheckConfigGoodsInclude(p.isCheckConfigGoodsInclude());
        ctx.setCheckGlobalExcludeGoods(p.isCheckGlobalExcludeGoods());
        ctx.setCheckGlobalCouponExcludeGoods(p.isCheckGlobalCouponExcludeGoods());
        ctx.setSort(p.isSort());

        ctx.setAssignMode(p.getAssignMode());

        ctx.setCheckConfigStatus(p.isCheckConfigStatus());
        ctx.setCheckConfigFetchTime(p.isCheckConfigFetchTime());
        ctx.setCheckConfigFetchLimit(p.isCheckConfigFetchLimit());
        ctx.setCheckUserFetchLimit(p.isCheckUserFetchLimit());
        ctx.setCheckSceneCode(p.isCheckSceneCode());
        ctx.setCheckSceneStatus(p.isCheckSceneStatus());
        ctx.setCheckSceneAssignMode(p.isCheckSceneAssignMode());
        ctx.setCheckSceneAssignRights(p.isCheckSceneAssignRights());
        ctx.setCheckSceneSendMode(p.isCheckSceneSendMode());
        return ctx;
    }

    @Override
    public void matching(MatcherContextDo ctx) {
        if (MapUtils.isEmpty(ctx.getConfigInfoMap())) {
            return;
        }

        List<MatcherRespItemDo> keyResp = new ArrayList<>();
        Map<Long, List<MatcherGoodsItemDo>> validGoods = new HashMap<>();
        for (MatcherConfigItemDo outerConfig : ctx.getOuterConfigList()) {
            try {
                MatcherRespItemDo respItem = new MatcherRespItemDo();
                respItem.setConfigId(outerConfig.getConfigId());
                respItem.setSceneCode(outerConfig.getSceneCode());
                respItem.setAppId(outerConfig.getAppId());

                MatcherErrContextDo errCtx = getCheckTool().check(ctx, respItem);
                if (Objects.nonNull(errCtx)) {
                    respItem.setErrCtx(errCtx);
                    keyResp.add(respItem);
                    continue;
                } else {
                    keyResp.add(respItem);
                }

                if (validGoods.containsKey(outerConfig.getConfigId())) {
                    continue;
                }
                List<MatcherGoodsItemDo> goodsList = getValidGoods(ctx, respItem);
                if (CollectionUtils.isNotEmpty(goodsList)) {
                    validGoods.put(outerConfig.getConfigId(), goodsList);
                }
            } catch (Exception e) {
                log.error("MatcherAbstract, matching execute error, config:{}, ctx:{}", GsonUtil.toJson(outerConfig), GsonUtil.toJson(ctx), e);
            }
        }
        ctx.setKeyResp(keyResp);
        ctx.setValidGoods(validGoods);
    }
}
