package com.xiaomi.nr.coupon.application.dubbo;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.api.dto.autotest.CouponCleanDataRequest;
import com.xiaomi.nr.coupon.api.service.DubboAutoTestService;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
@Slf4j
@Service(group = "${dubbo.group}", version = "1.0", timeout = 3000, delay = 10000,cluster = "broadcast")
@ApiModule(value = "自动化测试服务", apiInterface = DubboAutoTestService.class)
public class DubboAutoTestServiceImpl implements DubboAutoTestService {

    @Autowired
    private CouponConfigRepository couponConfigRepository;



    @ApiDoc("清除券")
    @Override
    public Result<Void> cleanCouponData(CouponCleanDataRequest request) {
        log.info("DubboAutoTestService cleanCouponData request:{}",request);
        try {
            couponConfigRepository.batchDeleteCouponConfig(request.getConfigIds().stream().map(x->x.intValue()).distinct().collect(Collectors.toList()));
            return Result.success(null);
        } catch (Exception e) {
            log.error("DubboAutoTestService cleanCouponData error request:{}",request,e);
            return Result.fromException(e, "清理缓存失败");
        }
    }

}
