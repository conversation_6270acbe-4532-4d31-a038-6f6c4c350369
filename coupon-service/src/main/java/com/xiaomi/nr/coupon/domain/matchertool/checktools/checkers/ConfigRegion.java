package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.enums.coupon.RegionTypeEnum;
import com.xiaomi.nr.coupon.enums.coupon.ShoppingModeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.storeinfo.po.OrgInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 校验区域消费劵
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Slf4j
@Component
public class ConfigRegion extends CheckerAbstract {

    @Override
    public boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {
        if (!ctx.isCheckConfigRegion()) {
            return true;
        }

        errCtx.setInvalidConfig(true);

        CouponConfigItem config = ctx.getConfigInfoMap().get(respItem.getConfigId());

        // 不限制指定区域可用
        if (MapUtils.isEmpty(config.getAssignArea())) {
            return true;
        }

        //使用门店和收货地址两个分支条件来判断
        String orgCode = ctx.getOrgCode();
        Long regionId = ctx.getCityId();
        Integer shoppingMode = ctx.getShoppingMode();
        boolean orgCodeEmpty = StringUtils.isEmpty(orgCode);

        //都不传，则不能用区域消费券
        if ((Objects.isNull(regionId) || regionId <= 0) && orgCodeEmpty) {
            errCtx.setErrCode(ErrCode.USE_COUPON_AREA_LIMIT.getCode());
            errCtx.setErrMsg("不符合消费券使用区域");
            return false;
        }

        if (StringUtils.isNotEmpty(orgCode) && ctx.getOrgInfo() == null) {
            errCtx.setErrCode(ErrCode.USE_COUPON_AREA_LIMIT.getCode());
            errCtx.setErrMsg("无法获取有效门店信息");
            return false;
        }

        //根据购物模式判断
        if (ShoppingModeEnum.LOGISTICS.getType().equals(shoppingMode) && regionId <= 0) {
            errCtx.setErrCode(ErrCode.USE_COUPON_AREA_LIMIT.getCode());
            errCtx.setErrMsg("不符合消费券使用区域");
            return false;
        }

        if (ShoppingModeEnum.SCENE.getType().equals(shoppingMode) && orgCodeEmpty) {
            errCtx.setErrCode(ErrCode.USE_COUPON_AREA_LIMIT.getCode());
            errCtx.setErrMsg("不符合消费券使用区域");
            return false;
        }

        boolean regionOrOrgCodeEmpty = regionId <= 0 || orgCodeEmpty;
        if (ShoppingModeEnum.LOGISTICS_SCENE_MIX.getType().equals(shoppingMode) && regionOrOrgCodeEmpty) {
            errCtx.setErrCode(ErrCode.USE_COUPON_AREA_LIMIT.getCode());
            errCtx.setErrMsg("不符合消费券使用区域");
            return false;
        }

        // 购物模式如果没有传，则要校正
        if (ShoppingModeEnum.DEFAULT.getType().equals(shoppingMode)) {
            if (regionId > 0 && !orgCodeEmpty) {
                shoppingMode = ShoppingModeEnum.LOGISTICS_SCENE_MIX.getType();
            } else if (regionId > 0) {
                shoppingMode = ShoppingModeEnum.LOGISTICS.getType();
            } else {
                shoppingMode = ShoppingModeEnum.SCENE.getType();
            }
        }

        for (Map.Entry<Integer, List<Integer>> entry : config.getAssignArea().entrySet()) {
            Integer regionType = entry.getKey();
            for (Integer id : entry.getValue()) {
                //物流＋现场购的混合模式，则优先判断都传的情况
                if (ShoppingModeEnum.LOGISTICS_SCENE_MIX.getType().equals(shoppingMode)) {
                    if (regionId != id.longValue()) {
                        continue;
                    }
                    if (checkRegion(regionType, id, ctx.getOrgInfo())) {
                        return true;
                    }
                }

                if (ShoppingModeEnum.LOGISTICS.getType().equals(shoppingMode)) {
                    if (regionId == id.longValue()) {
                        return true;
                    }
                }

                if (ShoppingModeEnum.SCENE.getType().equals(shoppingMode)) {
                    if (checkRegion(regionType, id, ctx.getOrgInfo())) {
                        return true;
                    }
                }
            }
        }

        errCtx.setErrCode(ErrCode.USE_COUPON_AREA_LIMIT.getCode());
        errCtx.setErrMsg("不符合消费券使用区域");
        return false;
    }

    private static boolean checkRegion(Integer regionType, Integer id, OrgInfo orgInfo) {
        if (regionType == RegionTypeEnum.REGION_PROVINCE.getType()) {
            return orgInfo.getArea().contains(RegionTypeEnum.REGION_PROVINCE.getValue() + "_" + id);
        } else if (regionType == RegionTypeEnum.REGION_CITY.getType()) {
            return orgInfo.getArea().contains(RegionTypeEnum.REGION_CITY.getValue() + "_" + id);
        }
        return false;
    }
}
