package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import cn.hutool.core.map.MapUtil;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponSceneItem;
import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.enums.couponconfig.SceneStatusEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 校验投放场景状态
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Component
public class SceneStatus extends CheckerAbstract {

    @Override
    public boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {
        if (!ctx.isCheckSceneStatus()) {
            return true;
        }

        errCtx.setInvalidConfig(true);

        CouponSceneItem sceneInfo = MapUtil.isNotEmpty(ctx.getSceneInfoMap()) ? ctx.getSceneInfoMap().get(respItem.getSceneCode()) : null;
        Integer sceneStatus = Objects.nonNull(sceneInfo) && Objects.nonNull(sceneInfo.getSceneStatus()) ? sceneInfo.getSceneStatus() : null;

        if (!SceneStatusEnum.Online.getValue().equals(sceneStatus)) {
            errCtx.setErrCode(ErrCode.ASSIGN_COUPON_SCENE_OFFLINE.getCode());
            errCtx.setErrMsg("投放场景已停止发放");
            return false;
        }

        return true;
    }
}
