package com.xiaomi.nr.coupon.enums.couponconfig;

/**
 * 优惠券投放场景状态 枚举
 *
 * <AUTHOR>
 */
public enum SceneStatusEnum {

    /**
     * 上线
     */
    Online(1,"上线"),

    /**
     * 下线
     */
    Offline(2,"下线");

    private final Integer value;
    private final String name;

    SceneStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static String findNameByValue(Integer value) {
        SceneStatusEnum[] values = SceneStatusEnum.values();
        for (SceneStatusEnum item : values) {
            if (item.getValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }
}

