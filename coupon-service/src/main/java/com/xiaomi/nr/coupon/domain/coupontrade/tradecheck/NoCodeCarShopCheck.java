package com.xiaomi.nr.coupon.domain.coupontrade.tradecheck;

import com.xiaomi.nr.coupon.api.dto.coupon.CouponBaseInfo;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.constant.CouponTagConst;
import com.xiaomi.nr.coupon.domain.coupontrade.TradeCheckAbstract;
import com.xiaomi.nr.coupon.domain.coupontrade.convert.CouponBaseInfoConvert;
import com.xiaomi.nr.coupon.domain.coupontrade.entity.TradeCheckContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.xiaomi.nr.coupon.constant.CouponConstant.SEND_SCENE_CAR_SHOP_GIFT_EXCHANGE;

/**
 * 销核结算-无码券-车商城
 *
 * <AUTHOR>
 * @date 2024/9/29
 */
@Component
public class NoCodeCarShopCheck extends TradeCheckAbstract {

    @Resource
    private CouponBaseInfoConvert couponBaseInfoConvert;

    @Resource
    private CouponInfoCheck couponInfoCheck;

    @Override
    protected Integer getFactoryKey() {
        return BizPlatformEnum.CAR_SHOP.getCode();
    }

    @Override
    protected boolean initData(TradeCheckContext ctx) throws Exception {
        CouponBaseInfo couponBaseInfo = couponBaseInfoConvert.convertNoCodeCouponPoConfig2BaseInfo(ctx.getCouponPo(), ctx.getConfig());
        // 气门芯需求新增车主专项标签
        if (SEND_SCENE_CAR_SHOP_GIFT_EXCHANGE.equals(couponBaseInfo.getSendScene())) {
            couponBaseInfo.getTags().add(CouponTagConst.CAR_OWNER_GIFT_EXCHANGE);
        }
        ctx.setCouponBaseInfo(couponBaseInfo);


        ctx.setCouponGroupNo(couponBaseInfoConvert.genCouponGroupNo(ctx.getConfig()));
        return true;
    }

    @Override
    protected boolean check(TradeCheckContext ctx) {
        // 公共校验
        if (!super.commonCheck(ctx)) {
            return false;
        }

        // 券配置是否车商城领域可用
        if (!BizPlatformEnum.CAR_SHOP.getCode().equals(ctx.getConfig().getBizPlatform())) {
            return false;
        }

        // 校验商品
        if (!couponInfoCheck.findGoodsInclude(ctx.getConfig(), ctx.getRequestModel(), ctx.getErrContext(), ctx.getCouponModeType())) {
            return false;
        }
        return true;
    }

    @Override
    protected void checkAfter(TradeCheckContext ctx) {
        ctx.getCheckoutCouponInfo().setValidSsuList(couponBaseInfoConvert.getValidSsuList(ctx.getConfig(), ctx.getRequestModel().getSkuPackageList()));
    }
}
