package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponactivity.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ActivityCacheItem implements Serializable {

    private static final long serialVersionUID = 3100034047111138445L;

    /**
     * 活动名称
     */
    @SerializedName("act_name")
    private String actName;

    /**
     * 活动码
     */
    @SerializedName("act_code")
    private String actCode;

    /**
     * 活动开始时间
     */
    @SerializedName("start_time")
    private Long startTime;

    /**
     * 活动结束时间
     */
    @SerializedName("end_time")
    private Long endTime;

    /**
     * 券信息
     */
    @SerializedName("coupons")
    private List<ActivityCacheCouponItem> coupons;
}
