package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission;

import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.po.MissionCacheItemPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;
import java.util.Map;

/**
 * 优惠券发放任务缓存
 *
 * <AUTHOR>
 */
public interface MissionCacheDao {

    /**
     * 根据发券任务id获取单个券发放任务信息
     * @param missionId Long
     * @return MissionCacheItemPo
     */
    MissionCacheItemPo get(Long missionId);


    /**
     * 根据发券任务id列表批量获取券发放任务信息
     * @param missionIdList List<Long>
     * @return Map<Long, MissionCacheItemPo>
     */
    Map<Long, MissionCacheItemPo> get(List<Long> missionIdList);

    /**
     * 获取当前有效发放任务id和券配置id的映射关系
     * @return 发放任务id和券配置id的映射关系
     */
    Map<Long, List<Long>> getMissionIdMap(List<Long> validConfigIdList);

    /**
     * 批量获取任务已发放数量
     * @param missionIds List<Long>
     * @return Map<Long, Long>
     */
    Map<Long, Long> getSendCount(List<Long> missionIds);

    /**
     * 老版本的，任务已发放数量批量加1，并校验是否超过最大发放数量
     * @param missionIds List<Long>
     * @param maxNums List<Long>
     * @throws BizError .
     */
    void incrOldSendCountAndCheck(List<Long> missionIds, List<Long> maxNums) throws BizError;

    /**
     * 任务已发放数量批量加1，并校验是否超过最大发放数量
     * @param missionIds List<Long>
     * @param maxNums List<Long>
     * @throws BizError .
     */
    void incrSendCountAndCheck(List<Long> missionIds, List<Long> maxNums) throws BizError;

    /**
     * 老版本的，任务已发放数量批量减1
     * @param missionIds List<Long>
     */
    void decrOldSendCount(List<Long> missionIds);

    /**
     * 任务已发放数量批量减1
     * @param missionIds List<Long>
     */
    void decrSendCount(List<Long> missionIds);

}
