package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import cn.hutool.core.map.MapUtil;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponSceneItem;
import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 校验基本信息
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Component
public class BaseInfo extends CheckerAbstract {

    @Override
    public boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {
        errCtx.setInvalidConfig(true);

        if (Objects.isNull(ctx) || Objects.isNull(respItem)) {
            errCtx.setErrCode(ErrCode.COUPON.getCode());
            errCtx.setErrMsg("券校验调用参数有误");
            return false;
        }

        if (Objects.nonNull(respItem.getConfigId())) {
            CouponConfigItem config = MapUtils.isNotEmpty(ctx.getConfigInfoMap()) ? ctx.getConfigInfoMap().get(respItem.getConfigId()) : null;
            if (Objects.isNull(config) ||
                    Objects.isNull(config.getConfigId()) ||
                    config.getConfigId() <= 0 ||
                    Objects.isNull(config.getCouponConfigInfo()) ||
                    Objects.isNull(config.getCouponConfigInfo().getStatus()) ||
                    Objects.isNull(config.getCouponConfigInfo().getSendScene())
            ) {
                errCtx.setErrCode(ErrCode.COUPON.getCode());
                errCtx.setErrMsg("无法获取到有效的券配置信息");
                return false;
            }

            //todo 临时加一下，后续售后服务券合上来的时候，再把下面这两行代码删掉 todo cxp
            /*if(!config.getBizPlatform().equals(BizPlatformEnum.RETAIL.getCode())) {
                errCtx.setErrCode(ErrCode.COUPON.getCode());
                errCtx.setErrMsg("非当前业务场景的券");
                return false;
            }*/
        }

        if (Objects.nonNull(respItem.getUserCouponId())) {
            CouponPo coupon = MapUtils.isNotEmpty(ctx.getUserCouponMap()) ? ctx.getUserCouponMap().get(respItem.getUserCouponId()) : null;
            if (Objects.isNull(coupon) ||
                    Objects.isNull(coupon.getId()) ||
                    Objects.isNull(coupon.getStartTime()) ||
                    Objects.isNull(coupon.getEndTime())
            ) {
                errCtx.setErrCode(ErrCode.COUPON.getCode());
                errCtx.setErrMsg("无法获取到有效的用户券信息");
                return false;
            }
        }

        if (Objects.nonNull(ctx.getOrgCode())) {
            if (Objects.isNull(ctx.getOrgInfo()) ||
                    Objects.isNull(ctx.getOrgInfo().getOrgType())) {
                errCtx.setErrCode(ErrCode.USE_COUPON_ORG_INFO_CACHE_NULL.getCode());
                errCtx.setErrMsg("无法获取到有效的门店信息");
                return false;
            }
        }

        if (Objects.nonNull(respItem.getSceneCode())) {
            CouponSceneItem sceneInfo = MapUtil.isNotEmpty(ctx.getSceneInfoMap()) ? ctx.getSceneInfoMap().get(respItem.getSceneCode()) : null;
            if (Objects.isNull(sceneInfo) || Objects.isNull(sceneInfo.getSceneStatus()) || Objects.isNull(sceneInfo.getAppIds())) {
                errCtx.setErrCode(ErrCode.ASSIGN_COUPON_SCENE_INVALID.getCode());
                errCtx.setErrMsg("无法获取到有效的投放场景信息");
                return false;
            }
        }

        return true;
    }
}
