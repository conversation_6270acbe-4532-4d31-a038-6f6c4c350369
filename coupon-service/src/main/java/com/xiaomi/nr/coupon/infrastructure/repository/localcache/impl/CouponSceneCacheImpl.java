package com.xiaomi.nr.coupon.infrastructure.repository.localcache.impl;

import com.xiaomi.nr.coupon.infrastructure.repository.localcache.CouponSceneCache;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponSceneItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @description: 优惠券场景本地缓存
 * @author: hejiapeng
 * @Date 2022/2/26 8:35 下午
 * @Version: 1.0
 **/
@Component
@Slf4j
public class CouponSceneCacheImpl implements CouponSceneCache {

    private Map<String, CouponSceneItem> localCache;

    private CouponSceneCacheImpl() {
        localCache = new ConcurrentHashMap<>();
    }

    /**
     * 根据场景编码获取场景信息
     * @param sceneCode
     * @return
     */
    @Override
    public CouponSceneItem getCouponSceneByCode(String sceneCode) {
        if(StringUtils.isEmpty(sceneCode)) {
            return null;
        }
        return localCache.get(sceneCode);
    }

    /**
     * 添加场景信息
     * @param couponSceneItem
     */
    @Override
    public void addCouponSceneIntoCache(CouponSceneItem couponSceneItem) {
        if (couponSceneItem == null) {
            return;
        }
        localCache.put(couponSceneItem.getSceneCode(), couponSceneItem);
    }
}
