package com.xiaomi.nr.coupon.enums.couponconfig;

/**
 * 履约方式 枚举
 *
 * <AUTHOR>
 */
public enum ShipmentIdEnum {

    /**
     * 所有方式
     */
    All(-1, "所有方式"),

    /**
     * 闪送
     */
    Lightning(139, "闪送");

    private final Integer value;
    private final String name;

    ShipmentIdEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static String findNameByValue(Integer value) {
        ShipmentIdEnum[] values = ShipmentIdEnum.values();
        for (ShipmentIdEnum item : values) {
            if (item.getValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }
}

