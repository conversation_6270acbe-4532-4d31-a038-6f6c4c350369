package com.xiaomi.nr.coupon.domain.coupon;

import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/12
 */
@Component
public class ProductCouponFactory {

    private final Map<BizPlatformEnum, AbstractProductCoupon> productCouponMap;

    @Autowired
    public ProductCouponFactory(List<AbstractProductCoupon> productCouponList) {
        productCouponMap = new HashMap<>(productCouponList.size());
        productCouponList.forEach(e -> productCouponMap.put(e.getBizPlatform(), e));
    }

    public AbstractProductCoupon getProductCoupon(BizPlatformEnum bizPlatform) {
        return productCouponMap.get(bizPlatform);
    }

}
