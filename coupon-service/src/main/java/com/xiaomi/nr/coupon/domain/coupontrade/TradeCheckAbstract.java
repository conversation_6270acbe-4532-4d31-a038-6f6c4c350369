package com.xiaomi.nr.coupon.domain.coupontrade;

import com.xiaomi.nr.coupon.api.dto.trade.CheckoutCouponReqModel;
import com.xiaomi.nr.coupon.api.dto.trade.ConsumeCouponRequest;
import com.xiaomi.nr.coupon.api.dto.trade.LockCouponRequest;
import com.xiaomi.nr.coupon.api.dto.trade.RollbackCouponRequest;
import com.xiaomi.nr.coupon.domain.common.UserCouponPush;
import com.xiaomi.nr.coupon.domain.coupontrade.entity.TradeCheckContext;
import com.xiaomi.nr.coupon.domain.coupontrade.tradecheck.CouponInfoCheck;
import com.xiaomi.nr.coupon.enums.coupon.CouponPushStatusEnum;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.UserCouponLogRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.UserUseCouponRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave.CouponInfoMapper;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.elasticsearch.common.Strings;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 结算业务领域抽象
 *
 * <AUTHOR>
 * @date 2024/1/4
 */
public abstract class TradeCheckAbstract {
    @Resource
    private CouponInfoMapper couponInfoMapper;

    @Resource
    private UserUseCouponRepository userUseCouponRepository;

    @Resource
    private UserCouponLogRepository userCouponLogRepository;

    @Resource
    private UserCouponPush userCouponPush;

    @Resource
    private CouponInfoCheck couponInfoCheck;

    protected abstract Integer getFactoryKey();


    protected List<CouponPo> getCouponPo(CheckoutCouponReqModel request, List<Long> couponIds) {
        // 当不传券ID的时候，获取用户所有未过期且未使用的劵
        if (CollectionUtils.isEmpty(couponIds)) {
            List<Integer> bizPlatformList = Lists.newArrayList(request.getBizPlatform());
            return couponInfoMapper.getAllCouponForUnused(request.getUserId(), TimeUtil.getNowUnixSecond(), bizPlatformList);
        }
        return userUseCouponRepository.getCouponInfos(request.getUserId(), couponIds);
    }

    /**
     * 根据券id获取优惠券
     * 核销使用 读主库 防止存库数据延迟异常
     *
     * @param userId
     * @param vid
     * @param couponIds
     * @return
     * @throws BizError
     */
    protected List<CouponPo> getCouponPoList(long userId, String vid, List<Long> couponIds) throws BizError {
        return userUseCouponRepository.getCouponPoList(userId, couponIds);
    }

    /**
     * 校验前置的基本检查
     *
     * @param ctx ctx
     * @return boolean
     */
    protected boolean preCheck(TradeCheckContext ctx) throws Exception {
        if (Objects.isNull(ctx) || Objects.isNull(ctx.getRequestModel()) || Strings.isEmpty(ctx.getCouponModeType()) || Objects.isNull(ctx.getCouponPo())) {
            throw new Exception("未初始化的调用");
        }

        if (Objects.isNull(ctx.getConfig()) || Objects.isNull(ctx.getConfig().getConfigId()) || ctx.getConfig().getConfigId() <= 0 || Objects.isNull(ctx.getConfig().getCouponConfigInfo())) {
            ctx.setErrContext(ErrCode.USE_COUPON_LOSE_EFFICACY, "无法获取有效的优惠券配置信息");
            return false;
        }

        if (Objects.isNull(ctx.getConfig().getBizPlatform()) || !ctx.getConfig().getBizPlatform().equals(ctx.getRequestModel().getBizPlatform())) {
            ctx.setErrContext(ErrCode.COUPON_BIZ_PLATFORM_MISMATCH, "非所属业务领域可用的券");
            return false;
        }
        return true;
    }

    /**
     * 校验前的数据初始化
     *
     * @param ctx ctx
     * @return boolean
     */
    protected abstract boolean initData(TradeCheckContext ctx) throws Exception;

    /**
     * 校验
     *
     * @param ctx ctx
     * @return boolean
     */
    protected abstract boolean check(TradeCheckContext ctx);

    /**
     * 校验后执行
     *
     * @param ctx ctx
     */
    protected abstract void checkAfter(TradeCheckContext ctx);


    /**
     * 锁定优惠券
     * @param request
     * @param couponPoList
     * @throws Exception
     */
    protected void lockCoupon(LockCouponRequest request, List<CouponPo> couponPoList) throws BizError {
        userUseCouponRepository.lockCoupon(request.getUserId(), request.getOrderId(), request.getCouponItems(), request.getOffline());
    }

    /**
     * 核销优惠券
     * @param request
     * @param couponPoList
     */
    protected void consumeCoupon(ConsumeCouponRequest request, List<CouponPo> couponPoList) throws Exception {
        // 核销券
        userUseCouponRepository.consumeCoupon(request.getUserId(), request.getCouponIds(), request.getOrderId());

        //记录日志
        userCouponLogRepository.insertConsumeCouponLog(request.getUserId(), request.getOrderId(), couponPoList, request.getOffline(), request.getClientId());
    }

    /**
     * 退还优惠券
     * @param request
     * @throws Exception
     */
    protected void returnCoupon(RollbackCouponRequest request, List<CouponPo> couponPoList) throws Exception {
        String currentStatus = couponPoList.get(0).getStat();
        boolean result = userUseCouponRepository.returnCoupon(request.getUserId(), request.getCouponIds(), request.getOrderId(), request.getOffline(), currentStatus);
        if (result) {
            //记录日志
            userCouponLogRepository.insertRollbackCouponLog(request.getUserId(), request.getOrderId(), couponPoList, request.getOffline(), request.getClientId());
        } else if (!CouponStatusEnum.UNUSED.getValue().equals(currentStatus)) {
            // 如果当前为可用状态，不抛异常
            throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券信息不正确");
        }
    }


    /**
     * 公共校验是否通过
     * 通过：true
     * 不通过：false
     *
     * @param ctx .
     * @return bool
     */
    protected boolean commonCheck(TradeCheckContext ctx) {
        //券配置时间检查
        if (!couponInfoCheck.timeCheckerNoCode(ctx.getCheckoutCouponInfo().getCouponBaseInfo(), ctx.getErrContext(), ctx.getCouponModeType())) {
            return false;
        }

        //状态检查
        if (!couponInfoCheck.statusCheckerNoCode(ctx.getCheckoutCouponInfo().getCouponBaseInfo(), ctx.getErrContext(), ctx.getRequestModel().isFromPriceProtect(), ctx.getCouponModeType())) {
            return false;
        }

        // 校验劵类型
        if (!couponInfoCheck.checkType(ctx.getConfig(), ctx.getErrContext(), ctx.getCouponModeType())) {
            return false;
        }

        // 校验优惠类型
        if (!couponInfoCheck.checkPromotionType(ctx.getConfig(), ctx.getRequestModel().getPromotionTypeList(), ctx.getErrContext())) {
            return false;
        }

        return true;
    }

    /**
     * exec
     *
     * @param ctx ctx
     */
    protected void exec(TradeCheckContext ctx) throws Exception {
        if (!preCheck(ctx)) {
            return;
        }
        if (!initData(ctx)) {
            return;
        }
        if (!check(ctx)) {
            return;
        }
        checkAfter(ctx);
    }

    /**
     * 批量锁定优惠券幂等性校验
     *
     * @param couponPos
     * @param orderId
     * @param vid
     * @return 是否幂等
     */
    protected boolean couponListLockCheck(List<CouponPo> couponPos, long orderId, String vid) throws BizError {
        long timeNow = TimeUtil.getNowUnixSecond();
        int isIdempotentCnt = 0;
        for (CouponPo couponPo : couponPos) {
            if (couponPo == null) {
                throw ExceptionHelper.create(ErrCode.USE_COUPON_NOT_FOUND, "优惠券不存在");
            }
            if (Long.parseLong(couponPo.getEndTime()) < timeNow || Long.parseLong(couponPo.getStartTime()) > timeNow) {
                throw ExceptionHelper.create(ErrCode.USE_COUPON_TIME_INVALID, "优惠券未在有效期内");
            }
            if (CouponStatusEnum.LOCKED.getValue().equals(couponPo.getStat()) && couponPo.getOrderId() == orderId) {
                isIdempotentCnt++;
                continue;
            }
            if (!CouponStatusEnum.UNUSED.getValue().equals(couponPo.getStat())) {
                throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券不可用");
            }
        }
        return isIdempotentCnt == couponPos.size();
    }

    /**
     * 批量核销优惠券幂等性校验
     *
     * @param couponPoList
     * @param orderId
     * @param vid
     * @return 是否幂等
     */
    protected boolean couponListConsumeCheck(List<CouponPo> couponPoList, long orderId, String vid) throws BizError {
        int isIdempotentCnt = 0;
        for (CouponPo couponPo : couponPoList) {
            if (CouponStatusEnum.USED.getValue().equals(couponPo.getStat()) && couponPo.getOrderId() == orderId) {
                isIdempotentCnt++;
                continue;
            }
            if (!CouponStatusEnum.LOCKED.getValue().equals(couponPo.getStat())) {
                throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券状态不正确");
            }
        }
        return isIdempotentCnt == couponPoList.size();
    }

    protected boolean couponListReturnCheck(List<CouponPo> couponPos, long orderId, String vid) throws BizError {
        int isIdempotentCnt = 0;
        for (CouponPo couponPo : couponPos) {
            if (CouponStatusEnum.UNUSED.getValue().equals(couponPo.getStat())) {
                if (couponPo.getOrderId() == orderId) {
                    isIdempotentCnt++;
                }
                continue;
            }
            if (!CouponStatusEnum.LOCKED.getValue().equals(couponPo.getStat()) && !couponPo.getStat().equals(CouponStatusEnum.USED.getValue())) {
                throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券状态不正确");
            }
        }
        return isIdempotentCnt == couponPos.size();
    }
}