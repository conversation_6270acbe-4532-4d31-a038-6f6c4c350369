package com.xiaomi.nr.coupon.domain.coupontrade.tradecheck;

import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.CouponServiceTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.CarCouponRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 汽车售后服务-基础保养券 券退还工具
 *
 * <AUTHOR>
 * @date 2024/5/13 16:27
 */
@Component
@Slf4j
public class BasicMaintenanceCouponReturnTools extends AfterSaleCouponReturnTools {
    @Resource
    private CarCouponRepository carCouponRepository;

    @PostConstruct
    public void init() {
        AfterSaleCouponReturnToolsFactory.register(CouponServiceTypeEnum.BASIC_MAINTENANCE, this);
    }


    /**
     * 优惠券退还校验 + 幂等性校验
     *
     * @param couponPos
     * @param orderId
     * @param vid
     * @return 是否幂等，券退还校验失败时抛出异常
     */
    @Override
    public boolean couponListReturnCheck(List<CouponPo> couponPos, long orderId, String vid) throws BizError {
        int isIdempotentCnt = 0;
        for (CouponPo couponPo : couponPos) {
            if (CouponStatusEnum.UNUSED.getValue().equals(couponPo.getStat())) {
                if (couponPo.getOrderId() == orderId) {
                    isIdempotentCnt++;
                }
                continue;
            }
            if (!CouponStatusEnum.LOCKED.getValue().equals(couponPo.getStat()) && !couponPo.getStat().equals(CouponStatusEnum.USED.getValue())) {
                throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券状态不正确");
            }
        }
        return isIdempotentCnt == couponPos.size();
    }

    /**
     * 优惠券回退
     *
     * @param vid
     * @param userId
     * @param orderId
     * @param offline
     * @param couponPos
     */
    @Override
    public void returnCoupon(String vid, long userId, long orderId, Integer offline, List<CouponPo> couponPos) throws BizError {
        if (CollectionUtils.isEmpty(couponPos)) {
            return;
        }

        // 基础保养券，一次只能使用一张
        String currentStatus = couponPos.get(0).getStat();
        List<Long> couponIds = couponPos.stream().map(CouponPo::getId).collect(Collectors.toList());
        carCouponRepository.returnCoupon(vid, userId, couponIds, orderId, offline, currentStatus);
    }
}
