package com.xiaomi.nr.coupon.constant;

/**
 * 通用常量定义
 */
public class CommonConstant {

    //常见常量值定义
    public final static String EMPTY_STR = "";
    public final static String ZERO_STR = "0";
    public final static int ZERO_INT = 0;
    public final static int ONE_INT = 1;
    public final static int TWO_INT = 2;
    public final static int THREE_INT = 3;
    public final static int FOUR_INT = 3;
    public final static long ZERO_LONG = 0L;
    public final static long ONE_LONG = 1L;

    //分页参数常量
    public final static int PAGESIZE_DEFAULT = 10;
    public final static int PAGESIZE_MAX = 50;

    // 优惠券状态校验最大限制数量
    public final static int COUPON_CHECK_MAX = 20;

    //用于排除指定mid
    public final static long EXCLUDE_USER_ID = 10000L;

    public final static String CLIENT_IDS = "180100031013,180100031016,180100031021,180100031022,180100031024,180100031027,180100031032,180100031036,180100031037,180100031039,180100031051,180100031052,180100031055,180100031056,180100031058,180100041061,180100041065,180100041074,180100041078,180100041079,180100041080,180100041081,180100041083,180100041086,180100041088,180100041089,180100041090,180100041099,180100041104,180100041114,180100041150,180100041171,180100041194";
    ;

    //查询缓存最大耗时限制
    public final static long REDIS_TIME_OUT = 50L;

    public final static int SUCCESS_CODE = 200;

    //逗号
    public final static String COMMA = ",";
}
