package com.xiaomi.nr.coupon.domain.coupontrade.tradecheck;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/11 14:43
 */
public abstract class AfterSaleCouponConsumeTools {
    /**
     * 优惠券核销校验 + 幂等性校验
     *
     * @param couponPos
     * @param orderId
     * @param vid
     * @param timeNow
     * @return 是否幂等，券锁定校验失败时抛出异常
     */
    public abstract boolean couponListConsumeCheck(List<CouponPo> couponPos, long orderId, String vid, long timeNow) throws BizError;

    /**
     * 优惠券核销
     *
     * @param vid
     * @param userId
     * @param orderId
     * @param offline
     * @param couponPos
     */
    public abstract void consumeCoupon(String vid, long userId, long orderId, Integer offline, List<CouponPo> couponPos) throws BizError;
}
