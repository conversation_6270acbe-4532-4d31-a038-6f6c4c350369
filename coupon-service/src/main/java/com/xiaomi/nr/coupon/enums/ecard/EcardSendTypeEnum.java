package com.xiaomi.nr.coupon.enums.ecard;

/**
 * 礼品卡发放方式 枚举
 *
 * <AUTHOR>
 */

public enum EcardSendTypeEnum {
    /**
     * 后台生成
     */
    reissue("reissue", "后台生成"),

    /**
     * 任务生成
     */
    marketing("marketing", "任务生成"),

    /**
     * 接口生成
     */
    external("external", "接口生成");


    private final String value;
    private final String name;

    EcardSendTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static EcardSendTypeEnum findByValue(String value) {
        EcardSendTypeEnum[] values = EcardSendTypeEnum.values();
        for (EcardSendTypeEnum ecardSendTypeEnum : values) {
            if (value.equals(ecardSendTypeEnum.value)) {
                return ecardSendTypeEnum;
            }
        }
        return null;
    }
}