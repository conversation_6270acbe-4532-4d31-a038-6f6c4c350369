package com.xiaomi.nr.coupon.enums.couponconfig;

/**
 * 使用有效期类型 枚举
 *
 * <AUTHOR>
 */
public enum UseTimeTypeEnum {

    /**
     * 固定有效期
     */
    Fixed(1, "固定有效期"),

    /**
     * 相对有效期
     */
    Relative(2, "相对有效期"),

    /**
     * 自定义
     */
    CUSTOM(3, "自定义");

    private final Integer value;
    private final String name;

    UseTimeTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static String findNameByValue(Integer value) {
        UseTimeTypeEnum[] values = UseTimeTypeEnum.values();
        for (UseTimeTypeEnum item : values) {
            if (item.getValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }
}

