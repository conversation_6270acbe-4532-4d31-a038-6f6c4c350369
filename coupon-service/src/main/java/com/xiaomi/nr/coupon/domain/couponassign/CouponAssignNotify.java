package com.xiaomi.nr.coupon.domain.couponassign;

import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.infrastructure.repository.mq.CouponAssignNotifyMqProducer;
import com.xiaomi.nr.coupon.infrastructure.repository.mq.model.CouponAssignNotifyMsg;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.support.RocketMQHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/13
 */
@Component
@Slf4j
public class CouponAssignNotify {

    @Autowired
    private CouponAssignNotifyMqProducer couponAssignNotifyMqProducer;

    @Value("${rocketmq.couponAssignNotify.topic}")
    private String couponAssignNotifyTopic;

    @Async("asyncExecutor")
    public void couponAssignNotify(CouponConfigItem configInfo, CouponPo coupon) {

        log.info("couponAssignNotify bizPlatform {}, sendScene {}, couponId {}", configInfo.getBizPlatform(), configInfo.getCouponConfigInfo().getSendScene(), coupon.getId());

        CouponAssignNotifyMsg notifyMsg = new CouponAssignNotifyMsg();
        try {

            Long couponId = coupon.getId();
            notifyMsg.setUserId(coupon.getUserId());
            notifyMsg.setConfigId(coupon.getTypeId());
            notifyMsg.setCouponId(couponId);
            notifyMsg.setStat(coupon.getStat());
            notifyMsg.setStartUseTime(Long.parseLong(coupon.getStartTime()));
            notifyMsg.setEndUseTime(Long.parseLong(coupon.getEndTime()));
            notifyMsg.setAssignTime(coupon.getAddTime());
            notifyMsg.setPromotionType(configInfo.getPromotionType().getCode());

            String msg = GsonUtil.toJson(notifyMsg);
            Message message = MessageBuilder.withPayload(msg)
                    .setHeader(RocketMQHeaders.KEYS, String.valueOf(couponId))
                    .build();
            BizPlatformEnum bizPlatform = BizPlatformEnum.valueOf(coupon.getBizPlatform());
            couponAssignNotify(msg, message, bizPlatform.getName());

        } catch (Exception e) {
            log.info("couponAssignNotify error. notifyMsg:{}, e ", GsonUtil.toJson(notifyMsg), e);
        }
    }

    public void couponAssignNotify(String msg, Message message, String tags) {
        for (int i = 0; i < 2; i++) {
            try {
                couponAssignNotifyMqProducer.send(couponAssignNotifyTopic + ":" + tags, message);
                log.info("couponAssignNotify success. tags:{}, msg:{}", tags, msg);
                break;
            } catch (Exception e) {
                log.error("CouponAssignNotify failed msg:{}. e ", msg, e);
            }
        }
    }
}
