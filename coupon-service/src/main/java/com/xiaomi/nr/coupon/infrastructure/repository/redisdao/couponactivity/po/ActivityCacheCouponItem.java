package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponactivity.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

@Data
public class ActivityCacheCouponItem implements Serializable {

    private static final long serialVersionUID = 3100044047111138445L;

    /**
     * mission_id 或 config_id
     */
    @SerializedName("id")
    private Long id;

    /**
     * config_id
     */
    @SerializedName("coupon_type_id")
    private Long configId;

    /**
     * 活动名称
     */
    @SerializedName("name")
    private String name;

    /**
     * sid
     */
    @SerializedName("sid")
    private String sid;

    /**
     * 使用渠道
     */
    @SerializedName("channel")
    private String channel;

    /**
     * client_id
     */
    @SerializedName("client_id")
    private String clientId;

    /**
     * 活动tag
     */
    @SerializedName("activity_tag")
    private String activityTag;

    /**
     * 券tag
     */
    @SerializedName("coupon_tag")
    private String couponTag;

    /**
     * bingo活动人群Id
     */
    @SerializedName("crowd")
    private String crowd;
}

