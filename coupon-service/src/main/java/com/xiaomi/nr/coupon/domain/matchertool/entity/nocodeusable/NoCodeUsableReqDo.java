package com.xiaomi.nr.coupon.domain.matchertool.entity.nocodeusable;

import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherBaseReqDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherGoodsItemDo;
import lombok.Data;

import java.util.List;

/**
 * NoCodeUsableReqDo
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Data
public class NoCodeUsableReqDo extends MatcherBaseReqDo {

    /**
     * 小米ID
     */
    private Long userId;

    /**
     * 所属业务领域　0-3C零售，3-汽车整车销售，4-汽车售后
     */
    private List<Integer> bizPlatform;

    /**
     * 使用渠道
     */
    private Integer useChannel;

    /**
     * client id（仅当不传门店ID时才用到）
     */
    private Long clientId;

    /**
     * 门店ID（以它作为商城/门店渠道的唯一判断条件，它与clientId至少要传一个）
     */
    private String orgCode;

    /**
     * 商品信息
     */
    private List<MatcherGoodsItemDo> goodsList;


    /**
     * 可用校验参数：用户券ID
     */
    private List<Long> userCouponIdList;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 购物模式
     * 0-默认值，
     * 1-物流，
     * 2-现场购，
     * 3-物流和现场购混合
     */
    private Integer shoppingMode;

    /**
     * 履约方式
     * -1：所有方式，
     * 139：门店闪送（这个是跟交易中台统一定义的）
     */
    private Integer shipmentId;

    /**
     * 优惠类型列表，当前只作用于车商城已领券
     */
    private List<Integer> promotionTypeList;


    /**
     * 是否校验所属业务场景
     */
    private boolean checkBizPlatform = false;

    /**
     * 是否校验履约方式
     */
    private boolean checkShipmentId = false;

    /**
     * 是否校验用户券状态
     */
    private boolean checkUserCouponStatus = false;

    /**
     * 是否检查可用时间
     */
    private boolean checkUserCouponTime = false;

    /**
     * 是否校验区域消费劵
     */
    private boolean checkConfigRegion = false;

    /**
     * 是否校验专店专用
     */
    private boolean checkUserCouponSpecialStore = false;


    /**
     * 是否校验券可用商品
     */
    private boolean checkConfigGoodsInclude = false;

    /**
     * 是否校验渠道、门店在券配置的可用范围之内
     */
    private boolean checkUserChannelAndOrgCode = false;

    /**
     * 是否校验全局排除商品
     */
    private boolean checkGlobalExcludeGoods = false;

    /**
     * 是否校验优惠券全局排除商品
     */
    private boolean checkGlobalCouponExcludeGoods = false;

    /**
     * 是否校验所属业务场景
     */
    private boolean checkUseChannel = false;

    /**
     * 是否校验券优惠类型
     */
    private boolean checkPromotionType = false;

    /**
     * 是否对结果进行排序
     */
    private boolean sort = false;

}
