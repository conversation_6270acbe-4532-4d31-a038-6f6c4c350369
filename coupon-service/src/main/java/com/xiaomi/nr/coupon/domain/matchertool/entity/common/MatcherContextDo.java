package com.xiaomi.nr.coupon.domain.matchertool.entity.common;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponSceneItem;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.po.CompareGoodsItem;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.storeinfo.po.OrgInfo;
import com.xiaomi.nr.coupon.util.TimeUtil;
import lombok.Data;

import java.util.*;

/**
 * 匹配器上下文（不可随意改动）
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Data
public class MatcherContextDo {

    //#################### 公共信息 ####################//

    /**
     * 公共参数：小米ID
     */
    private Long userId;

    /**
     * 所属业务领域　0-3C零售，3-汽车整车销售，4-汽车售后
     */
    private List<Integer> bizPlatform = new ArrayList<>();

    /**
     * 使用渠道
     */
    private Integer useChannel;

    /**
     * 公共参数：client id（仅当不传门店ID时才用到）
     */
    private Long clientId;

    /**
     * 公共参数：门店ID（以它作为商城/门店渠道的唯一判断条件，它与clientId至少要传一个）
     */
    private String orgCode;

    /**
     * 公共参数：外部传入的商品信息
     */
    private List<MatcherGoodsItemDo> outerGoodsList = new ArrayList<>();

    /**
     * 公共参数：是否校验所属业务场景
     */
    private boolean checkBizPlatform = false;

    /**
     * 校验使用渠道
     */
    private boolean checkUseChannel = false;

    /**
     * 公共参数：是否校验渠道、门店在券配置的可用范围之内
     */
    private boolean checkUserChannelAndOrgCode = false;

    /**
     * 公共参数：是否校验券可用商品
     */
    private boolean checkConfigGoodsInclude = false;

    /**
     * 公共参数：是否校验全局排除商品
     */
    private boolean checkGlobalExcludeGoods = false;

    /**
     * 公共参数：是否校验优惠券全局排除商品
     */
    private boolean checkGlobalCouponExcludeGoods = false;

    /**
     * 公共参数：是否对最终结果进行排序
     */
    private boolean sort = false;


    //#################### 可领校验参数 ####################//

    /**
     * 可领校验参数：外部券配置信息
     */
    private List<MatcherConfigItemDo> outerConfigList;

    /**
     * 可领校验参数：发放方式  1:外部系统发券（默认）, 2:内部系统灌券
     */
    private Integer assignMode;

    /**
     * 可领校验参数：是否校验券配置状态
     */
    private boolean checkConfigStatus = false;

    /**
     * 可领校验参数：是否校验券可领时间
     */
    private boolean checkConfigFetchTime = false;

    /**
     * 可领校验参数：是否校验用户的限领数
     */
    private boolean checkUserFetchLimit = false;

    /**
     * 可领校验参数：是否校验券的限领数
     */
    private boolean checkConfigFetchLimit = false;

    /**
     * 可领校验参数：是否校验投放场景码
     */
    private boolean checkSceneCode = false;

    /**
     * 可领校验参数：是否校验投放场景状态
     */
    private boolean checkSceneStatus = false;

    /**
     * 可领校验参数：是否校验券发放方式 外部接口/内部灌券
     */
    private boolean checkSceneAssignMode = false;

    /**
     * 可领校验参数：是否校验券投放方式 优惠券/优惠码
     */
    private boolean checkSceneSendMode = false;

    /**
     * 可领校验参数：是否校验发放权限
     */
    private boolean checkSceneAssignRights = false;

    /**
     * 可领校验参数：是否校验公开推广
     */
    private boolean checkPublicPromotion = false;


    //#################### 可用校验参数 ####################//

    /**
     * 可用校验参数：用户券ID
     */
    private List<Long> userCouponIdList;

    /**
     * 可用校验参数：城市ID
     */
    private Long cityId;

    /**
     * 可用校验参数：购物模式
     * 0-默认值，
     * 1-物流，
     * 2-现场购，
     * 3-物流和现场购混合
     */
    private Integer shoppingMode;

    /**
     * 可用校验参数：履约方式
     * -1：所有方式，
     * 139：门店闪送（这个是跟交易中台统一定义的）
     */
    private Integer shipmentId;

    /**
     * 优惠类型列表，当前只作用于车商城已领券
     */
    private List<Integer> promotionTypeList;

    /**
     * 可用校验参数：是否校验履约方式
     */
    private boolean checkShipmentId = false;

    /**
     * 可用校验参数：是否校验用户券状态
     */
    private boolean checkUserCouponStatus = false;

    /**
     * 可用校验参数：是否校验可用时间
     */
    private boolean checkUserCouponTime = false;

    /**
     * 可用校验参数：是否校验区域消费劵
     */
    private boolean checkConfigRegion = false;

    /**
     * 可用校验参数：是否校验专店专用
     */
    private boolean checkUserCouponSpecialStore = false;

    /**
     * 是否校验券优惠类型
     */
    private boolean checkPromotionType = false;

    //#################### 内部数据 ####################//

    /**
     * 内部数据：门店信息
     */
    private OrgInfo orgInfo;

    /**
     * 内部数据：券配置信息
     */
    private Map<Long, CouponConfigItem> configInfoMap = new HashMap<>();

    /**
     * 内部数据：用户券信息，用户券ID=>用户券信息
     */
    private Map<Long, CouponPo> userCouponMap = new HashMap<>();

    /**
     * 内部数据：全局排除商品
     */
    private CompareGoodsItem globalExcludeGoods;

    /**
     * 内部数据：优惠券全局排除商品
     */
    private CompareGoodsItem globalCouponExcludeGoods;

    /**
     * 内部数据：投放场景信息 场景code => 场景信息
     */
    private Map<String, CouponSceneItem> sceneInfoMap;

    /**
     * 内部数据：券已被领数量（如果不校验，此值为空） 券配置ID => 数量
     */
    private Map<Long, Integer> configFetchedCountMap = new HashMap<>();

    /**
     * 内部数据：用户已领券数量（如果不校验，此值为空） 券配置ID => 数量
     */
    private Map<Long, Integer> userFetchedCountMap = new HashMap<>();

    /**
     * 内部数据：当前秒时间戳（秒）
     */
    private long nowUnixSecond = TimeUtil.getNowUnixSecond();


    //#################### 匹配结果 ####################//

    /**
     * 匹配结果：关键结果，先取此结果，再结合别的信息，做最终输出
     */
    private List<MatcherRespItemDo> keyResp = new ArrayList<>();

    /**
     * 区配结果：可用商品，取传入商品与券配置可用商品的交集过滤结果，券配置ID=>商品信息列表
     */
    private Map<Long, List<MatcherGoodsItemDo>> validGoods = new HashMap<>();

    /**
     * 区配结果：券配置ID匹配不通过的原因
     */
    private Map<Long, MatcherErrContextDo> invalidConfigIds = new HashMap<>();

}
