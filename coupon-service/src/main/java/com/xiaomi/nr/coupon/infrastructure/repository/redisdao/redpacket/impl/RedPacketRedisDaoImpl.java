package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.redpacket.impl;

import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.redpacket.RedPacketRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.redpacket.po.RedPacketCachePo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.StringUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class RedPacketRedisDaoImpl implements RedPacketRedisDao {

    @Autowired
    @Qualifier("stringPulseTypeRedisTemplate")
    private StringRedisTemplate redisTemplate;

    private static final String USER_REDPACKET_KEY = "shopapi_redpacket_user_{userId}";

    /**
     * 查用户可用红包总额
     *
     * @param userId long
     * @return long
     */
    @Override
    public long getValidMoney(long userId) {
        String key = StringUtil.formatContent(USER_REDPACKET_KEY, String.valueOf(userId));
        long nowTime = TimeUtil.getNowUnixSecond();
        long money = 0L;
        Set<String> list = scan(key);
        if(list != null && !list.isEmpty()) {
            for (String item : list) {
                RedPacketCachePo info = GsonUtil.fromJson(item, RedPacketCachePo.class);
                if(info != null && info.getStartTime() < nowTime && nowTime < info.getEndTime()) {
                    money += info.getBalance();
                }
            }
        }
        return money;
    }

    /**
     * scan
     * @param key String
     * @return Set<String>
     */
    private Set<String> scan(String key) {
        return redisTemplate.execute((RedisCallback<Set<String>>) connection -> {
            Set<String> keysTmp = new HashSet<>();
            long cursorId;
            do {
                Cursor<byte[]> cursor = connection.sScan(key.getBytes(), new ScanOptions.ScanOptionsBuilder().build());
                while (cursor.hasNext()) {
                    keysTmp.add(new String(cursor.next()));
                }
                cursorId = cursor.getCursorId();
                if(!cursor.isClosed()) {
                    cursor.close();
                }
            }while (cursorId > 0);
            return keysTmp;
        });
    }
}
