package com.xiaomi.nr.coupon.domain.coupontrade.tradecheck;

import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.CouponServiceTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.CarCouponRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/1/20 16:01
 */
@Component
public class GlassRepairCouponReturnTools extends AfterSaleCouponReturnTools {

    @PostConstruct
    public void init() {
        AfterSaleCouponReturnToolsFactory.register(CouponServiceTypeEnum.GLASS_REPAIR, this);
    }

    @Resource
    private CarCouponRepository carCouponRepository;

    @Override
    public boolean couponListReturnCheck(List<CouponPo> couponPos, long orderId, String vid) throws BizError {
        int isIdempotentCnt = 0;
        for (CouponPo couponPo : couponPos) {
            if (CouponStatusEnum.UNUSED.getValue().equals(couponPo.getStat())) {
                if (couponPo.getOrderId() == orderId) {
                    isIdempotentCnt++;
                }
                continue;
            }
            if (!CouponStatusEnum.LOCKED.getValue().equals(couponPo.getStat()) && !couponPo.getStat().equals(CouponStatusEnum.USED.getValue())) {
                throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券状态不正确");
            }
        }
        return isIdempotentCnt == couponPos.size();
    }

    @Override
    public void returnCoupon(String vid, long userId, long orderId, Integer offline, List<CouponPo> couponPos)
            throws BizError {
        if (CollectionUtils.isEmpty(couponPos)) {
            return;
        }

        String currentStatus = couponPos.get(0).getStat();
        List<Long> couponIds = couponPos.stream().map(CouponPo::getId).collect(Collectors.toList());
        carCouponRepository.returnCoupon(vid, userId, couponIds, orderId, offline, currentStatus);
    }
}
