package com.xiaomi.nr.coupon.domain.coupontrade;

import com.xiaomi.nr.coupon.api.dto.coupon.CouponBaseInfo;
import com.xiaomi.nr.coupon.api.dto.trade.*;
import com.xiaomi.nr.coupon.domain.coupontrade.tradecheck.CouponInfoCheck;
import com.xiaomi.nr.coupon.domain.coupontrade.convert.CouponBaseInfoConvert;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponConvert;
import com.xiaomi.nr.coupon.domain.coupon.model.CheckoutCouponModel;
import com.xiaomi.nr.coupon.domain.coupon.model.ErrContext;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.ModeTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.UserCodeCouponRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.UserCouponLogRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponcode.po.CouponCodePo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class UserCodeCouponService extends UserCouponService {

    @Autowired
    private UserCodeCouponRepository userCodeCouponRepository;

    @Autowired
    private UserCouponLogRepository userCouponLogRepository;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Resource
    private CouponBaseInfoConvert couponBaseInfoConvert;

    @Resource
    private CouponInfoCheck couponInfo;

    @Resource
    private CouponConvert couponConvert;

    /**
     * 优惠券锁定
     */
    @Override
    public LockCouponResponse lockUserCoupon(LockCouponRequest request) throws BizError {
        log.info("userCodeCouponService.lockUserCoupon request:{}", request);
        LockCouponResponse response = new LockCouponResponse();

        final long userId = request.getUserId();
        final long orderId = request.getOrderId();

        CouponLockItem couponLockItem = request.getCouponItems().get(0);

        String md5Code = DigestUtils.md5DigestAsHex(couponLockItem.getCouponCode().getBytes());

        CouponCodePo couponCodePo = userCodeCouponRepository.getUsableCouponCode(md5Code, orderId);

        // 幂等
        if (isIdempotent(orderId, couponCodePo, CouponStatusEnum.LOCKED)) {
            response.setIdempotent(true);
            return response;
        }

        try {
            // 锁券
            userCodeCouponRepository.lockCodeCoupon(userId, orderId, couponCodePo.getId(), couponLockItem, request.getOffline(), request.getOrgCode());

            // 记日志
            userCouponLogRepository.insertLockCodeCouponLog(userId, orderId, md5Code, request.getOffline(), couponLockItem.getReplaceMoney(), couponLockItem.getReduceExpress());

        } catch (BizError bizError) {
            log.warn("userCodeCouponService.lockUserCoupon bizError. request:{}", request, bizError);
            throw bizError;
        } catch (Exception e) {
            log.error("userCodeCouponService.lockUserCoupon Exception. request:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "锁定优惠券失败", e);
        }
        return response;
    }

    /**
     * 优惠券使用
     */
    @Override
    public ConsumeCouponResponse consumeUserCoupon(ConsumeCouponRequest request) throws BizError {
        log.info("userCodeCouponService.consumeUserCoupon request:{}", request);
        ConsumeCouponResponse response = new ConsumeCouponResponse();

        final long userId = request.getUserId();
        final String couponCode = request.getCouponCode();
        final long orderId = request.getOrderId();

        if (StringUtils.isEmpty(couponCode)) {
            log.error("userCodeCouponService.consumeUserCoupon userId:{},couponCode:{},orderId:{},err:{}", userId, couponCode, orderId, "用户信息或订单信息不正确");
            throw ExceptionHelper.create(GeneralCodes.ParamError, "用户信息或优惠券信息不正确");
        }

        String md5Code = DigestUtils.md5DigestAsHex(couponCode.getBytes());

        CouponCodePo couponCodePo = userCodeCouponRepository.getLockedCouponCode(userId, orderId, md5Code);

        // 幂等
        if (isIdempotent(orderId, couponCodePo, CouponStatusEnum.USED)) {
            response.setIdempotent(true);
            return response;
        }

        try {
            // 核销券
            userCodeCouponRepository.consumeCodeCoupon(userId, couponCodePo.getId(), orderId);

            //记录日志
            userCouponLogRepository.insertConsumeCodeCouponLog(userId, orderId, md5Code, request.getOffline());

        } catch (BizError bizError) {
            log.error("userCodeCouponService.consumeUserCoupon bizError request:{}", request, bizError);
            throw bizError;
        } catch (Exception e) {
            log.error("userCodeCouponService.consumeUserCoupon Exception request:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "使用优惠券失败", e);
        }
        return response;
    }

    /**
     * 优惠券锁券
     */
    @Override
    public RollbackCouponResponse rollbackUserCoupon(RollbackCouponRequest request) throws BizError {
        log.info("userCodeCouponService.rollbackUserCoupon request:{}", request);
        RollbackCouponResponse response = new RollbackCouponResponse();

        final long userId = request.getUserId();
        final String couponCode = request.getCouponCode();
        final long orderId = request.getOrderId();

        //校验入参
        if (StringUtils.isEmpty(couponCode)) {
            log.warn("userCodeCouponService.rollbackUserCoupon param Error request:{}", request);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "用户信息或优惠券信息不正确");
        }

        String md5Code = DigestUtils.md5DigestAsHex(couponCode.getBytes());

        CouponCodePo couponCodePo = userCodeCouponRepository.getLockedCouponCode(userId, orderId, md5Code);

        if (isIdempotent(orderId, couponCodePo, CouponStatusEnum.UNUSED)) {
            response.setIdempotent(true);
            return response;
        }
        String currentStatus = couponCodePo.getStat();

        try {
            // 退还券码
            boolean result = userCodeCouponRepository.returnCodeCoupon(userId, orderId, couponCodePo.getId(), request.getOffline(), currentStatus);
            if (result) {
                userCouponLogRepository.insertRollbackCodeCouponLog(userId, orderId, md5Code, request.getOffline(), couponCodePo.getReplaceMoney(), couponCodePo.getReduceExpress());
            } else if (!CouponStatusEnum.UNUSED.equals(currentStatus)) { // 如果当前为可用状态，不抛异常
                throw ExceptionHelper.create(ErrCode.USE_COUPON_RETURN_ERROR, "优惠券信息不正确");
            }
        } catch (BizError bizError) {
            log.error("userCodeCouponService.rollbackUserCoupon bizError request:{}", request, bizError);
            throw bizError;
        } catch (Exception e) {
            log.error("userCodeCouponService.rollbackUserCoupon Exception request:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "退还优惠券失败", e);
        }
        return response;
    }

    /**
     * 幂等校验
     *
     * @param orderId
     * @param couponCodePo
     * @param statusEnum
     * @return
     */
    private boolean isIdempotent(long orderId, CouponCodePo couponCodePo, CouponStatusEnum statusEnum) {
        return statusEnum.getValue().equals(couponCodePo.getStat()) && couponCodePo.getOrderId() == orderId;
    }

    /**
     * 优惠券结算
     *
     * @param request 请求
     * @return CheckoutCouponResponse
     */
    @Override
    public CheckoutCouponResModel checkoutCoupon(CheckoutCouponReqModel request) {
        List<CouponCodePo> coupons = getCouponPoInfo(request.getCouponCode());

        // 劵配置信息
        List<Long> configIds = coupons.stream().map(CouponCodePo::getTypeId).distinct().collect(Collectors.toList());
        Map<Long, CouponConfigItem> configMap = couponConfigRepository.getCouponConfigs(configIds);

        // 每张券的处理
        Map<String, CheckoutCouponInfo> resData = new HashMap<>();
        for (CouponCodePo couponPo : coupons) {
            try {
                CheckoutCouponInfo checkoutCouponInfo = new CheckoutCouponInfo();
                CouponConfigItem config = configMap.get(couponPo.getTypeId());

                if (Objects.isNull(config) || Objects.isNull(config.getConfigId()) || config.getConfigId() <= 0 || Objects.isNull(config.getCouponConfigInfo())) {
                    checkoutCouponInfo.setValidCode(ErrCode.USE_COUPON_LOSE_EFFICACY.getCode());
                    checkoutCouponInfo.setInvalidReason("无法获取有效的优惠码配置信息");
                    resData.put(request.getCouponCode(), checkoutCouponInfo);
                    continue;
                }

                CouponBaseInfo baseInfo = couponBaseInfoConvert.convertCodeCouponPoConfig2BaseInfo(couponPo, config);
                checkoutCouponInfo.setCouponBaseInfo(baseInfo);
                ErrContext errContext = new ErrContext();

                // 特殊校验
                if (!couponInfo.specialConditionCheckerNoCode(baseInfo, errContext, request.isUsedCouponCheck(), ModeTypeEnum.Code.getMysqlValue())) {
                    checkoutCouponInfo.setValidCode(errContext.getErrorCode());
                    checkoutCouponInfo.setInvalidReason(errContext.getErrorMsg());
                    checkoutCouponInfo.setInvalidData(errContext.getErrorData());
                } else {
                    String couponOrgCode = couponPo.getOrgCode();
                    CheckoutCouponModel checkoutModel = couponConvert.convertCheckoutRequestToModel(request);

                    // 通用检查劵配置的条件
                    if (!couponInfo.checkCondition(couponOrgCode, config, checkoutModel, errContext, ModeTypeEnum.Code.getMysqlValue())) {
                        checkoutCouponInfo.setValidCode(errContext.getErrorCode());
                        checkoutCouponInfo.setInvalidReason(errContext.getErrorMsg());
                        checkoutCouponInfo.setInvalidData(errContext.getErrorData());
                    } else {
                        checkoutCouponInfo.setValidCode(0);
                        checkoutCouponInfo.setValidSkuList(couponBaseInfoConvert.getValidSkuList(config, request.getSkuPackageList()));
                        // 旧套装
                        checkoutCouponInfo.setValidPackageList(couponBaseInfoConvert.getValidPackageList(config, request.getSkuPackageList()));
                        // 新套装
                        checkoutCouponInfo.setValidSsuList(couponBaseInfoConvert.getValidSsuList(config, request.getSkuPackageList()));
                    }
                }
                resData.put(request.getCouponCode(), checkoutCouponInfo);
            } catch (Exception e) {
                log.error("UserCodeCouponService.checkoutCoupon, make checkout coupon info err. userId:{}, couponId:{}", request.getUserId(), couponPo.getId(), e);
            }
        }

        // 判断优惠码是否存在
        if (MapUtils.isEmpty(resData)) {
            CheckoutCouponInfo checkoutCouponInfo = new CheckoutCouponInfo();
            checkoutCouponInfo.setValidCode(ErrCode.COUPON_CODE_ILLEGAL.getCode());
            checkoutCouponInfo.setInvalidReason("优惠码不存在");
            resData.put(request.getCouponCode(), checkoutCouponInfo);
        }

        CheckoutCouponResModel result = new CheckoutCouponResModel();
        result.setCodeCoupons(resData);
        return result;
    }

    /**
     * 获取券数据
     *
     * @param code 券码
     * @return List<CouponCodePo>
     */
    private List<CouponCodePo> getCouponPoInfo(String code) {
        // 当不传券ID的时候，获取用户所有未过期且未使用的劵
        if (StringUtils.isEmpty(code) || StringUtils.isBlank(code)) {
            return Collections.emptyList();
        }
        List<CouponCodePo> coupons = userCodeCouponRepository.getCouponCodePos(code);

        List<CouponCodePo> result = new ArrayList<>();
        for (CouponCodePo coupon : coupons) {
            if (CouponStatusEnum.UNUSED.getValue().equals(coupon.getStat())) {
                result.add(coupon);
                break;
            }
        }
        if (result.size() == 0 && coupons.size() > 0) {
            result.add(coupons.get(0));
        }
        return result;
    }

}