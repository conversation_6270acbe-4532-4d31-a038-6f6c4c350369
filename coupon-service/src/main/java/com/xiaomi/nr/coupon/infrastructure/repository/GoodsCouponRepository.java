package com.xiaomi.nr.coupon.infrastructure.repository;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.GoodScope;
import com.xiaomi.nr.coupon.enums.couponconfig.ConfigStatusEnum;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.localcache.CouponConfigCache;
import com.xiaomi.nr.coupon.infrastructure.repository.localcache.GoodCouponCache;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.GoodCouponsRedisDao;
import com.xiaomi.nr.coupon.util.IdObjectCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 商品和券关系缓存
 * @author: hejiapeng
 * @Date 2022/3/6 9:53 上午
 * @Version: 1.0
 **/
@Component
@Slf4j
public class GoodsCouponRepository {

    @Autowired
    private GoodCouponCache goodCouponCache;

    @Autowired
    private CouponConfigCache couponConfigCache;

    @Autowired
    private GoodCouponsRedisDao goodCouponsRedisDao;


    /**
     * 获取单个商品可用券配置列表
     * @param good
     * @param level
     * @return
     */
    public List<Long> getGoodConfigs(Long good, String level) {
        Map<Long, List<Long>> configIds = batchGetGoodConfigs(Lists.newArrayList(good), level);
        return Optional.ofNullable(configIds.get(good)).orElse(Lists.newArrayList());
    }

    /**
     * 批量获取商品可用券配置列表
     * @param goods
     * @param level
     * @return
     */
    public Map<Long, List<Long>> batchGetGoodConfigs(List<Long> goods, String level){

        Map<Long, List<Long>> result = new HashMap<>(goods.size());
        if (CollectionUtils.isEmpty(goods)) {
            return result;
        }
        List<Long> notHitCache = Lists.newArrayList();
        try {

            for (Long good : goods) {
                String key = String.format("%s_%s", level, good);
                List<Long> fromCache = goodCouponCache.singleGet(key);
                if (CollectionUtils.isEmpty(fromCache)) {
                    notHitCache.add(good);
                } else {
                    result.put(good, fromCache);
                }
            }
            if (CollectionUtils.isEmpty(notHitCache)) {
                return result;
            }

            //2 缓存中不存在的，redis获取，回写缓存
            List<String> fromRedis = goodCouponsRedisDao.get(notHitCache, level);
            int size = notHitCache.size();
            Map<Long, List<Long>> redisRet = new HashMap<>(size);
            for (int i = 0; i < size; i++) {
                if (null == fromRedis.get(i)) {
                    continue;
                }
                List<Long> fetchableConfigIds = Arrays.stream(StringUtils.split(fromRedis.get(i), ","))
                        .map(p -> IdObjectCache.getConfigId(p)).collect(Collectors.toList());
                redisRet.put(notHitCache.get(i), filterValidGoods(notHitCache.get(i), fetchableConfigIds, level));
            }
            if (!fromRedis.isEmpty()) {
                result.putAll(redisRet);
                goodCouponCache.batchSet(redisRet, level);
            }
        } catch (Exception e) {
            log.error("ProductCouponDao onlineGet error. goods:{}, level:{}", goods, level, e);
        }
        return result;
    }

    /**
     * 实时过滤商品可用券配置列表
     * @param good
     * @param configIds
     * @param level
     * @return
     */
    private List<Long> filterValidGoods(Long good, List<Long> configIds, String level) {
        List<Long> configIdList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(configIds)) {
            return configIdList;
        }
        for (Long configId : configIds) {
            try {
                CouponConfigItem couponConfigItem = couponConfigCache.getCouponConfig(configId.intValue());
                if (couponConfigItem == null) {
                    continue;
                }
                if (ConfigStatusEnum.Online.getValue().intValue() != couponConfigItem.getCouponConfigInfo().getStatus()) {
                    continue;
                }
                GoodScope goodScope = couponConfigItem.getGoodScope();
                if (goodScope == null) {
                    log.warn("filterValidGoods goodScope is null, configId:{}", configId);
                    continue;
                }
                if (GoodsLevelEnum.Sku.getValue().equals(level) && goodScope.singleCheckValidSku(good)) {
                    configIdList.add(configId);
                    continue;
                }
                if (GoodsLevelEnum.Goods.getValue().equals(level) && goodScope.singleCheckValidGid(good)) {
                    configIdList.add(configId);
                    continue;
                }
                if (GoodsLevelEnum.Package.getValue().equals(level) && goodScope.singleCheckValidPackage(good)) {
                    configIdList.add(configId);
                }
                if (GoodsLevelEnum.Ssu.getValue().equals(level) && goodScope.singleCheckValidSsu(good)) {
                    configIdList.add(configId);
                }
            } catch (Exception e) {
                log.error("filterValidGoods error configId:{}", configId, e);
            }
        }
        return configIdList;
    }
}
