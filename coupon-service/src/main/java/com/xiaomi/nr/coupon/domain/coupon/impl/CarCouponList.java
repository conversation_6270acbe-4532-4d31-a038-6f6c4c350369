package com.xiaomi.nr.coupon.domain.coupon.impl;

import com.xiaomi.nr.coupon.api.dto.coupon.list.UserCouponListRequest;
import com.xiaomi.nr.coupon.domain.coupon.AbstractUserCouponList;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave.CarCouponListMapper;
import com.xiaomi.nr.coupon.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/20
 */
@Component
@Slf4j
public class CarCouponList extends AbstractUserCouponList {

    @Autowired
    private CarCouponListMapper carCouponListMapper;

    /**
     * 从DB获取用户优惠券列表初始信息
     *
     * @param req            用户优惠券列表请求参数
     * @param nowTime
     * @param nearlyDaysTime
     * @return List<> 优惠券列表(DB)
     */
    /**
     * 从DB获取用户优惠券列表初始信息
     *
     * @param req            用户优惠券列表请求参数
     * @return Pair<Integer, List<CouponPo>>   (页大小, 过滤后po)
     */
    @Override
    protected Pair<Integer, List<CouponPo>> getCouponPoList(UserCouponListRequest req) {

        long nowTime = TimeUtil.getNowUnixSecond();

        //基础查询参数
        List<Integer> bizPlatformList = req.getBizPlatform();
        String status = req.getStatus();
        String vid = req.getVid();
        List<Integer> serviceTypeList = req.getServiceTypeList();
        CouponStatusEnum statEnum = CouponStatusEnum.findByValue(status);
        if (Objects.isNull(statEnum)) {
            log.info("carCouponList.getCouponPoList(), 非法的优惠券状态, 请求参数request={}", req);
            return Pair.of(0, Collections.emptyList());
        }

        List<CouponPo> couponPoList = Collections.emptyList();
        switch (statEnum) {

            //优惠券状态为未使用(unused): 全量返回
            case UNUSED:
                couponPoList = carCouponListMapper.getCouponByUnused(vid, nowTime, bizPlatformList, serviceTypeList);
                break;

            default:
                log.info("coupon.CarCouponList.getCouponPoList(), 优惠券状态不合法, 请求参数request={}", req);
        }

        return Pair.of(couponPoList.size(), couponPoList);
    }
}
