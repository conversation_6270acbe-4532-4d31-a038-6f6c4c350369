package com.xiaomi.nr.coupon.domain.matchertool.core;

import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


/**
 * 校验工具抽象
 *
 * <AUTHOR>
 * @date 2023/7/24
 */
@Component
public abstract class MatcherCheckToolAbstract implements MatcherCheckToolInterface {

    @Resource
    private CheckerFactory checkerFactory;


    /**
     * 获取校验工具
     *
     * @return List<CheckerAbstract>
     */
    protected abstract List<String> getTools();

    @Override
    public MatcherErrContextDo check(MatcherContextDo ctx, MatcherRespItemDo respItem) {
        MatcherErrContextDo errCtx = new MatcherErrContextDo();
        for (String name : getTools()) {
            CheckerAbstract tool = checkerFactory.getProvider(name);
            if(Objects.isNull(tool)) {
                continue;
            }
            if(!tool.check(ctx, respItem, errCtx)) {
                return errCtx;
            }
        }
        return null;
    }

}