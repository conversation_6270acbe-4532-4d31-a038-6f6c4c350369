package com.xiaomi.nr.coupon.infrastructure.repository.localcache;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;

import java.util.List;
import java.util.Map;

/**
 * @description: 券配置缓存
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/2/28 9:33 上午
 * @Version: 1.0
 **/

public interface CouponConfigCache {

    /**
     * 从localCache里取优惠券配置信息
     *
     * @param configId Long
     * @return ConfigCacheItem
     */
    CouponConfigItem getCouponConfig(Integer configId);

    /**
     * 批量从localCache里取优惠券配置信息
     *
     * @param configIds List<Long>
     * @return Map<Long, ConfigCacheItem>
     */
    Map<Integer, CouponConfigItem> getCouponConfig(List<Integer> configIds);


    /**
     * 添加couponConfigItem到localCache
     *
     * @param couponConfigItem
     */
    void addCouponConfig(CouponConfigItem couponConfigItem);


    /**
     * 批量添加couponConfigItem到localCache
     * @param couponConfigItemMap
     */
    void batchAddCouponConfig(Map<Integer, CouponConfigItem> couponConfigItemMap);
}
