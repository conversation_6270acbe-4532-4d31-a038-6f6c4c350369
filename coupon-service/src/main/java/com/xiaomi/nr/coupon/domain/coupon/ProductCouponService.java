package com.xiaomi.nr.coupon.domain.coupon;

import com.xiaomi.nr.coupon.api.dto.coupon.*;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;
import java.util.Map;

/**
 * 产品站优惠券服务
 *
 * <AUTHOR>
 */
public interface ProductCouponService {

    /**
     * 获取商品可领券方法(用户没领的券，旧的还有调用，但以后都建议换成最新的)
     *
     * @param request ProductCouponRequest
     * @throws BizError
     */
    Map<Long, List<GoodsCouponEvent>> getProductApplyCoupon(ProductCouponRequest request) throws BizError;

    /**
     * 获取用户券列表下的商品可用券（旧的还有调用，但以后都建议换成最新的）
     *
     * @param request  ProductCouponRequest
     * @param eventMap eventMap
     * @return Map<Long, List < CouponList>>
     * @throws BizError
     */
    Map<Long, List<CouponList>> getProductUserCoupon(ProductCouponRequest request, Map<Long, CouponEventInfo> eventMap) throws BizError;

    /**
     * 单品页获取获取商品可用券接口（用户没领的券，最新的）
     *
     * @param request ProductUsableCouponRequest
     * @return ProductUsableCouponResponse
     * @throws BizError .
     */
    MatcherContextDo getProductFetchableCoupon(ProductUsableCouponRequest request) throws BizError;

    /**
     * 单品页获取获取商品可用券接口（用户已领的券，最新的）
     *
     * @param request ProductUsableCouponRequest
     * @return ProductUsableCouponResponse
     * @throws BizError .
     */
    MatcherContextDo getProductUserUsableCoupon(ProductUsableCouponRequest request) throws BizError;

    /**
     * 产品站排序
     *
     * @param keyResp       List<MatcherRespItemDo>
     * @param configInfoMap Map<Long, CouponConfigItem>
     * @param userCouponMap Map<Long, CouponPo>
     * @return List<MatcherRespItemDo>
     */
    List<MatcherRespItemDo> sort(List<MatcherRespItemDo> keyResp, Map<Long, CouponConfigItem> configInfoMap, Map<Long, CouponPo> userCouponMap);

}
