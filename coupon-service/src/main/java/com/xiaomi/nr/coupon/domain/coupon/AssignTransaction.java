package com.xiaomi.nr.coupon.domain.coupon;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CouponLogMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CouponMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponLogPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 发放优惠券事务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Deprecated
public class AssignTransaction {

    @Resource
    private CouponMapper couponMapper;

    @Resource
    private CouponLogMapper couponLogMapper;

    /**
     * 落库事务
     *
     * @param coupons    List<CouponDo>
     * @param couponLogs List<CouponLogPo>
     */
    @Transactional(transactionManager = "xmPulseWriteTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void insertDb(List<CouponPo> coupons, List<CouponLogPo> couponLogs) throws BizError {
        for (int i = 0; i < coupons.size(); i++) {
            CouponPo coupon = coupons.get(i);
            CouponLogPo couponLog = couponLogs.get(i);

            log.info("assign.coupon, 要写入的用户优惠券信息, info={}", coupon);

            //优惠券表
            Integer insertCouponCount = couponMapper.insert(coupon);
            if (insertCouponCount != 1) {
                log.error("assign.coupon, 写入用户优惠券表失败, userId={}, requestId={}, missionId={}", coupon.getUserId(), coupon.getRequestId(), coupon.getActivityId());
                throw ExceptionHelper.create(ErrCode.COUPON, String.format("提交信息失败==>任务[%s]", coupon.getActivityId()));
            }

            //优惠券日志表
            Integer insertLogCount = couponLogMapper.insert(couponLog);
            if (insertLogCount != 1) {
                log.error("assign.coupon, 写入用户优惠券日志表失败, userId={}, requestId={}, missionId={}", coupon.getUserId(), coupon.getRequestId(), coupon.getActivityId());
                throw ExceptionHelper.create(ErrCode.COUPON, String.format("提交信息失败！==>任务[%s]", coupon.getActivityId()));
            }
        }
    }
}
