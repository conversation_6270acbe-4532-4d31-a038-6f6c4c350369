package com.xiaomi.nr.coupon.domain.couponconfig.model;

import com.xiaomi.nr.coupon.enums.couponconfig.DeductTypeEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.GoodsScopeType;
import com.xiaomi.nr.coupon.enums.couponconfig.PromotionType;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @description: 优惠券配置聚合类
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/2/26 6:48 下午
 * @Version: 1.0
 **/
@Data
public class CouponConfigItem {

    /**
     * 券配置ID
     */
    private Integer configId;

    /**
     * 优惠类型 1:满减, 2:满折, 3:N元券, 4:立减
     */
    private PromotionType promotionType;

    /**
     * 商品范围类型 1 商品券 2 分类券
     */
    private GoodsScopeType goodsScopeType;

    /**
     * 抵扣类型 0:0元抵扣 1:n元抵扣
     */
    private DeductTypeEnum deductType;

    /**
     * 优惠券描述信息
     */
    private CouponConfigInfo couponConfigInfo;

    /**
     * 使用平台对应的门店 使用渠道类型(1:小米商城 2:小米之家（直营店+专卖店） 4:授权店) => 渠道可用门店配置
     */
    private Map<Integer, UseChannel> useChannelStore;

    /**
     * 指定可用地区配置
     * 区域类型（1:省 2:市） => 区域ID列表
     */
    private Map<Integer, List<Integer>> assignArea;

    /**
     * 使用商品范围
     */
    private GoodScope goodScope;

    /**
     * 扩展属性
     */
    private ExtPropInfo extPropInfo;

    /**
     * 只包含基本参数，不含可用商品信息
     */
    private boolean onlyBaseInfo;

    /**
     * 缓存过期时间长度
     */
    private long expireTime;

    /**
     * 优惠券类型
     */
    private Integer couponType;

    /**
     * 业务领域
     */
    private Integer bizPlatform;

    /**
     * BR单据
     */
    private String budgetApplyNo;

    /**
     * BR行号
     */
    private Long lineNum;

    /**
     * 服务场景，1-基础保养，2-漆面修复，3-上门补胎
     */
    private Integer serviceType = 0;

    /**
     * 使用次数限制，1-限制，2-不限制
     */
    private Integer timesLimit = 1;
}
