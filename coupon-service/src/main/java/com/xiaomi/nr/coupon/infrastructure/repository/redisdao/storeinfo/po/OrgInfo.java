package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.storeinfo.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class OrgInfo implements Serializable {
    private static final long serialVersionUID = -7524983476638062026L;
    /**
     * 区域id
     */
    private List<String> area;

    /**
     * 门店Code
     */
    @SerializedName("org_code")
    private String orgCode;

    /**
     * 区ID
     */
    @SerializedName("area_id")
    private Integer areaId;

    /**
     * 站点
     */
    @SerializedName("site_id")
    private Integer siteId;

    /**
     * 门店类型
     */
    @SerializedName("type")
    private Integer type = 0;

    /**
     * 省
     */
    @SerializedName("province")
    private Integer province;

    /**
     * 市ID
     */
    @SerializedName("city")
    private Integer city;

    /**
     * 门店类型（兼容扬帆店类型逻辑）
     */
    @SerializedName("org_type")
    private Integer orgType;
}
