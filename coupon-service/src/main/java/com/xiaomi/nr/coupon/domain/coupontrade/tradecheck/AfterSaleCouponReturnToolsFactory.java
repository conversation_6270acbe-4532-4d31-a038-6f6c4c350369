package com.xiaomi.nr.coupon.domain.coupontrade.tradecheck;

import com.xiaomi.nr.coupon.enums.couponconfig.CouponServiceTypeEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/13 16:16
 */
@Component
public class AfterSaleCouponReturnToolsFactory {
    /**
     * AfterSaleCouponReturnTools 工厂
     * Map<CouponServiceTypeEnum, AfterSaleCouponLockTools>
     */
    public static final Map<CouponServiceTypeEnum, AfterSaleCouponReturnTools> AFTER_SALE_COUPON_RETURN_TOOLS_MAP = new HashMap<>();

    public static void register(CouponServiceTypeEnum serviceTypeEnum, AfterSaleCouponReturnTools afterSaleCouponReturnTools) {
        if (Objects.nonNull(serviceTypeEnum)) {
            AFTER_SALE_COUPON_RETURN_TOOLS_MAP.put(serviceTypeEnum, afterSaleCouponReturnTools);
        }
    }

    public AfterSaleCouponReturnTools getAfterSaleCouponReturnTools(CouponServiceTypeEnum serviceTypeEnum) {
        return AFTER_SALE_COUPON_RETURN_TOOLS_MAP.get(serviceTypeEnum);
    }
}
