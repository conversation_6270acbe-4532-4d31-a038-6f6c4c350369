package com.xiaomi.nr.coupon.enums.couponmission;

/**
 * 券发放任务类型枚举
 */
public enum MissionTypeEnum {

    /**
     * 接口发放
     */
    INTERFACE(1,"interface", "接口发放"),

    /**
     * 任务发放(按人群发放)
     */
    MISSION(2,"mission", "任务发放(人群发放)");

    private final Integer mysqlValue;
    private final String redisValue;
    private final String name;

    MissionTypeEnum(Integer mysqlValue, String redisValue, String name) {
        this.mysqlValue =mysqlValue;
        this.redisValue = redisValue;
        this.name = name;
    }

    public Integer getMysqlValue() {
        return this.mysqlValue;
    }

    public String getRedisValue() {
        return this.redisValue;
    }

    public String getName() {
        return name;
    }

    public static String findRedisValueBymysqlValue(Integer value) {
        MissionTypeEnum[] values = MissionTypeEnum.values();
        for (MissionTypeEnum item : values) {
            if (item.getMysqlValue().equals(value)) {
                return item.getRedisValue();
            }
        }
        return null;
    }
}
