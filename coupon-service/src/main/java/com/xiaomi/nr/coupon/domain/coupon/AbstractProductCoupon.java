package com.xiaomi.nr.coupon.domain.coupon;

import com.xiaomi.nr.coupon.api.dto.coupon.*;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponConvert;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponFormatCommon;
import com.xiaomi.nr.coupon.domain.coupon.model.CouponInfoModel;
import com.xiaomi.nr.coupon.domain.coupon.model.ProductUsableCouponContext;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherToolFactory;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherGoodsItemDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.enums.coupon.ProductRelationTypeEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.PromotionType;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.assertj.core.util.Strings;

import javax.annotation.Resource;
import java.util.*;

/**
 * 产品站优惠券服务
 *
 * <AUTHOR>
 * @date 2024/9/12
 */
@Slf4j
public abstract class AbstractProductCoupon {

    /**
     * 获取业务领域
     *
     * @return BizPlatformEnum
     */
    public abstract BizPlatformEnum getBizPlatform();

    @Resource
    protected MatcherToolFactory matcherToolFactory;

    @Resource
    private ProductCouponService productCouponService;

    @Resource
    private CouponConvert couponConvert;

    @Resource
    private CouponFormatCommon couponFormatCommon;

    /**
     * 公共入参校验
     * @param req  req
     */
    public void checkReq(ProductUsableCouponRequest req) throws BizError {

        // 可领券、可用券不能都不查
        if (Objects.equals(Boolean.FALSE, req.getNeedFetchedCoupon())
                && Objects.equals(Boolean.FALSE, req.getNeedFetchableCoupon())
        ) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "可领券和可用券至少查询一种类型");
        }


        // 商品校验
        for (GoodsItemDto goodsInfo : req.getGoodsList()) {
            // 商品基本信息校验
            if (Objects.isNull(goodsInfo) || Objects.isNull(goodsInfo.getId())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "所传商品sku或套装ID不能都为空");
            }
            if (Strings.isNullOrEmpty(goodsInfo.getLevel())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "所传商品品级不能都为空");
            }
            if (goodsInfo.getId() <= 0L) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "所传商品id需大于0");
            }
        }

        // 优惠类型校验
        List<Integer> promotionTypeList = req.getPromotionTypeList();
        if (CollectionUtils.isNotEmpty(promotionTypeList)) {
            boolean illegalPromotionExists = promotionTypeList.stream()
                    .anyMatch(promotionType -> Objects.isNull(promotionType) || Objects.isNull(PromotionType.getPromotionType(promotionType)));
            if (illegalPromotionExists) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "优惠类型非法");
            }
        }

    }

    /**
     * 获取可领券
     * @param req     req
     * @param context   context
     */
    public abstract void getProductFetchableCoupon(ProductUsableCouponRequest req, ProductUsableCouponContext context) throws BizError;

    /**
     * 获取可用券
     * @param req     req
     * @param context   context
     */
    public abstract void getProductUserUsableCoupon(ProductUsableCouponRequest req, ProductUsableCouponContext context) throws BizError;

    /**
     * 请求商品转换
     *
     * @param goodsList
     * @return
     */
    protected final List<MatcherGoodsItemDo> convertReqDtoToMatcherGoodsItemDo(List<GoodsItemDto> goodsList) {
        List<MatcherGoodsItemDo> result = new ArrayList<>();
        for (GoodsItemDto e : goodsList) {
            if (Objects.isNull(e) ||
                    Objects.isNull(e.getId()) ||
                    e.getId() <= 0 ||
                    (!GoodsLevelEnum.Sku.getValue().equals(e.getLevel()) && !GoodsLevelEnum.Package.getValue().equals(e.getLevel()) && !GoodsLevelEnum.Ssu.getValue().equals(e.getLevel()))
            ) {
                continue;
            }
            MatcherGoodsItemDo item = new MatcherGoodsItemDo();
            item.setId(e.getId());
            item.setLevel(e.getLevel());
            item.setSalePrice(e.getSalePrice());
            item.setMarketPrice(e.getMarketPrice());
            item.setVirtual(e.getVirtual());
            item.setSaleMode(e.getSaleMode());
            item.setBusinessType(e.getBusinessType());
            item.setFinalStartTime(e.getFinalStartTime());
            item.setFinalEndTime(e.getFinalEndTime());
            result.add(item);
        }
        return result;
    }

    private void sort(ProductUsableCouponContext context) {
        List<MatcherRespItemDo> newKeyResp = productCouponService.sort(context.getKeyResp(), context.getConfigInfoMap(), context.getUserCouponMap());
        context.setKeyResp(newKeyResp);
    }

    /**
     * 将匹配关键结果转换成输出DTO
     *
     * @param keyResp       List<MatcherRespItemDo>
     * @param userCouponMap Map<Long, CouponPo>
     * @return List<GoodsUsableCouponRelationItemDto>
     */
    protected List<GoodsUsableCouponRelationItemDto> convertRespToRelationDto(List<MatcherRespItemDo> keyResp, Map<Long, CouponPo> userCouponMap) {
        List<GoodsUsableCouponRelationItemDto> result = new ArrayList<>();
        for (MatcherRespItemDo e : keyResp) {
            if (Objects.nonNull(e.getUserCouponId()) && e.getUserCouponId() > 0) {
                if (!userCouponMap.containsKey(e.getUserCouponId())) {
                    continue;
                }
                CouponPo coupon = userCouponMap.get(e.getUserCouponId());
                GoodsUsableCouponRelationItemDto item = convertCouponPoToSimpleRelationDto(coupon);
                result.add(item);
            } else {
                GoodsUsableCouponRelationItemDto item = convertConfigPoToSimpleRelationDto(e.getConfigId());
                result.add(item);
            }
        }
        return result;
    }

    /**
     * 可领券转精简券信息dto
     *
     * @param configId Long
     * @return GoodsUsableCouponRelationItemDto
     */
    GoodsUsableCouponRelationItemDto convertConfigPoToSimpleRelationDto(Long configId) {
        GoodsUsableCouponRelationItemDto couponSimpleItem = new GoodsUsableCouponRelationItemDto();
        couponSimpleItem.setRelationType(ProductRelationTypeEnum.FETCHABLE.getValue());
        couponSimpleItem.setConfigId(configId);
        return couponSimpleItem;
    }

    /**
     * 用户券转精简券信息dto
     *
     * @param coupon CouponPo
     * @return GoodsUsableCouponRelationItemDto
     */
    GoodsUsableCouponRelationItemDto convertCouponPoToSimpleRelationDto(CouponPo coupon) {
        GoodsUsableCouponRelationItemDto couponSimpleItem = new GoodsUsableCouponRelationItemDto();
        couponSimpleItem.setRelationType(ProductRelationTypeEnum.FETCHED.getValue());
        couponSimpleItem.setConfigId(coupon.getTypeId());
        couponSimpleItem.setId(coupon.getId());
        couponSimpleItem.setUseStartTime(Long.parseLong(coupon.getStartTime()));
        couponSimpleItem.setUseEndTime(Long.parseLong(coupon.getEndTime()));
        couponSimpleItem.setSendTime(coupon.getAddTime());
        return couponSimpleItem;
    }

    /**
     * 转换至输出DTO 商品 => 券列表
     *
     * @param keyResp    List<MatcherRespItemDo>
     * @param validGoods Map<Long, List<MatcherGoodsItemDo>>
     * @return Map<MatcherGoodsItemDo, List < MatcherRespItemDo>>
     */
    protected Map<MatcherGoodsItemDo, List<MatcherRespItemDo>> convertGoodsUsableCoupon(List<MatcherRespItemDo> keyResp, Map<Long, List<MatcherGoodsItemDo>> validGoods) {
        Map<String, List<MatcherRespItemDo>> cgs = new HashMap<>();
        Map<String, MatcherGoodsItemDo> gs = new HashMap<>();

        keyResp.forEach(e -> {
            if (!validGoods.containsKey(e.getConfigId())) {
                return;
            }
            List<MatcherGoodsItemDo> gList = validGoods.get(e.getConfigId());
            if (CollectionUtils.isEmpty(gList)) {
                return;
            }
            gList.forEach(g -> {
                String gKey = String.format("%d_%s", g.getId(), g.getLevel());
                if (!gs.containsKey(gKey)) {
                    gs.put(gKey, g);
                }
                if (!cgs.containsKey(gKey)) {
                    cgs.put(gKey, new ArrayList<>());
                }
                cgs.get(gKey).add(e);
            });
        });

        Map<MatcherGoodsItemDo, List<MatcherRespItemDo>> result = new HashMap<>();
        cgs.forEach((gl, respList) -> {
            MatcherGoodsItemDo g = gs.get(gl);
            result.put(g, respList);
        });
        return result;
    }

    /**
     * 内部商品转输出商品dto
     *
     * @param goods MatcherGoodsItemDo
     * @return ProductGoodsItemDto
     */
    protected ProductGoodsItemDto convertMatcherGoodsDoToSimpleDto(MatcherGoodsItemDo goods) {
        ProductGoodsItemDto goodsSimpleItem = new ProductGoodsItemDto();
        goodsSimpleItem.setId(goods.getId());
        goodsSimpleItem.setLevel(goods.getLevel());
        return goodsSimpleItem;
    }

    /**
     * 针对销售模式为定金预售商品，保证尾款可用券排在前面
     * 1、对于可领的券，将当下可用的有效期与尾款期无交集的后置，真实情况还和按用户领取后的可用时间为准
     * 2、对于用户已领的券，将有效期与尾款期无交集的后置
     *
     * @param goods         MatcherGoodsItemDo
     * @param keyResp       List<MatcherRespItemDo>
     * @param configInfoMap Map<Long, CouponConfigItem>
     * @param userCouponMap Map<Long, CouponPo>
     * @return List<MatcherRespItemDo>
     */
    protected List<MatcherRespItemDo> specialSort(MatcherGoodsItemDo goods, List<MatcherRespItemDo> keyResp, Map<Long, CouponConfigItem> configInfoMap, Map<Long, CouponPo> userCouponMap) {
        return keyResp;
    }

    public Map<ProductGoodsItemDto, List<GoodsUsableCouponRelationItemDto>> produceValidCoupons(ProductUsableCouponContext context) {
        List<MatcherRespItemDo> keyResp = context.getKeyResp();
        Map<Long, List<MatcherGoodsItemDo>> validGoods = context.getValidGoods();
        Map<MatcherGoodsItemDo, List<MatcherRespItemDo>> goodsCoupons = convertGoodsUsableCoupon(keyResp, validGoods);
        if (MapUtils.isEmpty(goodsCoupons)) {
            return Collections.emptyMap();
        }

        Map<Long, CouponConfigItem> configInfoMap = context.getConfigInfoMap();
        Map<Long, CouponPo> userCouponMap = context.getUserCouponMap();
        Map<ProductGoodsItemDto, List<GoodsUsableCouponRelationItemDto>> goodsValidCoupon = new HashMap<>();
        for (Map.Entry<MatcherGoodsItemDo, List<MatcherRespItemDo>> item : goodsCoupons.entrySet()) {
            MatcherGoodsItemDo itemGoods = item.getKey();
            List<MatcherRespItemDo> itemKeyResp = item.getValue();
            itemKeyResp = specialSort(itemGoods, itemKeyResp, configInfoMap, userCouponMap);
            ProductGoodsItemDto goodsSimpleItem = convertMatcherGoodsDoToSimpleDto(itemGoods);
            List<GoodsUsableCouponRelationItemDto> relationDto = convertRespToRelationDto(itemKeyResp, userCouponMap);
            goodsValidCoupon.put(goodsSimpleItem, relationDto);
        }
        return goodsValidCoupon;
    }

    public Map<Long, UsableConfigItemDto> produceConfigInfos(ProductUsableCouponContext context) {

        Map<Long, CouponConfigItem> configInfoMap = context.getConfigInfoMap();

        if (MapUtils.isEmpty(configInfoMap)) {
            return Collections.emptyMap();
        }

        List<MatcherRespItemDo> keyResp = context.getKeyResp();
        Map<Long, CouponInfoModel> configInfoModelMap = new HashMap<>();
        for (MatcherRespItemDo item : keyResp) {
            if (!configInfoMap.containsKey(item.getConfigId())) {
                continue;
            }
            if (configInfoModelMap.containsKey(item.getConfigId())) {
                continue;
            }
            CouponConfigItem config = configInfoMap.get(item.getConfigId());
            try {
                CouponInfoModel couponInfoModel = couponConvert.convertPoToModelItem(config, null);
                couponFormatCommon.setCouponAIS(couponInfoModel);
                configInfoModelMap.put(item.getConfigId(), couponInfoModel);
            } catch (Exception e) {
                log.error("getGoodsUsableConfigsInfo, config info convert error, config:{}", GsonUtil.toJson(config), e);
            }
        }
        return couponConvert.convertCouponInfoModelToUsableConfigItemDto(new ArrayList<>(configInfoModelMap.values()));
    }


    /**
     * 获取产品站可用券
     *
     * @param req   入参
     * @return  resp
     */
    public ProductUsableCouponResponse getProductUsableCoupon(ProductUsableCouponRequest req) throws BizError {

        // 1、入参校验
        checkReq(req);

        // 2、获取可领券
        ProductUsableCouponContext context = new ProductUsableCouponContext();
        if (req.getNeedFetchableCoupon()) {
            getProductFetchableCoupon(req, context);
        }

        // 3、获取可用券
        if (req.getNeedFetchedCoupon() && Objects.nonNull(req.getUserId()) && req.getUserId() > 0) {
            getProductUserUsableCoupon(req, context);
        }

        // 4、排序
        sort(context);

        ProductUsableCouponResponse resp = new ProductUsableCouponResponse();
        // 5、构造商品信息 => 用户券列表
        resp.setValidCoupons(produceValidCoupons(context));

        // 6、构造优惠券配置信息
        resp.setConfigInfos(produceConfigInfos(context));

        return resp;
    }
}
