package com.xiaomi.nr.coupon.domain.matchertool.entity.common;

import lombok.Data;

/**
 * 匹配不通过的原因
 *
 * <AUTHOR>
 * @date 2023/7/13
 */
@Data
public class MatcherErrContextDo {

    /**
     * 错误信息，失败必填
     */
    private String errMsg;

    /**
     * 错误码，失败必填
     */
    private int errCode;

    /**
     * 错误时的数据信息(目前判断时间的时候会用到)，失败非必填
     */
    private String errData;

    /**
     * 是否为券配置匹配不通过，失败必填
     */
    private boolean invalidConfig;

}