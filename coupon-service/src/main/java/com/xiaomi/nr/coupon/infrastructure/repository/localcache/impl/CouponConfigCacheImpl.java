package com.xiaomi.nr.coupon.infrastructure.repository.localcache.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.Expiry;
import com.github.benmanes.caffeine.cache.RemovalCause;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.infrastructure.repository.localcache.CouponConfigCache;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.index.qual.NonNegative;
import org.springframework.stereotype.Component;


import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @description: 优惠券内存缓存配置
 * @author: hejiapeng
 * @Date 2022/2/28 10:02 上午
 * @Version: 1.0
 **/
@Component
@Slf4j
public class CouponConfigCacheImpl implements CouponConfigCache {

    /**
     * 最大缓存数量, 此值可能随时间增长要重设
     */
    private static long MAX_CACHE_SIZE = 40000;

    /**
     * caffeine缓存，定制每个键的过期时间为券过期两个月后过期
     */
    private static Cache<Integer, CouponConfigItem> cache = Caffeine.newBuilder()
            .maximumSize(MAX_CACHE_SIZE)
            .expireAfter(new Expiry<Integer, CouponConfigItem>() {
                @Override
                public long expireAfterCreate(@NonNull Integer id, @NonNull CouponConfigItem couponConfigItem, @NonNegative long currentDuration) {
                    return TimeUnit.SECONDS.toNanos(couponConfigItem.getExpireTime());
                }

                @Override
                public long expireAfterUpdate(@NonNull Integer id, @NonNull CouponConfigItem couponConfigItem, long currentTime, @NonNegative long currentDuration) {
                    return TimeUnit.SECONDS.toNanos(couponConfigItem.getExpireTime());
                }

                @Override
                public long expireAfterRead(@NonNull Integer id, @NonNull CouponConfigItem couponConfigItem, long currentTime, @NonNegative long currentDuration) {
                    return currentDuration;
                }
            })
            .removalListener((Integer id, CouponConfigItem couponConfigItem, RemovalCause cause) -> {
                log.warn("couponCache:{} was removed by {}", id, cause.name());
            })
            .build();

    /**
     * 获取券批次配置
     * @param configId Long
     * @return
     */
    @Override
    public CouponConfigItem getCouponConfig(Integer configId) {
        return cache.getIfPresent(configId);
    }

    /**
     * 批量获取券批次配置
     * @param configIds List<Long>
     * @return
     */
    @Override
    public Map<Integer, CouponConfigItem> getCouponConfig(List<Integer> configIds) {
        Map<Integer, CouponConfigItem> configItemMap = new HashMap<>(configIds.size());
        for (Integer configId : configIds) {
            CouponConfigItem couponConfigItem = cache.getIfPresent(configId);
            if(couponConfigItem == null) {
                continue;
            }
            configItemMap.put(configId, couponConfigItem);
        }
        return configItemMap;
    }

    /**
     * 券批次配置数据添加
     * @param couponConfigItem
     */
    @Override
    public void addCouponConfig(CouponConfigItem couponConfigItem) {
        cache.put(couponConfigItem.getConfigId(), couponConfigItem);
    }

    /**
     * 券批次配置数据更新
     * @param couponConfigItemMap
     */
    @Override
    public void batchAddCouponConfig(Map<Integer, CouponConfigItem> couponConfigItemMap) {
        cache.putAll(couponConfigItemMap);
    }
}
