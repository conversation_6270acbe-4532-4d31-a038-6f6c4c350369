package com.xiaomi.nr.coupon.domain.matchertool.matchers;

import com.xiaomi.nr.coupon.domain.matchertool.checktools.CarShopProductNoCodeUsableCheckTool;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherAbstractUsable;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherCheckToolAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherBaseReqDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.nocodeusable.NoCodeUsableReqDo;
import com.xiaomi.nr.coupon.enums.matchertool.MatcherToolTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/14
 */
@Slf4j
@Component
public class CarShopProductMatcherNoCodeUsable extends MatcherAbstractUsable {

    @Resource
    private CarShopProductNoCodeUsableCheckTool checkTool;

    @Override
    public MatcherToolTypeEnum getMatcherToolTypeEnum() {
        return MatcherToolTypeEnum.CAR_SHOP_PRODUCT_NOCODE_USABLE;
    }


    /**
     * 构建context
     *
     * @param req .
     * @return .
     * @throws BizError .
     */
    @Override
    public MatcherContextDo newContext(MatcherBaseReqDo req) throws BizError {
        NoCodeUsableReqDo p = (NoCodeUsableReqDo) req;
        if (Objects.isNull(p)) {
            throw ExceptionHelper.create(GeneralCodes.InternalError, "可用匹配器入参不符合要求");
        }

        MatcherContextDo ctx = new MatcherContextDo();

        ctx.setUserId(p.getUserId());
        ctx.setBizPlatform(p.getBizPlatform());
        ctx.setOuterGoodsList(p.getGoodsList());
        ctx.setUseChannel(p.getUseChannel());
        ctx.setPromotionTypeList(p.getPromotionTypeList());

        ctx.setCheckBizPlatform(p.isCheckBizPlatform());
        ctx.setCheckUseChannel(p.isCheckUseChannel());
        ctx.setCheckUserCouponTime(p.isCheckUserCouponTime());
        ctx.setCheckUserCouponStatus(p.isCheckUserCouponStatus());
        ctx.setCheckConfigGoodsInclude(p.isCheckConfigGoodsInclude());
        ctx.setCheckPromotionType(p.isCheckPromotionType());
        ctx.setSort(p.isSort());

        return ctx;
    }

    @Override
    public void specialInitResource(MatcherContextDo ctx) throws BizError {

        Long userId = ctx.getUserId();
        if (Objects.isNull(userId) || userId <= 0) {
            ctx.setUserId(null);
            return;
        }

        List<CouponPo> userCouponList = userCouponListMapper.getProductUserCoupon(userId, ctx.getBizPlatform(), ctx.getNowUnixSecond());

        genUserCouponInfo(ctx, userCouponList);
    }

    @Override
    public MatcherCheckToolAbstract getCheckTool() {
        return checkTool;
    }
}
