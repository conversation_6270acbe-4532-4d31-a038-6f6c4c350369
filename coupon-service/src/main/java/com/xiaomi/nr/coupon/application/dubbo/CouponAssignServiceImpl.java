package com.xiaomi.nr.coupon.application.dubbo;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.api.dto.couponassign.*;
import com.xiaomi.nr.coupon.api.dto.couponcode.SingleExchangeRequest;
import com.xiaomi.nr.coupon.api.dto.couponcode.SingleExchangeResponse;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.api.service.CouponAssignService;
import com.xiaomi.nr.coupon.constant.CommonConstant;
import com.xiaomi.nr.coupon.domain.coupon.UserCouponList;
import com.xiaomi.nr.coupon.domain.couponassign.SingleAssigner;
import com.xiaomi.nr.coupon.domain.couponassign.model.SingleAssignRequestDo;
import com.xiaomi.nr.coupon.domain.couponassign.model.SingleAssignResponseDo;
import com.xiaomi.nr.coupon.domain.couponcode.SingleExchanger;
import com.xiaomi.nr.coupon.domain.couponcode.model.SingleExchangeRequestDo;
import com.xiaomi.nr.coupon.domain.couponcode.model.SingleExchangeResponseDo;
import com.xiaomi.nr.coupon.domain.fetchcheck.CouponFetchCheckService;
import com.xiaomi.nr.coupon.domain.fetchcheck.model.CouponFetchCheckInfo;
import com.xiaomi.nr.coupon.domain.fetchcheck.model.CouponFetchReqParam;
import com.xiaomi.nr.coupon.enums.couponconfig.SceneAssignModeEnum;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 优惠券发放服务
 *
 * <AUTHOR>
 */
@Service(timeout = 2000, group = "${dubbo.group}", version = "1.0", delay = 10000)
@Component
@Slf4j
@ApiModule(value = "发券服务", apiInterface = CouponAssignService.class)
public class CouponAssignServiceImpl implements CouponAssignService {

    @Resource
    private SingleAssigner singleAssigner;

    @Resource
    private CouponFetchCheckService couponFetchCheckService;

    @Resource
    private UserCouponList userCouponList;

    @Resource
    private SingleExchanger singleExchanger;

    /**
     * 单张券发放接口
     *
     * @param request SingleAssignRequest
     * @return SingleAssignResponse
     */
    @ApiDoc("发单张券")
    @Override
    public Result<SingleAssignResponse> single(SingleAssignRequest request) {
        try {
            //转换dto => do
            SingleAssignRequestDo reqDo = singleAssignRequestDto2Do(request);

            SingleAssignResponseDo respDto = singleAssigner.assign(reqDo);

            SingleAssignResponse result = singleAssignResponseDto2Do(respDto);

            log.info("coupon.singleAssign, 优惠券发放成功，request={}, response={}", request, result);

            return Result.success(result);
        } catch (BizError e) {
            log.info("coupon.singleAssign, {}， request={}", e.getMessage(), request);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("coupon.singleAssign, 优惠券发放遇到异常，request={}", request, e);
            return Result.fromException(e, "优惠券发放失败");
        }
    }

    /**
     * 单个券码兑换接口
     *
     * @param request ExchangeRequest
     * @return ExchangeResponse
     */
    @Override
    public Result<SingleExchangeResponse> singleExchange(SingleExchangeRequest request) {
        try {
            //转换dto => do
            SingleExchangeRequestDo reqDo = singleExchangeRequestDto2Do(request);

            SingleExchangeResponseDo respDto = singleExchanger.exchange(reqDo);

            SingleExchangeResponse result = singleExchangeResponseDto2Do(respDto);

            log.info("coupon.singleExchange, 优惠码兑换成功，request={}, response={}", request, result);

            return Result.success(result);
        } catch (BizError e) {
            log.info("coupon.singleExchange, {}， request={}", e.getMessage(), request);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("coupon.singleExchange, 优惠码兑换遇到异常，request={}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * dto => do
     *
     * @param r ExchangeRequest
     * @return CouponCodeRequestDo
     */
    private SingleExchangeRequestDo singleExchangeRequestDto2Do(SingleExchangeRequest r) {
        SingleExchangeRequestDo req = new SingleExchangeRequestDo();

        if (StringUtils.isEmpty(r.getCouponCode())) {
            r.setCouponCode("");
        }
        if (StringUtils.isEmpty(r.getOrgCode())) {
            r.setOrgCode("");
        }
        if (r.getUserId() == null || r.getUserId() < 0) {
            r.setUserId(0L);
        }

        req.setUserId(r.getUserId());
        req.setCouponCode(r.getCouponCode().toUpperCase());
        req.setOrgCode(r.getOrgCode());
        req.setAppId(r.getAppId());
        return req;
    }

    /**
     * do => dto
     *
     * @param data CouponCodeResponseDo
     * @return ExchangeResponse
     */
    private SingleExchangeResponse singleExchangeResponseDto2Do(SingleExchangeResponseDo data) {
        SingleExchangeResponse resp = new SingleExchangeResponse();
        resp.setIsIdempotent(data.getIsIdempotent());
        resp.setCouponId(data.getCouponId());
        return resp;
    }

    /**
     * dto => do
     *
     * @param r SingleAssignRequest
     * @return SingleAssignRequestDo
     */
    private SingleAssignRequestDo singleAssignRequestDto2Do(SingleAssignRequest r) {
        SingleAssignRequestDo req = new SingleAssignRequestDo();
        req.setRequestId(r.getRequestId());
        req.setUserId(r.getUserId());
        req.setBizPlatform(r.getBizPlatform());
        req.setSceneCode(r.getSceneCode());
        req.setConfigId(r.getConfigId());
        //默认接口发放
        if (r.getAssignMode() == null) {
            req.setAssignMode(SceneAssignModeEnum.Interface.getValue());
        } else {
            req.setAssignMode(r.getAssignMode());
        }

        if (SceneAssignModeEnum.Fill.getValue().equals(req.getAssignMode())) {
            req.setActivityId(String.valueOf(r.getRequestId()));
        } else {
            req.setActivityId(String.valueOf(r.getConfigId()));
        }

        req.setShareUserId(r.getShareUserId());
        req.setOrgCode(r.getOrgCode());
        req.setOrderId(r.getOrderId());
        req.setAppId(r.getAppId());
        req.setVid(r.getVid());
        req.setStartUseTime(r.getStartUseTime());
        req.setEndUseTime(r.getEndUseTime());
        return req;
    }

    /**
     * do => dto
     *
     * @param data SingleAssignResponseDo
     * @return SingleAssignResponse
     */
    private SingleAssignResponse singleAssignResponseDto2Do(SingleAssignResponseDo data) {
        SingleAssignResponse resp = new SingleAssignResponse();
        resp.setIsIdempotent(data.getIsIdempotent());
        resp.setCouponId(data.getCouponId());
        resp.setStartTime(data.getStartTime());
        resp.setEndTime(data.getEndTime());
        return resp;
    }


    /**
     * 券可领性校验
     *
     * @param request
     * @return
     */
    @Override
    public Result<CouponFetchCheckResponse> couponFetchCheck(CouponFetchCheckRequest request) {
        CouponFetchCheckResponse response = new CouponFetchCheckResponse();
        try {
            log.info("CouponAssignService.couponFetchCheck begin request:{}", request);
            CouponFetchReqParam couponFetchReqParam = new CouponFetchReqParam();
            initParam(request, couponFetchReqParam);
            Map<Long, CouponFetchCheckInfo> couponFetchCheckInfoMap = couponFetchCheckService.couponFetchCheck(couponFetchReqParam);
            Map<Long, List<Long>> couponMap = null;
            if (request.isWithValidCoupon()) {
                couponMap = userCouponList.getValidCouponIdListByUidConfigIds(request.getUid(), request.getConfigIdList());
            }
            response.setCouponFetchResult(convertToDTO(couponFetchCheckInfoMap, couponMap));
            log.info("CouponAssignService.couponFetchCheck end request:{} response:{}", request, response);
            return Result.success(response);
        } catch (BizError e) {
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("CouponAssignService.couponFetchCheck error request:{} e", request, e);
            return Result.fromException(e, "券可领性校验异常");
        }
    }

    @Override
    public Result<SingleAssignResponse> fillSingle(FillSingleAssignRequest request) {
        try {
            //转换dto => do
            SingleAssignRequestDo reqDo = fillSingleAssignRequestDto2Do(request);

            SingleAssignResponseDo respDto = singleAssigner.assign(reqDo);

            SingleAssignResponse result = singleAssignResponseDto2Do(respDto);

            log.info("coupon.fillSingle success，request={}, response={}", request, result);

            return Result.success(result);
        } catch (BizError e) {
            log.info("coupon.fillSingle, {}， request={}", e.getMessage(), request);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("coupon.fillSingle, 优惠券灌券发放异常，request={}", request, e);
            return Result.fromException(e, "优惠券灌券发放失败");
        }
    }

    /**
     * 初始化校验参数
     *
     * @param request
     * @param couponFetchReqParam
     */
    private void initParam(CouponFetchCheckRequest request, CouponFetchReqParam couponFetchReqParam) throws BizError {
        if (request.getUid() == null || request.getUid() <= 0) {
            throw ExceptionHelper.create(GeneralCodes.NotAuthorized, "登录信息不正确");
        }
        if (CollectionUtils.isEmpty(request.getConfigIdList())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "优惠券ID不能为空");
        }
        if (request.getConfigIdList().size() > CommonConstant.COUPON_CHECK_MAX) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "优惠券个数必须小于" + CommonConstant.COUPON_CHECK_MAX);
        }
        // 兼容老3c
        if (Objects.isNull(request.getBizPlatform())) {
            request.setBizPlatform(BizPlatformEnum.RETAIL.getCode());
        }
        couponFetchReqParam.setUid(request.getUid());
        couponFetchReqParam.setConfigIdList(request.getConfigIdList());
        couponFetchReqParam.setSceneCode(request.getSceneCode());
        couponFetchReqParam.setAppId(request.getAppId());
        couponFetchReqParam.setBizPlatform(request.getBizPlatform());
    }

    /**
     * 封装校验结果DTO
     *
     * @param couponFetchCheckInfoMap
     * @param couponMap
     * @return
     */
    private Map<Long, CouponFetchCheckDTO> convertToDTO(Map<Long, CouponFetchCheckInfo> couponFetchCheckInfoMap, Map<Long, List<Long>> couponMap) {
        Map<Long, CouponFetchCheckDTO> couponFetchCheckDTOMap = new HashMap<>();
        for (Long configId : couponFetchCheckInfoMap.keySet()) {
            CouponFetchCheckDTO couponFetchCheckDTO = new CouponFetchCheckDTO();
            CouponFetchCheckInfo couponFetchCheckInfo = couponFetchCheckInfoMap.get(configId);
            couponFetchCheckDTO.setFetched(couponFetchCheckInfo.isFetched());
            couponFetchCheckDTO.setFetchAble(couponFetchCheckInfo.isFetchAble());
            couponFetchCheckDTO.setInvalidCode(couponFetchCheckInfo.getInvalidCode());
            couponFetchCheckDTO.setInvalidReason(couponFetchCheckInfo.getInvalidReason());
            if (MapUtils.isNotEmpty(couponMap)) {
                couponFetchCheckDTO.setValidCouponList(couponMap.get(configId));
            }
            couponFetchCheckDTOMap.put(configId, couponFetchCheckDTO);
        }
        return couponFetchCheckDTOMap;
    }

    /**
     * dto => do
     *
     * @param r SingleAssignRequest
     * @return SingleAssignRequestDo
     */
    private SingleAssignRequestDo fillSingleAssignRequestDto2Do(FillSingleAssignRequest r) {
        SingleAssignRequestDo req = new SingleAssignRequestDo();
        req.setRequestId(r.getRequestId());
        req.setUserId(r.getUserId());
        req.setSceneCode(r.getSceneCode());
        req.setConfigId(r.getConfigId());
        //默认接口发放
        if (r.getAssignMode() == null) {
            req.setAssignMode(SceneAssignModeEnum.Interface.getValue());
        } else {
            req.setAssignMode(r.getAssignMode());
        }

        if (SceneAssignModeEnum.Fill.getValue().equals(req.getAssignMode())) {
            req.setActivityId(String.valueOf(r.getTaskId()));
        } else {
            req.setActivityId(String.valueOf(r.getConfigId()));
        }

        req.setShareUserId(r.getShareUserId());
        req.setOrgCode(r.getOrgCode());
        req.setOrderId(r.getOrderId());
        req.setAppId(r.getAppId());
        req.setVid(r.getVid());
        req.setBizPlatform(r.getBizPlatform());
        return req;
    }

}
