package com.xiaomi.nr.coupon.domain.coupon.impl;

import com.xiaomi.nr.coupon.api.dto.coupon.list.UserCouponListRequest;
import com.xiaomi.nr.coupon.domain.coupon.AbstractUserCouponList;
import com.xiaomi.nr.coupon.domain.coupon.constant.UserCouponListConst;
import com.xiaomi.nr.coupon.domain.coupon.model.CouponInfoModel;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave.UserCouponListMapper;
import com.xiaomi.nr.coupon.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/20
 */
@Component
@Slf4j
public class UserCouponListImpl extends AbstractUserCouponList {

    @Autowired
    private UserCouponListMapper userCouponListMapper;

    /**
     * 从DB获取用户优惠券列表初始信息
     *
     * @param req            用户优惠券列表请求参数
     * @return Pair<Integer, List<CouponPo>>   (页大小, 过滤后po)
     */
    @Override
    protected Pair<Integer, List<CouponPo>> getCouponPoList(UserCouponListRequest req) {

        // 1、查询DB
        long nowTime = TimeUtil.getNowUnixSecond();
        long nearlyDaysTime = nowTime - UserCouponListConst.USER_COUPON_VALID_PERIOD;
        List<CouponPo> originCouponPoList = getCouponPoList(req, nowTime, nearlyDaysTime);
        int poSize = originCouponPoList.size();

        // 2、过期的券只返回近90天以内的
        List<CouponPo> couponPoList = validExpiredCoupon(originCouponPoList, nearlyDaysTime);
        return Pair.of(poSize, couponPoList);
    }


    @Override
    protected List<CouponInfoModel> filterCoupon(UserCouponListRequest req, List<CouponInfoModel> couponInfoList) {
        String useChannel = req.getUseChannel();
        if(StringUtils.isEmpty(useChannel) || CollectionUtils.isEmpty(couponInfoList)){
            return couponInfoList;
        }

        String[] useChannels = useChannel.split(",");

        //过滤出包含指定使用渠道的优惠券信息
        List<CouponInfoModel> list = new ArrayList<>();
        for(CouponInfoModel item : couponInfoList){
            for(String channel : useChannels) {
                if (item.getUseChannel().contains(channel)) {
                    list.add(item);
                    break;
                }
            }
        }
        return list;
    }

    /**
     * 从DB获取用户优惠券列表初始信息
     *
     * @param req     用户优惠券列表请求参数
     * @return List<> 优惠券列表(DB)
     */
    private List<CouponPo> getCouponPoList(UserCouponListRequest req, long nowTime, long nearlyDaysTime) {

        //基础查询参数
        List<Integer> bizPlatformList = req.getBizPlatform();
        String status = req.getStatus();
        long userId = req.getUserId();
        Long lastId = req.getLastId();
        Integer pageSize = req.getPageSize();
        String sendChannel = req.getSendChannel();
        CouponStatusEnum statEunm = CouponStatusEnum.findByValue(status);
        if (Objects.isNull(statEunm)) {
            log.info("coupon.getUserCouponList.getCouponPoList(), 非法的优惠券状态, 请求参数request={}", req);
            return Collections.emptyList();
        }

        switch (statEunm) {

            //优惠券状态为所有(all): 全量返回
            case ALL:
                return userCouponListMapper.getCouponByAll(userId, sendChannel, nearlyDaysTime, bizPlatformList);

            //优惠券状态为未使用(unused): 全量返回
            case UNUSED:
                return userCouponListMapper.getCouponByUnused(userId, nowTime, sendChannel, bizPlatformList);

            //优惠券状态为已使用(used): 分页返回
            case USED:
                return userCouponListMapper.getCouponByUsedLocked(userId, sendChannel, lastId, pageSize, bizPlatformList);

            //优惠券状态为已分享/已领取(presented/received): 分页返回
            case PRESENTED:
            case RECEIVED:
                return userCouponListMapper.getCouponByStat(userId, status, sendChannel, lastId, pageSize, bizPlatformList);

            //优惠券状态为已使用+已分享+已领取(used+presented+received): 分页返回
            case USED_PRESENTED_RECEIVED:
                return userCouponListMapper.getCouponByUsedPreRec(userId, nowTime, sendChannel, lastId, pageSize, bizPlatformList);

            //优惠券状态为已过期(expired): 分页返回
            case EXPIRED:
                //只返回近90天以内过期的券
                return userCouponListMapper.getCouponByExpired(userId, nearlyDaysTime, nowTime, sendChannel, lastId, pageSize, bizPlatformList);

            default:
                log.info("coupon.UserCouponList.getCouponPoList(), 优惠券状态不合法, 请求参数request={}", req);
                return Collections.emptyList();
        }
    }

    /**
     * 过期的券只返回近90天以内的
     *
     * @param originCouponPoList
     * @param nearlyDaysTime
     * @return
     */
    private List<CouponPo> validExpiredCoupon(List<CouponPo> originCouponPoList, long nearlyDaysTime){
        if (CollectionUtils.isEmpty(originCouponPoList)) {
            return originCouponPoList;
        }

        List<CouponPo> couponPoList = new ArrayList<>();
        for (CouponPo couponPo : originCouponPoList) {
            if (StringUtils.equals(CouponStatusEnum.EXPIRED.getValue(), couponPo.getStat()) && couponPo.getAddTime() < nearlyDaysTime) {
                continue;
            }

            if (Long.parseLong(couponPo.getEndTime()) < nearlyDaysTime) {
                continue;
            }
            couponPoList.add(couponPo);
        }

        return couponPoList;
    }

}
