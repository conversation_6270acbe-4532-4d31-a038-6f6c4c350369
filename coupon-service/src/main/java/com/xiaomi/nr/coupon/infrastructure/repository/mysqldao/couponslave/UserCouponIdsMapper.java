package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户优惠券mapper
 *
 * <AUTHOR>
 */
@Mapper
@Repository
public interface UserCouponIdsMapper {

    /**
     * 根据券配置ID批量查询用户近*天的券列表（部分信息，支持批量查询）
     *
     * @param userId  long
     * @param addTime Long
     * @return ArrayList<CouponConfig>
     */
    @Select("<script>select id,type_id,start_time,end_time,stat" +
            " from tb_coupon" +
            " where user_id=#{userId} and add_time>=#{addTime} and type_id in <foreach collection='configIds' item='id' index='index' open='(' close=')' separator=','>#{id}</foreach></script>")
    ArrayList<CouponPo> getListByConfigIds(@Param("userId") long userId, @Param("configIds") List<Long> configIds, @Param("addTime") Long addTime);

}
