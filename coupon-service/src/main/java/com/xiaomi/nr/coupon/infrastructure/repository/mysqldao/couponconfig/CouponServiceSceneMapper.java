package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.po.ServiceScenePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description: 优惠券服务场景mapper
 * @author: heji<PERSON><PERSON>
 * @Date 2022/2/26 9:39 下午
 * @Version: 1.0
 **/
@Mapper
@Component
public interface CouponServiceSceneMapper {

    /**
     * 获取优惠券服务场景
     *
     */
    @Select("select id, name, coupon_type, deduct_rule, mutual_rule from nr_coupon_service_scene where status=1")
    List<ServiceScenePo> getAllCouponServiceScene();


}
