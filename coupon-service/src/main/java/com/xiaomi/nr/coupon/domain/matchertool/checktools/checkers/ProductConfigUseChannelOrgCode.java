package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import com.alibaba.nacos.common.utils.StringUtils;
import com.xiaomi.nr.coupon.constant.CouponConfigConstant;
import com.xiaomi.nr.coupon.constant.UseChannelClientRel;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.UseChannel;
import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.enums.coupon.CouponUseChannelEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.UseChannelEnum;
import com.xiaomi.nr.coupon.enums.store.StoreTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.storeinfo.po.OrgInfo;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 校验可用渠道&门店（产品站专用）
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Component
public class ProductConfigUseChannelOrgCode extends CheckerAbstract {

    @Override
    public boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {
        if (!ctx.isCheckUserChannelAndOrgCode()) {
            return true;
        }

        errCtx.setInvalidConfig(true);

        CouponConfigItem config = ctx.getConfigInfoMap().get(respItem.getConfigId());
        Long clientId = ctx.getClientId();
        String orgCode = ctx.getOrgCode();
        OrgInfo orgInfo = ctx.getOrgInfo();
        Map<Integer, UseChannel> channelMap = config.getUseChannelStore();

        Map<Long, Integer> clientIdMap = new HashMap<>();
        Map<String, Integer> orgCodeMap = new HashMap<>();
        for (Integer useChannel : channelMap.keySet()) {
            //全部门店
            if (channelMap.get(useChannel) != null && channelMap.get(useChannel).getAll()) {
                UseChannelEnum useChannelEnum = UseChannelEnum.findByCode(useChannel);
                if (Objects.isNull(useChannelEnum)) {
                    continue;
                }
                clientIdMap.putAll(UseChannelClientRel.getUseChannelClientRelation().get(useChannelEnum.getValue()).getClientIds().stream().collect(Collectors.toMap(x -> x, x -> 1, (key1, key2) -> key2)));
                continue;
            }
            //指定门店
            if (channelMap.get(useChannel) != null && channelMap.get(useChannel).getLimitIds() != null) {
                orgCodeMap.putAll(channelMap.get(useChannel).getLimitIds().stream().collect(Collectors.toMap(x -> x, x -> 1, (key1, key2) -> key2)));
            }
        }

        //校验clientId、orgCode
        if (clientIdMap.containsKey(clientId)) {
            return true;
        }

        if (StringUtils.isNotEmpty(orgCode)) {
            if (orgCodeMap.containsKey(orgCode)) {
                return true;
            }

            //是否属于米家
            Integer orgType = Objects.nonNull(orgInfo) ? orgInfo.getOrgType() : null;
            if (Objects.isNull(orgType)) {
                errCtx.setErrCode(ErrCode.USE_COUPON_ORGCODE_MISMATCH.getCode());
                errCtx.setErrMsg("优惠券不适用此门店");
                return false;
            }

            if (StoreTypeEnum.isDirect(orgType) || StoreTypeEnum.isSpecialty(orgType)) {
                clientId = CouponConfigConstant.MI_HOME_CLIENT_ID;
            }

            //是否属于授权店
            if (StoreTypeEnum.isAuthorized(orgType)) {
                clientId = CouponConfigConstant.AUTHORIZED_CLIENT_ID;
            }

            if(clientIdMap.containsKey(clientId)) {
                return true;
            }
        }

        errCtx.setErrCode(ErrCode.USE_COUPON_CHANNEL_MISMATCH.getCode());
        errCtx.setErrMsg("优惠券不适用此渠道");
        return false;
    }

}
