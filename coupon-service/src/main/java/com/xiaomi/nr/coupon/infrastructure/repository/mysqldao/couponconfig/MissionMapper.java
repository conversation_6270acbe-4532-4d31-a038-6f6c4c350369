package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;


/**
 * 券发放任务mapper
 *
 * <AUTHOR>
 */
@Mapper
@Component
public interface MissionMapper {

    /**
     * 根据missionId查询券配置ID
     *
     * @param missionId 券任务Id
     * @return Long 券发放任务
     */
    @Select("select type_id from nr_coupon_mission where id=#{missionId}")
    Long getConfigIdByMissionId(@Param("missionId") Long missionId);

}
