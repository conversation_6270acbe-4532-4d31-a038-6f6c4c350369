package com.xiaomi.nr.coupon.enums.couponconfig;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2024/5/10 15:30
 */
@Getter
@AllArgsConstructor
public enum CouponServiceTypeEnum {
    /**
     * 1: 基础保养
     */
    BASIC_MAINTENANCE(1, "基础保养"),

    /**
     * 2: 漆面修复
     */
    PAINT_REPAIR(2, "漆面修复"),

    /**
     * 3: 补胎
     */
    REPAIR_TAIR(3, "补胎"),
    ;

    /**
     * 枚举code
     */
    private final Integer code;

    /**
     * 枚举描述
     */
    private final String name;

    private static final HashMap<Integer, CouponServiceTypeEnum> MAPPING = new HashMap<>();

    static {
        for (CouponServiceTypeEnum e : CouponServiceTypeEnum.values()) {
            MAPPING.put(e.getCode(), e);
        }
    }

    public static CouponServiceTypeEnum valueOf(Integer code) {
        return MAPPING.get(code);
    }
}
