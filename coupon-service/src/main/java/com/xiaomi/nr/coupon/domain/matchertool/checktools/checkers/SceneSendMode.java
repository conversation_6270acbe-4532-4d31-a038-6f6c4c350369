package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import cn.hutool.core.map.MapUtil;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponSceneItem;
import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.enums.couponconfig.SceneSendModeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 校验券投放方式 优惠券/优惠码
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Component
public class SceneSendMode extends CheckerAbstract {

    @Override
    public boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {
        if (!ctx.isCheckSceneSendMode()) {
            return true;
        }

        errCtx.setInvalidConfig(true);

        CouponSceneItem sceneInfo = MapUtil.isNotEmpty(ctx.getSceneInfoMap()) ? ctx.getSceneInfoMap().get(respItem.getSceneCode()) : null;
        Integer sceneSendMode = Objects.nonNull(sceneInfo) && Objects.nonNull(sceneInfo.getSendMode()) ? sceneInfo.getSendMode() : null;

        if (!SceneSendModeEnum.Coupon.getValue().equals(sceneSendMode)) {
            errCtx.setErrCode(ErrCode.ASSIGN_COUPON_SENDMODE_MISMATCH.getCode());
            errCtx.setErrMsg("场景不支持当前投放方式");
            return false;
        }

        return true;
    }
}
