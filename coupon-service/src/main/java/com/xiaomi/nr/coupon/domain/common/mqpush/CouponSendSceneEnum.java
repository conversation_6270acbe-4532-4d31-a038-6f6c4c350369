package com.xiaomi.nr.coupon.domain.common.mqpush;

import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description 需要发送券使用消息的投放场景
 * <AUTHOR>
 * @date 2025-01-09 11:11
*/
@Getter
@AllArgsConstructor
public enum CouponSendSceneEnum {

    BINGO_MARKETING_ACTIVITY("********************************", "Bingo营销活动", BizPlatformEnum.RETAIL.getCode()),

    CLOUD_STORE_COMMUNITY_ACTIVITY("8D5D96C7D9E680EF29FF1F2FC87B8DF8", "云店社群营销活动", BizPlatformEnum.RETAIL.getCode()),

    INTERACTIVE_PLATFORM_ACTIVITY("7C783BEBB0D1C882E09DA5031B8EAEBF", "营销互动平台活动", BizPlatformEnum.RETAIL.getCode()),

    SERVICE_PACKAGE("08B818C8A63BD1849391A0430CDDCBA8", "购买服务包发券", BizPlatformEnum.CAR_AFTER_SALE.getCode()),

    ULTRA_FIRST_OWNER_NEED_MAINTENANCE("A52BB47F1A97E9DE954515463DD7B483", "Ultra首任车主权益-按需保养券", BizPlatformEnum.CAR_AFTER_SALE.getCode()),

    ULTRA_FIRST_OWNER_TIRE_REPAIR("BA3D69CF78978452D4A0D84152E33713", "Ultra首任车主权益-补胎券", BizPlatformEnum.CAR_AFTER_SALE.getCode()),

    CONSUMABLES_PACKAGE("3A82BCD62800D50CB782FFFF83542C64", "购买耗材包发券", BizPlatformEnum.CAR_AFTER_SALE.getCode()),

    OPTIONAL_TRACK_PACKAGE_COUPON_DISTRIBUTION("3F8EC16F5DD7C62352F13F781B73EE59", "选配赛道包发券", BizPlatformEnum.CAR_AFTER_SALE.getCode()),

    YU7_NEED_MAINTENANCE("3E7A51988676FB596506FBDB5A702FC5", "YU7购车免费基础权益-按需保养券", BizPlatformEnum.CAR_AFTER_SALE.getCode()),

    ULTRA_LIMIT_BOTTOM_PLATE("5E8385D64A98F1744F38BF832E9EA866", "Ultra纽北限量版-底护板", BizPlatformEnum.CAR_AFTER_SALE.getCode()),

    ULTRA_VIP_COURSE_DEDUCT("4A7E65E57A314DA73C361E614FC49329", "Ultra会员课程-0元抵扣券", BizPlatformEnum.CAR_SHOP.getCode()),

    ULTRA_VIP_Welcome_Gift_DEDUCT("E14D88941F5672AEF3263C4BB50EBB2E", "Ultra会员入会礼盒-0元抵扣券", BizPlatformEnum.CAR_SHOP.getCode()),
    ;

    private final String code;
    private final String name;
    private final Integer bizPlatform;
}
