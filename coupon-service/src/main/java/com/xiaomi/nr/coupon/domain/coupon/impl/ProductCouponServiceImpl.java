package com.xiaomi.nr.coupon.domain.coupon.impl;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.xiaomi.nr.coupon.api.dto.coupon.*;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.constant.CommonConstant;
import com.xiaomi.nr.coupon.constant.CouponConfigConstant;
import com.xiaomi.nr.coupon.constant.CouponConstant;
import com.xiaomi.nr.coupon.constant.UseChannelClientRel;
import com.xiaomi.nr.coupon.domain.coupon.ProductCouponService;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponConvert;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponFormatCommon;
import com.xiaomi.nr.coupon.domain.coupon.model.CouponInfoModel;
import com.xiaomi.nr.coupon.domain.coupon.model.GoodsUsableCouponTypeReq;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.GoodScope;
import com.xiaomi.nr.coupon.domain.couponconfig.model.UseChannel;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherBaseMethod;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherToolFactory;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherToolInterface;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherConfigItemDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherGoodsItemDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.nocodefetchable.NoCodeFetchableReqDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.nocodeusable.NoCodeUsableReqDo;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.*;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.enums.matchertool.MatcherToolTypeEnum;
import com.xiaomi.nr.coupon.enums.store.StoreTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.UserCouponRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.nacosconfig.NacosSwitchConfig;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponactivity.CouponActivityRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponactivity.po.ActivityCacheCouponItem;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponactivity.po.ActivityCacheInfo;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponactivity.po.ActivityCacheItem;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.GlobalConfigRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.po.CompareGoodsItem;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.storeinfo.StoreInfoRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.storeinfo.po.OrgInfo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProductCouponServiceImpl implements ProductCouponService {

    @Autowired
    private CouponConvert couponConvert;

    @Autowired
    private StoreInfoRedisDao storeInfoRedisDao;

    @Autowired
    private CouponFormatCommon couponFormatCommon;

    @Resource
    private MatcherToolFactory matcherToolFactory;

    @Autowired
    private GlobalConfigRedisDao globalConfigRedisDao;

    @Autowired
    private UserCouponRepository userCouponRepository;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private CouponActivityRedisDao couponActivityRedisDao;

    @Autowired
    private NacosSwitchConfig nacosSwitchConfig;

    @Autowired
    private MatcherBaseMethod matcherBaseMethod;


    /**
     * 获取商品可领券方法(用户没领的券)
     *
     * @param request ProductCouponRequest
     * @throws BizError（业务异常）
     */
    @Override
    public Map<Long, List<GoodsCouponEvent>> getProductApplyCoupon(ProductCouponRequest request) throws BizError {

        //获取当前有效的可领券活动(key:券配置id)
        Map<Long, CouponEventInfo> eventMap = getValidEventInfo(request.getClientId().toString(), request.getChannelId());

        //获取可领券id
        Set<Long> applyConfigIdList = GetApplyCouponIds(request.getCoinCouponTypeIdList(), request.getProMemberCouponIdList(), new HashSet<>(eventMap.keySet()));
        if (CollectionUtils.isEmpty(applyConfigIdList)) {
            return Collections.emptyMap();
        }

        //获取可领券对应的有效券配置信息
        Map<Long, List<CouponConfigItem>> couponConfigMap = GoodsUsableCouponType(buildRequest(request, applyConfigIdList));
        if (MapUtils.isEmpty(couponConfigMap)) {
            return Collections.emptyMap();
        }

        //券模型转换
        Map<Long, List<CouponInfoModel>> couponInfoModelMap = mergeCouponModel(couponConfigMap, null, eventMap);
        if (MapUtils.isEmpty(couponInfoModelMap)) {
            return Collections.emptyMap();
        }

        //券排序
        sortCouponModel(couponInfoModelMap);

        //格式化优惠券返回信息 model -> dto
        return formatCanApplyCoupon(couponInfoModelMap, request, eventMap);
    }


    /**
     * 获取用户券列表下的商品可用券
     *
     * @param request  ProductCouponRequest
     * @param eventMap eventMap
     * @return Map<Long, List < CouponList>>
     * @throws BizError (业务异常)
     */
    @Override
    public Map<Long, List<CouponList>> getProductUserCoupon(ProductCouponRequest request, Map<Long, CouponEventInfo> eventMap) throws BizError {

        if (Objects.isNull(request.getUserId()) || request.getUserId() <= CommonConstant.ZERO_LONG) {
            return Collections.emptyMap();
        }

        //获取该用户下的券信息
        long nowTime = TimeUtil.getNowUnixSecond();
        List<Integer> bizPlatform = Lists.newArrayList(BizPlatformEnum.RETAIL.getCode());
        Map<Long, List<CouponPo>> configToCouponMap = userCouponRepository.getUserCoupon(request.getUserId(), CouponStatusEnum.UNUSED.getValue(), bizPlatform, nowTime);
        if (MapUtils.isEmpty(configToCouponMap)) {
            return Collections.emptyMap();
        }

        //获取用户可用的券配置基础信息
        Map<Long, List<CouponConfigItem>> configMap = GoodsUsableCouponType(buildRequest(request, configToCouponMap.keySet()));
        if (MapUtils.isEmpty(configMap)) {
            return Collections.emptyMap();
        }

        //券模型转换
        Map<Long, List<CouponInfoModel>> couponInfoModelMap = mergeCouponModel(configMap, configToCouponMap, null);

        //券排序
        sortCouponModel(couponInfoModelMap);

        //格式化优惠券返回信息 model -> dto
        return formatUserCoupon(couponInfoModelMap, eventMap);
    }


    /**
     * 券排序
     *
     * @param couponInfoModelMap 券模型
     */
    private void sortCouponModel(Map<Long, List<CouponInfoModel>> couponInfoModelMap) {
        //券排序
        for (Long sku : couponInfoModelMap.keySet()) {
            if (CollectionUtils.isNotEmpty(couponInfoModelMap.get(sku))) {
                List<CouponInfoModel> sortCoupon = sortCoupon(couponInfoModelMap.get(sku));
                couponInfoModelMap.get(sku).clear();
                couponInfoModelMap.get(sku).addAll(sortCoupon);
            }
        }
    }


    /**
     * 格式化用户券列表返回
     *
     * @param couponInfoModelMap 用户券
     * @param eventMap           可领券活动
     * @return Map<Long, List < CouponList>>
     */
    private Map<Long, List<CouponList>> formatUserCoupon(Map<Long, List<CouponInfoModel>> couponInfoModelMap, Map<Long, CouponEventInfo> eventMap) {
        Map<Long, List<CouponList>> userCouponMap = new HashMap<>();
        boolean hasEvent = MapUtils.isEmpty(eventMap);
        for (Long sku : couponInfoModelMap.keySet()) {

            List<CouponList> couponLists = new ArrayList<>();
            for (CouponInfoModel couponInfoModel : couponInfoModelMap.get(sku)) {
                if (!hasEvent) {
                    couponLists.add(couponConvert.convert2CouponListDto(couponInfoModel, eventMap.get(couponInfoModel.getConfigId())));
                } else {
                    couponLists.add(couponConvert.convert2CouponListDto(couponInfoModel, null));
                }
            }

            if (!userCouponMap.containsKey(sku)) {
                userCouponMap.put(sku, couponLists);
                continue;
            }

            userCouponMap.get(sku).addAll(couponLists);
        }

        return userCouponMap;
    }


    /**
     * 券模型转换&排序
     *
     * @param couponConfigMap   券配置信息
     * @param configToCouponMap 用户券信息
     * @return 券model
     * @throws BizError
     */
    private Map<Long, List<CouponInfoModel>> mergeCouponModel(Map<Long, List<CouponConfigItem>> couponConfigMap,
                                                              Map<Long, List<CouponPo>> configToCouponMap,
                                                              Map<Long, CouponEventInfo> eventMap) throws BizError {

        Map<Long, List<CouponInfoModel>> resultMap = new HashMap<>();
        long nowTime = TimeUtil.getNowUnixSecond();
        //po -> model
        for (Long sku : couponConfigMap.keySet()) {
            List<CouponInfoModel> couponInfoModelList = new ArrayList<>();
            for (CouponConfigItem couponConfigItem : couponConfigMap.get(sku)) {

                List<CouponPo> couponPoList = null;
                Long configId = Long.parseLong(String.valueOf(couponConfigItem.getConfigId()));
                if (MapUtils.isNotEmpty(configToCouponMap)) {
                    couponPoList = configToCouponMap.get(configId);
                }

                //限仅3c的券
                if(!couponConfigItem.getBizPlatform().equals(BizPlatformEnum.RETAIL.getCode())) {
                    continue;
                }

                CouponInfoModel couponInfoModel;
                //可领券
                if (CollectionUtils.isEmpty(couponPoList)) {
                    //领券时间校验
                    if (nowTime > couponConfigItem.getCouponConfigInfo().getEndFetchTime()) {
                        continue;
                    }

                    if (!Objects.equals(ConfigStatusEnum.Online.getValue(), couponConfigItem.getCouponConfigInfo().getStatus())) {
                        continue;
                    }
                    couponInfoModel = couponConvert.convertPoToModelItem(couponConfigItem, eventMap.get(configId));
                    couponFormatCommon.setCouponAIS(couponInfoModel);
                    if (Objects.equals(ConfigStatusEnum.Online.getValue(), couponInfoModel.getConfigStatus())) {
                        couponInfoModelList.add(couponInfoModel);
                    }

                } else {
                    //可用券
                    for (CouponPo couponPo : couponPoList) {
                        couponInfoModel = couponConvert.convertPoToModel(couponPo, couponConfigItem);
                        couponFormatCommon.setCouponAIS(couponInfoModel);
                        couponInfoModelList.add(couponInfoModel);
                    }
                }
            }

            resultMap.put(sku, couponInfoModelList);
        }

        return resultMap;
    }


    /**
     * 格式化可领券活动的优惠券返回信息(model -> dto)
     *
     * @param couponModelMap 券model
     * @param request        ProductCouponRequest
     * @param eventMap       领券活动信息
     * @return Map<Long, List < GoodsCouponEvent>>
     */
    private Map<Long, List<GoodsCouponEvent>> formatCanApplyCoupon(Map<Long, List<CouponInfoModel>> couponModelMap, ProductCouponRequest request, Map<Long, CouponEventInfo> eventMap) {

        Map<Long, List<GoodsCouponEvent>> resultMap = new HashMap<>();
        for (Long sku : couponModelMap.keySet()) {
            for (CouponInfoModel couponModel : couponModelMap.get(sku)) {

                //model -> dto
                CouponEventInfo event = eventMap.get(couponModel.getConfigId());
                CouponList couponList = couponConvert.convert2CouponListDto(couponModel, event);
                if (StringUtils.isNotEmpty(PromotionType.getValueByCode(couponModel.getUseType()))) {
                    couponList.setType(PromotionType.getValueByCode(couponModel.getUseType()));
                }

                if (CollectionUtils.isNotEmpty(request.getCoinCouponTypeIdList()) && request.getCoinCouponTypeIdList().contains(couponList.getTypeId().toString())) {
                    couponList.setType(PromotionType.TypeCodeCouponCoin.getValue());
                }

                if (CollectionUtils.isNotEmpty(request.getProMemberCouponIdList()) && request.getProMemberCouponIdList().contains(couponList.getTypeId().toString())) {
                    couponList.setTags(Collections.singletonList(PromotionType.ProMemberCoupon.getValue()));
                }

                GoodsCouponEvent goodsCouponEvent = new GoodsCouponEvent(couponList, event);
                if (!resultMap.containsKey(sku)) {
                    resultMap.put(sku, new ArrayList<>(Collections.singletonList(goodsCouponEvent)));
                    continue;
                }
                resultMap.get(sku).add(goodsCouponEvent);
            }
        }

        return resultMap;
    }


    /**
     * 获取可参加的领券id
     *
     * @param coinCouponTypeIdList  米金兑换券
     * @param proMemberCouponIdList 会员券
     * @param configIds             可领券活动的券id
     * @return Set<Long>
     */
    public Set<Long> GetApplyCouponIds(List<String> coinCouponTypeIdList, List<String> proMemberCouponIdList, Set<Long> configIds) {

        Set<Long> configIdList = CollectionUtils.isEmpty(configIds) ? new HashSet<>() : configIds;

        //米金兑换券
        addList(coinCouponTypeIdList, configIdList);

        //pro会员券
        addList(proMemberCouponIdList, configIdList);

        return configIdList;
    }


    /**
     * 添加集合
     *
     * @param applyConfigIdList 活动可领券id
     * @param configIdList      券id列表
     */
    private void addList(List<String> applyConfigIdList, Set<Long> configIdList) {
        if (CollectionUtils.isEmpty(applyConfigIdList)) {
            return;
        }

        for (String idStr : applyConfigIdList) {
            if (StringUtils.isEmpty(idStr)) {
                continue;
            }

            configIdList.add(Long.parseLong(idStr));
        }
    }


    /**
     * 构建券类型参数
     *
     * @param request      接口请求参数
     * @param configIdList 券配置id
     * @return GoodsUsableCouponTypeReq
     */
    private GoodsUsableCouponTypeReq buildRequest(ProductCouponRequest request, Set<Long> configIdList) {
        GoodsUsableCouponTypeReq req = new GoodsUsableCouponTypeReq();
        req.setClientId(request.getClientId());
        req.setUserId(request.getUserId());
        req.setConfigIdList(configIdList);
        req.setGoodsInfo(new ArrayList<>(request.getGoodsInfoList()));
        req.setOrgCode(request.getOrgCode());
        req.setGoodsType(StringUtils.equals("goods", request.getLevel()) ? 1 : 2);
        return req;
    }


    /**
     * 获取当前有效的领券活动信息
     *
     * @param clientId  clientId
     * @param channelId 渠道id
     * @return 领券活动信息
     */
    private Map<Long, CouponEventInfo> getValidEventInfo(String clientId, String channelId) {

        Map<Long, CouponEventInfo> resultMap = new HashMap<>();
        ActivityCacheInfo actInfo = couponActivityRedisDao.get();
        if (Objects.isNull(actInfo)) {
            return Collections.emptyMap();
        }

        long nowTime = TimeUtil.getNowUnixSecond();
        for (ActivityCacheItem act : actInfo.getData()) {
            //判断活动时间是否合法(//过滤已过期或未开始活动)
            if (act.getStartTime() == CommonConstant.ZERO_LONG || act.getEndTime() == CommonConstant.ZERO_LONG || act.getStartTime() > nowTime || act.getEndTime() <= nowTime) {
                continue;
            }

            for (ActivityCacheCouponItem coupon : act.getCoupons()) {

                if (StringUtils.isNotEmpty(coupon.getClientId())) {
                    String[] clients = StringUtils.split(coupon.getClientId(), ",");
                    Map<String, List<String>> clientChannelMap = new HashMap<>();
                    for (String client : clients) {
                        String[] infoArr = StringUtils.split(client, "_");

                        if (infoArr.length != 2) {
                            continue;
                        }

                        if (StringUtils.isNotEmpty(infoArr[1])) {
                            String[] channels = StringUtils.split(infoArr[1], "#");
                            clientChannelMap.put(infoArr[0], Arrays.asList(channels));
                        }
                    }
                    List<String> channels = clientChannelMap.get(clientId);
                    if (CollectionUtils.isEmpty(channels) || !channels.contains(channelId)) {
                        continue;
                    }
                }

                Long configId = coupon.getConfigId();
                if (!resultMap.containsKey(configId)) {
                    CouponEventInfo couponEventInfo = new CouponEventInfo();
                    couponEventInfo.setActTag(coupon.getActivityTag());
                    couponEventInfo.setCouponTag(coupon.getCouponTag());
                    couponEventInfo.setClientId(coupon.getClientId());
                    couponEventInfo.setSid(coupon.getSid());
                    couponEventInfo.setActId(coupon.getId());
                    couponEventInfo.setActName(act.getActName());
                    couponEventInfo.setActCode(act.getActCode());
                    couponEventInfo.setStartTime(act.getStartTime());
                    couponEventInfo.setEndTime(act.getEndTime());
                    couponEventInfo.setCrowd(coupon.getCrowd());
                    resultMap.put(configId, couponEventInfo);
                } else {
                    resultMap.get(configId).setSid(coupon.getSid());
                }
            }
        }

        return resultMap;
    }


    /**
     * 获取券类型中该商品可用券
     *
     * @param req GoodsUsableCouponTypeReq
     * @return Map<Long, List < CouponConfigItem>>
     * @throws BizError (业务异常)
     */
    private Map<Long, List<CouponConfigItem>> GoodsUsableCouponType(GoodsUsableCouponTypeReq req) throws BizError {
        if (CollectionUtils.isEmpty(req.getConfigIdList())) {
            return Collections.emptyMap();
        }
        Map<Long, List<CouponConfigItem>> resultMap = new HashMap<>();

        //批量取出券配置信息
        Map<Long, CouponConfigItem> couponConfigMap = couponConfigRepository.getCouponConfigs(new ArrayList<>(req.getConfigIdList()));
        for (Map.Entry<Long, CouponConfigItem> item : couponConfigMap.entrySet()) {
            CouponConfigItem couponConfigItem = item.getValue();

            //基础券信息校验
            if (!checkCouponConfig(couponConfigItem, req)) {
                continue;
            }

            //校验商品用券条件,返回能用券的商品
            Set<Long> fulfillGoods = checkGoodsUsableCoupon(couponConfigItem, req);
            if (CollectionUtils.isEmpty(fulfillGoods)) {
                continue;
            }

            //构建商品能用券的映射关系
            for (Long sku : fulfillGoods) {
                if (resultMap.containsKey(sku)) {
                    resultMap.get(sku).add(couponConfigItem);
                    continue;
                }
                resultMap.put(sku, new ArrayList<>(Collections.singletonList(couponConfigItem)));
            }
        }

        return resultMap;
    }


    /**
     * 券列表排序
     *
     * @param list 未排序的券
     * @return 排序后的券
     */
    private List<CouponInfoModel> sortCoupon(List<CouponInfoModel> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        //券排序
        list = list.stream().sorted((c1, c2) -> {

            // 超级补贴券 >> 商品券 >> 运费券
            if (CouponConstant.COUPON_TYPE_SORT_MAP.getOrDefault(c1.getCouponType(), 1) < CouponConstant.COUPON_TYPE_SORT_MAP.getOrDefault(c2.getCouponType(), 1)) {
                return -1;
            }
            if (CouponConstant.COUPON_TYPE_SORT_MAP.getOrDefault(c1.getCouponType(), 1) > CouponConstant.COUPON_TYPE_SORT_MAP.getOrDefault(c2.getCouponType(), 1)) {
                return 1;
            }

            // 满减、立减 > 折扣 > N元购
            if (CouponConstant.PROMOTION_TYPE_SORT_MAP.getOrDefault(c1.getUseType(), 0) < CouponConstant.PROMOTION_TYPE_SORT_MAP.getOrDefault(c2.getUseType(), 0)) {
                return -1;
            }
            if (CouponConstant.PROMOTION_TYPE_SORT_MAP.getOrDefault(c1.getUseType(), 0) > CouponConstant.PROMOTION_TYPE_SORT_MAP.getOrDefault(c2.getUseType(), 0)) {
                return 1;
            }

            //满件 > 满元
            if (c1.getQuotaType() < c2.getQuotaType()) {
                return 1;
            }
            if (c1.getQuotaType() > c2.getQuotaType()) {
                return -1;
            }

            // 满件|满元 从小到大
            Integer bottomValue1 = c1.getQuotaCount();
            Integer bottomValue2 = c2.getQuotaCount();
            if (c1.getQuotaType() == BottomTypeEnum.Money.getCode() || c1.getQuotaType() == BottomTypeEnum.EveMoney.getCode()) {
                bottomValue1 = c1.getQuotaMoney();
                bottomValue2 = c2.getQuotaMoney();
            }

            if (bottomValue1 < bottomValue2) {
                return -1;
            }
            if (bottomValue1 > bottomValue2) {
                return 1;
            }

            //优惠值从大到小
            if (Objects.equals(PromotionType.ConditionDiscount.getCode(), c1.getUseType())) {
                //折扣券折数从小到大
                if (c1.getReduceDiscount() < c2.getReduceDiscount()) {
                    return -1;
                }
                if (c1.getReduceDiscount() > c2.getReduceDiscount()) {
                    return 1;
                }

            } else {
                //满减券优惠力度从大到小
                if (c1.getReduceMoney() < c2.getReduceMoney()) {
                    return 1;
                }
                if (c1.getReduceMoney() > c2.getReduceMoney()) {
                    return -1;
                }
            }

            //结束时间从小到大
            return c1.getEndTime().compareTo(c2.getEndTime());

        }).collect(Collectors.toList());

        return list;
    }


    /**
     * 券配置基础校验
     *
     * @param couponConfigItem 券配置信息
     * @param req              GoodsUsableCouponTypeReq
     * @return bool
     * @throws BizError
     */
    private boolean checkCouponConfig(CouponConfigItem couponConfigItem, GoodsUsableCouponTypeReq req) throws BizError {

        if (Objects.isNull(couponConfigItem)) {
            return false;
        }

        //券类型校验
        if (Objects.isNull(couponConfigItem.getPromotionType())) {
            return false;
        }

        //godsInclude校验
        if (Objects.isNull(couponConfigItem.getGoodScope())) {
            return false;
        }

        //clientId、orgCode校验校验
        if (!checkClientId(couponConfigItem.getUseChannelStore(), req.getClientId(), req.getOrgCode())) {
            return false;
        }

        //这里的方法都是3c产品站老接口所用
        if(!BizPlatformEnum.RETAIL.getCode().equals(couponConfigItem.getBizPlatform())) {
            return false;
        }

        return true;
    }


    /**
     * 校验商品信息
     *
     * @param couponConfigItem 券配置
     * @param req              GoodsUsableCouponTypeReq
     * @return 可用券商品的sku||packageId
     */
    private Set<Long> checkGoodsUsableCoupon(CouponConfigItem couponConfigItem, GoodsUsableCouponTypeReq req) {

        //全局商品排除校验
        CompareGoodsItem compareGoodsItem = globalConfigRedisDao.getGlobalInExclude();
        CompareGoodsItem compareGoodsItemCoupon = globalConfigRedisDao.getGlobalCouponInExclude();

        Set<Long> goodsList = new HashSet<>();
        for (ProductInfo goodsInfo : req.getGoodsInfo()) {

            //特价商品不可用券过滤
            boolean isSpecial = goodsInfo.getMarketPrice() > goodsInfo.getSalePrice();
            if (couponConfigItem.getExtPropInfo().getCheckPrice() == CommonConstant.ONE_INT && isSpecial) {
                continue;
            }

            //第三方商品过滤（只有自营和采销可以用券,有品和POP不能用券）
            if (checkGoodsSource(goodsInfo.getSource(), goodsInfo.getBusinessType())) {
                continue;
            }

            //券包含商品校验
            if (!isGoodsInclude(goodsInfo, couponConfigItem.getGoodScope())) {
                continue;
            }

            //全局商品排除
            if (isGlobalInExclude(goodsInfo, compareGoodsItem)) {
                continue;
            }

            //全局券商品排除
            if (isGlobalInExclude(goodsInfo, compareGoodsItemCoupon)) {
                continue;
            }

            goodsList.add(Objects.isNull(goodsInfo.getPackageId()) || goodsInfo.getPackageId() < CommonConstant.ONE_LONG ? goodsInfo.getSku() : goodsInfo.getPackageId());
        }

        return goodsList;
    }


    /**
     * 校验商品来源
     *
     * @param source       商品来源
     * @param businessType 商户类型
     * @return bool
     */
    //只有自营和采销的才能用券(有品和POP的不能用券)，source=2(代表有品)、businessType=2(pop)
    private boolean checkGoodsSource(int source, int businessType) {
        if (source == 2 || businessType == 2) {
            return true;
        }
        return false;
    }


    /**
     * 商品全局黑名单排除
     *
     * @param goodsInfo        商品信息
     * @param compareGoodsItem 全局排除商品
     * @return bool
     */
    private boolean isGlobalInExclude(ProductInfo goodsInfo, CompareGoodsItem compareGoodsItem) {
        if (goodsInfo.getSku() != null && CollectionUtils.isNotEmpty(compareGoodsItem.getSku()) && compareGoodsItem.getSku().contains(goodsInfo.getSku().toString())) {
            return true;
        }

        if (goodsInfo.getPackageId() != null && CollectionUtils.isNotEmpty(compareGoodsItem.getPackages()) && compareGoodsItem.getPackages().contains(goodsInfo.getPackageId().toString())) {
            return true;
        }
        return false;
    }


    /**
     * clientId & orgCode校验
     *
     * @param useChannelStore 券可用渠道
     * @param clientId        clientId
     * @param orgCode         orgCode
     * @return bool
     * @throws BizError
     */
    //todo 优化校验写法
    private boolean checkClientId(Map<Integer, UseChannel> useChannelStore, long clientId, String orgCode) throws BizError {

        if (MapUtils.isEmpty(useChannelStore)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "无法获取有效的使用渠道");
        }

        Map<Long, Integer> clientIdMap = new HashMap<>();
        Map<String, Integer> orgCodeMap = new HashMap<>();
        for (Integer useChannel : useChannelStore.keySet()) {

            //全部门店
            if (useChannelStore.get(useChannel) != null && useChannelStore.get(useChannel).getAll()) {
                UseChannelEnum useChannelEnum = UseChannelEnum.findByCode(useChannel);
                if (Objects.isNull(useChannelEnum)) {
                    continue;
                }

                clientIdMap.putAll(UseChannelClientRel.getUseChannelClientRelation().get(useChannelEnum.getValue()).getClientIds().stream().collect(Collectors.toMap(x -> x, x -> 1, (key1, key2) -> key2)));
                continue;
            }

            //指定门店
            if (useChannelStore.get(useChannel) != null && useChannelStore.get(useChannel).getLimitIds() != null) {
                orgCodeMap.putAll(useChannelStore.get(useChannel).getLimitIds().stream().collect(Collectors.toMap(x -> x, x -> 1, (key1, key2) -> key2)));

            }

        }

        //校验clientId、orgCode
        if (clientIdMap.containsKey(clientId)) {
            return true;
        } else {
            if (StringUtils.isNotEmpty(orgCode)) {

                if (orgCodeMap.containsKey(orgCode)) {
                    return true;
                }

                //取出门店信息
                OrgInfo orgInfo = storeInfoRedisDao.getOrgInfo(orgCode);
                if (Objects.isNull(orgInfo)) {
                    return false;
                }

                //是否属于米家
                if (Objects.equals(StoreTypeEnum.ORG_TYPE_DIRECT.getOrgType(), orgInfo.getType()) || Objects.equals(StoreTypeEnum.ORG_TYPE_SPECIALTY.getOrgType(), orgInfo.getType())) {
                    clientId = CouponConfigConstant.MI_HOME_CLIENT_ID;
                }

                //是否属于授权店
                if (Objects.equals(StoreTypeEnum.ORG_TYPE_AUTHORIZED.getOrgType(), orgInfo.getType())) {
                    clientId = CouponConfigConstant.AUTHORIZED_CLIENT_ID;
                }

                return clientIdMap.containsKey(clientId);
            }

            return false;
        }
    }


    /**
     * 校验商品是否在券可用商品中
     *
     * @param goodsInfo 商品信息
     * @param goodScope 券配置中商品信息
     * @return bool
     */
    private boolean isGoodsInclude(ProductInfo goodsInfo, GoodScope goodScope) {

        //检验SKU是否存在于可用商品中
        if (goodsInfo.getSku() != null && CollectionUtils.isNotEmpty(goodScope.getSkus()) && goodScope.singleCheckValidSku(goodsInfo.getSku())) {
            return true;
        }

        //检验PackageId是否存在于可用商品中
        if (goodsInfo.getPackageId() != null && CollectionUtils.isNotEmpty(goodScope.getPackages()) && goodScope.singleCheckValidPackage(goodsInfo.getPackageId())) {
            return true;
        }

        return false;
    }


    /**
     * 判断券线上线下是否可用
     *
     * @param useChannelList
     * @return
     */
    private String checkOnlineOrOffline(List<Integer> useChannelList) {
        Map<Integer, Integer> useChannelMap = useChannelList.stream().collect(Collectors.toMap(x -> x, v -> 1, (key1, key2) -> key2));

        if (useChannelMap.containsKey(UseChannelEnum.MiShop.getCode())) {
            if (useChannelMap.size() == 1) {
                return UseChannelType.Online.getMysqlValue();
            }

            if (useChannelMap.containsKey(UseChannelEnum.MiHome.getCode()) || useChannelMap.containsKey(UseChannelEnum.MiAuthorized.getCode())) {
                return UseChannelType.OnlineOffline.getMysqlValue();
            }
        } else {
            if (useChannelMap.containsKey(UseChannelEnum.MiHome.getCode()) || useChannelMap.containsKey(UseChannelEnum.MiAuthorized.getCode())) {
                return UseChannelType.Offline.getMysqlValue();
            }
        }

        return null;

    }

    /**
     * 单品页获取获取商品可用券接口（用户没领的券，最新的）
     *
     * @param request ProductUsableCouponRequest
     * @return MatcherContextDo
     * @throws BizError .
     */
    @SentinelResource(value = "com.xiaomi.nr.coupon.domain.coupon.impl.ProductCouponServiceImpl.getProductFetchableCoupon", fallback = "getProductFetchableCouponSentinelDowngrade")
    @Override
    public MatcherContextDo getProductFetchableCoupon(ProductUsableCouponRequest request) throws BizError {

        Integer bizPlatform = request.getBizPlatform();
        BizPlatformEnum bizPlatformEnum = BizPlatformEnum.valueOf(bizPlatform);

        switch (bizPlatformEnum) {
            case RETAIL:
                return getRetailProductFetchableCoupon(request);
            case CAR_SHOP:
                return getCarShopProductFetchableCoupon(request);
            default:
                throw ExceptionHelper.create(GeneralCodes.ParamError, String.format("暂不支持%s业务领域", bizPlatformEnum.getDesc()));
        }
    }

    /**
     * 单品页获取获取商品可用券接口-3C
     *
     * @param request   ProductUsableCouponRequest
     * @return MatcherContextDo
     */
    private MatcherContextDo getRetailProductFetchableCoupon(ProductUsableCouponRequest request) throws BizError {
        List<MatcherConfigItemDo> configs = CollectionUtils.isNotEmpty(request.getConfigList()) ? request.getConfigList().stream()
                .filter(e -> Objects.nonNull(e) && e.getConfigId() > 0)
                .map(e -> {
                    MatcherConfigItemDo item = new MatcherConfigItemDo();
                    item.setConfigId(e.getConfigId());
                    return item;
                }).collect(Collectors.toList()) : Collections.emptyList();


        NoCodeFetchableReqDo fetchReq = new NoCodeFetchableReqDo();
        fetchReq.setUserId(request.getUserId());
        fetchReq.setBizPlatform(Lists.newArrayList(BizPlatformEnum.RETAIL.getCode()));
        fetchReq.setClientId(request.getClientId());
        fetchReq.setOrgCode(request.getOrgCode());
        fetchReq.setConfigList(configs);
        fetchReq.setGoodsList(convertReqDtoToMatcherGoodsItemDo(request.getGoodsList()));

        fetchReq.setCheckBizPlatform(true);
        fetchReq.setCheckConfigStatus(true);
        fetchReq.setCheckConfigFetchTime(true);

        fetchReq.setCheckUserChannelAndOrgCode(true);
        fetchReq.setCheckConfigGoodsInclude(true);
        fetchReq.setCheckGlobalExcludeGoods(true);
        fetchReq.setCheckGlobalCouponExcludeGoods(true);

        MatcherToolInterface fetchMatcher = matcherToolFactory.getProvider(MatcherToolTypeEnum.PRODUCT_NOCODE_FETCHABLE);
        MatcherContextDo fetchResp = fetchMatcher.execute(fetchReq);
        if (Objects.isNull(fetchResp) || CollectionUtils.isEmpty(fetchResp.getKeyResp())) {
            return null;
        }
        return fetchResp;
    }

    private MatcherContextDo getCarShopProductFetchableCoupon(ProductUsableCouponRequest request) throws BizError {

        NoCodeFetchableReqDo fetchReq = new NoCodeFetchableReqDo();
        fetchReq.setUserId(request.getUserId());
        fetchReq.setBizPlatform(Lists.newArrayList(BizPlatformEnum.CAR_SHOP.getCode()));
        fetchReq.setGoodsList(convertReqDtoToMatcherGoodsItemDo(request.getGoodsList()));
        fetchReq.setUseChannel(request.getUseChannel());

        fetchReq.setCheckBizPlatform(true);
        fetchReq.setCheckUseChannel(true);
        fetchReq.setCheckPublicPromotion(true);
        fetchReq.setCheckConfigStatus(true);
        fetchReq.setCheckConfigFetchTime(true);
        fetchReq.setCheckConfigFetchLimit(true);
        // 查询已领券 => 产品站 => 需要校验用户限领
        fetchReq.setCheckUserFetchLimit(request.getNeedFetchedCoupon());

        MatcherToolInterface fetchMatcher = matcherToolFactory.getProvider(MatcherToolTypeEnum.CAR_SHOP_PRODUCT_NOCODE_FETCHABLE);
        MatcherContextDo fetchResp = fetchMatcher.execute(fetchReq);
        if (Objects.isNull(fetchResp) || CollectionUtils.isEmpty(fetchResp.getKeyResp())) {
            return null;
        }
        return fetchResp;
    }


    /**
     * 单品页获取获取商品可用券接口（用户没领的券，最新的，降配方法）
     *
     * @param request ProductUsableCouponRequest
     * @return MatcherContextDo
     * @throws BizError .
     */
    public MatcherContextDo getProductFetchableCouponSentinelDowngrade(ProductUsableCouponRequest request) throws BizError {
        log.info("getProductFetchableCouponSentinelDowngrade, request:{}", GsonUtil.toJson(request));
        return null;
    }

    /**
     * 单品页获取获取商品可用券接口（用户已领的券，最新的，降配方法）
     *
     * @param request ProductUsableCouponRequest
     * @return MatcherContextDo
     * @throws BizError .
     */
    public MatcherContextDo getProductUserUsableCouponSentinelDowngrade(ProductUsableCouponRequest request) throws BizError {
        log.info("getProductUserUsableCouponSentinelDowngrade, request:{}", GsonUtil.toJson(request));
        return null;
    }

    /**
     * 单品页获取获取商品可用券接口（用户已领的券，最新的）
     *
     * @param request ProductUsableCouponRequest
     * @return MatcherContextDo
     * @throws BizError .
     */
    @SentinelResource(value = "com.xiaomi.nr.coupon.domain.coupon.impl.ProductCouponServiceImpl.getProductUserUsableCoupon", fallback = "getProductUserUsableCouponSentinelDowngrade")
    @Override
    public MatcherContextDo getProductUserUsableCoupon(ProductUsableCouponRequest request) throws BizError {
        if (Objects.isNull(request.getUserId())) {
            return null;
        }
        NoCodeUsableReqDo usableReq = new NoCodeUsableReqDo();
        usableReq.setUserId(request.getUserId());
        usableReq.setBizPlatform(Lists.newArrayList(BizPlatformEnum.RETAIL.getCode()));
        usableReq.setClientId(request.getClientId());
        usableReq.setOrgCode(request.getOrgCode());
        usableReq.setGoodsList(convertReqDtoToMatcherGoodsItemDo(request.getGoodsList()));

        usableReq.setCheckBizPlatform(true);
        usableReq.setCheckUserCouponStatus(true);
        usableReq.setCheckUserCouponTime(true);
        usableReq.setCheckConfigRegion(true);
        usableReq.setCheckUserCouponSpecialStore(true);

        usableReq.setCheckUserChannelAndOrgCode(true);
        usableReq.setCheckConfigGoodsInclude(true);
        usableReq.setCheckGlobalExcludeGoods(true);
        usableReq.setCheckGlobalCouponExcludeGoods(true);

        MatcherToolInterface usableMatcher = matcherToolFactory.getProvider(MatcherToolTypeEnum.PRODUCT_NOCODE_USABLE);
        MatcherContextDo usableResp = usableMatcher.execute(usableReq);
        if (Objects.isNull(usableResp) || CollectionUtils.isEmpty(usableResp.getKeyResp())) {
            return null;
        }
        return usableResp;
    }

    /**
     * 产品站排序
     *
     * @param keyResp       List<MatcherRespItemDo>
     * @param configInfoMap Map<Long, CouponConfigItem>
     * @param userCouponMap Map<Long, CouponPo>
     * @return List<MatcherRespItemDo>
     */
    @Override
    public List<MatcherRespItemDo> sort(List<MatcherRespItemDo> keyResp, Map<Long, CouponConfigItem> configInfoMap, Map<Long, CouponPo> userCouponMap) {
        if (CollectionUtils.isEmpty(keyResp) || MapUtils.isEmpty(configInfoMap)) {
            return Collections.emptyList();
        }
        return matcherBaseMethod.noCodeSort(keyResp, configInfoMap, userCouponMap);
    }

    //请求商品转换
    private List<MatcherGoodsItemDo> convertReqDtoToMatcherGoodsItemDo(List<GoodsItemDto> goodsList) {
        List<MatcherGoodsItemDo> result = new ArrayList<>();
        for (GoodsItemDto e : goodsList) {
            if (Objects.isNull(e) ||
                    Objects.isNull(e.getId()) ||
                    e.getId() <= 0 ||
                    (!GoodsLevelEnum.Sku.getValue().equals(e.getLevel()) && !GoodsLevelEnum.Package.getValue().equals(e.getLevel()) && !GoodsLevelEnum.Ssu.getValue().equals(e.getLevel()) )
            ) {
                continue;
            }
            MatcherGoodsItemDo item = new MatcherGoodsItemDo();
            item.setId(e.getId());
            item.setLevel(e.getLevel());
            item.setSalePrice(e.getSalePrice());
            item.setMarketPrice(e.getMarketPrice());
            item.setVirtual(e.getVirtual());
            item.setSaleMode(e.getSaleMode());
            item.setBusinessType(e.getBusinessType());
            item.setFinalStartTime(e.getFinalStartTime());
            item.setFinalEndTime(e.getFinalEndTime());
            result.add(item);
        }
        return result;
    }

}
