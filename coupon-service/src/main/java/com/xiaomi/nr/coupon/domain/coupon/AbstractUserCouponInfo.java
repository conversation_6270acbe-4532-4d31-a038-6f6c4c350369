package com.xiaomi.nr.coupon.domain.coupon;

import com.xiaomi.nr.coupon.api.dto.coupon.info.CouponInfoItemDto;
import com.xiaomi.nr.coupon.api.dto.coupon.info.CouponInfoRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.info.CouponInfoResponse;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponConvert;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponFormatCommon;
import com.xiaomi.nr.coupon.domain.coupon.model.CouponInfoModel;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 优惠券信息抽象类
 *
 * <AUTHOR>
 * @date 2024/5/21
 */

public abstract class AbstractUserCouponInfo {

    @Resource
    private CouponFormatCommon couponFormatCommon;

    @Resource
    private CouponConvert couponConvert;

    /**
     * 获取优惠券信息
     *
     * @param req   入参
     * @return  优惠券信息
     */
    public CouponInfoResponse couponInfo(CouponInfoRequest req) throws BizError {

        CouponInfoResponse result = new CouponInfoResponse();
        result.setData(Collections.emptyMap());
        // 1、查询DB
        List<CouponPo> couponPoList = getCouponInfo(req);
        if (CollectionUtils.isEmpty(couponPoList)) {
            return result;
        }

        // 2、修正状态
        adjustStatus(couponPoList);

        // 3、格式化优惠券信息(po -> model)
        List<Integer> bizPlatform = Lists.newArrayList(req.getBizPlatform());
        List<CouponInfoModel> modelList = couponFormatCommon.formatCouponList(bizPlatform, couponPoList);

        // 4、model -> dto
        Map<Long, CouponInfoItemDto> data = new HashMap<>();
        for (CouponInfoModel item : modelList) {
            CouponInfoItemDto info = couponConvert.convert2CouponInfoDto(item, CouponInfoItemDto::new);
            if (info == null) {
                continue;
            }
            data.put(item.getId(), info);
        }

        result.setData(data);
        return result;
    }

    /**
     * 获取优惠券信息
     *
     * @param req   入参
     * @return  优惠券信息
     */
    public abstract List<CouponPo> getCouponInfo(CouponInfoRequest req);

    private void adjustStatus(List<CouponPo> couponPoList) {
        //修正状态：lock的也算是已使用状态
        couponPoList.forEach(item -> {
            String status = item.getStat();
            if (CouponStatusEnum.LOCKED.getValue().equals(status)) {
                status = CouponStatusEnum.USED.getValue();
            }
            item.setStat(status);
        });
    }



}
