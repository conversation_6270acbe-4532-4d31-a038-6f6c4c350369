package com.xiaomi.nr.coupon.domain.couponconfig.convert;

import com.xiaomi.nr.coupon.api.dto.couponconfig.MissionDto;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.enums.couponconfig.ConfigStatusEnum;
import com.xiaomi.nr.coupon.enums.couponmission.MissionTypeEnum;
import com.xiaomi.nr.coupon.enums.couponmission.TimeTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.po.MissionCacheItemPo;
import com.xiaomi.nr.coupon.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class MissionConvert {




    /**
     * 生成单个发放任务信息缓存 po -> missionCache
     * @return MissionCacheItemPo 券发放任务缓存信息(redis)
     */
    public MissionCacheItemPo makeMissionCache(CouponConfigItem couponConfigItem) {

        if (couponConfigItem == null) {
            return new MissionCacheItemPo();
        }

        Integer days = couponConfigItem.getCouponConfigInfo().getUseDuration();
        String timeType = days == 0 ? TimeTypeEnum.SECTION.getRedisValue() : TimeTypeEnum.DAYS.getRedisValue();
        Long id = Long.parseLong(String.valueOf(couponConfigItem.getConfigId()));
        String name = "";
        String stat = "";
        if(ConfigStatusEnum.Stop.getValue() == couponConfigItem.getCouponConfigInfo().getStatus() ||
                ConfigStatusEnum.Offline.getValue() == couponConfigItem.getCouponConfigInfo().getStatus()){
            stat = "cancel";
        }else{
            stat = "approved";
        }
        String missionType = MissionTypeEnum.findRedisValueBymysqlValue(1);
        Long couponStartTime =couponConfigItem.getCouponConfigInfo().getStartUseTime();
        Long couponEndTime = couponConfigItem.getCouponConfigInfo().getEndUseTime();
        Long sendStartTime = couponConfigItem.getCouponConfigInfo().getStartFetchTime();
        Long sendEndTime = couponConfigItem.getCouponConfigInfo().getEndFetchTime();
        Long maxSendNum =  couponConfigItem.getCouponConfigInfo().getApplyCount().longValue();
        Long groupSendNum =  couponConfigItem.getCouponConfigInfo().getApplyCount().longValue();




        Long couponConfigId = id;
        Long adminId = 0L;


        MissionCacheItemPo missionCache = new MissionCacheItemPo();
        missionCache.setId(id);
        missionCache.setName(name);
        missionCache.setStatus(stat);
        missionCache.setMissionType(missionType);
        missionCache.setTimeType(timeType);
        missionCache.setCouponStartTime(couponStartTime);
        missionCache.setCouponEndTime(couponEndTime);

        missionCache.setDays(days / 24);
        missionCache.setHours(days % 24);

        missionCache.setSendStartTime(sendStartTime);
        missionCache.setSendEndTime(sendEndTime);
        missionCache.setMaxSendNum(maxSendNum);
        missionCache.setGroupSendNum(groupSendNum);
        missionCache.setCouponConfigId(couponConfigId);
        missionCache.setAdminId(adminId);
        return missionCache;
    }




    /**
     * missionCache -> missionDto
     *
     * @param missionCache 券发放任务缓存信息
     * @return MissionDto  券发放任务Dto信息
     */
    protected MissionDto convertMissionDto(MissionCacheItemPo missionCache) {
        if (Objects.isNull(missionCache)) {
            return null;
        }

        Long missionId = missionCache.getId();
        String missionName = missionCache.getName();
        String missionType = missionCache.getMissionType();
        Long sendNumLimit = missionCache.getMaxSendNum();
        String timeType = missionCache.getTimeType();
        String timeTypeDesc = formatTimeTypeDesc(timeType);
        Long couponStartTime = missionCache.getCouponStartTime();
        Integer couponDays = missionCache.getDays();
        Long addTime = missionCache.getAddTime();
        Long couponEndTime = missionCache.getCouponEndTime();

        MissionDto missionDto = new MissionDto();
        missionDto.setMissionId(missionId);
        missionDto.setMissionName(missionName);
        missionDto.setMissionType(missionType);
        missionDto.setSendNumLimit(sendNumLimit);
        missionDto.setTimeType(timeType);
        missionDto.setTimeTypeDesc(timeTypeDesc);
        missionDto.setCouponStartTime(couponStartTime);
        missionDto.setCouponEndTime(couponEndTime);
        missionDto.setCouponDays(couponDays);
        missionDto.setAddTime(addTime);

        return missionDto;
    }

    /**
     * 券的真实有效期类别描述
     *
     * @param timeType 券有效期类型
     * @return String  券有效期类型描述
     */
    protected String formatTimeTypeDesc(String timeType) {
        if (StringUtils.equals(TimeTypeEnum.SECTION.getRedisValue(), timeType)) {
            return TimeTypeEnum.SECTION.getName();
        }

        if (StringUtils.equals(TimeTypeEnum.DAYS.getRedisValue(), timeType)) {
            return TimeTypeEnum.DAYS.getName();
        }
        return "";
    }





    /**
     * 生成券任务缓存信息列表
     * @return List<>         券发放任务缓存信息列表(缓存)
     */
   /* private List<MissionCacheItemPo> makeMissionCacheListV2(List<MissionPo> missionPoList, List<CouponConfigPO> couponConfigPOS) {

        Map<Long, List<MissionPo>> missionMap = getMissionMap(missionPoList);
        List<MissionCacheItemPo> missionCacheList = new ArrayList<>();

        for (CouponConfigPO couponConfigPO : couponConfigPOS) {
            long couponTypeId = couponConfigPO.getId();
            if (!missionMap.containsKey(couponTypeId)) {
                continue;
            }
            long globalCouponEndTime = Math.max(couponConfigPO.getEndFetchTime() + couponConfigPO.getUseDuration() * 3600, couponConfigPO.getEndUseTime());
            List<MissionPo> missionList = missionMap.get(couponTypeId);
            for (MissionPo missionPo : missionList) {
                long missionEndTime = missionPo.getCouponEndTime();
                if (missionPo.getCouponDays() > 0) {
                    missionEndTime = TimeUtil.getNowUnixSecond() + missionPo.getCouponDays() * 3600;
                }

                if (missionEndTime > globalCouponEndTime) {
                    log.info("makeMissionCacheList, 发放任务的有效期不在券配置最大有效期内, couponConfigId={}, missionId={}",couponTypeId,missionPo.getId());
                    continue;
                }

                //初始化券发放任务缓存信息
                missionCacheList.add(makeMissionCache(missionPo, MissionVersionEnum.NEW_MISSION.getCode()));
            }
        }

        return missionCacheList;
    }
*/


    public Map<Long, MissionDto> convertMissionDto(List<CouponConfigItem> configItemList){

        Map<Long, MissionDto> missionDtoMap = new HashMap<>();
        for(CouponConfigItem couponItem : configItemList){

            long globalCouponEndTime = Math.max(couponItem.getCouponConfigInfo().getEndFetchTime() + couponItem.getCouponConfigInfo().getUseDuration() * 3600, couponItem.getCouponConfigInfo().getEndUseTime());
            long missionEndTime = couponItem.getCouponConfigInfo().getEndUseTime();
            if (couponItem.getCouponConfigInfo().getUseDuration() > 0) {
                missionEndTime = TimeUtil.getNowUnixSecond() + couponItem.getCouponConfigInfo().getUseDuration() * 3600;
            }

            if (missionEndTime > globalCouponEndTime) {
                log.info("makeMissionCacheList, 发放任务的有效期不在券配置最大有效期内, couponConfigId={}, missionId={}", couponItem.getConfigId(),couponItem.getConfigId());
                continue;
            }

            MissionCacheItemPo missionCacheItemPo = makeMissionCache(couponItem);
            missionDtoMap.put(missionCacheItemPo.getId(), convertMissionDto(missionCacheItemPo));
        }

        return missionDtoMap;
    }

}
