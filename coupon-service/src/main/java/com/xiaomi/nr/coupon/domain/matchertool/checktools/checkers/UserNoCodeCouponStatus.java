package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.couponcode.StatusEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 校验用户券状态
 *
 * <AUTHOR>
 * @date 2023/7/24
 */
@Slf4j
@Component
public class UserNoCodeCouponStatus extends CheckerAbstract {

    @Override
    public boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {
        if(!ctx.isCheckUserCouponStatus()) {
            return true;
        }

        if (Objects.isNull(respItem.getUserCouponId())) {
            errCtx.setErrCode(ErrCode.USE_COUPON_NOT_FOUND.getCode());
            errCtx.setErrMsg("无效的券");
            return false;
        }

        CouponPo coupon = ctx.getUserCouponMap().get(respItem.getUserCouponId());
        if (Objects.isNull(coupon)) {
            errCtx.setErrCode(ErrCode.USE_COUPON_ORGCODE_UNUSED.getCode());
            errCtx.setErrMsg("优惠券不可用");
            return false;
        }

        long endTime;
        try {
            endTime = Long.parseLong(coupon.getEndTime());
        } catch (Exception e) {
            log.info("NoCodeCouponStat.check，券可用时间转换出错，coupon={}, err:{}", GsonUtil.toJson(coupon), e.getMessage());
            errCtx.setErrCode(ErrCode.USE_COUPON_TIME_INVALID.getCode());
            errCtx.setErrMsg("无效的优惠券");
            return false;
        }

        if (StatusEnum.USED.getValue().equals(coupon.getStat())) {
            errCtx.setErrCode(ErrCode.USE_COUPON_STAT_ERROR.getCode());
            errCtx.setErrMsg("优惠券已使用");
            return false;
        }

        if (CouponStatusEnum.LOCKED.getValue().equals(coupon.getStat())) {
            errCtx.setErrCode(ErrCode.USE_COUPON_STAT_ERROR.getCode());
            errCtx.setErrMsg("优惠券已使用");
            return false;
        }

        if (StatusEnum.EXPIRED.getValue().equals(coupon.getStat())) {
            errCtx.setErrCode(ErrCode.USE_COUPON_STAT_ERROR.getCode());
            errCtx.setErrMsg("优惠券已过期");
            return false;
        }

        if (CouponStatusEnum.EXPIRED.getValue().equals(coupon.getStat()) || (CouponStatusEnum.UNUSED.getValue().equals(coupon.getStat()) && endTime < ctx.getNowUnixSecond())) {
            errCtx.setErrCode(ErrCode.USE_COUPON_STAT_ERROR.getCode());
            errCtx.setErrMsg("优惠券已过期");
            return false;
        }

        if (!StatusEnum.UNUSED.getValue().equals(coupon.getStat())) {
            errCtx.setErrCode(ErrCode.USE_COUPON_STAT_ERROR.getCode());
            errCtx.setErrMsg("优惠券为不可用状态");
            return false;
        }

        return true;
    }

}
