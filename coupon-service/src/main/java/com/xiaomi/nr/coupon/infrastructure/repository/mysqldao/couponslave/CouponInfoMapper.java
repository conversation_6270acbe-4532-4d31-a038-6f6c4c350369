package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface CouponInfoMapper {

    String SELECT_PARAMS = " id, user_id, biz_platform, type_id, activity_id, start_time, end_time,days, stat, order_id, use_time, " +
            "expire_time, is_pass, admin_id, admin_name,add_time, send_type, from_order_id, replace_money, invalid_time," +
            "last_update_time, offline, reduce_express, parent_id, send_channel,extend_info ";

    /**
     * 获取优惠券信息
     *
     * @param userId Long
     * @param couponIds List<Long>
     * @return List<CouponPo>
     */
    @Select("<script>" +
            "select " + SELECT_PARAMS +
            " from tb_coupon " +
            " where user_id=#{userId} and id in <foreach collection='couponIds' item='id' index='index' open='(' close=')' separator=','>#{id}</foreach>" +
            "</script>")
    List<CouponPo> batchGetCouponInfo(@Param("userId") Long userId, @Param("couponIds") List<Long> couponIds);

    /**
     * 获取优惠券信息
     *
     * @param userId Long
     * @return List<CouponPo>
     */
    @Select("select " + SELECT_PARAMS +
            " from tb_coupon " +
            " where user_id=#{userId} and id =#{couponId}")
    CouponPo getCouponInfo(@Param("userId") Long userId, @Param("couponId") Long couponId);


    /**
     * 获取优惠券信息
     *
     * @param couponIds List<Long>
     * @return List<CouponPo>
     */
    @Select("<script>" +
            "select " + SELECT_PARAMS +
            " from tb_coupon" +
            " where id in <foreach collection='couponIds' item='id' index='index' open='(' close=')' separator=','>#{id}</foreach>" +
            "</script>")
    List<CouponPo> getCouponInfoCouponIds(@Param("couponIds") List<Long> couponIds);

    @Select("<script>" +
            "select " + SELECT_PARAMS +
            " from tb_coupon " +
            " where user_id=#{userId} and stat='unused' and end_time &gt; #{nowTime} " +
            " and biz_platform in <foreach collection='bizPlatformList' item='bizPlatform' index='index' open='(' close=')' separator=','>#{bizPlatform}</foreach> " +
            " order by add_time desc" +
            "</script>")
    List<CouponPo> getAllCouponForUnused(@Param("userId") Long userId, @Param("nowTime") long nowTime, @Param("bizPlatformList") List<Integer> bizPlatformList);

}
