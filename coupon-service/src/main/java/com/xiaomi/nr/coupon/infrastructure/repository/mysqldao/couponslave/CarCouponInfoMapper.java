package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 优惠券信息抽象类
 *
 * <AUTHOR>
 * @date 2024/5/21
 */
@Mapper
public interface CarCouponInfoMapper {

    String SELECT_PARAMS = " id, vid, biz_platform, type_id, activity_id, start_time, end_time,days, stat, order_id, use_time, " +
            "expire_time, is_pass, admin_id, admin_name,add_time, send_type, from_order_id, replace_money, invalid_time," +
            "last_update_time, offline, reduce_express, parent_id, send_channel,extend_info, times_limit, used_times, service_type ";

    /**
     * 获取优惠券信息
     *
     * @param vid vid
     * @param couponIds 优惠券id列表
     * @return List<CouponPo>   优惠券列表
     */
    @Select("<script>" +
            "select " + SELECT_PARAMS +
            "from tb_car_coupon " +
            "where vid=#{vid} " +
            "and id in " +
            "   <foreach collection='couponIds' item='id' index='index' open='(' close=')' separator=','>" +
            "       #{id}" +
            "   </foreach>" +
            "</script>")
    List<CouponPo> batchGetCouponInfo(@Param("vid") String vid, @Param("couponIds") List<Long> couponIds);

}
