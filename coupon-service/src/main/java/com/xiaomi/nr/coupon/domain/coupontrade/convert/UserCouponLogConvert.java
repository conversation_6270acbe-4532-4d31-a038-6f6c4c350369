package com.xiaomi.nr.coupon.domain.coupontrade.convert;

import com.xiaomi.nr.coupon.constant.CouponConstant;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.coupon.OrderStatusEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CodeCouponLogPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponLogPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.util.TestHelper;
import com.xiaomi.nr.coupon.util.TimeUtil;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description:
 * @author: hejiap<PERSON>
 * @Date 2023/1/16 10:26 上午
 * @Version: 1.0
 **/
@Component
public class UserCouponLogConvert {

    /**
     * 初始化CodeCouponLogPo
     * @param userId
     * @param orderId
     * @param couponIndex
     * @param offline
     * @param replaceMoney
     * @param reduceExpress
     * @return
     */
    private CodeCouponLogPo initCodeCouponLogPo(Long userId, Long orderId, String couponIndex, Integer offline, BigDecimal replaceMoney, BigDecimal reduceExpress) {
        CodeCouponLogPo log = new CodeCouponLogPo();
        log.setCouponIndex(couponIndex);
        log.setReduceExpress(reduceExpress);
        log.setReplaceMoney(replaceMoney);
        log.setOffline(offline);
        log.setOrderId(orderId);
        log.setUserId(userId);
        log.setAddTime(TimeUtil.getNowUnixSecond());
        log.setAdminId(0L);
        return log;
    }

    /**
     * 初始化明码券日志
     *
     * @param userId
     * @param orderId
     * @param couponIndex
     * @param offline
     * @param replaceMoney
     * @param reduceExpress
     * @return
     */
    public CodeCouponLogPo initLockCodeCouponLog(Long userId, Long orderId, String couponIndex, Integer offline, BigDecimal replaceMoney, BigDecimal reduceExpress) {

        if (TestHelper.isLoadRunnerUser(userId)) {
            couponIndex = String.valueOf(System.nanoTime());
        }

        CodeCouponLogPo log = initCodeCouponLogPo(userId, orderId, couponIndex, offline, replaceMoney, reduceExpress);
        log.setUseDesc(CouponConstant.COUPON_LOG_LOCK_DESC);
        log.setAdminDesc(StringUtils.EMPTY);
        log.setLogType(OrderStatusEnum.LOCK.getType());
        return log;
    }

    /**
     * 初始化明码券日志
     *
     * @param userId
     * @param orderId
     * @param couponIndex
     * @param offline
     * @param replaceMoney
     * @param reduceExpress
     * @return
     */
    public CodeCouponLogPo initConsumerCodeCouponLog(Long userId, Long orderId, String couponIndex, Integer offline, BigDecimal replaceMoney, BigDecimal reduceExpress) {

        CodeCouponLogPo log = initCodeCouponLogPo(userId, orderId, couponIndex, offline, replaceMoney, reduceExpress);
        log.setUseDesc(CouponConstant.COUPON_LOG_USED_DESC);
        log.setAdminDesc(StringUtils.EMPTY);
        log.setLogType(OrderStatusEnum.SUBMIT.getType());
        return log;
    }

    /**
     * 初始化明码券日志
     *
     * @param userId
     * @param orderId
     * @param couponIndex
     * @param offline
     * @param replaceMoney
     * @param reduceExpress
     * @return
     */
    public CodeCouponLogPo initRollbackCodeCouponLog(Long userId, Long orderId, String couponIndex, Integer offline, BigDecimal replaceMoney, BigDecimal reduceExpress) {

        CodeCouponLogPo log = initCodeCouponLogPo(userId, orderId, couponIndex, offline, replaceMoney, reduceExpress);
        log.setUseDesc(CouponConstant.COUPON_LOG_REFUND_DESC);
        log.setAdminDesc(StringUtils.EMPTY);
        log.setLogType(OrderStatusEnum.ROLLBACK.getType());
        return log;
    }

    /**
     * 构建用券日志
     * @param userId
     * @param couponPos
     * @param offline
     * @return
     */
    public List<CouponLogPo> buildConsumeCouponLogPos(Long userId, List<CouponPo> couponPos, Integer offline, Long clientId) {
        List<CouponLogPo> couponLogPos = Lists.newArrayList();
        for (CouponPo couponPo : couponPos) {
            CouponLogPo couponLogPo = new CouponLogPo();
            couponLogPo.setCouponId(couponPo.getId());
            couponLogPo.setUserId(userId);
            couponLogPo.setType(String.valueOf(couponPo.getTypeId()));
            couponLogPo.setOldStat(CouponStatusEnum.UNUSED.getValue());
            couponLogPo.setNewStat(CouponStatusEnum.USED.getValue());
            couponLogPo.setCouponDesc(CouponConstant.COUPON_LOG_USED_DESC);
            couponLogPo.setAdminId(CouponConstant.DB_ADMIN_ID);
            couponLogPo.setAdminName(String.valueOf(clientId));
            couponLogPo.setAddTime(TimeUtil.getNowUnixSecond());
            couponLogPo.setOffline(offline);
            couponLogPos.add(couponLogPo);
        }
        return couponLogPos;
    }

    /**
     * 构建退券日志
     * @param userId
     * @param couponPos
     * @param offline
     * @return
     */
    public List<CouponLogPo> buildRollbackCouponLogPos(Long userId, List<CouponPo> couponPos, Integer offline, Long clientId) {
        List<CouponLogPo> couponLogPos = Lists.newArrayList();
        for (CouponPo couponPo : couponPos) {
            CouponLogPo couponLogPo = new CouponLogPo();
            couponLogPo.setCouponId(couponPo.getId());
            couponLogPo.setUserId(userId);
            couponLogPo.setType(String.valueOf(couponPo.getTypeId()));
            couponLogPo.setOldStat(couponPo.getStat());
            couponLogPo.setNewStat(CouponStatusEnum.UNUSED.getValue());
            couponLogPo.setCouponDesc(CouponConstant.COUPON_LOG_REFUND_DESC);
            couponLogPo.setAdminId(CouponConstant.DB_ADMIN_ID);
            couponLogPo.setAdminName(String.valueOf(clientId));
            couponLogPo.setAddTime(TimeUtil.getNowUnixSecond() + 1L);
            couponLogPo.setOffline(offline);
            couponLogPos.add(couponLogPo);
        }
        return couponLogPos;
    }
}
