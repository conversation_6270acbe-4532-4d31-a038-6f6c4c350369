package com.xiaomi.nr.coupon.domain.coupontrade;

import com.xiaomi.nr.coupon.api.dto.trade.*;
import com.xiaomi.nr.coupon.domain.common.UserCouponPush;
import com.xiaomi.nr.coupon.domain.common.model.CouponUsePushContext;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponConvert;
import com.xiaomi.nr.coupon.domain.coupon.model.CheckoutCouponModel;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.ServiceSceneItem;
import com.xiaomi.nr.coupon.domain.coupontrade.entity.TradeCheckContext;
import com.xiaomi.nr.coupon.enums.coupon.CouponPushStatusEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.ModeTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class UserNoCodeCouponService extends UserCouponService {


    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Resource
    private CouponConvert couponConvert;

    @Resource
    private TradeCheckFactory tradeCheckFactory;

    @Resource
    private UserCouponPush userCouponPush;

    /**
     * 优惠券结算
     *
     * @param request 请求
     * @return CheckoutCouponResponse
     */
    @Override
    public CheckoutCouponResModel checkoutCoupon(CheckoutCouponReqModel request) throws BizError {

        List<Long> couponIds = Objects.isNull(request.getCouponIds()) ? Collections.emptyList() : request.getCouponIds();

        // 业务领域
        TradeCheckAbstract provider = tradeCheckFactory.getProvider(request.getBizPlatform());

        List<CouponPo> coupons = provider.getCouponPo(request, couponIds);
        List<Long> configIds = coupons.stream().map(CouponPo::getTypeId).distinct().collect(Collectors.toList());
        Map<Long, CouponConfigItem> configMap = couponConfigRepository.getCouponConfigs(configIds);
        CheckoutCouponModel requestModel = couponConvert.convertCheckoutRequestToModel(request);

        // 每张券的处理
        Map<Long, CheckoutCouponInfo> resData = new HashMap<>(coupons.size());
        Map<String, CouponGroupInfo> couponGroupInfoMap = new HashMap<>();
        for (CouponPo couponPo : coupons) {
            try {
                CouponConfigItem config = configMap.get(couponPo.getTypeId());

                // 初始化数据
                TradeCheckContext ctx = new TradeCheckContext();
                ctx.setCouponModeType(ModeTypeEnum.NoCode.getMysqlValue());
                ctx.setRequestModel(requestModel);
                ctx.setConfig(config);
                ctx.setCouponPo(couponPo);
                ctx.setRequestVid(request.getVid());

                // 逻辑执行
                provider.exec(ctx);

                // 校验结果
                if (ctx.isCheckPass()) {
                    ctx.setFailResult(GeneralCodes.OK.getCode(), null, null);
                } else {
                    ctx.setFailResult(ctx.getErrContext().getErrorCode(), ctx.getErrContext().getErrorMsg(), ctx.getErrContext().getErrorData());
                }

                // 券结算信息
                resData.put(couponPo.getId(), ctx.getCheckoutCouponInfo());

                // 叠加分组信息（不管券能否使用，都返回，促销也会兼容不存在的情况）
                CouponGroupInfo groupInfo = getCouponGroupInfo(resData.get(couponPo.getId()));
                if (Objects.nonNull(groupInfo)) {
                    couponGroupInfoMap.put(groupInfo.getCouponGroupNo(), groupInfo);
                }
            } catch (Exception e) {
                log.error("UserNoCodeCouponService.checkoutCoupon, make checkout coupon info err. couponId:{}", couponPo.getId(), e);
            }
        }

        for (Long couponId : couponIds) {
            if (MapUtils.isEmpty(resData) || !resData.containsKey(couponId)) {
                // 判断优惠券是否存在
                CheckoutCouponInfo checkoutCouponInfo = new CheckoutCouponInfo();
                checkoutCouponInfo.setValidCode(ErrCode.USE_COUPON_NOT_FOUND.getCode());
                checkoutCouponInfo.setInvalidReason("优惠券不存在");
                resData.put(couponId, checkoutCouponInfo);
            }
        }

        CheckoutCouponResModel result = new CheckoutCouponResModel();
        result.setNoCodeCoupons(resData);
        result.setCouponGroupInfoMap(couponGroupInfoMap);
        return result;
    }

    /**
     * 优惠券锁定
     */
    @Override
    public LockCouponResponse lockUserCoupon(LockCouponRequest request) throws BizError {
        log.info("userNoCodeCouponService.lockUserCoupon request:{}", request);
        LockCouponResponse response = new LockCouponResponse();

        final long userId = request.getUserId();
        final long orderId = request.getOrderId();
        List<CouponLockItem> couponItems = request.getCouponItems();
        List<Long> couponIds = couponItems.stream().map(CouponLockItem::getCouponId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(couponIds)) {
            log.error("userNoCodeCouponService.lockUserCoupon userId:{},couponId:{},orderId:{},err:{}", userId, couponIds, orderId, "未查到有效的优惠券");
            throw ExceptionHelper.create(GeneralCodes.ParamError, "未查到有效的优惠券");
        }

        // 业务领域
        TradeCheckAbstract provider = tradeCheckFactory.getProvider(request.getBizPlatform());

        List<CouponPo> couponPoList = provider.getCouponPoList(userId, request.getVid(), couponIds);

        // 券锁定校验 + 幂等性校验
        boolean isIdempotent = provider.couponListLockCheck(couponPoList, orderId, request.getVid());
        if (isIdempotent) {
            response.setIdempotent(true);
            return response;
        }

        try {
            // 锁券
            provider.lockCoupon(request, couponPoList);
        } catch (BizError bizError) {
            log.error("userNoCodeCouponService.lockUserCoupon bizError. request:{}", request, bizError);
            throw bizError;
        } catch (Exception e) {
            log.error("userNoCodeCouponService.lockUserCoupon Exception. request:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "锁定优惠券失败", e);
        }
        return response;
    }

    /**
     * 优惠券使用
     */
    @Override
    public ConsumeCouponResponse consumeUserCoupon(ConsumeCouponRequest request) throws BizError {
        log.info("userNoCodeCouponService.consumeUserCoupon request:{}", request);
        ConsumeCouponResponse response = new ConsumeCouponResponse();

        final long userId = request.getUserId();
        List<Long> couponIds = request.getCouponIds();
        final long orderId = request.getOrderId();
        if (CollectionUtils.isEmpty(couponIds)) {
            log.error("userNoCodeCouponService.consumeUserCoupon userId:{},couponId:{},orderId:{},err:{}", userId, couponIds, orderId, "未查到有效的优惠券");
            throw ExceptionHelper.create(GeneralCodes.ParamError, "未查到有效的优惠券");
        }

        // 业务领域
        TradeCheckAbstract provider = tradeCheckFactory.getProvider(request.getBizPlatform());

        List<CouponPo> couponPoList = provider.getCouponPoList(userId, request.getVid(), couponIds);

        // 券核销校验 + 幂等性校验
        boolean isIdempotent = provider.couponListConsumeCheck(couponPoList, orderId, request.getVid());
        if (isIdempotent) {
            response.setIdempotent(true);
            return response;
        }

        try {
            provider.consumeCoupon(request, couponPoList);

            CouponUsePushContext couponUsePushContext = new CouponUsePushContext(
                    request.getUserId(),
                    request.getVid(),
                    couponPoList,
                    CouponPushStatusEnum.CONSUME.getCode(),
                    TimeUtil.getNowUnixMillis(),
                    request.getOrderId()
            );

            // 发使用消息
            userCouponPush.couponUsePush(couponUsePushContext);

        } catch (BizError bizError) {
            log.error("userNoCodeCouponService.consumeUserCoupon bizError request:{}", request, bizError);
            throw bizError;
        } catch (Exception e) {
            log.error("userNoCodeCouponService.consumeUserCoupon Exception request:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "使用优惠券失败", e);
        }
        return response;
    }

    /**
     * 优惠券锁券
     */
    @Override
    public RollbackCouponResponse rollbackUserCoupon(RollbackCouponRequest request) throws BizError {
        log.info("userNoCodeCouponService.rollbackUserCoupon request:{}", request);
        RollbackCouponResponse response = new RollbackCouponResponse();

        final long userId = request.getUserId();
        final List<Long> couponIds = request.getCouponIds();
        final long orderId = request.getOrderId();
        if (CollectionUtils.isEmpty(couponIds)) {
            log.error("userNoCodeCouponService.rollbackUserCoupon userId:{},couponId:{},orderId:{},err:{}", userId, couponIds, orderId, "未查到有效的优惠券");
            throw ExceptionHelper.create(GeneralCodes.ParamError, "未查到有效的优惠券");
        }

        // 业务领域
        TradeCheckAbstract provider = tradeCheckFactory.getProvider(request.getBizPlatform());

        List<CouponPo> couponPoList = provider.getCouponPoList(request.getUserId(), request.getVid(), couponIds);

        // 券退还校验 + 幂等性校验
        boolean isIdempotent = provider.couponListReturnCheck(couponPoList, orderId, request.getVid());
        if (isIdempotent) {
            response.setIdempotent(true);
            return response;
        }

        try {
            provider.returnCoupon(request, couponPoList);

            CouponUsePushContext couponUsePushContext = new CouponUsePushContext(
                    request.getUserId(),
                    request.getVid(),
                    couponPoList,
                    CouponPushStatusEnum.RETURN.getCode(),
                    TimeUtil.getNowUnixMillis(),
                    request.getOrderId()
            );

            //发消息
            userCouponPush.couponUsePush(couponUsePushContext);

        } catch (BizError bizError) {
            log.error("userNoCodeCouponService.rollbackUserCoupon bizError request:{}", request, bizError);
            throw bizError;
        } catch (Exception e) {
            log.error("userNoCodeCouponService.rollbackUserCoupon Exception request:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "退还优惠券失败", e);
        }
        return response;
    }

    /**
     * 用券叠加分组信息
     *
     * @param checkoutInfo CheckoutCouponInfo
     * @return 单个叠加分组信息map
     */
    private CouponGroupInfo getCouponGroupInfo(CheckoutCouponInfo checkoutInfo) {
        if (Objects.isNull(checkoutInfo) || Objects.isNull(checkoutInfo.getCouponBaseInfo())) {
            log.error("getCouponGroupInfo checkoutInfo or couponBaseInfo is null, checkoutInfo:{}", GsonUtil.toJson(checkoutInfo));
            return null;
        }

        String couponGroupNo = checkoutInfo.getCouponGroupNo();
        Integer serviceScene = checkoutInfo.getCouponBaseInfo().getServiceScene();
        if (Strings.isEmpty(couponGroupNo) || Objects.isNull(serviceScene)) {
            log.error("getCouponGroupInfo groupNo or serviceScene is null, groupNo:{}, serviceScene:{}", couponGroupNo, serviceScene);
            return null;
        }

        ServiceSceneItem serviceSceneConfig = couponConfigRepository.getCouponServiceScene(serviceScene);
        if (Objects.isNull(serviceSceneConfig)) {
            log.error("getCouponGroupInfo serviceScene info not exist, groupNo:{}, serviceScene:{}", couponGroupNo, serviceScene);
            return null;
        }

        CouponGroupInfo info = new CouponGroupInfo();
        info.setCouponGroupNo(couponGroupNo);
        info.setDeductRule(serviceSceneConfig.getDeductRule());
        info.setMutualRule(serviceSceneConfig.getMutualRule());

        return info;
    }

}
