package com.xiaomi.nr.coupon.enums.coupon;

import com.xiaomi.nr.coupon.enums.couponconfig.FetchLimitTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2024/5/11 17:23
 */
@Getter
@AllArgsConstructor
public enum CarCouponLogTypeEnum {
    /**
     * 1: 核销
     */
    CONSUME(1, "核销"),

    /**
     * 2: 退还
     *
     */
    RETURN(2, "退还"),
    ;

    /**
     * 枚举code
     */
    private final Integer code;

    /**
     * 枚举描述
     */
    private final String desc;

    private static final HashMap<Integer, CarCouponLogTypeEnum> MAPPING = new HashMap<>();

    static {
        for (CarCouponLogTypeEnum e : CarCouponLogTypeEnum.values()) {
            MAPPING.put(e.getCode(), e);
        }
    }

    public static CarCouponLogTypeEnum valueOf(Integer code) {
        return MAPPING.get(code);
    }
}
