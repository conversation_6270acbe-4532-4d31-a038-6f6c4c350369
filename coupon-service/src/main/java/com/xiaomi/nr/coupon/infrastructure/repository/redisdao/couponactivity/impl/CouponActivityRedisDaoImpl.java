package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponactivity.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponactivity.CouponActivityRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponactivity.po.ActivityCacheInfo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;


/**
 * 领券活动配置redis缓存操作对象
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CouponActivityRedisDaoImpl implements CouponActivityRedisDao {


    /**
     * pid -> configIds
     */
    private Cache<String, ActivityCacheInfo> cache = Caffeine.newBuilder()
            .initialCapacity(1)
            .maximumSize(10)
            .expireAfterWrite(30, TimeUnit.SECONDS)
            .recordStats()
            .build();

    /**
     * 缓存操作对象
     */
    @Autowired
    @Qualifier("stringMiscRedisTemplate")
    private StringRedisTemplate redisTemplate;

    private static final String KEY_COUPON_ACTIVITY_CONFIG_CACHE = "pulse_getcoupon_event_new_v2";


    @Override
    public void set(String data) {
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        operations.set(KEY_COUPON_ACTIVITY_CONFIG_CACHE, data);
    }

    @Override
    public ActivityCacheInfo get() {
        ActivityCacheInfo activityCacheInfo = getActivityCacheInfo();
        if (activityCacheInfo != null) {
            return activityCacheInfo;
        }
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        String dataStr = operations.get(KEY_COUPON_ACTIVITY_CONFIG_CACHE);
        activityCacheInfo = StringUtils.isEmpty(dataStr) ? new ActivityCacheInfo() : GsonUtil.fromJson(dataStr, ActivityCacheInfo.class);
        setActivityCacheInfoCache(activityCacheInfo);
        return activityCacheInfo;
    }


    /**
     * 获取缓存
     * @return
     */
    private ActivityCacheInfo getActivityCacheInfo() {
        return cache.getIfPresent(KEY_COUPON_ACTIVITY_CONFIG_CACHE);
    }

    /**
     * 设置缓存
     */
    protected void setActivityCacheInfoCache(ActivityCacheInfo activityCacheInfo) {
        cache.put(KEY_COUPON_ACTIVITY_CONFIG_CACHE, activityCacheInfo);
    }

}
