package com.xiaomi.nr.coupon.domain.matchertool.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 校验器工厂
 *
 * <AUTHOR>
 * @date 2023/7/24
 */
@Component
public class CheckerFactory {

    private final Map<String, CheckerAbstract> toolMap;

    @Autowired
    public CheckerFactory(List<CheckerAbstract> toolList) {
        toolMap = new HashMap<>(toolList.size());
        toolList.forEach(e -> toolMap.put(e.getClass().getName(), e));
    }

    public CheckerAbstract getProvider(String className) {
        return toolMap.get(className);
    }
}
