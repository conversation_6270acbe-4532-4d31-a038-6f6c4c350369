package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Strings;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 校验可用时间
 *
 * <AUTHOR>
 * @date 2023/7/24
 */
@Slf4j
@Component
public class UserNoCodeUseTime extends CheckerAbstract {

    @Override
    public boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {
        if (!ctx.isCheckUserCouponTime()) {
            return true;
        }

        if (Objects.isNull(respItem.getUserCouponId())) {
            errCtx.setErrCode(ErrCode.USE_COUPON_NOT_FOUND.getCode());
            errCtx.setErrMsg("无效的券");
            return false;
        }

        CouponPo coupon = ctx.getUserCouponMap().get(respItem.getUserCouponId());
        long nowTime = ctx.getNowUnixSecond();

        if (coupon == null || Strings.isNullOrEmpty(coupon.getStartTime()) || Strings.isNullOrEmpty(coupon.getEndTime())) {
            errCtx.setErrCode(ErrCode.ASSIGN_COUPON_LOSE_EFFICACY.getCode());
            errCtx.setErrMsg("券的可用时间不符合要求");
            return false;
        }

        long startTime;
        long endTime;
        try {
            startTime = Long.parseLong(coupon.getStartTime());
            endTime = Long.parseLong(coupon.getEndTime());
        } catch (Exception e) {
            log.error("NoCodeUseTime.check，券可用时间转换出错，coupon={}, err:{}", GsonUtil.toJson(coupon), e.getMessage());
            errCtx.setErrCode(ErrCode.USE_COUPON_TIME_INVALID.getCode());
            errCtx.setErrMsg("无效的券");
            return false;
        }

        if (startTime > nowTime) {
            errCtx.setErrCode(ErrCode.USE_COUPON_NOSTART_TIME.getCode());
            errCtx.setErrMsg("券未在可用有效期内");
            return false;
        }

        if (endTime < nowTime) {
            errCtx.setErrCode(ErrCode.USE_COUPON_OVER_TIME.getCode());
            errCtx.setErrMsg("券未在可用有效期内");
            return false;
        }

        return true;
    }
}
