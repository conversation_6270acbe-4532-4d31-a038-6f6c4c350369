package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigInfo;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.enums.couponconfig.UseTimeTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import org.springframework.stereotype.Component;

/**
 * 校验可领取时间
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Component
public class ConfigFetchTime extends CheckerAbstract {

    @Override
    public boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {
        if (!ctx.isCheckConfigFetchTime()) {
            return true;
        }

        errCtx.setInvalidConfig(true);

        CouponConfigItem configItem = ctx.getConfigInfoMap().get(respItem.getConfigId());
        CouponConfigInfo config = configItem.getCouponConfigInfo();
        long nowTime = ctx.getNowUnixSecond();

        if (
                config == null ||
                        config.getStartFetchTime() == null || config.getStartFetchTime() <= 0 ||
                        config.getEndFetchTime() == null || config.getEndFetchTime() <= 0 ||
                        config.getStartFetchTime() >= config.getEndFetchTime()
        ) {
            errCtx.setErrCode(ErrCode.ASSIGN_COUPON_LOSE_EFFICACY.getCode());
            errCtx.setErrMsg("券配置的发放时间不符合要求");
            return false;
        }

        long useEndTime = 0L;
        if (UseTimeTypeEnum.Relative.getValue().equals(config.getUseTimeType())) {
            if (config.getUseDuration() == null || config.getUseDuration() <= 0) {
                errCtx.setErrCode(ErrCode.ASSIGN_COUPON_LOSE_EFFICACY.getCode());
                errCtx.setErrMsg("券的有效使用时间不符合要求");
                return false;
            }
            useEndTime = nowTime + config.getUseDuration() * 3600;
        } else if (UseTimeTypeEnum.Fixed.getValue().equals(config.getUseTimeType())) {
            if (config.getStartUseTime() == null || config.getStartUseTime() <= 0 ||
                    config.getEndUseTime() == null || config.getEndUseTime() <= 0 ||
                    config.getStartUseTime() > config.getEndUseTime()
            ) {
                errCtx.setErrCode(ErrCode.ASSIGN_COUPON_LOSE_EFFICACY.getCode());
                errCtx.setErrMsg("券的有效使用时间区间不符合要求");
                return false;
            }
            useEndTime = config.getEndUseTime();
        }

        if (config.getStartFetchTime() > nowTime) {
            errCtx.setErrCode(ErrCode.ASSIGN_COUPON_NOSTART_TIME.getCode());
            errCtx.setErrMsg("券可领取时间未开始");
            return false;
        }

        if (config.getEndFetchTime() < nowTime) {
            errCtx.setErrCode(ErrCode.ASSIGN_COUPON_OVER_TIME.getCode());
            errCtx.setErrMsg("券可领取时间已结束");
            return false;
        }

        if (useEndTime <= nowTime) {
            errCtx.setErrCode(ErrCode.ASSIGN_COUPON_OVER_TIME.getCode());
            errCtx.setErrMsg("不能发放已过期的券");
            return false;
        }

        return true;
    }
}
