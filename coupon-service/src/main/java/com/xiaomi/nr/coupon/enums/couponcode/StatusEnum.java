package com.xiaomi.nr.coupon.enums.couponcode;

/**
 * 券码状态 枚举
 *
 * <AUTHOR>
 */
public enum StatusEnum {

    /**
     * 未兑换
     */
    UNUSED("unused","未兑换"),

    /**
     * 已兑换
     */
    USED("used","已兑换"),

    /**
     * 已锁定
     */
    LOCKED("locked","已锁定"),

    /**
     * 已兑换
     */
    EXPIRED("expired","已过期")

    ;

    private final String value;
    private final String name;

    StatusEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static String findNameByValue(String value) {
        StatusEnum[] values = StatusEnum.values();
        for (StatusEnum item : values) {
            if (item.getValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }
}

