package com.xiaomi.nr.coupon.domain.couponconfig;

import com.google.common.collect.Maps;
import com.xiaomi.goods.gis.dto.batched.BatchedMultiInfoResponse;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsMultiInfoResponse;
import com.xiaomi.nr.coupon.api.dto.couponconfig.CouponConfigRelationGoodsResponse;
import com.xiaomi.nr.coupon.api.dto.couponconfig.GoodsCouponConfigRelResponse;
import com.xiaomi.nr.coupon.api.dto.couponconfig.GoodsItem;
import com.xiaomi.nr.coupon.api.dto.couponconfig.GoodsRelationCouponConfigResponse;
import com.xiaomi.nr.coupon.constant.CouponConfigConstant;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.GoodScope;
import com.xiaomi.nr.coupon.enums.couponconfig.PromotionType;
import com.xiaomi.nr.coupon.enums.couponconfig.UseTypeEnum;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrInfo;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.GoodsCouponRepository;
import com.xiaomi.nr.coupon.infrastructure.rpc.gis.GoodsInfoProxyService;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RelationGoods {

    @Autowired
    private GoodsCouponRepository goodsCouponRepository;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private GoodsInfoProxyService goodsInfoProxyService;


    /**
     * 根据优惠券配置ID获取可用的货品或套装列表
     *
     * @param configId long
     * @return CouponConfigRelationGoodsResponse
     * @throws BizError BizError
     */
    public CouponConfigRelationGoodsResponse getGoodsList(long configId) throws BizError {
        if (configId <= 0) {
            log.info("RelationGoods getGoodsList param error configId:{}", configId);
            throw ErrInfo.ERR_COUPON_CONFIG_ID;
        }

        CouponConfigItem couponConfigItem = couponConfigRepository.getCouponConfig(configId);
        if (couponConfigItem == null || couponConfigItem.getConfigId() <= 0) {
            log.info("RelationGoods getGoodsList couponConfigItem is null configId:{},couponConfigItem:{}", configId, couponConfigItem);
            throw ErrInfo.ERR_COUPON_CONFIG_CACHE_NOT_FIND;
        }

        List<CouponConfigRelationGoodsResponse.GoodsItem> goodsList = new ArrayList<>();

        GoodScope goodScope = couponConfigItem.getGoodScope();
        if (CollectionUtils.isNotEmpty(goodScope.getGoods())) {
            Map<Long, Long> goodsToPid = getGoodsToPid(goodScope.getGoods());
            goodScope.getGoods().forEach((k) -> {
                CouponConfigRelationGoodsResponse.GoodsItem goodsItem = new CouponConfigRelationGoodsResponse.GoodsItem();
                goodsItem.setId(k);
                goodsItem.setProductId(goodsToPid.get(k));
                goodsItem.setLevel(GoodsLevelEnum.Goods.getValue());
                goodsList.add(goodsItem);
            });
        }
        if (CollectionUtils.isNotEmpty(goodScope.getPackages())) {
            Map<Long, Long> packageToPid = getPackageToPid(goodScope.getPackages());
            goodScope.getPackages().forEach((k) -> {
                CouponConfigRelationGoodsResponse.GoodsItem goodsItem = new CouponConfigRelationGoodsResponse.GoodsItem();
                goodsItem.setId(k);
                goodsItem.setProductId(packageToPid.get(k));
                goodsItem.setLevel(GoodsLevelEnum.Package.getValue());
                goodsList.add(goodsItem);
            });
        }

        CouponConfigRelationGoodsResponse result = new CouponConfigRelationGoodsResponse();
        result.setId(couponConfigItem.getConfigId());
        result.setName(couponConfigItem.getCouponConfigInfo().getName());
        result.setTypeCode(couponConfigItem.getPromotionType().getValue());
        result.setTypeCodeDesc(UseTypeEnum.findNameByRedisValue(couponConfigItem.getPromotionType().getValue()));
        result.setUseType(couponConfigItem.getPromotionType().getValue());
        result.setUseTypeDesc(UseTypeEnum.findNameByRedisValue(couponConfigItem.getPromotionType().getValue()));
        if (PromotionType.ConditionReduce == couponConfigItem.getPromotionType() || PromotionType.DirectReduce == couponConfigItem.getPromotionType()) {
            BigDecimal money = new BigDecimal(couponConfigItem.getCouponConfigInfo().getPromotionValue());
            money = money.divide(new BigDecimal("100"), 2, BigDecimal.ROUND_DOWN).stripTrailingZeros();
            result.setShowValue(money.toPlainString());
            result.setShowUnit(CouponConfigConstant.COUPON_CASH_UNIT);
        } else if (PromotionType.ConditionDiscount == couponConfigItem.getPromotionType()) {
            BigDecimal discount = new BigDecimal(couponConfigItem.getCouponConfigInfo().getPromotionValue());
            discount = discount.divide(new BigDecimal("1000"), 2, BigDecimal.ROUND_UP).stripTrailingZeros();
            result.setShowValue(discount.toPlainString());
            result.setShowUnit(CouponConfigConstant.COUPON_DISCOUNT_UNIT);
        } else {
            result.setShowUnit("");
            result.setShowValue("");
        }

        //可用商品列表
        result.setList(goodsList);

        return result;
    }

    /**
     * 根据SKU/套装ID获取可用的优惠券配置列表
     *
     * @param id    long
     * @param level String
     * @return GoodsRelationCouponConfigResponse
     * @throws BizError BizError
     */
    public GoodsRelationCouponConfigResponse getCouponConfigList(long id, String level) throws BizError {

        if (id <= 0) {
            log.info("couponConfig.goods.relation, 根据SKU/套装ID获取可用的优惠券配置列表，id不符合要求, id={}, level={}", id, level);
            throw ErrInfo.ERR_COUPON_CONFIG_SKU_PACKAGE_ID;
        }

        if(!GoodsLevelEnum.findByValue(level)){
            log.info("couponConfig.goods.relation, 根据SKU/套装ID获取可用的优惠券配置列表，level不符合要求, id={}, level={}", id, level);
            throw ErrInfo.ERR_COUPON_CONFIG_LEVEL;
        }

        List<Long> configIds = goodsCouponRepository.getGoodConfigs(id, level);

        if (CollectionUtils.isEmpty(configIds)) {
            log.info("couponConfig.goods.relation, 根据SKU/套装ID获取可用的优惠券配置列表，从缓存里取到的数据为空, id={}, level={}", id, level);
            throw ErrInfo.ERR_COUPON_CONFIG_GOODS_CACHE_NOT_FIND;
        }

        Map<Long, CouponConfigItem> couponConfigItemMap = couponConfigRepository.getCouponConfigsOnlyBaseInfo(configIds);

        GoodsRelationCouponConfigResponse result = new GoodsRelationCouponConfigResponse();

        if (MapUtils.isEmpty(couponConfigItemMap)) {
            return result;
        }

        for (CouponConfigItem couponConfigItem : couponConfigItemMap.values()) {
            if (couponConfigItem == null) {
                continue;
            }
            GoodsRelationCouponConfigResponse.GoodsRelationCouponConfigItem newItem = new GoodsRelationCouponConfigResponse.GoodsRelationCouponConfigItem();
            newItem.setId(couponConfigItem.getConfigId());
            newItem.setName(couponConfigItem.getCouponConfigInfo().getName());
            result.add(newItem);
        }
        return result;
    }

    /**
     * 根据goods获取可用券列表
     *
     * @param goodsItems
     * @param configIds
     * @return
     * @throws BizError
     */
    public GoodsCouponConfigRelResponse getGoodsCouponConfigIds(List<GoodsItem> goodsItems, List<Long> configIds, boolean withFuture) throws BizError {

        if (CollectionUtils.isEmpty(goodsItems)) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "商品列表不能为空");
        }
        if (CollectionUtils.isEmpty(configIds)) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "优惠券列表不能为空");
        }

        GoodsCouponConfigRelResponse response = new GoodsCouponConfigRelResponse();

        Map<String, List<GoodsItem>> goodsItemMap = goodsItems.stream().collect(Collectors.groupingBy(GoodsItem::getLevel));

        Map<Long, CouponConfigItem> couponConfigItemMap = couponConfigRepository.getCouponConfigs(configIds);

        long nowTime = TimeUtil.getNowUnixSecond();
        List<CouponConfigItem> couponConfigItems = couponConfigItemMap.values().stream().filter(item -> item.getCouponConfigInfo().isOnline() && item.getCouponConfigInfo().isFetchAblePeriod(nowTime, withFuture)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(couponConfigItems)) {
            return response;
        }

        response.setGidCouponConfigIds(buildGoodsCouponRel(couponConfigItems, goodsItemMap, GoodsLevelEnum.Goods.getValue()));
        response.setCidCouponConfigIds(buildGoodsCouponRel(couponConfigItems, goodsItemMap, GoodsLevelEnum.Package.getValue()));
        response.setSsuCouponConfigIds(buildGoodsCouponRel(couponConfigItems, goodsItemMap, GoodsLevelEnum.Ssu.getValue()));
        response.setSkuCouponConfigIds(buildGoodsCouponRel(couponConfigItems, goodsItemMap, GoodsLevelEnum.Sku.getValue()));

        return response;
    }

    /**
     * 获取优惠券可用商品列表
     *
     * @param configIds
     * @return
     * @throws BizError
     */
    public Map<Long, List<GoodsItem>> getCouponConfigGoodsRel(List<Long> configIds, Boolean withSku) throws BizError {

        if (CollectionUtils.isEmpty(configIds)) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "优惠券Id列表不能为空");
        }

        Map<Long, CouponConfigItem> couponConfigItemMap = couponConfigRepository.getCouponConfigs(configIds);
        if (MapUtils.isEmpty(couponConfigItemMap)) {
            log.warn("getCouponConfigGoodsRel couponConfigItem is null configId:{}", configIds);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "优惠券Id列表不正确");
        }

        Map<Long, List<GoodsItem>> couponGoodsRel = new HashMap<>();

        for (CouponConfigItem couponConfigItem : couponConfigItemMap.values()) {
            GoodScope goodScope = couponConfigItem.getGoodScope();
            if (goodScope == null) {
                continue;
            }

            List<GoodsItem> goodsItems = Lists.newArrayList();

            Set<Long> goods = goodScope.getGoods();
            if (CollectionUtils.isNotEmpty(goods)) {
                for (Long goodIs : goods) {
                    GoodsItem goodsItem = new GoodsItem();
                    goodsItem.setLevel(GoodsLevelEnum.Goods.getValue());
                    goodsItem.setId(goodIs);
                    goodsItems.add(goodsItem);
                }
            }

            Set<Long> packages = goodScope.getPackages();
            if (CollectionUtils.isNotEmpty(packages)) {
                for (Long packageId : packages) {
                    GoodsItem goodsItem = new GoodsItem();
                    goodsItem.setLevel(GoodsLevelEnum.Package.getValue());
                    goodsItem.setId(packageId);
                    goodsItems.add(goodsItem);
                }
            }

            Set<Long> ssuList = goodScope.getSsus();
            if (CollectionUtils.isNotEmpty(ssuList)) {
                for (Long ssuId : ssuList) {
                    GoodsItem goodsItem = new GoodsItem();
                    goodsItem.setLevel(GoodsLevelEnum.Ssu.getValue());
                    goodsItem.setId(ssuId);
                    goodsItems.add(goodsItem);
                }
            }

            Set<Long> skuList = goodScope.getSkus();
            if (withSku && CollectionUtils.isNotEmpty(skuList)) {
                for (Long skuId : skuList) {
                    GoodsItem goodsItem = new GoodsItem();
                    goodsItem.setLevel(GoodsLevelEnum.Sku.getValue());
                    goodsItem.setId(skuId);
                    goodsItems.add(goodsItem);
                }
            }

            couponGoodsRel.put(couponConfigItem.getConfigId().longValue(), goodsItems);

        }
        return couponGoodsRel;
    }

    /**
     * 计算商品和券关系
     *
     * @param couponConfigItems
     * @param goodsItemMap
     * @param goodType
     * @return
     */
    private Map<Long, List<Long>> buildGoodsCouponRel(List<CouponConfigItem> couponConfigItems, Map<String, List<GoodsItem>> goodsItemMap, String goodType) {
        Map<Long, List<Long>> goodValidRet = Maps.newHashMap();

        List<GoodsItem> gidItem = goodsItemMap.get(goodType);
        if (CollectionUtils.isEmpty(gidItem)) {
            return goodValidRet;
        }
        for (GoodsItem goodsItem : gidItem) {
            Long itemId = goodsItem.getId();
            goodValidRet.put(itemId, Lists.newArrayList());
            for (CouponConfigItem couponConfigItem : couponConfigItems) {
                // sku
                if (GoodsLevelEnum.Sku.getValue().equals(goodType) && couponConfigItem.getGoodScope().singleCheckValidSku(itemId)) {
                    goodValidRet.get(itemId).add(couponConfigItem.getConfigId().longValue());
                    continue;
                }
                // gid 货品
                if (GoodsLevelEnum.Goods.getValue().equals(goodType) && couponConfigItem.getGoodScope().singleCheckValidGid(itemId)) {
                    goodValidRet.get(itemId).add(couponConfigItem.getConfigId().longValue());
                    continue;
                }
                // cid 套装
                if (GoodsLevelEnum.Package.getValue().equals(goodType) && couponConfigItem.getGoodScope().singleCheckValidPackage(itemId)) {
                    goodValidRet.get(itemId).add(couponConfigItem.getConfigId().longValue());
                    continue;
                }
                // ssu
                if (GoodsLevelEnum.Ssu.getValue().equals(goodType) && couponConfigItem.getGoodScope().singleCheckValidSsu(itemId)) {
                    goodValidRet.get(itemId).add(couponConfigItem.getConfigId().longValue());
                }
            }
        }
        return goodValidRet;
    }


    /**
     * 获取goods对应pid
     *
     * @param goods 货品列表
     * @return Map<Long, Long>
     * @throws BizError 业务异常
     */
    private Map<Long, Long> getGoodsToPid(Set<Long> goods) throws BizError {

        GoodsMultiInfoResponse response = goodsInfoProxyService.getGoodsMultiInfo(Lists.newArrayList(goods), false, false);

        Map<Long, Long> dataMap = new LinkedHashMap<>();
        response.getGoodsMap().forEach((key, value) -> dataMap.put(key, value.getProductId()));
        return dataMap;
    }


    /**
     * 获取套装对应pid
     *
     * @param packages 套装列表
     * @return Map<Long, Long>
     * @throws BizError 业务异常
     */
    private Map<Long, Long> getPackageToPid(Set<Long> packages) throws BizError {

        BatchedMultiInfoResponse response = goodsInfoProxyService.getBatchedMultiInfo(Lists.newArrayList(packages), false);

        Map<Long, Long> dataMap = new LinkedHashMap<>();
        response.getBatchedMap().forEach((key, value) -> dataMap.put(key, value.getProductId()));
        return dataMap;
    }
}
