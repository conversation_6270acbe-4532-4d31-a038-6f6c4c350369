package com.xiaomi.nr.coupon.enums.goods;

/**
 * 商品销售模式 枚举
 *
 * <AUTHOR>
 * @date 2023/7/24
 */
public enum SaleModeEnum {
    /**
     * 常态销售
     */
    STANDARD("standard", "常态销售"),

    /**
     * 盲售
     */
    PREPAY("prepay", "盲售"),

    /**
     * 定金预售
     */
    BOOKING("booking", "定金预售"),

    /**
     * 全款预售
     */
    PRESALES("presales", "全款预售"),

    /**
     * 预约带地址
     */
    SUBSCRIBE("subscribe", "预约带地址"),

    /**
     * 预约不带地址
     */
    RESERVE("reserve", "预约不带地址"),

    /**
     * 自定义
     */
    WEBVIEW("webview", "自定义"),

    /**
     * 眼镜
     */
    GLASS("glass", "眼镜"),
    ;

    private final String value;
    private final String name;

    SaleModeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static boolean findByValue(String value) {
        SaleModeEnum[] values = SaleModeEnum.values();
        for (SaleModeEnum item : values) {
            if (item.getValue().equals(value)) {
                return true;
            }
        }
        return false;
    }
}
