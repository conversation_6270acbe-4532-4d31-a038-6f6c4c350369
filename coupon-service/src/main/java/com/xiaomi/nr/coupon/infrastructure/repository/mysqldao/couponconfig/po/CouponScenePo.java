package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.po;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: 优惠券场景PO
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/2/26 9:41 下午
 * @Version: 1.0
 **/
@Data
public class CouponScenePo implements Serializable {

    private static final long serialVersionUID = 5594861580443408691L;

    /**
     * 投放场景id
     */
    private Long id;

    /**
     * 投放场景编码
     */
    private String sceneCode;

    /**
     * 投放方式 1:优惠券, 2:兑换码
     */
    private Integer sendMode;

    /**
     * 发放方式(以逗号分隔) 1:外部系统发券, 2:内部系统灌券
     */
    private String assignMode;

    /**
     * 可用状态 1:上线 2:下线
     */
    private Integer status;
}
