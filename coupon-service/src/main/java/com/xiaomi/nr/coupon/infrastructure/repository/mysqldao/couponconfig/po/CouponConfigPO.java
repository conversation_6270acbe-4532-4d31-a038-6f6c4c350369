package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 优惠券配置实体
 */
@Data
public class CouponConfigPO implements Serializable {

    private static final long serialVersionUID = -7278527488012115290L;

    /**
     * 配置ID
     */
    @SerializedName("id")
    private Integer id;

    /**
     * 配置名称
     */
    @SerializedName("n")
    private String name;

    /**
     * 状态 1:上线, 2:下线, 3:终止
     */
    @SerializedName("s")
    private Integer status;

    /**
     * 使用范围描述
     */
    @SerializedName("cd")
    private String couponDesc;

    /**
     * 优惠券类型  1: 商品券 2: 运费券
     */
    @SerializedName("ct")
    private Integer couponType = 1;

    /**
     * 业务领域 0:3C业务  3:汽车业务
     */
    @SerializedName("tpf")
    private Integer bizPlatform;

    /**
     * 履约方式  -1: 所有方式 139: 门店闪送 (这个跟交易中台统一定义)
     */
    @SerializedName("si")
    private Integer shipmentId = -1;

    /**
     * 优惠类型 1:满减, 2:满折, 3:N元券, 4:立减
     */
    @SerializedName("pt")
    private Integer promotionType;

    /**
     * 发放场景
     */
    @SerializedName("ss")
    private String sendScene;

    /**
     * 可领取的开始时间
     */
    @SerializedName("sft")
    private Long startFetchTime;

    /**
     * 可领取的结束时间
     */
    @SerializedName("eft")
    private Long endFetchTime;

    /**
     * 使用有效期的类型
     */
    @SerializedName("utt")
    private Integer useTimeType;

    /**
     * 可使用的开始时间
     */
    @SerializedName("sut")
    private Long startUseTime;

    /**
     * 可使用的结束时间
     */
    @SerializedName("eut")
    private Long endUseTime;

    /**
     * 有效时长(单位小时)
     */
    @SerializedName("ud")
    private Integer useDuration;

    /**
     * 使用渠道 (1:小米商城 2:直营店 3:专卖店 4:授权店 5:堡垒店)
     */
    @SerializedName("uc")
    private String useChannel;

    /**
     * 使用平台
     */
    @SerializedName("up")
    private String usePlatform;

    /**
     * 使用门店
     */
    @SerializedName("us")
    private String useStore;

    /**
     * 门槛类型 1满元 2满件 3每满元 4每满件
     */
    @SerializedName("bt")
    private Integer bottomType;

    /**
     * 满元门槛值（单位分）
     */
    @SerializedName("bp")
    private Integer bottomPrice;

    /**
     * 满件门槛值（单位个）
     */
    @SerializedName("bc")
    private Integer bottomCount;

    /**
     * 优惠值（单位个/分）
     */
    @SerializedName("pv")
    private Long promotionValue;

    /**
     * 最大减免金额（单位分）
     */
    @SerializedName("mr")
    private Integer maxReduce;

    /**
     * 商品范围类型 1 商品券 2 分类券
     */
    @SerializedName("st")
    private Integer scopeType;

    /**
     * 券可用商品
     */
    private String goodsInclude;

    /**
     * 排除商品
     */
    private String goodsExclude;

    /**
     * 类目Id列表
     */
    private String categoryIds;

    /**
     * 可发放的总数量
     */
    @SerializedName("ac")
    private Integer applyCount;

    /**
     * 每人限领的数量
     */
    @SerializedName("fl")
    private Integer fetchLimit;

    /**
     * 附加属性 1可包邮 2可转增 3指定地区
     */
    @SerializedName("ep")
    private String extProp;

    /**
     * 可使用地区
     * 区域类型（1:省 2:市） => 区域ID列表
     */
    @SerializedName("ai")
    private String areaIds;

    /**
     * 发放渠道 老数据和接口兼容使用
     */
    @SerializedName("sc")
    private String sendChannel;

    /**
     * 服务场景：基础保养、漆面修复、上门补胎
     */
    @SerializedName("sct")
    private Integer serviceType;

    /**
     * 限领类型 1限领 2不限领
     */
    @SerializedName("flt")
    private Integer fetchLimitType;

    /**
     * 最后更新时间
     */
    private Timestamp updateTime;

    /**
     * 预算申请单号
     */
    @SerializedName("budget_apply_no")
    private String budgetApplyNo;

    /**
     * 行号
     */
    @SerializedName("line_num")
    private Long lineNum;

    /**
     * 使用次数限制，1-限制，2-不限制
     */
    @SerializedName("tlt")
    private Integer timesLimit = 1;

    /**
     * 是否公开推广：1-是; 2-否
     */
    @SerializedName("pp")
    private Integer publicPromotion = 2;
}
