package com.xiaomi.nr.coupon.domain.matchertool.core;

import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherBaseReqDo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

/**
 * 匹配器接口
 *
 * <AUTHOR>
 * @date 2023/7/19
 */
public interface MatcherInterface {

    /**
     * 构建context
     *
     * @param req .
     * @return .
     * @throws BizError .
     */
    MatcherContextDo newContext(MatcherBaseReqDo req) throws BizError;

    /**
     * 初始化资源
     *
     * @param ctx .
     * @throws BizError .
     */
    void initResource(MatcherContextDo ctx) throws BizError;

    /**
     * 匹配过程
     *
     * @param ctx .
     * @throws BizError .
     */
    void matching(MatcherContextDo ctx) throws BizError;

    /**
     * 过滤
     *
     * @param ctx .
     * @throws BizError .
     */
    void filter(MatcherContextDo ctx) throws BizError;

    /**
     * 获取校验工具
     *
     * @return  校验工具
     */
    MatcherCheckToolAbstract getCheckTool();

}