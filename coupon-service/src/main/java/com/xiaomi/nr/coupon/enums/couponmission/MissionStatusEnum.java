package com.xiaomi.nr.coupon.enums.couponmission;

/**
 * 发放任务状态 枚举
 *
 * <AUTHOR>
 */
public enum MissionStatusEnum {

    /**
     * 创建
     */
    Add("add", "add", "创建"),

    /**
     * 审核中
     */
    Approving("approving", "approving", "审核中"),

    /**
     * 审核通过
     */
    Approved("approved", "approved", "审核通过"),

    /**
     * 已拒绝
     */
    Reject("reject", "reject", "已拒绝"),

    /**
     * 已终止
     */
    Cancel("cancel", "cancel", "已终止");

    private final String redisValue;
    private final String mysqlValue;
    private final String name;

    MissionStatusEnum(String redisValue, String mysqlValue, String name) {
        this.redisValue = redisValue;
        this.mysqlValue = mysqlValue;
        this.name = name;
    }

    public String getRedisValue() {
        return this.redisValue;
    }

    public String getMysqlValue() {
        return this.mysqlValue;
    }

    public String getName() {
        return name;
    }

    public static String findNameByRedisValue(String value) {
        MissionStatusEnum[] values = MissionStatusEnum.values();
        for (MissionStatusEnum item : values) {
            if (item.getRedisValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }

    public static String findRedisValueByMysqlValue(String value) {
        MissionStatusEnum[] values = MissionStatusEnum.values();
        for (MissionStatusEnum item : values) {
            if (item.getMysqlValue().equals(value)) {
                return item.getRedisValue();
            }
        }
        return null;
    }
}

