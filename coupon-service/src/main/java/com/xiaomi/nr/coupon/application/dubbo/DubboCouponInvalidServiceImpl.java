package com.xiaomi.nr.coupon.application.dubbo;

import com.google.common.base.Stopwatch;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponCheckResp;
import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponReq;
import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponResp;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.api.service.DubboCouponInvalidService;
import com.xiaomi.nr.coupon.domain.couponinvalid.CouponInvalidFlowFactory;
import com.xiaomi.nr.coupon.domain.couponinvalid.CouponInvalidService;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 券作废
 *
 * <AUTHOR>
 * @date 2024/5/19
 */
@Service(timeout = 2000, group = "${dubbo.group}", version = "1.0", delay = 10000)
@Component
@Slf4j
@ApiModule(value = "券作废服务", apiInterface = DubboCouponInvalidService.class)
public class DubboCouponInvalidServiceImpl implements DubboCouponInvalidService {

    @Autowired
    private CouponInvalidFlowFactory couponInvalidFlowFactory;

    /**
     * 批量作废券校验
     *
     * @param req InvalidCouponReq
     * @return InvalidCouponCheckResp
     */
    @Override
    @ApiDoc("券作废校验")
    public Result<InvalidCouponCheckResp> invalidCouponCheck(@Valid InvalidCouponReq req) {

        Stopwatch stopwatch = Stopwatch.createStarted();
        try {

            // 1、入参校验
            checkInvalidCouponReq(req);

            // 2、批量作废券校验
            CouponInvalidService couponInvalidService = couponInvalidFlowFactory.getCouponInvalidService(req.getBizPlatform());
            InvalidCouponCheckResp resp = new InvalidCouponCheckResp();
            resp.setCantInvalidCouponList(couponInvalidService.invalidCouponCheck(req));

            log.info("DubboCouponInvalidServiceImpl.invalidCouponCheck success, runTime={}ms, req={}, resp={}", stopwatch.elapsed(TimeUnit.MILLISECONDS), GsonUtil.toJson(req), GsonUtil.toJson(resp));
            return Result.success(resp);
        } catch (Exception e) {
            log.error("DubboCouponInvalidServiceImpl.invalidCouponCheck failed, req={}, error: ", GsonUtil.toJson(req), e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 批量作废券
     *
     * @param req InvalidCouponReq
     * @return InvalidCouponResp
     */
    @Override
    @ApiDoc("券作废")
    public Result<InvalidCouponResp> invalidCoupon(@Valid InvalidCouponReq req) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {

            // 1、入参校验
            checkInvalidCouponReq(req);

            // 2、批量作废券
            CouponInvalidService couponInvalidService = couponInvalidFlowFactory.getCouponInvalidService(req.getBizPlatform());
            InvalidCouponResp resp = couponInvalidService.invalidCoupon(req);

            log.info("DubboCouponInvalidServiceImpl.invalidCoupon success, runTime={}ms, req={}, resp={}", stopwatch.elapsed(TimeUnit.MILLISECONDS), GsonUtil.toJson(req), GsonUtil.toJson(resp));
            return Result.success(resp);
        } catch (Exception e) {
            log.error("DubboCouponInvalidServiceImpl.invalidCoupon failed, req={}, error: ", GsonUtil.toJson(req), e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 入参校验
     *
     * @param req   InvalidCouponReq
     */
    private void checkInvalidCouponReq(InvalidCouponReq req) throws BizError {

        // 业务领域校验
        BizPlatformEnum bizPlatform = BizPlatformEnum.valueOf(req.getBizPlatform());
        if (Objects.isNull(bizPlatform)) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "业务领域非法");
        }

        // 目前只提供售后服务领域作废券功能
        if (!BizPlatformEnum.CAR_AFTER_SALE.equals(bizPlatform)) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "暂不支持作废非汽车售后服务领域的优惠券");
        }

        // 汽车服务券场景下vid校验
        if (BizPlatformEnum.CAR_AFTER_SALE.equals(bizPlatform) && StringUtils.isBlank(req.getVid())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "vid不能为空");
        }

        List<Long> couponIdList = req.getCouponIdList();
        if (couponIdList.stream().distinct().count() != couponIdList.size()) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "存在重复couponId");
        }
    }
}
