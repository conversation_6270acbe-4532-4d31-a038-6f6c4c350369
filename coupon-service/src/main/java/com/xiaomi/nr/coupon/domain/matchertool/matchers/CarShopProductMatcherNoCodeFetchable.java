package com.xiaomi.nr.coupon.domain.matchertool.matchers;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.matchertool.checktools.CarShopProductNoCodeFetchableCheckTool;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherAbstractFetchable;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherCheckToolAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.*;
import com.xiaomi.nr.coupon.domain.matchertool.entity.nocodefetchable.NoCodeFetchableReqDo;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.enums.matchertool.MatcherToolTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.GoodsCouponRepository;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 车商城产品站-无码券可领匹配器
 *
 * <AUTHOR>
 * @date 2024/9/6
 */
@Slf4j
@Component
public class CarShopProductMatcherNoCodeFetchable extends MatcherAbstractFetchable {

    @Resource
    private CarShopProductNoCodeFetchableCheckTool checkTool;

    @Resource
    private GoodsCouponRepository goodsCouponRepository;

    @Override
    public MatcherToolTypeEnum getMatcherToolTypeEnum() {
        return MatcherToolTypeEnum.CAR_SHOP_PRODUCT_NOCODE_FETCHABLE;
    }

    /**
     * 构建context
     *
     * @param req .
     * @return .
     * @throws BizError .
     */
    @Override
    public MatcherContextDo newContext(MatcherBaseReqDo req) throws BizError {

        NoCodeFetchableReqDo p = (NoCodeFetchableReqDo) req;
        if (Objects.isNull(p)) {
            throw ExceptionHelper.create(GeneralCodes.InternalError, "车商城可领匹配器入参不符合要求");
        }

        MatcherContextDo ctx = new MatcherContextDo();

        ctx.setUserId(p.getUserId());
        ctx.setBizPlatform(p.getBizPlatform());
        ctx.setOuterGoodsList(p.getGoodsList());
        ctx.setUseChannel(p.getUseChannel());

        ctx.setCheckBizPlatform(p.isCheckBizPlatform());
        ctx.setCheckUseChannel(p.isCheckUseChannel());
        ctx.setCheckPublicPromotion(p.isCheckPublicPromotion());
        ctx.setCheckConfigStatus(p.isCheckConfigStatus());
        ctx.setCheckConfigFetchTime(p.isCheckConfigFetchTime());
        ctx.setCheckConfigFetchLimit(p.isCheckConfigFetchLimit());
        ctx.setCheckUserFetchLimit(p.isCheckUserFetchLimit());

        ctx.setSort(p.isSort());

        return ctx;
    }

    /**
     * 初始化资源
     *
     * @param ctx .
     * @throws BizError .
     */
    @Override
    public void specialInitResource(MatcherContextDo ctx) throws BizError {

        // 入参校验已经限制了车商城渠道只能透传ssu
        List<Long> ssuList = ctx.getOuterGoodsList().stream().map(MatcherGoodsItemDo::getId).collect(Collectors.toList());
        Map<Long, List<Long>> ssuConfigMap = goodsCouponRepository.batchGetGoodConfigs(ssuList, GoodsLevelEnum.Ssu.getValue());
        List<Long> configIdList = ssuConfigMap.values().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(configIdList)) {
            Map<Long, CouponConfigItem> configInfoMap = getCouponConfigInfos(configIdList);

            if (MapUtils.isEmpty(configInfoMap)) {
                throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_NOT_FOUND, "无法获取到有效的券配置信息");
            }
            ctx.setConfigInfoMap(configInfoMap);
        }

        if (ctx.isCheckConfigFetchLimit()) {
            ctx.setConfigFetchedCountMap(getConfigByFetchedCount(configIdList));
        }

        if (ctx.isCheckUserFetchLimit()) {
            ctx.setUserFetchedCountMap(getConfigByUserFetchedCount(ctx.getUserId(), configIdList));
        }
    }

    /**
     * 匹配过程
     *
     * @param ctx .
     */
    @Override
    public void matching(MatcherContextDo ctx) {

        Map<Long, CouponConfigItem> configInfoMap = ctx.getConfigInfoMap();
        if (MapUtils.isEmpty(configInfoMap)) {
            return;
        }

        List<MatcherRespItemDo> keyResp = new ArrayList<>();
        Map<Long, List<MatcherGoodsItemDo>> validGoods = new HashMap<>();
        for (CouponConfigItem configItem : configInfoMap.values()) {
            try {
                Long configId = configItem.getConfigId().longValue();
                MatcherRespItemDo respItem = new MatcherRespItemDo();
                respItem.setConfigId(configId);

                MatcherErrContextDo errCtx = checkTool.check(ctx, respItem);
                if (Objects.nonNull(errCtx)) {
                    respItem.setErrCtx(errCtx);
                    keyResp.add(respItem);
                    continue;
                } else {
                    keyResp.add(respItem);
                }

                List<MatcherGoodsItemDo> goodsList = getValidGoods(ctx, respItem);
                if (CollectionUtils.isNotEmpty(goodsList)) {
                    validGoods.put(configId, goodsList);
                }
            } catch (Exception e) {
                log.error("MatcherAbstract, matching execute error, configId:{}, ctx:{}", configItem.getConfigId(), GsonUtil.toJson(ctx), e);
            }
        }

        ctx.setKeyResp(keyResp);
        ctx.setValidGoods(validGoods);
    }

    @Override
    public MatcherCheckToolAbstract getCheckTool() {
        return checkTool;
    }

}
