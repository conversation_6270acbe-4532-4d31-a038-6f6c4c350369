package com.xiaomi.nr.coupon.domain.coupontrade.tradecheck;

import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.coupontrade.convert.CouponBaseInfoConvert;
import com.xiaomi.nr.coupon.domain.coupontrade.TradeCheckAbstract;
import com.xiaomi.nr.coupon.domain.coupontrade.entity.TradeCheckContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 销核结算-无码券-3C新零售
 *
 * <AUTHOR>
 * @date 2024/1/5
 */
@Component
public class NoCodeRetailCheck extends TradeCheckAbstract {

    @Resource
    private CouponBaseInfoConvert couponBaseInfoConvert;

    @Resource
    private CouponInfoCheck couponInfoCheck;

    @Override
    protected Integer getFactoryKey() {
        return BizPlatformEnum.RETAIL.getCode();
    }

    @Override
    protected boolean initData(TradeCheckContext ctx) throws Exception {
        ctx.setCouponBaseInfo(couponBaseInfoConvert.convertNoCodeCouponPoConfig2BaseInfo(ctx.getCouponPo(), ctx.getConfig()));
        ctx.setCouponGroupNo(couponBaseInfoConvert.genCouponGroupNo(ctx.getConfig()));
        ctx.setCouponOrgCode(couponInfoCheck.getCouponOrgCode(ctx.getCouponPo().getExtendInfo()));
        return true;
    }

    @Override
    protected boolean check(TradeCheckContext ctx) {
        // 公共校验
        if (!super.commonCheck(ctx)) {
            return false;
        }

        // 校验渠道
        if (!couponInfoCheck.checkChannel(ctx.getConfig(), ctx.getRequestModel(), ctx.getErrContext(), ctx.getCouponModeType())) {
            return false;
        }

        //专店专用
        if (!couponInfoCheck.checkStore(ctx.getConfig(), ctx.getRequestModel(), ctx.getErrContext(), ctx.getCouponOrgCode(), ctx.getCouponModeType())) {
            return false;
        }

        // 校验区域
        if (!couponInfoCheck.checkConsumerCoupon(ctx.getConfig(), ctx.getRequestModel(), ctx.getErrContext())) {
            return false;
        }

        // 履约方式
        if (!couponInfoCheck.checkShipmentId(ctx.getConfig(), ctx.getRequestModel(), ctx.getErrContext())) {
            return false;
        }

        // 校验商品
        if (!couponInfoCheck.findGoodsInclude(ctx.getConfig(), ctx.getRequestModel(), ctx.getErrContext(), ctx.getCouponModeType())) {
            return false;
        }
        return true;
    }

    @Override
    protected void checkAfter(TradeCheckContext ctx) {
        ctx.getCheckoutCouponInfo().setValidSkuList(couponBaseInfoConvert.getValidSkuList(ctx.getConfig(), ctx.getRequestModel().getSkuPackageList()));
        // 老套装
        ctx.getCheckoutCouponInfo().setValidPackageList(couponBaseInfoConvert.getValidPackageList(ctx.getConfig(), ctx.getRequestModel().getSkuPackageList()));
        // 新套装
        ctx.getCheckoutCouponInfo().setValidSsuList(couponBaseInfoConvert.getValidSsuList(ctx.getConfig(), ctx.getRequestModel().getSkuPackageList()));
    }

}
