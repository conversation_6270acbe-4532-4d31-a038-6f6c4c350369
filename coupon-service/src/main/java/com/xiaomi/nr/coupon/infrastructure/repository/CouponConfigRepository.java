package com.xiaomi.nr.coupon.infrastructure.repository;

import com.google.common.collect.Lists;
import com.xiaomi.common.perfcounter.PerfCounter;
import com.xiaomi.nr.coupon.constant.CouponConstant;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponSceneItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.GoodScope;
import com.xiaomi.nr.coupon.domain.couponconfig.model.ServiceSceneItem;
import com.xiaomi.nr.coupon.infrastructure.repository.localcache.CouponConfigCache;
import com.xiaomi.nr.coupon.infrastructure.repository.localcache.CouponSceneCache;
import com.xiaomi.nr.coupon.infrastructure.repository.localcache.ServiceSceneCache;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.CouponConfigMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.CouponSceneMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.CouponScenePermissionMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.CouponServiceSceneMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.po.CouponConfigPO;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.po.CouponScenePo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.po.ServiceScenePo;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.CouponConfigRedisDao;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * @description: 优惠券配置仓库
 * @author: hejiapeng
 * @Date 2022/2/28 9:53 上午
 * @Version: 1.0
 **/
@Slf4j
@Component
public class CouponConfigRepository implements InitializingBean {

    private static final int LIMIT = 50;

    private static final int THREE_MONTH_TIMESTAMP = 7776000;

    private AtomicLong latestVersion = new AtomicLong(0L);

    private AtomicLong lastUpdateVersion = new AtomicLong(-1L);

    @Value("${coupon.config.cache.biz.platform}")
    private List<Integer> couponConfigCacheBizPlatform;

    @Resource
    private CouponConfigCache couponConfigCache;

    @Resource
    private CouponSceneCache couponSceneCache;

    @Resource
    private ServiceSceneCache serviceSceneCache;

    @Resource
    private CouponConfigRedisDao couponConfigRedisDao;

    @Resource
    private CouponConfigMapper couponConfigMapper;

    @Resource
    private CouponSceneMapper couponSceneMapper;

    @Resource
    private CouponServiceSceneMapper couponServiceSceneMapper;

    @Resource
    private CouponScenePermissionMapper couponScenePermissionMapper;

    @Resource
    private CouponConfigItemFactory couponConfigItemFactory;

    /**
     * 根据configId 获取对应的CouponConfig
     *
     * @param configId Long
     * @return ConfigCacheItem
     */
    public CouponConfigItem getCouponConfig(Long configId) {
        if (configId == null || configId <= 0) {
            return null;
        }
        Map<Long, CouponConfigItem> couponConfigs = this.getCouponConfigs(Lists.newArrayList(configId));
        return Optional.ofNullable(couponConfigs.get(configId)).orElse(null);
    }


    /**
     * 批量从localCache里取优惠券配置信息
     *
     * @param configIds List<Long>
     * @return Map<Long, ConfigCacheItem>
     */
    public Map<Long, CouponConfigItem> getCouponConfigs(List<Long> configIds) {
        return getCouponConfigsByOption(configIds, false);
    }

    /**
     * 批量获取优惠券基础配置
     *
     * @param configIds
     * @return
     */
    public Map<Long, CouponConfigItem> getCouponConfigsOnlyBaseInfo(List<Long> configIds) {
        return getCouponConfigsByOption(configIds, true);
    }

    /**
     * 从内存缓存中获取，不走redis
     * 和领券校验有关，强假设 - 内存中拿不到则一定不能领
     * <p>
     * 只限需要快速获取的场景使用，不保证能获取到
     * </p>
     *
     * @param configId
     * @return
     */
    public CouponConfigItem getCouponConfigOnlyCache(Long configId) {
        if (configId == null || configId <= 0) {
            return null;
        }
        CouponConfigItem couponConfigItem = couponConfigCache.getCouponConfig(configId.intValue());
        if (couponConfigItem == null) {
            log.warn("getCouponConfigFromCache error configId:{}, appId:{}", configId, RpcContext.getContext().getAttachment("application"));
        }
        return couponConfigItem;
    }

    /**
     * 从localCache里取发放场景配置
     *
     * @param sceneCode
     * @return
     */
    public CouponSceneItem getCouponScene(String sceneCode) {
        return couponSceneCache.getCouponSceneByCode(sceneCode);
    }

    /**
     * 从localCache里取服务场景配置
     *
     * @param id
     * @return
     */
    public ServiceSceneItem getCouponServiceScene(Integer id) {
        return serviceSceneCache.getServiceSceneById(id);
    }

    /**
     * 刷新localCaceh中优惠券配置
     *
     * @return
     */
    public synchronized void loadAllCouponConfigToCache() {
        long nowTime = TimeUtil.getNowUnixSecond();
        // 获取券时间阀值, 过期一天内
        long thresholdTime = nowTime - 86400;

        long maxUpdateVersion = lastUpdateVersion.get();

        String updateVersion = TimeUtil.formatSecond(maxUpdateVersion);

        // 有效券列表
        long validConfigCount = 0L;
        List<Integer> validConfigIds = couponConfigMapper.getValidConfigIdByUseTime(thresholdTime, updateVersion);
        if (CollectionUtils.isNotEmpty(validConfigIds)) {
            int loopTime = getSplitCount(validConfigIds.size(), LIMIT);
            for (int i = 0; i < loopTime; i++) {
                List<CouponConfigPO> couponConfigPOs = couponConfigMapper.getByIds(validConfigIds.subList(i * LIMIT, Math.min((i + 1) * LIMIT, validConfigIds.size())));
                long maxSeq = couponConfigPOs.stream().mapToLong(po -> po.getUpdateTime().getTime() / 1000).max().orElse(maxUpdateVersion);
                maxUpdateVersion = Math.max(maxUpdateVersion, maxSeq);

                // 针对当前环境涉及业务领域进行过滤
                couponConfigPOs = couponConfigPOs.stream()
                        .filter(po -> couponConfigCacheBizPlatform.contains(po.getBizPlatform()))
                        .collect(Collectors.toList());
                couponConfigCache.batchAddCouponConfig(convertCouponConfig(couponConfigPOs, false));
                validConfigCount += couponConfigPOs.size();
            }
        }
        log.info("loadAllCouponConfigToCache validConfigIds.size:{}, validConfigCount:{}", validConfigIds.size(), validConfigCount);

        // 无效券列表
        long inValidConfigCount = 0L;
        List<Integer> inValidConfigIds = couponConfigMapper.getInvalidConfigIdByUseTime(nowTime - THREE_MONTH_TIMESTAMP, thresholdTime, updateVersion);
        if (CollectionUtils.isNotEmpty(inValidConfigIds)) {
            int loopTime = getSplitCount(inValidConfigIds.size(), LIMIT);
            for (int i = 0; i < loopTime; i++) {
                List<CouponConfigPO> couponConfigPOs = couponConfigMapper.getByIds(inValidConfigIds.subList(i * LIMIT, Math.min((i + 1) * LIMIT, inValidConfigIds.size())));
                // 针对当前环境涉及业务领域进行过滤
                couponConfigPOs = couponConfigPOs.stream()
                        .filter(po -> couponConfigCacheBizPlatform.contains(po.getBizPlatform()))
                        .collect(Collectors.toList());
                couponConfigCache.batchAddCouponConfig(convertCouponConfig(couponConfigPOs, true));
                inValidConfigCount += couponConfigPOs.size();
            }
        }
        latestVersion.set(maxUpdateVersion);
        lastUpdateVersion.set(maxUpdateVersion);

        log.info("loadAllCouponConfigToCache inValidConfigIds.size:{}, inValidConfigCount:{}", inValidConfigIds.size(), inValidConfigCount);
        log.info("loadAllCouponConfigToCache totalSize:{}, totalCount:{}", inValidConfigIds.size() + validConfigIds.size(), validConfigCount + inValidConfigCount);
    }

    /**
     * 刷新localCaceh中优惠券配置
     *
     * @return
     */
    public synchronized long loadIncrCouponConfigToCache() {
        long maxUpdateVersion = latestVersion.get();
        // 获取券时间阀值, 过期一天内
        long nowTime = TimeUtil.getNowUnixSecond() - 86400;
        // 有效券列表
        List<Integer> validConfigIds = couponConfigMapper.getValidConfigIdByUseTime(nowTime, TimeUtil.formatSecond(maxUpdateVersion));
        if (CollectionUtils.isEmpty(validConfigIds)) {
            return maxUpdateVersion;
        }
        int loopTime = getSplitCount(validConfigIds.size(), LIMIT);
        long validConfigCount = 0L;
        for (int i = 0; i < loopTime; i++) {
            List<CouponConfigPO> couponConfigPOs = couponConfigMapper.getByIds(validConfigIds.subList(i * LIMIT, Math.min((i + 1) * LIMIT, validConfigIds.size())));
            long maxSeq = couponConfigPOs.stream().mapToLong(po -> po.getUpdateTime().getTime() / 1000).max().orElse(maxUpdateVersion);
            maxUpdateVersion = Math.max(maxUpdateVersion, maxSeq);

            // 针对当前环境涉及业务领域进行过滤
            couponConfigPOs = couponConfigPOs.stream()
                    .filter(po -> couponConfigCacheBizPlatform.contains(po.getBizPlatform()))
                    .collect(Collectors.toList());
            couponConfigCache.batchAddCouponConfig(convertCouponConfig(couponConfigPOs, false));
            validConfigCount += couponConfigPOs.size();
        }
        latestVersion.set(maxUpdateVersion);
        log.info("loadIncrCouponConfigToCache validConfigIds.size: {}, validConfigCount: {}", validConfigIds.size(), validConfigCount);
        return maxUpdateVersion;
    }

    /**
     * 刷新localCaceh中优惠券商品信息
     *
     * @return
     */
    public synchronized void loadCouponGoodsToCache() {
        // 有效券列表
        List<Integer> validConfigIds = couponConfigMapper.getValidConfigIdByUseTime(TimeUtil.getNowUnixSecond(), "0");
        if (CollectionUtils.isEmpty(validConfigIds)) {
            return;
        }
        for (Integer configId : validConfigIds) {
            CouponConfigItem couponConfigItem = couponConfigCache.getCouponConfig(configId);
            if (couponConfigItem == null) {
                continue;
            }
            String goodsCachePo = couponConfigRedisDao.getOnlyGoodInfo(configId);
            if (StringUtils.isEmpty(goodsCachePo)) {
                continue;
            }
            GoodScope goodScope = GsonUtil.fromJson(goodsCachePo, GoodScope.class);
            couponConfigItem.setGoodScope(goodScope);
        }
        log.info("loadIncrCouponConfigToCache.loadCouponGoodsToCache:{}", validConfigIds.size());
    }

    /**
     * 刷新localCaceh中优惠券配置 暂时只有单测使用
     *
     * @return
     */
    public void loadValidCouponConfigToCache() {
        long nowTime = TimeUtil.getNowUnixSecond();
        // 获取券时间阀值, 过期一天内
        long thresholdTime = nowTime - 86400;

        // 有效券列表
        List<Integer> validConfigIds = couponConfigMapper.getValidConfigIdByUseTimeV2(thresholdTime);
        if (CollectionUtils.isNotEmpty(validConfigIds)) {
            int loopTime = getSplitCount(validConfigIds.size(), LIMIT);
            for (int i = 0; i < loopTime; i++) {
                List<CouponConfigPO> couponConfigPOs = couponConfigMapper.getByIds(validConfigIds.subList(i * LIMIT, Math.min((i + 1) * LIMIT, validConfigIds.size())));
                couponConfigCache.batchAddCouponConfig(convertCouponConfig(couponConfigPOs, false));
            }
        }
    }

    /**
     * 刷新localCache中优惠券发放场景配置
     */
    public void refreshCouponSceneCache() {
        List<CouponScenePo> couponScenePos = couponSceneMapper.getAllCouponScene();
        for (CouponScenePo couponScenePo : couponScenePos) {
            try {

                List<String> couponScenePermissions = couponScenePermissionMapper.getCouponScenePermission(couponScenePo.getId());

                CouponSceneItem couponSceneItem = new CouponSceneItem();
                couponSceneItem.setSceneCode(couponScenePo.getSceneCode()).setSceneStatus(couponScenePo.getStatus());
                couponSceneItem.setAssignMode(Arrays.stream(StringUtils.split(couponScenePo.getAssignMode(), ",")).map(Integer::parseInt).collect(Collectors.toSet()));
                couponSceneItem.setSendMode(couponScenePo.getSendMode());
                couponSceneItem.setAppIds(new HashSet<>(couponScenePermissions));

                couponSceneCache.addCouponSceneIntoCache(couponSceneItem);
            } catch (Exception e) {
                log.error("CouponConfigRepository.refreshCouponSceneCache err sceneCode:{}", couponScenePo.getSceneCode(), e);
            }
        }
    }

    /**
     * 刷新localCache中优惠券发放场景配置
     */
    public void refreshServiceSceneCache() {
        List<ServiceScenePo> couponServiceScenePoList = couponServiceSceneMapper.getAllCouponServiceScene();
        for (ServiceScenePo couponServiceScenePo : couponServiceScenePoList) {
            try {
                ServiceSceneItem serviceSceneItem = new ServiceSceneItem();
                BeanUtils.copyProperties(couponServiceScenePo, serviceSceneItem);
                serviceSceneCache.addServiceSceneIntoCache(serviceSceneItem);
            } catch (Exception e) {
                log.error("CouponConfigRepository.refreshServiceSceneCache err id:{}", couponServiceScenePo.getId(), e);
            }
        }
    }

    /**
     * 获取券配置缓存信息 底层方法
     *
     * @param configIds
     * @param onlyBaseInfo
     * @return
     */
    private Map<Long, CouponConfigItem> getCouponConfigsByOption(List<Long> configIds, boolean onlyBaseInfo) {
        if (CollectionUtils.isEmpty(configIds)) {
            return MapUtils.EMPTY_MAP;
        }
        Map<Long, CouponConfigItem> couponConfigs = new HashMap<>(configIds.size());
        long nowTime = TimeUtil.getNowUnixSecond();
        List<Long> notExistConfigIds = new ArrayList<>();
        for (Long configId : configIds) {
            try {
                CouponConfigItem couponConfigItem = couponConfigCache.getCouponConfig(configId.intValue());
                // 内存缓存为空 或者 需要全部信息，但是缓存中只有基本信息
                if (couponConfigItem == null || (!onlyBaseInfo && couponConfigItem.isOnlyBaseInfo())) {
                    CouponConfigPO couponConfigPO = couponConfigRedisDao.getAllCouponInfo(configId.intValue());
                    if (Objects.isNull(couponConfigPO)) {
                        log.warn("redis not find info configId:{}", configId);
                        notExistConfigIds.add(configId);
                        continue;
                    }
                    couponConfigItem = couponConfigItemFactory.buildCouponConfigItem(couponConfigPO, onlyBaseInfo);

                    //只在券未过期回写缓存，并且记录日志
                    if (couponConfigItem != null && couponConfigItem.getCouponConfigInfo().getEndUseTime() >= nowTime) {
                        couponConfigCache.addCouponConfig(couponConfigItem);
                        log.warn("localCache not find an useAble configId:{}", configId);
                    }
                }
                if (couponConfigItem != null) {
                    couponConfigs.put(configId, couponConfigItem);
                } else {
                    notExistConfigIds.add(configId);
                }
            } catch (Exception e) {
                log.error("getCouponConfigsByOption warn. configIds:{}, e:{}", configIds, e);
            }
        }
        log.debug("getCouponConfigsByOption end. configIds:{}, onlyBaseInfo:{}, notExistConfigIds {}", configIds, onlyBaseInfo, notExistConfigIds);
        return couponConfigs;
    }


    /**
     * 批量从localCache里取优惠券配置信息
     *
     * @param configIds List<Long>
     * @return Map<Long, ConfigCacheItem>
     */
    public Map<Long, CouponConfigItem> getCouponConfigMap(List<Long> configIds) {
        return getCouponConfigsOnlyBase(configIds, true);
    }


    /**
     * 获取券配置缓存信息 底层方法
     *
     * @param configIds
     * @return
     */
    public Map<Long, CouponConfigItem> getCouponConfigsOnlyBase(List<Long> configIds, boolean onlyBaseInfo) {
        if (CollectionUtils.isEmpty(configIds)) {
            return Collections.emptyMap();
        }

        Map<Long, CouponConfigItem> couponConfigMap = new HashMap<>(configIds.size());
        long nowTime = TimeUtil.getNowUnixSecond();
        List<Integer> noCacheConfigIds = new ArrayList<>();
        List<Long> notExistConfigIds = new ArrayList<>();
        for (Long configId : configIds) {

            CouponConfigItem couponConfigItem = couponConfigCache.getCouponConfig(configId.intValue());
            // 内存缓存为空或者 需要全部信息，但是缓存中只有基本信息
            if (couponConfigItem == null || (!onlyBaseInfo && couponConfigItem.isOnlyBaseInfo())) {
                noCacheConfigIds.add(configId.intValue());
                continue;
            }

            couponConfigMap.put(configId, couponConfigItem);
        }


        //本地缓存没取到，从redis批量去取
        Map<Integer, CouponConfigPO> configInfoCachePoMap = getBatchCouponConfigCache(noCacheConfigIds);
        if (MapUtils.isNotEmpty(configInfoCachePoMap)) {
            for (CouponConfigPO couponConfigPO : configInfoCachePoMap.values()) {

                CouponConfigItem couponConfigItem = couponConfigItemFactory.buildCouponConfigItem(couponConfigPO, true);
                if (Objects.isNull(couponConfigItem)) {
                    continue;
                }

                couponConfigMap.put(couponConfigPO.getId().longValue(), couponConfigItem);

                //只在券未过期回写缓存，并且记录日志
                if (couponConfigItem.getCouponConfigInfo().getEndUseTime() >= nowTime) {
                    couponConfigCache.addCouponConfig(couponConfigItem);
                    log.warn("localCache not find an useAble configId:{}", couponConfigPO.getId());
                    PerfCounter.count("localCache_not_findUseAble", 1);
                }
            }
        }

        log.debug("getCouponConfigsByOption end. configIds:{}, notExistConfigIds {}", configIds, notExistConfigIds);
        return couponConfigMap;
    }


    /**
     * 批量获取券配置信息
     *
     * @param configIdList 券id列表
     * @return 券配置信息缓存
     */
    private Map<Integer, CouponConfigPO> getBatchCouponConfigCache(List<Integer> configIdList) {

        Map<Integer, CouponConfigPO> couponConfigPOMap = new HashMap<>();

        int size = configIdList.size();
        if (size <= CouponConstant.GET_COUPON_CONFIG_CACHE_LIMIT) {
            couponConfigPOMap = couponConfigRedisDao.batchGetOnlyBaseInfo(configIdList);
        } else {

            //批量取券缓存，每30个为一批次
            int toIndex = CouponConstant.GET_COUPON_CONFIG_CACHE_LIMIT;
            for (int i = 0; i < size; i += CouponConstant.GET_COUPON_CONFIG_CACHE_LIMIT) {
                if (i + CouponConstant.GET_COUPON_CONFIG_CACHE_LIMIT > size) {
                    toIndex = size - i;
                }

                couponConfigPOMap.putAll(couponConfigRedisDao.batchGetOnlyBaseInfo(configIdList.subList(i, i + toIndex)));
            }
        }

        return couponConfigPOMap;
    }


    /**
     * 券配置领域模型转换
     *
     * @param couponConfigPOs
     * @param onlyBaseInfo
     * @return
     */
    private Map<Integer, CouponConfigItem> convertCouponConfig(List<CouponConfigPO> couponConfigPOs, boolean onlyBaseInfo) {

        if (CollectionUtils.isEmpty(couponConfigPOs)) {
            return MapUtils.EMPTY_MAP;
        }

        Map<Integer, CouponConfigItem> couponConfigItemMap = new HashMap<>(couponConfigPOs.size());

        for (CouponConfigPO couponConfig : couponConfigPOs) {
            // 单个获取防止redis大key阻塞 效率差时 改为批量获取
            if (!onlyBaseInfo) {
                String goodsCachePo = couponConfigRedisDao.getOnlyGoodInfo(couponConfig.getId());
                couponConfig.setGoodsInclude(goodsCachePo);
            }
            CouponConfigItem couponConfigItem = couponConfigItemFactory.buildCouponConfigItem(couponConfig, onlyBaseInfo);
            if (couponConfigItem == null) {
                log.error("CouponConfigRepository.convertCouponConfig err, couponConfig:{}", couponConfig);
                continue;
            }
            couponConfigItemMap.put(couponConfig.getId(), couponConfigItem);
        }
        return couponConfigItemMap;
    }


    private int getSplitCount(int totalSize, int pageSize) {
        return totalSize / pageSize + (totalSize % pageSize == 0 ? 0 : 1);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("cur profile coupon config cache biz platform is {}", GsonUtil.toJson(couponConfigCacheBizPlatform));
    }
}
