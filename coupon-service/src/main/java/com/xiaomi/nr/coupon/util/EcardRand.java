package com.xiaomi.nr.coupon.util;

import com.xiaomi.newretail.common.tools.base.exception.BaseException;
import com.xiaomi.nr.coupon.constant.EcardConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class EcardRand {

    @Value("${keycenter.sid}")
    private String sidTmp;
    private static String sid;

    @Autowired
    private void set() {
        sid = sidTmp;
    }

    private static Random rand =new SecureRandom();



    /**
     * 生成卡号
     *
     * @param isVirtual Integer
     * @param money     String
     * @return Long
     */
    public static Long getEcardId(Integer isVirtual, String money) {
        long cardId;
        int cardType;
        if (isVirtual == 1) {
            cardType = 2;
        } else {
            cardType = 1;
        }
        Integer moneyInt = (int) Float.parseFloat(money);
        String randStr = randomizeNum(EcardConstant.CARDID_LEN, EcardConstant.randNum());
        String cardIdStr = String.format("%d%04d%s", cardType, moneyInt, randStr);
        cardId = Long.parseLong(cardIdStr);
        return cardId;
    }

    /**
     * 生成卡SN
     *
     * @param skuStr String
     * @return String
     */
    public static String getSn(String skuStr) {
        long sku = Long.parseLong(skuStr);
        if (sku == 0) {
            return "";
        }
        String randStr = randomizeNum(EcardConstant.SN_LEN, EcardConstant.randNum());
        return String.format("%d/%s", sku, randStr);
    }

    /**
     * 生成卡密
     *
     * @return String
     */
    public static Map<String,String> getSecretPassword() {
        Map<String,String> map = new HashMap<>();
        String passStr = randomizeNum(EcardConstant.PASSWORD_LEN, EcardConstant.randNum());
        int sign = verifyFormula(passStr);
        String password = String.format("%s%d", passStr, sign);
        String passwordIdx = DigestUtils.md5Hex(password);
        map.put("passwordIdx",passwordIdx);
        String passwordKeyCenter;
        try {
            passwordKeyCenter = KeyCenterUtil.encrypt(password, sid);
        } catch (Exception e) {
            log.warn("keycenter密码加密错误, sid={}",sid);
            throw new BaseException(-1, "keycenter密码加密错误");
        }
        map.put("password",passwordKeyCenter);
        return map;
    }

    /**
     * 生成指定位数的随机数
     *
     * @param length int
     * @param numSet int[]
     * @return String
     */
    public static String randomizeNum(int length, int[] numSet) {
        int lenStr = numSet.length;
        StringBuilder str = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int idx = rand.nextInt(lenStr);
            str.append(numSet[idx]);
        }
        return str.toString();
    }

    /**
     * 生成校验位公式
     *
     * @param str String
     * @return int
     */
    public static int verifyFormula(String str) {
        String[] strs = str.split("");
        int[] strInt = Arrays.stream(strs).mapToInt(Integer::parseInt).toArray();
        if (strInt.length < EcardConstant.CARDID_LEN) {
            return 0;
        }
        return ((strInt[2] - '0') + (strInt[0] - '0') * 2 + (strInt[1] - '0') + (strInt[5] - '0') * 2) % 10;
    }

}
