package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.redpacket.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;


/**
 * 单个红包缓存信息
 *
 * <AUTHOR>
 */
@Data
public class RedPacketCachePo implements Serializable {

    private static final long serialVersionUID = 6679580456127622193L;

    @SerializedName("redpacket_id")
    private Long redPacketId;

    @SerializedName("user_id")
    private Long userId;

    @SerializedName("type_id")
    private Integer typeId;

    @SerializedName("uniq_id")
    private String uniqId;

    @SerializedName("start_time")
    private Long startTime;

    @SerializedName("end_time")
    private Long endTime;

    @SerializedName("amount")
    private Long amount;

    @SerializedName("balance")
    private Long balance;

    @SerializedName("add_time")
    private Long addTime;

    @SerializedName("update_time")
    private Long updateTime;
}

