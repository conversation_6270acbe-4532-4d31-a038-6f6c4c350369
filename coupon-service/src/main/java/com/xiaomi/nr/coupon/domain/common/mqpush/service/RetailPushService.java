package com.xiaomi.nr.coupon.domain.common.mqpush.service;

import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.common.model.MessageBuildContext;
import com.xiaomi.nr.coupon.domain.common.mqpush.CouponSendSceneEnum;
import com.xiaomi.nr.coupon.domain.common.mqpush.CouponUsePushFactory;
import com.xiaomi.nr.coupon.domain.common.mqpush.CouponUsePushService;
import com.xiaomi.nr.coupon.infrastructure.repository.mq.CouponUsePushMqProducer;
import com.xiaomi.nr.coupon.infrastructure.repository.mq.convert.CouponUseMessageConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-01-08 20:28
 */
@Slf4j
@Component
public class RetailPushService implements CouponUsePushService {

    private final Set<String> sceneSet = new HashSet<>();

    @Autowired
    private CouponUsePushMqProducer couponUsePushMqProducer;

    @Autowired
    private CouponUseMessageConvert messageConvert;

    @PostConstruct
    public void init() {
        List<String> sceneCodes = Arrays.stream(CouponSendSceneEnum.values())
                .filter(code -> Objects.equals(code.getBizPlatform(), BizPlatformEnum.RETAIL.getCode()))
                .map(CouponSendSceneEnum::getCode)
                .collect(Collectors.toList());
        sceneSet.addAll(sceneCodes);
        CouponUsePushFactory.register(getBizType(), this);
    }

    @Override
    public void sendMessage(Message<?> message, Long couponId, String sceneCode) {
        if (!sceneSet.contains(sceneCode)) {
            return;
        }
        couponUsePushMqProducer.sendMessage(message, couponId, StringUtils.EMPTY);
    }

    @Override
    public Message<?> buildMessage(MessageBuildContext context) {
        return messageConvert.buildCouponUseMessage(context.getCouponUsePushContext(), context.getCouponPo(), context.getSceneCode(), context.getProfile());
    }

    @Override
    public Set<String> getSceneSet() {
        return sceneSet;
    }

    @Override
    public BizPlatformEnum getBizType() {
        return BizPlatformEnum.RETAIL;
    }
}
