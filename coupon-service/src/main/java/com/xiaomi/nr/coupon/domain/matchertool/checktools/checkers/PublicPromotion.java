package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import com.xiaomi.nr.coupon.constant.CommonConstant;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigInfo;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import org.springframework.stereotype.Component;

/**
 * 公开推广校验
 *
 * <AUTHOR>
 * @date 2024/9/9
 */
@Component
public class PublicPromotion extends CheckerAbstract {
    /**
     * 校验
     *
     * @param ctx      MatcherContextDo
     * @param respItem MatcherRespItemDo
     * @param errCtx   MatcherErrContextDo
     * @return boolean
     */
    @Override
    protected boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {

        if (!ctx.isCheckPublicPromotion()) {
            return true;
        }

        errCtx.setInvalidConfig(true);

        CouponConfigItem configItem = ctx.getConfigInfoMap().get(respItem.getConfigId());
        CouponConfigInfo config = configItem.getCouponConfigInfo();

        if (config.getPublicPromotion() == CommonConstant.ONE_INT) {
            return true;
        }

        errCtx.setErrCode(ErrCode.NOT_PUBLIC_PROMOTION.getCode());
        errCtx.setErrMsg("非公开推广券");
        return false;
    }
}
