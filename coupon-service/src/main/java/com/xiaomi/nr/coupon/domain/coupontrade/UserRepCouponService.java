package com.xiaomi.nr.coupon.domain.coupontrade;

import com.xiaomi.nr.coupon.api.dto.coupon.CouponBaseInfo;
import com.xiaomi.nr.coupon.api.dto.trade.*;
import com.xiaomi.nr.coupon.domain.common.UserCouponPush;
import com.xiaomi.nr.coupon.domain.common.model.CouponUsePushContext;
import com.xiaomi.nr.coupon.domain.coupontrade.tradecheck.CouponInfoCheck;
import com.xiaomi.nr.coupon.domain.coupontrade.convert.CouponBaseInfoConvert;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponConvert;
import com.xiaomi.nr.coupon.domain.coupon.model.CheckoutCouponModel;
import com.xiaomi.nr.coupon.domain.coupon.model.ErrContext;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.enums.coupon.CouponPushStatusEnum;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.coupon.SubmitTypeEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.ModeTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.UserCouponLogRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.UserRepCouponRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave.CouponInfoMapper;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class UserRepCouponService extends UserCouponService {

    @Resource
    private CouponInfoMapper couponInfoMapper;

    @Resource
    private UserRepCouponRepository userRepCouponRepository;

    @Resource
    private UserCouponLogRepository userCouponLogRepository;

    @Resource
    private CouponConfigRepository couponConfigRepository;

    @Resource
    private CouponInfoCheck couponInfoCheck;

    @Resource
    private UserCouponPush userCouponPush;

    @Resource
    private CouponConvert couponConvert;

    @Resource
    private CouponBaseInfoConvert couponBaseInfoConvert;


    /**
     * 优惠券结算
     *
     * @param request 请求
     * @return CheckoutCouponResponse
     */
    @Override
    public CheckoutCouponResModel checkoutCoupon(CheckoutCouponReqModel request) throws BizError {
        final long userId = request.getUserId();
        final long usedCouponId = Optional.ofNullable(request.getUsedCoupon()).orElse(0L);
        List<Long> couponIds = Objects.isNull(request.getCouponIds()) ? Collections.emptyList() : request.getCouponIds();
        List<CouponPo> coupons = getCouponPoInfo(userId, couponIds, usedCouponId, request.getBizPlatform());
        List<Long> configIds = coupons.stream().map(CouponPo::getTypeId).distinct().collect(Collectors.toList());
        Map<Long, CouponConfigItem> configMap = couponConfigRepository.getCouponConfigs(configIds);
        Integer bizPlatform = request.getBizPlatform();

        // 每张券的处理
        Map<Long, CheckoutCouponInfo> resData = new HashMap<>(coupons.size());
        for (CouponPo couponPo : coupons) {
            try {
                CheckoutCouponInfo checkoutCouponInfo = new CheckoutCouponInfo();

                CouponConfigItem config = configMap.get(couponPo.getTypeId());
                if (Objects.isNull(config) || Objects.isNull(config.getConfigId()) || config.getConfigId() <= 0 || Objects.isNull(config.getCouponConfigInfo())) {
                    checkoutCouponInfo.setValidCode(ErrCode.USE_COUPON_LOSE_EFFICACY.getCode());
                    checkoutCouponInfo.setInvalidReason("无法获取有效的优惠券配置信息");
                    resData.put(couponPo.getId(), checkoutCouponInfo);
                    continue;
                }

                if (Objects.isNull(bizPlatform) || !bizPlatform.equals(config.getBizPlatform())) {
                    continue;
                }

                //　基本信息
                CouponBaseInfo baseInfo = couponBaseInfoConvert.convertNoCodeCouponPoConfig2BaseInfo(couponPo, config);
                checkoutCouponInfo.setCouponBaseInfo(baseInfo);
                checkoutCouponInfo.setCouponGroupNo(couponBaseInfoConvert.genCouponGroupNo(config));
                ErrContext errContext = new ErrContext();

                // 特殊校验
                boolean usedCouponCheck = request.isUsedCouponCheck();
                if (request.getSubmitType() == SubmitTypeEnum.REPLACE.getCode() && usedCouponId == couponPo.getId()) {
                    usedCouponCheck = true;
                }

                if (!couponInfoCheck.specialConditionCheckerNoCode(baseInfo, errContext, usedCouponCheck, ModeTypeEnum.NoCode.getMysqlValue())) {
                    checkoutCouponInfo.setValidCode(errContext.getErrorCode());
                    checkoutCouponInfo.setInvalidReason(errContext.getErrorMsg());
                    checkoutCouponInfo.setInvalidData(errContext.getErrorData());
                } else {
                    String couponOrgCode = couponInfoCheck.getCouponOrgCode(couponPo.getExtendInfo());
                    CheckoutCouponModel checkoutModel = couponConvert.convertCheckoutRequestToModel(request);

                    // 通用检查劵配置的条件
                    if (!couponInfoCheck.checkCondition(couponOrgCode, config, checkoutModel, errContext, ModeTypeEnum.NoCode.getMysqlValue())) {
                        checkoutCouponInfo.setValidCode(errContext.getErrorCode());
                        checkoutCouponInfo.setInvalidReason(errContext.getErrorMsg());
                        checkoutCouponInfo.setInvalidData(errContext.getErrorData());
                    } else {
                        checkoutCouponInfo.setValidCode(0);
                        checkoutCouponInfo.setValidSkuList(couponBaseInfoConvert.getValidSkuList(config, request.getSkuPackageList()));
                        checkoutCouponInfo.setValidPackageList(couponBaseInfoConvert.getValidPackageList(config, request.getSkuPackageList()));
                        checkoutCouponInfo.setValidSsuList(couponBaseInfoConvert.getValidSsuList(config, request.getSkuPackageList()));
                    }
                }
                resData.put(couponPo.getId(), checkoutCouponInfo);
            } catch (Exception e) {
                log.error("userRepCouponService.checkoutCoupon, make checkout coupon info err. userId:{}, couponId:{}", userId, couponPo.getId(), e);
            }
        }

        // 判断优惠券是否存在
        for (Long couponId : couponIds) {
            if (MapUtils.isEmpty(resData) || !resData.containsKey(couponId)) {
                CheckoutCouponInfo checkoutCouponInfo = new CheckoutCouponInfo();
                checkoutCouponInfo.setValidCode(ErrCode.USE_COUPON_NOT_FOUND.getCode());
                checkoutCouponInfo.setInvalidReason("优惠券不存在");
                resData.put(couponId, checkoutCouponInfo);
            }
        }

        CheckoutCouponResModel result = new CheckoutCouponResModel();
        result.setNoCodeCoupons(resData);
        return result;
    }

    /**
     * 优惠券锁定
     */
    @Override
    public LockCouponResponse lockUserCoupon(LockCouponRequest request) throws BizError {
        log.info("userRepCouponService.lockUserCoupon request:{}", request);
        LockCouponResponse response = new LockCouponResponse();

        final long userId = request.getUserId();
        final long orderId = request.getOrderId();
        final long usedCoupon = Optional.ofNullable(request.getUsedCoupon()).orElse(0L);
        List<CouponLockItem> couponItems = request.getCouponItems();

        // 改配单无券可用
        if (CollectionUtils.isEmpty(couponItems)) {
            if (usedCoupon > 0) {
                CouponPo couponPo = userRepCouponRepository.getCouponInfo(userId, usedCoupon);
                checkUsedCoupon(orderId, couponPo);
                response.setIdempotent(true);
                return response;
            }
        }

        List<Long> couponIds = couponItems.stream().map(CouponLockItem::getCouponId).collect(Collectors.toList());
        List<CouponPo> couponPos = userRepCouponRepository.getCouponPoList(userId, couponIds);
        if (couponListLockCheck(couponPos, usedCoupon, orderId)) {
            response.setIdempotent(true);
            return response;
        }

        String curStatus = CouponStatusEnum.UNUSED.getValue();
        CouponPo couponPo = couponPos.get(0);
        if (couponPo.getId() == usedCoupon) {
            curStatus = CouponStatusEnum.USED.getValue();
        }

        try {
            // 锁券
            userRepCouponRepository.lockCoupon(userId, orderId, couponItems, curStatus, request.getOffline());
        } catch (BizError bizError) {
            log.error("userRepCouponService.lockUserCoupon bizError. request:{}", request, bizError);
            throw bizError;
        } catch (Exception e) {
            log.error("userRepCouponService.lockUserCoupon Exception. request:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "锁定优惠券失败", e);
        }
        return response;
    }

    /**
     * 优惠券使用
     */
    @Override
    public ConsumeCouponResponse consumeUserCoupon(ConsumeCouponRequest request) throws BizError {
        log.info("userRepCouponService.consumeUserCoupon request:{}", request);
        ConsumeCouponResponse response = new ConsumeCouponResponse();

        final long userId = request.getUserId();
        List<Long> couponIds = request.getCouponIds();
        final long orderId = request.getOrderId();
        final long usedCoupon = request.getUsedCoupon();

        // 原订单用券，改配单无券可用
        if(CollectionUtils.isEmpty(couponIds)){
            CouponPo couponPo = userRepCouponRepository.getCouponInfo(userId, usedCoupon);
            if(CouponStatusEnum.UNUSED.getValue().equals(couponPo.getStat())){
                response.setIdempotent(true);
                return response;
            }
            checkUsedCoupon(orderId, couponPo);
            try {
                userRepCouponRepository.consumeCoupon(userId, couponIds, usedCoupon, orderId);
                userCouponLogRepository.insertRollbackCouponLog(userId, orderId, Lists.newArrayList(couponPo), request.getOffline(), request.getClientId());
            } catch (BizError bizError) {
                log.error("userRepCouponService.consumeUserCoupon bizError request:{}", request, bizError);
                throw bizError;
            } catch (Exception e) {
                log.error("userRepCouponService.consumeUserCoupon Exception request:{}", request, e);
                throw ExceptionHelper.create(GeneralCodes.InternalError, "使用优惠券失败", e);
            }
            return response;
        }

        if (CollectionUtils.isEmpty(couponIds)) {
            log.error("userRepCouponService.consumeUserCoupon userId:{},couponId:{},orderId:{},err:{}", userId, couponIds, orderId, "用户信息或订单信息不正确");
            throw ExceptionHelper.create(GeneralCodes.ParamError, "用户信息或优惠券信息不正确");
        }

        List<CouponPo> couponPos = userRepCouponRepository.getCouponPoList(userId, couponIds);
        if (couponListConsumeCheck(couponPos, orderId)) {
            response.setIdempotent(true);
            return response;
        }

        try {
            // 核销券
            userRepCouponRepository.consumeCoupon(userId, couponIds, usedCoupon, orderId);
            //记录日志
            userCouponLogRepository.insertConsumeCouponLog(userId, orderId, couponPos, request.getOffline(), request.getClientId());
            //发使用消息
            CouponUsePushContext couponUsePushContext = new CouponUsePushContext(
                    request.getUserId(),
                    request.getVid(),
                    couponPos,
                    CouponPushStatusEnum.CONSUME.getCode(),
                    TimeUtil.getNowUnixMillis(),
                    request.getOrderId()
            );
            userCouponPush.couponUsePush(couponUsePushContext);

        } catch (BizError bizError) {
            log.error("userRepCouponService.consumeUserCoupon bizError request:{}", request, bizError);
            throw bizError;
        } catch (Exception e) {
            log.error("userRepCouponService.consumeUserCoupon Exception request:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "使用优惠券失败", e);
        }
        return response;
    }

    /**
     * 优惠券锁券
     */
    @Override
    public RollbackCouponResponse rollbackUserCoupon(RollbackCouponRequest request) throws BizError {
        log.info("userRepCouponService.rollbackUserCoupon request:{}", request);
        RollbackCouponResponse response = new RollbackCouponResponse();

        final long userId = request.getUserId();
        final List<Long> couponIds = request.getCouponIds();
        final long orderId = request.getOrderId();
        final long usedCoupon = request.getUsedCoupon();

        if(CollectionUtils.isEmpty(couponIds)){
            CouponPo couponPo = userRepCouponRepository.getCouponInfo(userId, usedCoupon);
            Boolean idempotent = rollbackUsedCoupon(request, userId, orderId, usedCoupon, couponPo);
            response.setIdempotent(idempotent);
            return response;
        }

        //校验入参
        if (CollectionUtils.isEmpty(couponIds)) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "用户信息或优惠券信息不正确");
        }

        List<CouponPo> couponPos = userRepCouponRepository.getCouponPoList(userId, couponIds);

        CouponPo couponPo = couponPos.get(0);
        if (couponPo == null) {
            throw ExceptionHelper.create(ErrCode.USE_COUPON_NOT_FOUND, "优惠券不存在");
        }
        if (couponPo.getId() == usedCoupon) {
            Boolean idempotent = rollbackUsedCoupon(request, userId, orderId, usedCoupon, couponPo);
            response.setIdempotent(idempotent);
            return response;
        }

        if (couponListRollBackCheck(couponPos, orderId)) {
            response.setIdempotent(true);
            return response;
        }
        String currentStatus = couponPos.get(0).getStat();
        CouponPo usedCouponPo = userRepCouponRepository.getCouponInfo(userId, usedCoupon);
        try {
            boolean result = userRepCouponRepository.returnCoupon(userId, couponIds, usedCoupon, orderId, request.getOffline(), currentStatus, usedCouponPo.getStat());
            if (result) {
                //记录日志
                userCouponLogRepository.insertRollbackCouponLog(userId, orderId, couponPos, request.getOffline(), request.getClientId());
                //发消息
                CouponUsePushContext couponUsePushContext = new CouponUsePushContext(
                        request.getUserId(),
                        request.getVid(),
                        couponPos,
                        CouponPushStatusEnum.RETURN.getCode(),
                        TimeUtil.getNowUnixMillis(),
                        request.getOrderId()
                );
                userCouponPush.couponUsePush(couponUsePushContext);
            } else if (!CouponStatusEnum.UNUSED.getValue().equals(currentStatus)) { // 如果当前为可用状态，不抛异常
                throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券信息不正确");
            }
        } catch (BizError bizError) {
            log.error("userRepCouponService.rollbackUserCoupon bizError request:{}", request, bizError);
            throw bizError;
        } catch (Exception e) {
            log.error("userRepCouponService.rollbackUserCoupon Exception request:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "退还优惠券失败", e);
        }
        return response;
    }

    private Boolean rollbackUsedCoupon(RollbackCouponRequest request, long userId, long orderId, long usedCoupon, CouponPo couponPo) throws BizError {
        if(CouponStatusEnum.USED.getValue().equals(couponPo.getStat()) && couponPo.getOrderId() == orderId){
            return true;
        }
        if (couponPo.getOrderId() != orderId) {
            throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券和订单不匹配");
        }
        if (!CouponStatusEnum.UNUSED.getValue().equals(couponPo.getStat()) && !CouponStatusEnum.LOCKED.getValue().equals(couponPo.getStat())) {
            throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券状态不匹配");
        }
        try {
            userRepCouponRepository.returnCoupon(userId, null, usedCoupon, orderId, request.getOffline(), null, couponPo.getStat());
        } catch (BizError bizError) {
            log.error("userRepCouponService.consumeUserCoupon bizError request:{}", request, bizError);
            throw bizError;
        } catch (Exception e) {
            log.error("userRepCouponService.consumeUserCoupon Exception request:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "使用优惠券失败", e);
        }
        return false;
    }


    private Boolean checkUsedCoupon(long orderId, CouponPo couponPo) throws BizError {
        if (!CouponStatusEnum.USED.getValue().equals(couponPo.getStat())) {
            throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券状态不匹配");
        }
        if (couponPo.getOrderId() != orderId) {
            throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券和订单不匹配");
        }
        return false;
    }


    /**
     * 批量锁定优惠券校验
     *
     * @param couponPos
     * @param orderId
     * @return
     * @throws BizError
     */
    private boolean couponListLockCheck(List<CouponPo> couponPos, long usedCoupon, long orderId) throws BizError {
        long timeNow = TimeUtil.getNowUnixSecond();
        int isIdempotentCnt = 0;
        for (CouponPo couponPo : couponPos) {
            if (couponPo == null) {
                throw ExceptionHelper.create(ErrCode.USE_COUPON_NOT_FOUND, "优惠券不存在");
            }
            if (Long.parseLong(couponPo.getEndTime()) < timeNow || Long.parseLong(couponPo.getStartTime()) > timeNow) {
                throw ExceptionHelper.create(ErrCode.USE_COUPON_TIME_INVALID, "优惠券未在有效期内");
            }
            if (CouponStatusEnum.LOCKED.getValue().equals(couponPo.getStat()) && couponPo.getOrderId() == orderId) {
                isIdempotentCnt++;
                continue;
            }
            if (couponPo.getId() == usedCoupon) {
                if (!CouponStatusEnum.USED.getValue().equals(couponPo.getStat())) {
                    throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券状态不匹配");
                }
                if (couponPo.getOrderId() != orderId) {
                    throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券和订单不匹配");
                }
            } else {
                if (!CouponStatusEnum.UNUSED.getValue().equals(couponPo.getStat())) {
                    throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券不可用");
                }
            }
        }
        return isIdempotentCnt == couponPos.size();
    }

    /**
     * 批量核销优惠券校验
     *
     * @param couponPos
     * @param orderId
     * @return
     * @throws BizError
     */
    private boolean couponListConsumeCheck(List<CouponPo> couponPos, long orderId) throws BizError {

        int isIdempotentCnt = 0;
        for (CouponPo couponPo : couponPos) {
            // 锁定是新券和老券不同，核销是新券老券相同则会被该地方幂等，有问题 需注意
            if (CouponStatusEnum.USED.getValue().equals(couponPo.getStat()) && couponPo.getOrderId() == orderId) {
                isIdempotentCnt++;
                continue;
            }
            if (!CouponStatusEnum.LOCKED.getValue().equals(couponPo.getStat())) {
                throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券状态不正确");
            }
        }
        return isIdempotentCnt == couponPos.size();
    }

    /**
     * 批量退还优惠券校验
     *
     * @param couponPos
     * @return
     * @throws BizError
     */
    private boolean couponListRollBackCheck(List<CouponPo> couponPos, long orderId) throws BizError {
        int isIdempotentCnt = 0;
        for (CouponPo couponPo : couponPos) {
            if (CouponStatusEnum.UNUSED.getValue().equals(couponPo.getStat())) {
                if (couponPo.getOrderId() == orderId) {
                    isIdempotentCnt++;
                }
                continue;
            }
            if (!CouponStatusEnum.LOCKED.getValue().equals(couponPo.getStat()) && !couponPo.getStat().equals(CouponStatusEnum.USED.getValue())) {
                throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券状态不正确");
            }
        }
        return isIdempotentCnt == couponPos.size();
    }

    /**
     * 获取券数据
     *
     * @param userId    用户ID
     * @param couponIds 券ID
     * @return List<CouponPo>
     */
    private List<CouponPo> getCouponPoInfo(Long userId, List<Long> couponIds, Long usedCoupon, Integer bizPlatform) throws BizError {
        List<CouponPo> coupons;
        // 当不传券ID的时候，获取用户所有未过期且未使用的劵
        if (CollectionUtils.isEmpty(couponIds)) {
            coupons = couponInfoMapper.getAllCouponForUnused(userId, TimeUtil.getNowUnixSecond(), Lists.newArrayList(bizPlatform));
        } else {
            coupons = userRepCouponRepository.getCouponInfos(userId, couponIds);
        }
        if (usedCoupon > 0) {
            CouponPo couponPo = userRepCouponRepository.getCouponInfo(userId, usedCoupon);
            coupons.add(couponPo);
        }
        return coupons;
    }

}
