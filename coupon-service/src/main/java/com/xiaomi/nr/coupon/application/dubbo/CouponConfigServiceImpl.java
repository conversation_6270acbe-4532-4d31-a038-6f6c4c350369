package com.xiaomi.nr.coupon.application.dubbo;

import com.google.common.base.Stopwatch;
import com.xiaomi.nr.coupon.api.dto.couponconfig.*;
import com.xiaomi.nr.coupon.api.service.CouponConfigService;
import com.xiaomi.nr.coupon.domain.couponconfig.CouponConfig;
import com.xiaomi.nr.coupon.domain.couponconfig.GoodsCouponConfigService;
import com.xiaomi.nr.coupon.domain.couponconfig.RelationGoods;
import com.xiaomi.nr.coupon.domain.couponconfig.ValidCouponConfigService;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service(timeout = 2000, group = "${dubbo.group}", version = "1.0", delay = 10000)
@Component
@Slf4j
public class CouponConfigServiceImpl implements CouponConfigService {


    @Autowired
    private RelationGoods relationGoods;

    @Resource
    private ValidCouponConfigService validCouponConfig;

    @Resource
    private CouponConfig couponConfigService;

    @Resource
    private GoodsCouponConfigService goodsCouponConfigService;


    /**
     * 根据优惠券配置ID获取可用的货品或套装列表
     *
     * @param request CouponConfigRelationGoodsRequest
     * @return CouponConfigRelationGoodsResponse
     */
    @Override
    public Result<CouponConfigRelationGoodsResponse> getGoodsList(CouponConfigRelationGoodsRequest request) {
        try {
            log.info("CouponConfigService getGoodsList request:{}",request);
            CouponConfigRelationGoodsResponse response = relationGoods.getGoodsList(request.getTypeId());
            return Result.success(response);
        } catch (BizError e) {
            log.info("couponConfig.goods.relation, goodsList fail, type_id={}", request.getTypeId());
            return Result.fromException(e);
        } catch (Exception e) {
            log.error("couponConfig.goods.relation, goodsList error, type_id={}, err={}", request.getTypeId(), e);
            return Result.fromException(e);
        }
    }



    /**
     * 根据SKU/套装ID获取可用的优惠券配置列表
     *
     * @param request GoodsRelationCouponConfigRequest
     * @return GoodsRelationCouponConfigResponse
     */
    @Override
    public Result<GoodsRelationCouponConfigResponse> getConfigList(@Valid GoodsRelationCouponConfigRequest request) {
        try {
            GoodsRelationCouponConfigResponse response = relationGoods.getCouponConfigList(request.getId(), request.getLevel());
            return Result.success(response);
        } catch (BizError e) {
            log.info("couponConfig.goods.relation, couponConfigList fail, id={}, level={}", request.getId(), request.getLevel());
            return Result.fromException(e);
        } catch (Exception e) {
            log.error("couponConfig.goods.relation, couponConfigList error, id={}, level={}", request.getId(), request.getLevel(), e);
            return Result.fromException(e);
        }
    }

    @Override
    public Result<GoodsCouponConfigRelResponse> getGoodsCouponConfigRel(@Valid GoodsCouponConfigRelRequest request) {
        try {
            GoodsCouponConfigRelResponse goodsCouponConfigIds = relationGoods.getGoodsCouponConfigIds(request.getGoodsItems(), request.getConfigIds(), request.isWithFuture());
            return Result.success(goodsCouponConfigIds);
        } catch (BizError e) {
            log.warn("CouponConfigService.getGoodsCouponConfigRel fail, configIds:{}", request.getConfigIds());
            return Result.fromException(e);
        } catch (Exception e) {
            log.error("CouponConfigService.getGoodsCouponConfigRel error, configIds:{}", request.getConfigIds(), e);
            return Result.fromException(e);
        }
    }

    @Override
    public Result<CouponConfigGoodsRelResponse> getCouponConfigGoodsRel(@Valid CouponConfigGoodsRelRequest request) {
        CouponConfigGoodsRelResponse response = new CouponConfigGoodsRelResponse();
        try {
            Map<Long, List<GoodsItem>> couponConfigGoodsRel = relationGoods.getCouponConfigGoodsRel(request.getConfigIds(), request.isWithSku());
            response.setCouponGoods(couponConfigGoodsRel);
            return Result.success(response);
        } catch (BizError e) {
            log.warn("CouponConfigService.getCouponConfigGoodsRel fail, configIds:{}", request.getConfigIds());
            return Result.fromException(e);
        } catch (Exception e) {
            log.error("CouponConfigService.getCouponConfigGoodsRel error, configIds:{}", request.getConfigIds(), e);
            return Result.fromException(e);
        }
    }


    /**
     * 根据SKU/套装ID、发放渠道查询有效券配置信息列表接口
     *
     * @param request 请求参数信息
     * @return List<> 券配置信息列表
     */
    @Override
    @Deprecated
    public Result<List<CouponTypeInfoListResponse>> getValidConfigList(@Valid CouponTypeInfoListRequest request) {
        List<GoodsItem> goodsItemList = request.getGoodsItemList();
        String sendChannel = request.getSendChannel();

        //参数校验
        if (StringUtils.isEmpty(sendChannel)) {
            log.info("coupon.CouponConfigService, getValidConfigList fail paramError,sendChannel={}",sendChannel);
            return Result.fail(GeneralCodes.ParamError, "参数sendChannel券发放渠道不能为空");
        }

        if (CollectionUtils.isEmpty(goodsItemList)) {
            log.info("coupon.CouponConfigService, getValidConfigList fail paramError, goodsTtem={}", goodsItemList);
            return Result.fail(GeneralCodes.ParamError, "参数SKU/套装id不能为空");
        }

        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            List<CouponTypeInfoListResponse> responseList = validCouponConfig.getCouponTypeInfo(sendChannel, goodsItemList);
            log.info("coupon.CouponConfigService.getValidConfigList() execute success, request={}, runTime={}ms",request , stopwatch.elapsed(TimeUnit.MILLISECONDS));
            stopwatch.stop();
            return Result.success(responseList);

        } catch (BizError e) {
            log.warn("coupon.CouponConfigService, getValidConfigList BizError,fail reason={}", e.getMessage(),e);
            return Result.fromException(e, e.getMessage());

        } catch (Exception e) {
            log.warn("coupon.CouponConfigService, getValidConfigList Exception,error reason={}", e.getMessage(), e);
            return Result.fromException(e, e.getMessage());
        }
    }



    /**
     * 根据券配置ID列表,查询券配置息信息列表
     *
     * @param request 券配置ID列表
     * @return List<> 券配置信息列表
     */
    @Override
    public Result<List<CouponTypeInfoDto>> getCouponConfigInfoList(@Valid CouponConfigInfoRequest request) {
        List<Long> configIdList = request.getConfigIdList();
        if (CollectionUtils.isEmpty(configIdList)) {
            return Result.fail(GeneralCodes.ParamError, "参数configIdList:券配置ID列表为空");
        }
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            List<CouponTypeInfoDto> responseList = validCouponConfig.getCouponTypeInfoById(configIdList, request.isWithProducts());
            stopwatch.stop();
            return Result.success(responseList);
        } catch (BizError e) {
            log.error("coupon.CouponConfigService, getCouponConfigInfoList BizError, fail request:{},reason:{}", request, e.getMessage());
            return Result.fromException(e);

        } catch (Exception e) {
            log.error("coupon.CouponConfigService, getCouponConfigInfoList Exception,error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 根据券配置ID查询券配置发放剩余量(批量)
     *
     * @param request 券配置ID列表
     * @return List<> 剩余量列表
     */
    @Override
    public Result<Map<Integer, CouponConfigSurplusDto>> getCouponConfigSurplus(@Valid CouponConfigSurplusRequest request) {
        if (request == null) {
            log.warn("couponConfig.getCouponConfigSurplus, 参数不符合要求, request={}", (Object) null);
            return Result.fail(GeneralCodes.ParamError, "参数不符合要求");
        }
        if (CollectionUtils.isEmpty(request.getConfigIds())) {
            log.warn("couponConfig.getCouponConfigSurplus, 参数优惠券配置ID不能为空, request={}", request);
            return Result.fail(GeneralCodes.ParamError, "参数优惠券配置ID不能为空");
        }

        Map<Integer, CouponConfigSurplusDto> response = new HashMap<>(request.getConfigIds().size());
        long runStartTime = TimeUtil.getNowUnixMillis();
        try {
            response = couponConfigService.getSendSurplus(request.getConfigIds());
            log.info("couponConfig.getCouponConfigSurplus, 根据券配置ID查询券配置发放剩余量, configIds={}, response={}, runTime={}ms", request.getConfigIds(), response, TimeUtil.sinceMillis(runStartTime));
            return Result.success(response);
        } catch (Exception e) {
            log.error("couponConfig.getCouponConfigSurplus, 根据券配置ID查询券配置发放剩余量出错, configIds={}, class={}, errMsg={}, runTime={}ms", request.getConfigIds(), e.getClass(), e.getMessage(), TimeUtil.sinceMillis(runStartTime));
            return Result.success(response);
        }
    }

    @Override
    public Result<ValidConfigListResponse> getValidConfigListV2(@Valid ValidConfigListRequest request) {

        if(CollectionUtils.isEmpty(request.getGoodsItemList())){
            return Result.fail(GeneralCodes.ParamError, "参数商品列表不能为空");
        }
        if(StringUtils.isEmpty(request.getSceneCode())){
            return Result.fail(GeneralCodes.ParamError, "参数投放场景不能为空");
        }

        ValidConfigListResponse response;
        try{
            log.info("couponConfig.getValidConfigListV2, request={}", request);
            response = goodsCouponConfigService.getGoodsValidCoupon(request);
            return Result.success(response);
        }catch (Exception e){
            log.warn("coupon.CouponConfigService, getValidConfigListV2 Exception,request={}, error reason={}",request , e.getMessage(), e);
            return Result.fromException(e);
        }
    }
}
