package com.xiaomi.nr.coupon.enums.couponcode;

/**
 * 券码使用方式 枚举
 *
 * <AUTHOR>
 */
public enum UseModeEnum {

    /**
     * 明码结算
     */
    CHECKOUT(1,"明码结算"),

    /**
     * 兑换
     */
    EXCHANGE(2, "兑换");

    private final Integer value;
    private final String name;

    UseModeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static String findNameByValue(Integer value) {
        UseModeEnum[] values = UseModeEnum.values();
        for (UseModeEnum item : values) {
            if (item.getValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }
}

