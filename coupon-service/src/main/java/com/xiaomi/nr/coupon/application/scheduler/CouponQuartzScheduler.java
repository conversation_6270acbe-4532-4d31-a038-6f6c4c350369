package com.xiaomi.nr.coupon.application.scheduler;

import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * @description: 优惠券场景定时任务
 * @author: hejiapeng
 * @Date 2022/2/26 8:44 下午
 * @Version: 1.0
 **/
@Slf4j
@Order(1)
@Component
public class CouponQuartzScheduler implements ApplicationRunner {

    private Lock configPoolUpdateLock = new ReentrantLock();

    private Lock couponSceneLock = new ReentrantLock();

    private Lock serviceSceneLock = new ReentrantLock();

    @Resource
    private CouponConfigRepository couponConfigRepository;

    /**
     * 全量刷新优惠券配置
     */
    @Scheduled(fixedDelay = 1000 * 900, initialDelay = 1000 * 900)
    public void loadCouponCacheToCache() {
        boolean lockOk = false;
        long runStartTime = TimeUtil.getNowUnixMillis();
        try {
            lockOk = configPoolUpdateLock.tryLock(1, TimeUnit.SECONDS);
            if (!lockOk) {
                log.info("try configPoolUpdateLock fail,wait next scheduling");
                return;
            }
            couponConfigRepository.loadAllCouponConfigToCache();
            log.info("CouponQuartzScheduler.loadCouponCacheToCache, 刷新券配置任务结束, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));
        } catch (Exception e) {
            log.error("CouponQuartzScheduler.loadCouponCacheToCache, 刷新券配置失败, runTime={}毫秒, {}", TimeUtil.sinceMillis(runStartTime), e);
        } finally {
            if (lockOk) {
                configPoolUpdateLock.unlock();
            }
        }
    }

    /**
     * 全量刷新优惠券配置
     */
    //@Scheduled(fixedDelay = 60000 * 60, initialDelay = 60000 * 60)
    public void loadCouponGoodCacheToCache() {
        long runStartTime = TimeUtil.getNowUnixMillis();
        try {
            couponConfigRepository.loadCouponGoodsToCache();
            log.info("CouponQuartzScheduler.loadCouponGoodCacheToCache, 刷新券商品任务结束, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));
        } catch (Exception e) {
            log.error("CouponQuartzScheduler.loadCouponGoodCacheToCache, 刷新券商品投失败, runTime={}毫秒, {}", TimeUtil.sinceMillis(runStartTime), e);
        }
    }

    /**
     * 全量刷新券投放场景
     */
    @Scheduled(fixedDelay = 60000 * 5, initialDelay = 60000 * 5)
    public void loadCouponSceneCache() {
        boolean locked = false;
        long runStartTime = TimeUtil.getNowUnixMillis();
        try {
            locked = couponSceneLock.tryLock();
            if(!locked){
                log.info("try loadCouponSceneCache fail, wait next scheduling");
                return;
            }
            couponConfigRepository.refreshCouponSceneCache();
            log.info("CouponQuartzScheduler.loadCouponSceneCache, 刷新券配置投放场景任务结束, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));
        } catch (Exception e) {
            log.error("CouponQuartzScheduler.loadCouponSceneCache, 刷新券配置投放场景任务失败, runTime={}毫秒, {}", TimeUtil.sinceMillis(runStartTime), e);
        } finally {
            if(locked){
                couponSceneLock.unlock();
            }
        }
    }

    /**
     * 全量刷新券投放场景
     */
    @Scheduled(fixedDelay = 60000 * 5, initialDelay = 60000 * 5)
    public void loadServiceSceneCache() {
        boolean locked = false;
        long runStartTime = TimeUtil.getNowUnixMillis();
        try {
            locked = serviceSceneLock.tryLock();
            if(!locked){
                log.info("try loadServiceSceneCache fail, wait next scheduling");
                return;
            }
            couponConfigRepository.refreshServiceSceneCache();
            log.info("CouponQuartzScheduler.loadServiceSceneCache, 刷新券配置服务场景任务结束, runTime={}毫秒", TimeUtil.sinceMillis(runStartTime));
        } catch (Exception e) {
            log.error("CouponQuartzScheduler.loadServiceSceneCache, 刷新券配置服务场景任务失败, runTime={}毫秒, {}", TimeUtil.sinceMillis(runStartTime), e);
        } finally {
            if(locked){
                serviceSceneLock.unlock();
            }
        }
    }


    @Override
    public void run(ApplicationArguments args) throws Exception {
        loadCouponSceneCache();
        loadCouponCacheToCache();
        loadServiceSceneCache();
    }
}
