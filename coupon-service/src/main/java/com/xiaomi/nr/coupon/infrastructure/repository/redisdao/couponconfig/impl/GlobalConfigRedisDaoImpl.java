package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.xiaomi.nr.coupon.constant.CacheKeyConstant;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.GlobalConfigRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.po.CompareGoodsItem;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class GlobalConfigRedisDaoImpl implements GlobalConfigRedisDao {
    /**
     * 全局排除
     */
    private static final String KEY_GLOBAL_EXCLUDE = "global_exclude";
    /**
     * 活动全局排除
     */
    private static final String KEY_GLOBAL_EXCLUDE_ACT = "global_exclude_act";
    /**
     * 券全局排除
     */
    private static final String KEY_GLOBAL_EXCLUDE_COUPON = "global_exclude_coupon";
    /**
     * 全局排除本地缓存时间
     */
    private static final Long TTL_MINUTE_GLOBAL_EXCLUDE = 60L;
    /**
     * 全局排除本地缓存最大容量
     */
    private static final Long MAXIMUM_SIZE_GLOBAL_EXCLUDE = 3L;
    /**
     * 全局排除本地缓存. 缓存60分钟
     */
    private final Cache<String, CompareGoodsItem> globalExcludeCache = CacheBuilder.newBuilder().maximumSize(MAXIMUM_SIZE_GLOBAL_EXCLUDE)
            .expireAfterWrite(TTL_MINUTE_GLOBAL_EXCLUDE, TimeUnit.MINUTES).build();

    /**
     * 缓存操作对象
     */
    @Autowired
    @Qualifier("stringPulseTypeRedisTemplate")
    private StringRedisTemplate redisTemplate;

    /**
     * 获取全局排除商品
     *
     * @return 商品信息
     */
    @Override
    public CompareGoodsItem getGlobalInExclude() {
        CompareGoodsItem compareItem = globalExcludeCache.getIfPresent(KEY_GLOBAL_EXCLUDE);
        if (compareItem != null) {
            return compareItem;
        }

        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_GLOBAL_INEXCLUDE);
        String json = operations.get(key);
        compareItem = GsonUtil.fromJson(json, CompareGoodsItem.class);
        if (compareItem == null) {
            log.warn("globalInExclude is empty.");
            return null;
        }

        globalExcludeCache.put(KEY_GLOBAL_EXCLUDE, compareItem);
        return compareItem;
    }

    /**
     * 获取全局排除活动或券
     *
     * @return 商品信息
     */
    @Override
    public CompareGoodsItem getGlobalActInExclude() {
        CompareGoodsItem compareItem = globalExcludeCache.getIfPresent(KEY_GLOBAL_EXCLUDE_ACT);
        if (compareItem != null) {
            return compareItem;
        }

        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_GLOBAL_INEXCLUDE);
        String json = operations.get(key);
        compareItem = GsonUtil.fromJson(json, CompareGoodsItem.class);
        if (compareItem == null) {
            log.warn("globalActInExclude is empty.");
            return null;
        }

        globalExcludeCache.put(KEY_GLOBAL_EXCLUDE_ACT, compareItem);
        return compareItem;
    }

    /**
     * 获取全局排除活动或券
     *
     * @return 券信息
     */
    @Override
    public CompareGoodsItem getGlobalCouponInExclude() {
        CompareGoodsItem compareItem = globalExcludeCache.getIfPresent(KEY_GLOBAL_EXCLUDE_COUPON);
        if (compareItem != null) {
            return compareItem;
        }

        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_COUPON_GLOBAL_INEXCLUDE);
        String json = operations.get(key);
        compareItem = GsonUtil.fromJson(json, CompareGoodsItem.class);
        if (compareItem == null) {
            log.warn("globalCouponInExclude is empty.");
            return null;
        }
        globalExcludeCache.put(KEY_GLOBAL_EXCLUDE_COUPON, compareItem);
        return compareItem;
    }
}

