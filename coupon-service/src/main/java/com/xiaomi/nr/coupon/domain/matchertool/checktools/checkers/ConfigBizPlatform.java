package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 校验券配置的所属业务领域
 *
 * <AUTHOR>
 * @date 2024/1/3
 */
@Component
public class ConfigBizPlatform extends CheckerAbstract {

    @Override
    public boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {
        if (!ctx.isCheckBizPlatform()) {
            return true;
        }

        errCtx.setInvalidConfig(true);

        CouponConfigItem config = ctx.getConfigInfoMap().get(respItem.getConfigId());

        if (Objects.nonNull(config.getBizPlatform()) && CollectionUtils.isNotEmpty(ctx.getBizPlatform()) && ctx.getBizPlatform().contains(config.getBizPlatform())) {
            return true;
        }

        errCtx.setErrCode(ErrCode.COUPON_BIZ_PLATFORM_MISMATCH.getCode());
        errCtx.setErrMsg("非所属业务领域的券");
        return false;
    }
}
