package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 汽车专用券mapper（以vid为分库条件）
 *
 * <AUTHOR>
 */
@Mapper
@Repository
public interface CarCouponListMapper {

    String SELECT_PARAMS = "id, vid, biz_platform, user_id, type_id, activity_id, start_time, end_time,days, stat, order_id, use_time, " +
            "expire_time, is_pass, admin_id, admin_name,add_time, send_type, from_order_id, replace_money, invalid_time," +
            "last_update_time, offline, reduce_express, parent_id, send_channel, service_type, times_limit, used_times";

    /**
     * 获取vid未使用的券
     *
     * @param vid     .
     * @param stat    .
     * @param nowTime .
     * @return
     */
    @Select("<script>" +
            "select " + SELECT_PARAMS +
            " from tb_car_coupon " +
            " where vid=#{vid} and stat=#{stat} and end_time &gt; #{nowTime} " +
            " and biz_platform in <foreach collection='bizPlatformList' item='bizPlatform' index='index' open='(' close=')' separator=','>#{bizPlatform}</foreach>" +
            " order by add_time desc" +
            "</script>")
    List<CouponPo> getUserCoupon(@Param("vid") String vid, @Param("stat") String stat, @Param("nowTime") long nowTime, @Param("bizPlatformList") List<Integer> bizPlatformList);

    /**
     * 获取vid未使用的券
     *
     * @param vid
     * @param nowTime
     * @param bizPlatformList
     * @return
     */
    @Select("<script>" +
            "select " + SELECT_PARAMS +
            " from tb_car_coupon " +
            " where vid=#{vid} and stat='unused' and end_time &gt; #{nowTime} " +
            " and biz_platform in <foreach collection='bizPlatformList' item='bizPlatform' index='index' open='(' close=')' separator=','>#{bizPlatform}</foreach> " +
            " order by add_time desc" +
            "</script>")
    List<CouponPo> getAllCouponForUnused(@Param("vid") String vid, @Param("nowTime") long nowTime, @Param("bizPlatformList") List<Integer> bizPlatformList);

    /**
     * 获取优惠券信息
     *
     * @param vid       String
     * @param couponIds List<Long>
     * @return List<CouponPo>
     */
    @Select("<script>" +
            "select  " + SELECT_PARAMS +
            " from tb_car_coupon " +
            " where vid=#{vid} and id in <foreach collection='couponIds' item='id' index='index' open='(' close=')' separator=','>#{id}</foreach> " +
            "</script>")
    List<CouponPo> batchGetCouponInfo(@Param("vid") String vid, @Param("couponIds") List<Long> couponIds);

    /**
     * 查询该用户下状态 stat=unused 的优惠券列表
     *
     * @param vid         vid
     * @param nowTime     当前时间
     * @param bizPlatformList 业务领域列表
     * @param serviceTypeList 服务场景列表
     * @return List<>   券信息列表
     */
    @Select("<script>" +
            " select " + SELECT_PARAMS +
            " from tb_car_coupon " +
            " where vid=#{vid} " +
            " and stat='unused' " +
            " and end_time &gt; #{nowTime} " +
            " and biz_platform in " +
            "   <foreach collection='bizPlatformList' item='bizPlatform' index='index' open='(' close=')' separator=','>" +
            "       #{bizPlatform}" +
            "   </foreach> " +
            " <if test=\"serviceTypeList != null and serviceTypeList.size > 0\"> " +
            "    and service_type in " +
            "        <foreach collection='serviceTypeList' item='serviceType' index='index' open='(' close=')' separator=','>" +
            "            #{serviceType}" +
            "        </foreach> " +
            " </if>" +
            " order by add_time desc" +
            "</script>")
    List<CouponPo> getCouponByUnused(@Param("vid") String vid,
                                     @Param("nowTime") long nowTime,
                                     @Param("bizPlatformList") List<Integer> bizPlatformList,
                                     @Param("serviceTypeList") List<Integer> serviceTypeList);

}
