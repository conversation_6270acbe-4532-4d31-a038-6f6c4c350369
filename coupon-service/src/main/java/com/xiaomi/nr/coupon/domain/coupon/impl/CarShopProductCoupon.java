package com.xiaomi.nr.coupon.domain.coupon.impl;

import com.xiaomi.nr.coupon.api.dto.coupon.GoodsItemDto;
import com.xiaomi.nr.coupon.api.dto.coupon.ProductUsableCouponRequest;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.coupon.AbstractProductCoupon;
import com.xiaomi.nr.coupon.domain.coupon.model.ProductUsableCouponContext;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherToolInterface;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.nocodefetchable.NoCodeFetchableReqDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.nocodeusable.NoCodeUsableReqDo;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.enums.matchertool.MatcherToolTypeEnum;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 车商城产品站优惠券服务
 *
 * <AUTHOR>
 * @date 2024/9/12
 */
@Component
public class CarShopProductCoupon extends AbstractProductCoupon {


    /**
     * 获取业务领域
     *
     * @return BizPlatformEnum
     */
    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.CAR_SHOP;
    }

    /**
     * 入参校验
     * @param req  req
     */
    public void checkReq(ProductUsableCouponRequest req) throws BizError {

        super.checkReq(req);

        // 车商城渠道校验
        Integer useChannel = req.getUseChannel();
        if (Objects.isNull(useChannel) || useChannel <= 0) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "渠道非法");
        }

        // 商品校验
        for (GoodsItemDto goodsInfo : req.getGoodsList()) {
            if (!GoodsLevelEnum.Ssu.getValue().equals(goodsInfo.getLevel())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "所传商品品级只能为ssu");
            }
        }
    }

    /**
     * 获取可领券
     *
     * @param req     req
     * @param context context
     */
    @Override
    public void getProductFetchableCoupon(ProductUsableCouponRequest req, ProductUsableCouponContext context) throws BizError {
        NoCodeFetchableReqDo fetchReq = new NoCodeFetchableReqDo();
        fetchReq.setUserId(req.getUserId());
        fetchReq.setBizPlatform(Lists.newArrayList(BizPlatformEnum.CAR_SHOP.getCode()));
        fetchReq.setGoodsList(convertReqDtoToMatcherGoodsItemDo(req.getGoodsList()));
        fetchReq.setUseChannel(req.getUseChannel());

        fetchReq.setCheckBizPlatform(true);
        fetchReq.setCheckUseChannel(true);
        fetchReq.setCheckPublicPromotion(true);
        fetchReq.setCheckConfigStatus(true);
        fetchReq.setCheckConfigFetchTime(true);
        fetchReq.setCheckConfigFetchLimit(true);
        // 查询已领券 => 产品站 => 需要校验用户限领
        fetchReq.setCheckUserFetchLimit(req.getNeedFetchedCoupon());

        MatcherToolInterface fetchMatcher = matcherToolFactory.getProvider(MatcherToolTypeEnum.CAR_SHOP_PRODUCT_NOCODE_FETCHABLE);
        MatcherContextDo fetchResp = fetchMatcher.execute(fetchReq);
        if (Objects.nonNull(fetchResp) && CollectionUtils.isNotEmpty(fetchResp.getKeyResp())) {
            context.fill(fetchResp);
        }
    }

    /**
     * 获取可用券
     *
     * @param req     req
     * @param context context
     */
    @Override
    public void getProductUserUsableCoupon(ProductUsableCouponRequest req, ProductUsableCouponContext context) throws BizError {

        NoCodeUsableReqDo usableReq = new NoCodeUsableReqDo();
        usableReq.setUserId(req.getUserId());
        usableReq.setBizPlatform(Lists.newArrayList(BizPlatformEnum.CAR_SHOP.getCode()));
        usableReq.setGoodsList(convertReqDtoToMatcherGoodsItemDo(req.getGoodsList()));
        usableReq.setUseChannel(req.getUseChannel());
        usableReq.setPromotionTypeList(req.getPromotionTypeList());

        usableReq.setCheckBizPlatform(true);
        usableReq.setCheckUseChannel(true);
        usableReq.setCheckUserCouponTime(true);
        usableReq.setCheckUserCouponStatus(true);
        usableReq.setCheckConfigGoodsInclude(true);
        usableReq.setCheckPromotionType(true);

        MatcherToolInterface usableMatcher = matcherToolFactory.getProvider(MatcherToolTypeEnum.CAR_SHOP_PRODUCT_NOCODE_USABLE);
        MatcherContextDo usableResp = usableMatcher.execute(usableReq);
        if (Objects.nonNull(usableResp) && CollectionUtils.isNotEmpty(usableResp.getKeyResp())) {
            context.fill(usableResp);
        }
    }
}
