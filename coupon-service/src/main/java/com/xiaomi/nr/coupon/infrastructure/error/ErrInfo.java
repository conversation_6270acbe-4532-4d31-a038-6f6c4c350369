package com.xiaomi.nr.coupon.infrastructure.error;

import com.xiaomi.youpin.infra.rpc.errors.*;

/**
 * 错误常量
 * <p>
 * 错误码值分三部分 400{scope}{internalCode},
 * 400:          固定值
 * scope:        定义级别 {@link Scopes}
 * internalCode：错误级别的确切问题
 *
 * </p>
 *
 * <AUTHOR>
 */
public class ErrInfo {
    public static final BizError ERR_COUPON_CONFIG_ID = ExceptionHelper.create(ErrCode.COUPON, "优惠券配置ID不符合要求");
    public static final BizError ERR_COUPON_CONFIG_ID_EMPTY = ExceptionHelper.create(ErrCode.COUPON, "优惠券配置ID列表不能为空");
    public static final BizError ERR_COUPON_CONFIG_ACTION = ExceptionHelper.create(ErrCode.COUPON, "优惠券配置操作超出已知范围");
    public static final BizError ERR_COUPON_CONFIG_GOODS_ID = ExceptionHelper.create(ErrCode.COUPON, "货品ID必须为正整数");
    public static final BizError ERR_COUPON_CONFIG_PACKAGE_ID = ExceptionHelper.create(ErrCode.COUPON, "套装ID必须为正整数");
    public static final BizError ERR_COUPON_CONFIG_SKU = ExceptionHelper.create(ErrCode.COUPON, "SKU必须为正整数");
    public static final BizError ERR_COUPON_CONFIG_SKU_PACKAGE_ID = ExceptionHelper.create(ErrCode.COUPON, "SKU或套装ID必须为正整数");
    public static final BizError ERR_COUPON_CONFIG_LEVEL = ExceptionHelper.create(ErrCode.COUPON, "品级超出已知范围");
    public static final BizError ERR_COUPON_CONFIG_CACHE_NOT_FIND = ExceptionHelper.create(ErrCode.COUPON, "优惠券配置信息未找到");
    public static final BizError ERR_COUPON_CONFIG_GOODS_CACHE_NOT_FIND = ExceptionHelper.create(ErrCode.COUPON, "商品对应优惠券配置列表信息未找到");
    public static final BizError ERR_ECARD_CANCEL_FAIL = ExceptionHelper.create(ErrCode.ECARD, "标准换新现金券作废失败");
    public static final BizError ERR_ECARD_ASSIGN_FAIL = ExceptionHelper.create(ErrCode.ECARD, "标准换新现金券发放失败");
    public static final BizError ERR_ECARD_QUERY_FAIL = ExceptionHelper.create(ErrCode.ECARD, "现金券信息未找到");
    public static final BizError ERR_COUPON_CONFIG_INTERFACE_BUZY = ExceptionHelper.create(ErrCode.COUPON, "接口繁忙，请稍后重试!");
}
