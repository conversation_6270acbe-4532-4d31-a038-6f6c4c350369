package com.xiaomi.nr.coupon.domain.matchertool.core;

import com.alibaba.nacos.common.utils.StringUtils;
import com.xiaomi.nr.coupon.constant.CommonConstant;
import com.xiaomi.nr.coupon.constant.CouponConstant;
import com.xiaomi.nr.coupon.constant.UseChannelClientRel;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponSceneItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.GoodScope;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherGoodsItemDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.enums.coupon.CouponUseChannelEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.BottomTypeEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.PromotionType;
import com.xiaomi.nr.coupon.enums.goods.BusinessTypeEnum;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.enums.goods.SaleModeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.ExtendInfo;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponassign.CouponAssignRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.GlobalConfigRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.po.CompareGoodsItem;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.storeinfo.StoreInfoRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.storeinfo.po.OrgInfo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 公用方法
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Slf4j
@Component
public class MatcherBaseMethod {

    @Resource
    private CouponConfigRepository couponConfigRepository;

    @Resource
    private StoreInfoRedisDao storeInfoRedisDao;

    @Resource
    private GlobalConfigRedisDao globalConfigRedisDao;

    @Resource
    private CouponAssignRedisDao assignRedisDao;


    /**
     * 是否为商城渠道的client id
     *
     * @param clientId .
     * @return boolean
     */
    protected boolean isMiShopChannelClientId(Long clientId) {
        Set<Long> clientIds = UseChannelClientRel.getUseChannelClientRelation().get(CouponUseChannelEnum.MI_SHOP.getValue()).getClientIds();
        return clientIds.contains(clientId);
    }

    /**
     * 获取商品全局排除
     *
     * @return CompareGoodsItem
     */
    protected CompareGoodsItem getGlobalInExclude() {
        return globalConfigRedisDao.getGlobalInExclude();
    }

    /**
     * 获取商品优惠券全局排除
     *
     * @return CompareGoodsItem
     */
    protected CompareGoodsItem getGlobalCouponInExclude() {
        return globalConfigRedisDao.getGlobalCouponInExclude();
    }

    /**
     * 获取券配置信息
     *
     * @param configIdList 券配置ID列表
     * @return Map<Long, CouponConfigItem>
     */
    protected Map<Long, CouponConfigItem> getCouponConfigInfos(List<Long> configIdList) {
        return couponConfigRepository.getCouponConfigs(configIdList);
    }

    /**
     * 获取门店信息
     *
     * @param orgCode 门店ID
     * @return 门店信息
     */
    protected OrgInfo getOrgInfo(String orgCode) throws BizError {
        if (StringUtils.isEmpty(orgCode) || StringUtils.isBlank(orgCode)) {
            return null;
        }
        OrgInfo orgInfo = storeInfoRedisDao.getOrgInfo(orgCode);
        if (orgInfo == null || Objects.isNull(orgInfo.getOrgType())) {
            throw ExceptionHelper.create(ErrCode.USE_COUPON_ORG_INFO_CACHE_NULL, "无法获取门店信息");
        }
        return orgInfo;
    }


    /**
     * 获取投放场景信息
     *
     * @param sceneCodes List<String>
     * @return Map
     * @throws BizError .
     */
    protected Map<String, CouponSceneItem> getSceneInfo(List<String> sceneCodes) throws BizError {
        if (CollectionUtils.isNotEmpty(sceneCodes)) {
            return Collections.emptyMap();
        }
        Map<String, CouponSceneItem> result = new HashMap<>();
        for (String code : sceneCodes) {
            CouponSceneItem cache = couponConfigRepository.getCouponScene(code);
            if (cache == null || cache.getSceneCode().isEmpty() || !code.equals(cache.getSceneCode())) {
                log.error("MatcherBaseMethod.getSceneInfo, 无法获取有效投放场景信息, sceneCode:{}", code);
            }
            result.put(code, cache);
        }
        return result;
    }

    /**
     * 获取券配置被领取的数量
     *
     * @param configIdList List<Long>
     * @return Map<Long, Integer>
     * @throws BizError
     */
    protected Map<Long, Integer> getConfigByFetchedCount(List<Long> configIdList) throws BizError {
        if (CollectionUtils.isEmpty(configIdList)) {
            return Collections.emptyMap();
        }
        Map<Long, Integer> res = assignRedisDao.batchGetInventory(configIdList);
        if (Objects.isNull(res)) {
            return Collections.emptyMap();
        }
        return res;
    }

    /**
     * 获取券配置被用户已领取数量
     *
     * @param configIdList List<Long>
     * @return Map<Long, Integer>
     * @throws BizError
     */
    protected Map<Long, Integer> getConfigByUserFetchedCount(Long userId, List<Long> configIdList) throws BizError {
        if (CollectionUtils.isEmpty(configIdList) || Objects.isNull(userId) || userId <= 0) {
            return Collections.emptyMap();
        }
        Map<Long, Integer> res = assignRedisDao.batchGetFetchCount(userId, configIdList);
        if (Objects.isNull(res)) {
            return Collections.emptyMap();
        }
        return res;
    }

    /**
     * 获取可用商品
     *
     * @param ctx      ctx
     * @param respItem MatcherRespItemDo
     * @return 过滤后的可用商品
     */
    protected List<MatcherGoodsItemDo> getValidGoods(MatcherContextDo ctx, MatcherRespItemDo respItem) {
        List<MatcherGoodsItemDo> outerGoodsList = ctx.getOuterGoodsList();

        if (CollectionUtils.isEmpty(outerGoodsList)) {
            return Collections.emptyList();
        }

        List<MatcherGoodsItemDo> validGoods = new ArrayList<>();
        for (MatcherGoodsItemDo outerItem : outerGoodsList) {
            if (isValidGoods(ctx, respItem, outerItem)) {
                validGoods.add(outerItem);
            }
        }
        return validGoods;
    }

    /**
     * 检查是否为可用商品
     *
     * @param ctx            ctx
     * @param respItem       MatcherRespItemDo
     * @param outerGoodsItem 外部商品
     * @return boolean
     */
    protected boolean isValidGoods(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherGoodsItemDo outerGoodsItem) {
        CouponConfigItem config = ctx.getConfigInfoMap().get(respItem.getConfigId());
        boolean isCheckGlobalExcludeGoods = ctx.isCheckGlobalExcludeGoods();
        CompareGoodsItem globalExcludeGoods = ctx.getGlobalExcludeGoods();
        boolean isCheckGlobalCouponExcludeGoods = ctx.isCheckGlobalCouponExcludeGoods();
        CompareGoodsItem globalCouponExcludeGoods = ctx.getGlobalCouponExcludeGoods();
        GoodScope configGoods = config.getGoodScope();

        //特价商品不可用券过滤
        if (Objects.nonNull(outerGoodsItem.getSalePrice()) && Objects.nonNull(outerGoodsItem.getMarketPrice())) {
            if (!checkSpecialPrice(outerGoodsItem.getMarketPrice(), outerGoodsItem.getSalePrice(), config.getExtPropInfo().getCheckPrice())) {
                return false;
            }
        }

        //销售模式
        if (Objects.nonNull(outerGoodsItem.getSaleMode())) {
            if (!checkSaleMode(outerGoodsItem.getSaleMode())) {
                return false;
            }
        }

        //商家类型（只有自营和采销可以用券）
        if (Objects.nonNull(outerGoodsItem.getBusinessType())) {
            if (!checkBusinessType(outerGoodsItem.getBusinessType())) {
                return false;
            }
        }

        //0元商品不可用券
        if (Objects.nonNull(outerGoodsItem.getSalePrice())) {
            if (outerGoodsItem.getSalePrice() <= 0L) {
                return false;
            }
        }

        //虚拟商品不可用券
        if (Objects.nonNull(outerGoodsItem.getVirtual())) {
            if (outerGoodsItem.getVirtual()) {
                return false;
            }
        }

        //全局排除
        if (isCheckGlobalExcludeGoods) {
            if (isGlobalInExclude(outerGoodsItem, globalExcludeGoods)) {
                return false;
            }
        }

        //优惠券全局排除
        if (isCheckGlobalCouponExcludeGoods) {
            if (isGlobalInExclude(outerGoodsItem, globalCouponExcludeGoods)) {
                return false;
            }
        }

        if (GoodsLevelEnum.Sku.getValue().equals(outerGoodsItem.getLevel()) && configGoods.singleCheckValidSku(outerGoodsItem.getId())) {
            return true;
        }

        if (GoodsLevelEnum.Package.getValue().equals(outerGoodsItem.getLevel()) && configGoods.singleCheckValidPackage(outerGoodsItem.getId())) {
            return true;
        }

        if (GoodsLevelEnum.Ssu.getValue().equals(outerGoodsItem.getLevel()) && configGoods.singleCheckValidSsu(outerGoodsItem.getId())) {
            return true;
        }

        return false;
    }

    /**
     * 如果配置了特价商品不可用券，则不返回券
     *
     * @param marketPrice  市场价
     * @param salePrice    销售价
     * @param isCheckPrice 是否特价可用券
     * @return boolean
     */
    protected boolean checkSpecialPrice(Long marketPrice, Long salePrice, int isCheckPrice) {
        boolean isSpecial = marketPrice > salePrice;
        if (isCheckPrice == CommonConstant.ONE_INT && isSpecial) {
            return false;
        }
        return true;
    }

    /**
     * 不展示优惠券的销售模式，比如盲售
     *
     * @param saleMode 销售模式
     * @return boolean
     */
    protected boolean checkSaleMode(String saleMode) {
        List<String> sm = Arrays.asList(
                SaleModeEnum.STANDARD.getValue(),
                SaleModeEnum.BOOKING.getValue(),
                SaleModeEnum.PRESALES.getValue()
        );
        return sm.contains(saleMode);
    }

    /**
     * 第三方商品不可用券（比如有品商品、POP商品）
     *
     * @param businessType 商家类型
     * @return boolean
     */
    protected boolean checkBusinessType(Integer businessType) {
        List<Integer> bs = Arrays.asList(
                BusinessTypeEnum.CN_ORDER.getValue(),
                BusinessTypeEnum.CN_FAMILYS.getValue(),
                BusinessTypeEnum.CN_YPORDER.getValue(),
                BusinessTypeEnum.CN_YP_VMI_SELF.getValue()
        );
        return bs.contains(businessType);
    }

    /**
     * 是否为定金预售
     *
     * @param saleMode 销售模式
     * @return boolean
     */
    protected boolean isBookingSaleMode(String saleMode) {
        return SaleModeEnum.BOOKING.getValue().equals(saleMode);
    }

    /**
     * 判断两段时间是否存在交集
     *
     * @param aStartTime Long
     * @param aEndTime   Long
     * @param bStartTime Long
     * @param bEndTime   Long
     * @return boolean
     */
    protected boolean existIntersect(Long aStartTime, Long aEndTime, Long bStartTime, Long bEndTime) {
        if (aStartTime == null || aEndTime == null || bStartTime == null || bEndTime == null) {
            return false;
        }
        if (aStartTime < bStartTime && bStartTime < aEndTime) {
            return true;
        }
        if (bStartTime < aStartTime && aStartTime < bEndTime) {
            return true;
        }
        return false;
    }


    /**
     * 商品全局黑名单排除
     *
     * @param goodsInfo        商品信息
     * @param excludeGoodsItem 全局排除商品
     * @return bool true：排除，false：非排除
     */
    protected boolean isGlobalInExclude(MatcherGoodsItemDo goodsInfo, CompareGoodsItem excludeGoodsItem) {
        if (
                Objects.nonNull(goodsInfo.getId()) && GoodsLevelEnum.Sku.getValue().equals(goodsInfo.getLevel()) &&
                        CollectionUtils.isNotEmpty(excludeGoodsItem.getSku()) && excludeGoodsItem.getSku().contains(goodsInfo.getId().toString())
        ) {
            return true;
        }

        if (
                Objects.nonNull(goodsInfo.getId()) && GoodsLevelEnum.Package.getValue().equals(goodsInfo.getLevel()) &&
                        CollectionUtils.isNotEmpty(excludeGoodsItem.getPackages()) && excludeGoodsItem.getPackages().contains(goodsInfo.getId().toString())
        ) {
            return true;
        }

        return false;
    }

    /**
     * 获取领取用户券的门店ID
     *
     * @param extendInfoStr 用户券po里的extend json
     * @return String
     */
    protected String getCouponOrgCode(String extendInfoStr) {
        if (StringUtils.isEmpty(extendInfoStr)) {
            return "";
        }
        ExtendInfo extendInfo = GsonUtil.fromJson(extendInfoStr, ExtendInfo.class);
        if (extendInfo != null && !StringUtils.isBlank(extendInfo.getOrgCode())) {
            return extendInfo.getOrgCode();
        }
        return "";
    }

    /**
     * 无码券排序
     *
     * @param keyResp       List<MatcherRespItemDo>
     * @param configInfoMap Map<Long, CouponConfigItem>
     * @param userCouponMap Map<Long, CouponPo>
     * @return List<MatcherRespItemDo>
     */
    public List<MatcherRespItemDo> noCodeSort(List<MatcherRespItemDo> keyResp, Map<Long, CouponConfigItem> configInfoMap, Map<Long, CouponPo> userCouponMap) {
        if (CollectionUtils.isEmpty(keyResp) || MapUtils.isEmpty(configInfoMap)) {
            return Collections.emptyList();
        }

        //券排序（注意k1、k2是倒序遍历的，也就是说k1是第二条数据，k2才是第一条数据）
        return keyResp.stream().sorted((k1, k2) -> {
            CouponConfigItem c1 = configInfoMap.get(k1.getConfigId());
            CouponConfigItem c2 = configInfoMap.get(k2.getConfigId());

            // 超级补贴券 > 商品券 > 运费券
            if (CouponConstant.COUPON_TYPE_SORT_MAP.getOrDefault(c1.getCouponType(), 1) < CouponConstant.COUPON_TYPE_SORT_MAP.getOrDefault(c2.getCouponType(), 1)) {
                return -1;
            }
            if (CouponConstant.COUPON_TYPE_SORT_MAP.getOrDefault(c1.getCouponType(), 1) > CouponConstant.COUPON_TYPE_SORT_MAP.getOrDefault(c2.getCouponType(), 1)) {
                return 1;
            }

            // N元券 > 立减券 > 满减券 > 折扣券
            if (CouponConstant.PROMOTION_TYPE_SORT_MAP.getOrDefault(c1.getPromotionType().getCode(), 0) < CouponConstant.PROMOTION_TYPE_SORT_MAP.getOrDefault(c2.getPromotionType().getCode(), 0)) {
                return -1;
            }
            if (CouponConstant.PROMOTION_TYPE_SORT_MAP.getOrDefault(c1.getPromotionType().getCode(), 0) > CouponConstant.PROMOTION_TYPE_SORT_MAP.getOrDefault(c2.getPromotionType().getCode(), 0)) {
                return 1;
            }

            //满件 > 满元
            if (c1.getCouponConfigInfo().getBottomType() < c2.getCouponConfigInfo().getBottomType()) {
                return 1;
            }
            if (c1.getCouponConfigInfo().getBottomType() > c2.getCouponConfigInfo().getBottomType()) {
                return -1;
            }

            // 满件|满元 从小到大
            Integer bottomValue1 = c1.getCouponConfigInfo().getBottomCount();
            Integer bottomValue2 = c2.getCouponConfigInfo().getBottomCount();
            if (c1.getCouponConfigInfo().getBottomType() == BottomTypeEnum.Money.getCode() || c1.getCouponConfigInfo().getBottomType() == BottomTypeEnum.EveMoney.getCode()) {
                bottomValue1 = c1.getCouponConfigInfo().getBottomPrice();
                bottomValue2 = c2.getCouponConfigInfo().getBottomPrice();
            }

            if (bottomValue1 < bottomValue2) {
                return -1;
            }
            if (bottomValue1 > bottomValue2) {
                return 1;
            }

            //优惠值从大到小
            if (Objects.equals(PromotionType.ConditionDiscount.getCode(), c1.getPromotionType().getCode())) {
                //折扣券折数从小到大
                if (c1.getCouponConfigInfo().getPromotionValue() < c2.getCouponConfigInfo().getPromotionValue()) {
                    return -1;
                }
                if (c1.getCouponConfigInfo().getPromotionValue() > c2.getCouponConfigInfo().getPromotionValue()) {
                    return 1;
                }

            } else {
                long c1ReduceMoney = c1.getCouponConfigInfo().getPromotionValue();
                if (Objects.nonNull(c1.getCouponConfigInfo().getMaxReduce()) && c1.getCouponConfigInfo().getMaxReduce() > 0L) {
                    c1ReduceMoney = Math.min(c1.getCouponConfigInfo().getPromotionValue(), c1.getCouponConfigInfo().getMaxReduce());
                }
                long c2ReduceMoney = c2.getCouponConfigInfo().getPromotionValue();
                if (Objects.nonNull(c2.getCouponConfigInfo().getMaxReduce()) && c2.getCouponConfigInfo().getMaxReduce() > 0L) {
                    c2ReduceMoney = Math.min(c2.getCouponConfigInfo().getPromotionValue(), c2.getCouponConfigInfo().getMaxReduce());
                }

                //满减券优惠力度从大到小
                if (c1ReduceMoney < c2ReduceMoney) {
                    return 1;
                }
                if (c1ReduceMoney > c2ReduceMoney) {
                    return -1;
                }
            }

            //结束时间从小到大
            if (Objects.nonNull(k1.getUserCouponId()) && Objects.nonNull(k2.getUserCouponId()) && k1.getUserCouponId() > 0 && k2.getUserCouponId() > 0 && userCouponMap.containsKey(k1.getUserCouponId()) && userCouponMap.containsKey(k2.getUserCouponId())) {
                //可用结束时间从小到大
                String k1UseEndTimeStr = userCouponMap.get(k1.getUserCouponId()).getEndTime();
                String k2UseEndTimeStr = userCouponMap.get(k2.getUserCouponId()).getEndTime();
                Long k1UseEndTime = Long.parseLong(k1UseEndTimeStr == null ? CommonConstant.ZERO_STR : k1UseEndTimeStr);
                Long k2UseEndTime = Long.parseLong(k2UseEndTimeStr == null ? CommonConstant.ZERO_STR : k2UseEndTimeStr);
                return k1UseEndTime.compareTo(k2UseEndTime);
            } else {
                //领取结束时间从小到大
                return c1.getCouponConfigInfo().getEndFetchTime().compareTo(c2.getCouponConfigInfo().getEndFetchTime());
            }

        }).collect(Collectors.toList());
    }

}
