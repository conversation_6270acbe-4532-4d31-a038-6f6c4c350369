package com.xiaomi.nr.coupon.domain.matchertool.matchers;

import com.xiaomi.nr.coupon.domain.matchertool.checktools.NoCodeFetchableCheckTool;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherAbstractFetchable;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherCheckToolAbstract;
import com.xiaomi.nr.coupon.enums.matchertool.MatcherToolTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 无码券可领匹配器
 *
 * <AUTHOR>
 * @date 2023/7/13
 */
@Slf4j
@Component
public class MatcherNoCodeFetchable extends MatcherAbstractFetchable {

    @Resource
    private NoCodeFetchableCheckTool checkTool;

    @Override
    public MatcherCheckToolAbstract getCheckTool() {
        return checkTool;
    }

    @Override
    public MatcherToolTypeEnum getMatcherToolTypeEnum() {
        return MatcherToolTypeEnum.NOCODE_FETCHABLE;
    }

}
