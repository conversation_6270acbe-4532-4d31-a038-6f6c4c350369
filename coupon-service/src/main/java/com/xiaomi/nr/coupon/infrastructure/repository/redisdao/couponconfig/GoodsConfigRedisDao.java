package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig;

import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.po.GoodsConfigRelationPo;

import java.util.List;
import java.util.Map;

/**
 * 商品可用券配置redis缓存操作对象
 *
 * <AUTHOR>
 */
public interface GoodsConfigRedisDao {

    /**
     * 获取商品可用券配置列表缓存
     *
     * @param id Long
     * @param level String
     * @return GoodsConfigRelationPo
     */
    GoodsConfigRelationPo get(Long id, String level);

    /**
     * 批量获取商品可用券配置列表缓存
     *
     * @param params List<String> {level}_{id}
     * @return Map<String, GoodsConfigRelationPo>
     */
    Map<String, GoodsConfigRelationPo> get(List<String> params);
}

