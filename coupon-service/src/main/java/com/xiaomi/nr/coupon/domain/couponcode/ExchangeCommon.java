package com.xiaomi.nr.coupon.domain.couponcode;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.enums.couponcode.StatusEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponcodeslave.CouponCodeReadMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponcode.po.CouponCodePo;
import com.xiaomi.nr.coupon.infrastructure.rpc.sid.SidProxy;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 券码兑换公共方法
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ExchangeCommon {

    @Resource
    private CouponConfigRepository configCache;

    @Resource
    private CouponCodeReadMapper couponCodeReadMapper;

    @Resource
    private SidProxy sidProxy;

    /**
     * 获取券码信息
     *
     * @param userId Long
     * @param index  String
     * @return CouponCodePo
     * @throws BizError
     */
    public CouponCodePo getCouponCodeInfo(Long userId, String index) throws BizError {
        long runStartTime = TimeUtil.getNowUnixMillis();
        List<CouponCodePo> data;
        try {
            data = couponCodeReadMapper.getCouponCode(index);
            log.info("couponCode.singleExchange, 获取优惠码信息, runTime={}毫秒, userId={}, index={}, data={}", TimeUtil.sinceMillis(runStartTime), userId, index, data);
        } catch (Exception e) {
            log.error("couponCode.singleExchange, 获取优惠码信息失败, runTime={}毫秒, userId={}, index={}, err={}", TimeUtil.sinceMillis(runStartTime), userId, index, e.getMessage());
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_EXCHANGE_FAIL, "兑换失败");
        }
        if (data == null || data.isEmpty() || data.get(0) == null) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_ILLEGAL, "优惠码不存在");
        }
        for (CouponCodePo item : data) {
            if (item != null && StatusEnum.UNUSED.getValue().equals(item.getStat())) {
                return item;
            }
        }
        return data.get(0);
    }

    /**
     * 获取券配置缓存信息
     *
     * @param id Long
     * @return CouponConfigItem
     * @throws BizError .
     */
    public CouponConfigItem getConfigInfo(Long id) throws BizError {
        CouponConfigItem cache = configCache.getCouponConfig(id);
        if (cache == null || cache.getConfigId() <= 0 || id == null || id.intValue() != cache.getConfigId() || cache.getCouponConfigInfo() == null) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_INVALID, "优惠码无效");
        }
        return cache;
    }

    /**
     * 调sid服务生成优惠ID
     *
     * @param userId long
     * @param count  int
     * @return List<Long>
     * @throws BizError .
     */
    public List<Long> getCouponIds(long userId, int count) throws BizError {
        long runStartTime = TimeUtil.getNowUnixMillis();
        List<Long> ids;
        try {
            ids = sidProxy.get(userId, count);
            log.info("coupon.singleExchange, 调sid服务生成优惠ID成功, runTime={}毫秒, userId={}, count={}, ids={}", TimeUtil.sinceMillis(runStartTime), userId, count, ids);
        } catch (Exception e) {
            log.error("coupon.singleExchange, 调sid服务生成优惠ID失败, runTime={}毫秒, userId={}, err={}", TimeUtil.sinceMillis(runStartTime), userId, e.getMessage());
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_EXCHANGE_FAIL, "兑换失败");
        }
        if (ids != null && ids.size() == count && ids.get(0) > 0) {
            return ids;
        }
        log.info("coupon.singleExchange, 无法通过sid服务获取优惠券ID, runTime={}毫秒, userId={}", TimeUtil.sinceMillis(runStartTime), userId);
        throw ExceptionHelper.create(ErrCode.COUPON_CODE_EXCHANGE_FAIL, "兑换失败");
    }
}
