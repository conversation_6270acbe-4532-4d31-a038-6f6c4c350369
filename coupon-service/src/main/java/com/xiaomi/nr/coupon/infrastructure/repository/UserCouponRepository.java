package com.xiaomi.nr.coupon.infrastructure.repository;

import com.xiaomi.nr.coupon.enums.coupon.OrderStatusEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CouponMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponOptPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave.CouponInfoMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave.UserCouponListMapper;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class UserCouponRepository {

    @Autowired
    private UserCouponListMapper userCouponListMapper;

    @Autowired
    private CouponMapper couponMapper;

    @Autowired
    private CouponInfoMapper couponInfoMapper;


    /**
     * 获取该用户下不同券类型对应的券
     *
     * @param userId       用户ID
     * @param stat         优惠券状态
     * @param bizPlatform 所属业务领域
     * @param nowTime      当前时间
     * @return 券配置id与用户券对应关系
     */
    public Map<Long, List<CouponPo>> getUserCoupon(long userId, String stat, List<Integer> bizPlatform, long nowTime) {

        List<CouponPo> userCouponList = userCouponListMapper.getUserCoupon(userId, stat, nowTime, bizPlatform);
        if (CollectionUtils.isEmpty(userCouponList)) {
            return Collections.emptyMap();
        }

        //构建券配置id和用户券的映射关系
        Map<Long, List<CouponPo>> configToCouponMap = new HashMap<>();
        for (CouponPo couponPo : userCouponList) {
            if (!bizPlatform.contains(couponPo.getBizPlatform())) {
                continue;
            }
            Long configId = couponPo.getTypeId();
            if (!configToCouponMap.containsKey(configId)) {
                configToCouponMap.put(configId, new ArrayList<>(Collections.singletonList(couponPo)));
                continue;
            }
            configToCouponMap.get(configId).add(couponPo);
        }
        return configToCouponMap;
    }

    /**
     * 根据券id获取优惠券
     * 核销使用 读主库 防止存库数据延迟异常
     *
     * @param userId
     * @param couponIds
     * @return
     * @throws BizError
     */
    public List<CouponPo> getCouponPoList(Long userId, List<Long> couponIds) throws BizError {
        List<CouponPo> couponPos = couponMapper.getByCouponIdList(userId, couponIds);
        if (CollectionUtils.isEmpty(couponPos)) {
            throw ExceptionHelper.create(ErrCode.USE_COUPON_NOT_FOUND, "无可用优惠券");
        }
        if (couponPos.size() != couponIds.size()) {
            log.error("getCouponPoList fail. userId:{},couponIds:{},couponCnt:{}", userId, couponIds, couponPos.size());
            throw ExceptionHelper.create(ErrCode.USE_COUPON_NOT_FOUND, "获取优惠券数量不一致");
        }
        return couponPos;
    }

    /**
     * 获取该用户对应的券
     *
     * @param userId    用户ID
     * @param couponId 优惠券ID
     * @return Map<Long, CouponPo>
     */
    public CouponPo getCouponInfo(long userId, Long couponId) throws BizError {
        CouponPo couponPo = couponInfoMapper.getCouponInfo(userId, couponId);
        if (couponPo == null) {
            throw ExceptionHelper.create(ErrCode.USE_COUPON_NOT_FOUND, "优惠券不存在");
        }
        return couponPo;
    }

    /**
     * 封装券下单操作类型
     *
     * @param userId
     * @param orderId
     * @param lock
     * @return
     */
    public CouponOptPo buildCouponOptPo(long userId, long orderId, OrderStatusEnum lock) {
        CouponOptPo couponOptPo = new CouponOptPo();
        couponOptPo.setId(getId(orderId, lock.getType()));
        couponOptPo.setUserId(userId);
        couponOptPo.setOrderId(orderId);
        couponOptPo.setOptType(lock.getType());
        couponOptPo.setAddTime(TimeUtil.getNowUnixSecond());
        return couponOptPo;
    }

    /**
     * 获取该用户对应的券
     *
     * @param userId    用户ID
     * @param couponIds 优惠券ID列表
     * @return Map<Long, CouponPo>
     */
    public List<CouponPo> getCouponInfos(long userId, List<Long> couponIds) {
        return couponInfoMapper.batchGetCouponInfo(userId, couponIds);
    }

    private Long getId(Long orderId, Integer optType){
        return orderId * 10 + optType;
    }

}
