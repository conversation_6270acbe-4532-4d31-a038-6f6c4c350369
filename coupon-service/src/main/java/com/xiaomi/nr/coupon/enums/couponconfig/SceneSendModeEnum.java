package com.xiaomi.nr.coupon.enums.couponconfig;

/**
 * 优惠券投放方式 枚举
 *
 * <AUTHOR>
 */
public enum SceneSendModeEnum {

    /**
     * 优惠券
     */
    Coupon(1,"优惠券"),

    /**
     * 兑换码
     */
    ExchangeCode(2,"兑换码");

    private final Integer value;
    private final String name;

    SceneSendModeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static String findNameByValue(Integer value) {
        SceneSendModeEnum[] values = SceneSendModeEnum.values();
        for (SceneSendModeEnum item : values) {
            if (item.getValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }
}

