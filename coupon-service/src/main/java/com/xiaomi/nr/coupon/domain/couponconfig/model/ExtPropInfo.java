package com.xiaomi.nr.coupon.domain.couponconfig.model;

import com.xiaomi.nr.coupon.constant.CommonConstant;
import lombok.Data;

/**
 * @description: 优惠券扩展属性
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/3/5 4:51 下午
 * @Version: 1.0
 **/
@Data
public class ExtPropInfo {

    /**
     * 是否包邮 1-是 2-否
     */
    private int postFree;

    /**
     * 可分享 1-是 2-否
     */
    private int share;

    /**
     * 限制地区 1-是 2-否
     */
    private int area;

    /**
     * 是否会员 1 - 是， 2否
     */
    private int proMember;

    /**
     * 是否专店专用 1-是， 2-否
     */
    private int specialStore;


    /**
     * 套装是否允许部分商品参加活动(套装是否拆分) 1允许 2不允许
     */
    private int checkPackage = 1;

    /**
     * 特价商品是否参与  1表示不参与，2表示参与
     */
    private int checkPrice = 2;

    /**
     * 定金券 or 尾款券
     */
    private int checkoutStage;

    /**
     * 是否展示有效期
     */
    private int displayDate = 1;

    /**
     * 是否转增
     * @return
     */
    public boolean shareAble(){
        return this.share == CommonConstant.ONE_INT;
    }

    /**
     * 是否包邮
     * @return
     */
    public boolean postFree(){
        return this.postFree == CommonConstant.ONE_INT;
    }

    /**
     * 区域限制
     */
    public boolean areaLimit(){
        return this.area == CommonConstant.ONE_INT;
    }

    /**
     * 专店专用
     */
    public boolean specialStore(){
        return this.specialStore == CommonConstant.ONE_INT;
    }

    /**
     * 是否检查套装内商品可用
     */
    public boolean checkPackage(){
        return this.checkPackage == CommonConstant.ONE_INT;
    }

    /**
     * 特价商品是否可用
     * @return
     */
    public boolean checkPrice(){
        return this.checkPrice == CommonConstant.TWO_INT;
    }

}
