package com.xiaomi.nr.coupon.domain.matchertool.entity.common;

import lombok.Data;

/**
 * MatcherGoodsItemDo
 *
 * <AUTHOR>
 * @date 2023/7/13
 */
@Data
public class MatcherGoodsItemDo {

    /**
     * sku或套装ID
     */
    private Long id;

    /**
     * 商品品级：sku / package / ssu
     */
    private String level;

    /**
     * 当前销售价（直降后的，单位分）
     */
    private Long salePrice;

    /**
     * 当前市场价（单位分）
     */
    private Long marketPrice;

    /**
     * 是否为虚拟商品
     */
    private Boolean virtual;

    /**
     * 商品销售模式，比如定金预售对应的标识
     */
    private String saleMode;

    /**
     * 商家类型，比如小米自营商品/POP对应的标识
     */
    private Integer businessType;

    /**
     * 尾款支付开始时间，定金预售是必须要传的（单位秒）
     */
    private Long finalStartTime;

    /**
     * 尾款支付结束时间，定金预售是必须要传的（单位秒）
     */
    private Long finalEndTime;

}
