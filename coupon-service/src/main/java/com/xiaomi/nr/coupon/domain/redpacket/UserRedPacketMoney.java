package com.xiaomi.nr.coupon.domain.redpacket;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.redpacketslave.RedPacketMoneyMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.redpacket.RedPacketRedisDao;
import com.xiaomi.nr.coupon.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 用户红包总额
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class UserRedPacketMoney {

    @Resource
    private RedPacketMoneyMapper redPacketMoneyMapper;

    @Resource
    private RedPacketRedisDao redPacketRedisDao;

    /**
     * 查用户可用红包总额
     *
     * @param userId long
     * @return long
     */
    public long getValidMoneyByMysql(long userId) {
        return redPacketMoneyMapper.getValidMoney(userId, TimeUtil.getNowUnixSecond());
    }

    /**
     * 查用户可用红包总额
     *
     * @param userId long
     * @return long
     */
    public long getValidMoneyByRedis(long userId) {
        return redPacketRedisDao.getValidMoney(userId);
    }


}
