package com.xiaomi.nr.coupon.infrastructure.repository.localcache;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponSceneItem;

import java.util.Map;


/**
 * 优惠券场景本地缓存
 *
 * <AUTHOR>
 */
public interface CouponSceneCache {

    /**
     * 根据场景编码获取场景
     *
     * @param sceneCode
     * @return
     */
    CouponSceneItem getCouponSceneByCode(String sceneCode);


    /**
     * 批量设置优惠券场景到localCache
     *
     * @param couponSceneItem
     */
    void addCouponSceneIntoCache(CouponSceneItem couponSceneItem);

}
