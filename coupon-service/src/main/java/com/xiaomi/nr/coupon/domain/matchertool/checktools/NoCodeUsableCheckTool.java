package com.xiaomi.nr.coupon.domain.matchertool.checktools;

import com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers.*;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherCheckToolAbstract;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * 无码券可用校验工具
 *
 * <AUTHOR>
 * @date 2023/7/24
 */
@Component
public class NoCodeUsableCheckTool extends MatcherCheckToolAbstract {

    private final List<String> toolList = new ArrayList<>();

    @PostConstruct
    public void init() {
        toolList.add(BaseInfo.class.getName());
        toolList.add(ConfigBizPlatform.class.getName());
        toolList.add(ConfigShipmentId.class.getName());
        toolList.add(UserNoCodeUseTime.class.getName());
        toolList.add(UserNoCodeSpecialStore.class.getName());
        toolList.add(UserNoCodeCouponStatus.class.getName());
        toolList.add(ConfigGoodsValid.class.getName());
        toolList.add(ConfigUseChannelOrgCode.class.getName());
        toolList.add(ConfigRegion.class.getName());
    }

    @Override
    public List<String> getTools() {
        return toolList;
    }

}
