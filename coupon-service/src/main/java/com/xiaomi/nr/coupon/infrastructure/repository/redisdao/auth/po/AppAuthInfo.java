package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.auth.po;

import com.google.gson.annotations.SerializedName;
import com.xiaomi.nr.coupon.enums.couponconfig.SendChannelEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * AppAuthInfo 基础缓存信息
 */
@Data
public class AppAuthInfo implements Serializable {
    private static final long serialVersionUID = -5855364548372416392L;

    /**
     * appId
     */
    @SerializedName("appid")
    private String appId;

    /**
     * secret key
     */
    @SerializedName("key")
    private String secret;

    /**
     * 该系统可发放渠道
     */
    private List<String> channelList = Arrays.asList(SendChannelEnum.Others.getRedisValue());

}
