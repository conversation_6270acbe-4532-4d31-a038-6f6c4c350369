package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherGoodsItemDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * 校验券可用商品
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Component
public class ConfigGoodsValid extends CheckerAbstract {

    @Override
    public boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {
        if (!ctx.isCheckConfigGoodsInclude()) {
            return true;
        }

        errCtx.setInvalidConfig(true);

        if (CollectionUtils.isEmpty(ctx.getOuterGoodsList())) {
            errCtx.setErrCode(ErrCode.USE_COUPON_GOODS_MISMATCH.getCode());
            errCtx.setErrMsg("不含可用优惠券商品");
            return false;
        }

        for (MatcherGoodsItemDo outerItem : ctx.getOuterGoodsList()) {
            if (isValidGoods(ctx, respItem, outerItem)) {
                return true;
            }
        }

        errCtx.setErrCode(ErrCode.USE_COUPON_GOODS_MISMATCH.getCode());
        errCtx.setErrMsg("不含可用优惠券商品");
        return false;
    }
}
