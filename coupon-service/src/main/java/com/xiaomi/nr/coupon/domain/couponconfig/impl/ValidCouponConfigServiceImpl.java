package com.xiaomi.nr.coupon.domain.couponconfig.impl;

import com.xiaomi.nr.coupon.api.dto.couponconfig.CouponTypeInfoDto;
import com.xiaomi.nr.coupon.api.dto.couponconfig.CouponTypeInfoListResponse;
import com.xiaomi.nr.coupon.api.dto.couponconfig.GoodsItem;
import com.xiaomi.nr.coupon.api.dto.couponconfig.MissionDto;
import com.xiaomi.nr.coupon.constant.CouponConstant;
import com.xiaomi.nr.coupon.domain.couponconfig.ValidCouponConfigService;
import com.xiaomi.nr.coupon.domain.couponconfig.convert.CouponConfigConvert;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigInfo;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.enums.couponconfig.ConfigStatusEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.GoodsCouponRepository;
import com.xiaomi.nr.coupon.util.SystemHelper;
import com.xiaomi.nr.coupon.infrastructure.repository.localcache.LocalCacheCoupon;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.MissionCacheDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.po.MissionCacheItemPo;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ValidCouponConfigServiceImpl implements ValidCouponConfigService {

    @Resource
    private LocalCacheCoupon localCache;

    @Resource
    private MissionCacheDao missionCacheDao;

    @Resource
    private SystemHelper systemHelper;

    @Resource
    private CouponConfigConvert couponConfigConvert;

    @Resource
    private GoodsCouponRepository goodsCouponRepository;

    @Resource
    private CouponConfigRepository couponConfigRepository;


    /**
     * 根据SKU/套装ID、发放渠道查询有效券配置信息列表接口 (批量)
     *
     * @param sendChannel   券发放渠道
     * @param goodsItemList SKU/套装ID信息
     * @return List<>       券配置信息列表返回值
     * @throws BizError 业务异常
     */
    @Override
    public List<CouponTypeInfoListResponse> getCouponTypeInfo(String sendChannel, List<GoodsItem> goodsItemList) throws BizError {
        List<CouponTypeInfoListResponse> responseList = new ArrayList<>();
        for (GoodsItem goodsItem : goodsItemList) {
            Long id = goodsItem.getId();
            String level = goodsItem.getLevel();

            if (id == null || id < 0) {
                log.warn("coupon.CouponConfigService, sku/package id wrongful, id={}", id);
                throw ExceptionHelper.create(ErrCode.COUPON, "所传SKU/套装,id不合法, id=" + id);
            }
            if (StringUtils.isEmpty(level)) {
                log.warn("coupon.CouponConfigService, level wrongful, level={}", level);
                throw ExceptionHelper.create(ErrCode.COUPON, "所传SKU/套装,层级level不合法, level=" + level);
            }
            responseList.add(new CouponTypeInfoListResponse(goodsItem, getValidConfigList(id, level, sendChannel)));
        }

        return responseList;
    }


    /**
     * 根据券配置ID获取券配置信息(批量)
     *
     * @param configIdList 券配置ID列表
     * @return List<>      券配置信息列表
     */
    @Override
    public List<CouponTypeInfoDto> getCouponTypeInfoById(List<Long> configIdList, boolean withProductInfo) throws BizError {
        //根据券配置id查询缓存中券配置的相关信息
        Map<Long, CouponConfigItem> configCacheList = couponConfigRepository.getCouponConfigs(configIdList);

        //从缓存中获取券配置id和发放任务id对应的关系列表,拿到每个券配置id对应的发放任务id
        List<Long> validMissionIdList = getValidMissionId(configIdList);

        //根据发放任务id从缓存中获取发放任务信息
        Map<Long, MissionCacheItemPo> missionCacheList = getMissionCache(validMissionIdList);
        Map<Long, List<MissionCacheItemPo>> missionMap = getMissionMap(missionCacheList);

        Map<Long, CouponConfigItem> configCacheMap = new HashMap<>();
        for (Map.Entry<Long, CouponConfigItem> itemEntry : configCacheList.entrySet()) {
            configCacheMap.put(itemEntry.getKey().longValue(), itemEntry.getValue());
        }

        //组装最后的列表结果并返回
        return makeCouponTypeInfo(configCacheMap, missionMap, withProductInfo);
    }


    /**
     * 根据SKU/套装ID、发放渠道查询有效券配置信息列表接口主逻辑 (单个)
     *
     * @param id          SKU/套装ID
     * @param level       SKU/套装级别
     * @param sendChannel 发放渠道
     * @return List<>     券配置相关信息
     * @throws BizError 业务异常
     */
    private List<CouponTypeInfoDto> getValidConfigList(Long id, String level, String sendChannel) throws BizError {

        //根据SKU/套装id从缓存中获取券配置id等信息
        List<Long> configIdList = getConfigId(id, level);
        if (CollectionUtils.isEmpty(configIdList)) {
            return Collections.emptyList();
        }

        //根据券配置id查询缓存中券配置的相关信息
        Map<Long, CouponConfigItem> configCacheList = couponConfigRepository.getCouponConfigsOnlyBaseInfo(configIdList);
        if (MapUtils.isEmpty(configCacheList)) {
            return Collections.emptyList();
        }

        //根据发放渠道过滤出有效的券配置信息
        Map<Long, CouponConfigItem> validConfigCacheList = getValidSendChannelConfig(sendChannel, configCacheList);
        List<Long> validConfigId = new ArrayList<>(validConfigCacheList.keySet());

        //从缓存中获取券配置id和发放任务id对应的关系列表,拿到每个券配置id对应的发放任务id
        List<Long> validMissionIdList = getValidMissionId(validConfigId);

        //根据发放任务id从缓存中获取发放任务信息
        Map<Long, MissionCacheItemPo> missionCacheList = getMissionCache(validMissionIdList);
        Map<Long, List<MissionCacheItemPo>> missionMap = getMissionMap(missionCacheList);

        //组装最后的列表返回结果
        List<CouponTypeInfoDto> couponTypeInfoDtoList = makeCouponTypeInfo(validConfigCacheList, missionMap, false);

        //过滤掉无效的发放任务并返回结果
        return getValidCouponInfoDto(couponTypeInfoDtoList);
    }


    /**
     * 根据SKU/套装ID从缓存中获取券配置id列表
     *
     * @param id    SKU/套装ID
     * @param level SKU/套装
     * @return List<>    券配置id列表
     */
    private List<Long> getConfigId(long id, String level) {
        List<Long> configs = goodsCouponRepository.getGoodConfigs(id, level);
        if (CollectionUtils.isEmpty(configs)) {
            return Collections.emptyList();
        }
        return configs;
    }



    /**
     * 根据发放渠道过滤有效的券配置信息
     *
     * @param sendChannel 发放渠道
     * @param list        过滤前的券配置信息列表
     * @return List       过滤后的券配置信息列表
     */
    private Map<Long, CouponConfigItem> getValidSendChannelConfig(String sendChannel, Map<Long, CouponConfigItem> list) {
        Map<Long, CouponConfigItem> result = new HashMap<>(list.size());
        long nowTime = TimeUtil.getNowUnixSecond();

        for (Map.Entry<Long, CouponConfigItem> item : list.entrySet()) {
            CouponConfigInfo couponConfigInfo = item.getValue().getCouponConfigInfo();
            if (item.getValue() == null || couponConfigInfo.getEndFetchTime() == null) {
                continue;
            }
            if (ConfigStatusEnum.Online.getValue().intValue() != couponConfigInfo.getStatus()) {
                continue;
            }
            if (couponConfigInfo.getEndFetchTime() <= nowTime) {
                continue;
            }

            if (!StringUtils.equals(sendChannel, couponConfigInfo.getSendChannel())) {
                continue;
            }
            result.put(item.getKey().longValue(), item.getValue());
        }
        return result;
    }


    /**
     * 根据券配置id得到对应的发放任务id
     *
     * @param validConfigIdList 券配置id
     * @return List<Long>       发放任务id
     * @throws BizError 业务异常
     */
    private List<Long> getValidMissionId(List<Long> validConfigIdList) {
        List<Long> validMissionIdList = Lists.newArrayList();

        List<Long> notMatchIds = Lists.newArrayList();
        long limitId = systemHelper.isTestSystem() ? CouponConstant.NEW_COUPON_ID_LIMIT_TEST : CouponConstant.NEW_COUPON_ID_LIMIT;
        for (Long configId : validConfigIdList) {
            if(configId > limitId) {
                validMissionIdList.add(configId);
            } else {
                notMatchIds.add(configId);
            }
        }

        Map<Long, List<Long>> missionMapTypeList = missionCacheDao.getMissionIdMap(notMatchIds);
        for (List<Long> missionIds : missionMapTypeList.values()) {
            validMissionIdList.addAll(missionIds);
        }
        return validMissionIdList;
    }

    /**
     * 根据missionId获取发放任务缓存信息(批量)
     *
     * @param missionIds 发放任务ID
     * @return Map<>     发放任务缓存信息
     * @throws BizError 业务异常
     */
    private Map<Long, MissionCacheItemPo> getMissionCache(List<Long> missionIds) throws BizError {
        if (CollectionUtils.isEmpty(missionIds)) {
            return Collections.emptyMap();
        }

        long limitId = systemHelper.isTestSystem() ? CouponConstant.NEW_COUPON_ID_LIMIT_TEST : CouponConstant.NEW_COUPON_ID_LIMIT;

        List<Long> oldMissionIds = missionIds.stream().filter(configId -> configId < limitId ).collect(Collectors.toList());
        Map<Long, MissionCacheItemPo> missionCacheList = localCache.getCouponMission(oldMissionIds);

        if (CollectionUtils.isNotEmpty(oldMissionIds) && MapUtils.isEmpty(missionCacheList)) {
            log.warn("getMissionCache getCouponMission by missionId fail. missionIds={}", missionIds);
            throw ExceptionHelper.create(ErrCode.COUPON, "获取券任务配置信息失败");
        }
        return missionCacheList;
    }


    /**
     * 构建券配置id和券发放任务映射
     *
     * @param missionCacheList 券发放任务列表
     * @return Map             券配置id和券发放任务的对应关系列表
     */
    private Map<Long, List<MissionCacheItemPo>> getMissionMap(Map<Long, MissionCacheItemPo> missionCacheList) {
        Map<Long, List<MissionCacheItemPo>> resultMap = new HashMap<>();

        for (Map.Entry<Long, MissionCacheItemPo> item : missionCacheList.entrySet()) {
            long configId = item.getValue().getCouponConfigId();
            if (resultMap.containsKey(configId)) {
                resultMap.get(configId).add(item.getValue());
                continue;
            }
            resultMap.put(configId, Lists.newArrayList(item.getValue()));
        }
        return resultMap;
    }


    /**
     * 根据券配置信息和发放任务信息构建券配置DTO信息
     *
     * @param configCacheList 券配置信息列表
     * @param missionCacheMap 发放任务信息列表
     * @return List<>         接口返回值结构
     */
    private List<CouponTypeInfoDto> makeCouponTypeInfo(Map<Long, CouponConfigItem> configCacheList, Map<Long, List<MissionCacheItemPo>> missionCacheMap, boolean withProductInfo) throws BizError {
        List<CouponTypeInfoDto> resultList = new ArrayList<>();

        long limitId = systemHelper.isTestSystem() ? CouponConstant.NEW_COUPON_ID_LIMIT_TEST : CouponConstant.NEW_COUPON_ID_LIMIT;

        for (Map.Entry<Long, CouponConfigItem> item : configCacheList.entrySet()) {

            Long configId = item.getKey();
            if(configId == null || configId <= 0) {
                continue;
            }

            CouponTypeInfoDto couponTypeInfoDto = couponConfigConvert.initCouponTypeInfo(item.getValue(), withProductInfo);

            couponTypeInfoDto.setMissions(getMissionDtos(missionCacheMap, limitId, item.getValue()));

            if(couponTypeInfoDto == null || couponTypeInfoDto.getConfigId() == null || couponTypeInfoDto.getConfigId() <= 0) {
                continue;
            }
            resultList.add(couponTypeInfoDto);
        }
        return resultList;
    }

    /**
     * 获取任务信息
     * @param missionCacheMap
     * @param limitId
     * @param configItem
     * @return
     */
    private List<MissionDto> getMissionDtos(Map<Long, List<MissionCacheItemPo>> missionCacheMap, long limitId, CouponConfigItem configItem) {
        List<MissionDto> missionDtoList;
        if(configItem.getConfigId() > limitId) {
            missionDtoList = couponConfigConvert.getValidMissionDtoList(configItem);
        } else {
            missionDtoList = couponConfigConvert.getValidMissionDtoList(missionCacheMap.get(configItem.getConfigId()));
        }
        return missionDtoList;
    }


    /**
     * 过滤出无效的券发放任务
     *
     * @param couponTypeInfoDtoList 全量未过滤无效的券发放任务列表
     * @return List<>               有效的券发放任务列表
     */
    private List<CouponTypeInfoDto> getValidCouponInfoDto(List<CouponTypeInfoDto> couponTypeInfoDtoList){

        List<CouponTypeInfoDto> validCouponTypeInfoDtoList = new ArrayList<>();
        for(CouponTypeInfoDto couponTypeInfoDto : couponTypeInfoDtoList){
            List<MissionDto> missionDtoList = couponTypeInfoDto.getMissions();
            if(CollectionUtils.isEmpty(missionDtoList)){
                continue;
            }

            List<MissionDto> validMissionDtoList = new ArrayList<>();
            for(MissionDto missionDto : missionDtoList){
                if(checkValidMission(missionDto)){
                    validMissionDtoList.add(missionDto);
                }
            }
            couponTypeInfoDto.setMissions(validMissionDtoList);
            validCouponTypeInfoDtoList.add(couponTypeInfoDto);

        }

        return validCouponTypeInfoDtoList;
    }


    /**
     * 校验券配置信息是否有效
     *
     * @param missionDto   券任务结束时间
     * @return Boolean
     */
    private Boolean checkValidMission(MissionDto missionDto) {
        long nowTime = TimeUtil.getNowUnixSecond();
        int days = missionDto.getCouponDays();
        int hours = missionDto.getCouponHours();
        long couponEndTime = missionDto.getCouponEndTime();

        if (hours > 0 || days > 0) {
            couponEndTime = nowTime + (long) days * 86400 + (long) hours * 3600;
        }

        if (couponEndTime > nowTime) {
            return true;
        }
        return false;
    }

}
