package com.xiaomi.nr.coupon.domain.matchertool.core;

import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;

/**
 * 具体校验抽象
 *
 * <AUTHOR>
 * @date 2023/7/24
 */
public abstract class CheckerAbstract extends MatcherBaseMethod {

    /**
     * 校验
     *
     * @param ctx      MatcherContextDo
     * @param respItem MatcherRespItemDo
     * @param errCtx   MatcherErrContextDo
     * @return boolean
     */
    protected abstract boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx);

}