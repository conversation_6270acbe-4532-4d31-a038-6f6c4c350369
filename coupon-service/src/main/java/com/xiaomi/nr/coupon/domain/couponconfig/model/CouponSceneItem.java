package com.xiaomi.nr.coupon.domain.couponconfig.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * @description: 优惠券场景聚合类
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @Date 2022/2/26 8:19 下午
 * @Version: 1.0
 **/
@Data
@Accessors(chain = true)
public class CouponSceneItem implements Serializable {

    /**
     * 投放场景编码
     */
    private String sceneCode;

    /**
     * 投放方式  1:优惠券, 2:兑换码
     */
    private Integer sendMode;

    /**
     * 发放方式  1:外部系统发券, 2:内部系统灌券
     */
    private Set<Integer> assignMode;

    /**
     * 投放场景状态  1:上线, 2:下线
     */
    private Integer sceneStatus;

    /**
     * 可发放券的系统ID
     */
    private Set<String> appIds;
}
