package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import cn.hutool.core.map.MapUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponSceneItem;
import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Objects;
import java.util.Set;

/**
 * 校验发放权限
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Component
public class SceneAssignRights extends CheckerAbstract {

    @Override
    public boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {
        if (!ctx.isCheckSceneAssignRights()) {
            return true;
        }

        errCtx.setInvalidConfig(true);

        CouponSceneItem sceneInfo = MapUtil.isNotEmpty(ctx.getSceneInfoMap()) ? ctx.getSceneInfoMap().get(respItem.getSceneCode()) : null;
        Set<String> sceneAppIds = Objects.nonNull(sceneInfo) && Objects.nonNull(sceneInfo.getAppIds()) ? sceneInfo.getAppIds() : Collections.emptySet();

        if (CollectionUtils.isEmpty(sceneAppIds)) {
            errCtx.setErrCode(ErrCode.ASSIGN_COUPON_SCENE_CODE_EMPTY.getCode());
            errCtx.setErrMsg("投放场景无权使用");
            return false;
        }

        if (!sceneAppIds.contains(respItem.getAppId())) {
            errCtx.setErrCode(ErrCode.ASSIGN_COUPON_APPID_NOAUTH.getCode());
            errCtx.setErrMsg("没有权限发放此场景的券");
            return false;
        }

        return true;
    }
}
