package com.xiaomi.nr.coupon.infrastructure.rpc.sid;

import com.xiaomi.nr.coupon.constant.CommonConstant;
import com.xiaomi.nr.coupon.infrastructure.rpc.sid.sdk.Result;
import com.xiaomi.nr.coupon.infrastructure.rpc.sid.sdk.SidWrapper;
import com.xiaomi.nr.coupon.infrastructure.rpc.sid.sdk.idinfo;
import com.xiaomi.nr.coupon.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SidProxy {

    @Resource
    private SidWrapper sidClient;

    /**
     * 获取批量Sid列表
     *
     * @param userId 用户ID
     * @param count 数量
     * @return sid 列表
     */
    public List<Long> get(Long userId, Integer count) throws Exception {
        List<Long> result;
        try {
            result = getSids(userId, count);
        } catch (Exception e) {
            log.error("frist get sid error, err:{}", e.getMessage());
            result = getSids(userId, count);
        }
        return result;
    }

    private List<Long> getSids(Long userId, Integer count) throws Exception {
        long runStartTime = TimeUtil.getNowUnixMillis();
        sidClient.setTimeout(1000);
        Result res = sidClient.Get(count, "xiaomi_pulse","couponid", "xiaomi_pulse");
        log.info("sidserver.response，userId={}, count={}, res={}, runTime={}毫秒", userId, count, res, TimeUtil.sinceMillis(runStartTime));

        if(res == null){
            throw new Exception("请求sid服务返回失败");
        }

        if(res.getErrorNo() != CommonConstant.SUCCESS_CODE) {
            throw new Exception(String.format("sid服务返回请求失败，err=%s", res.getErrorMsg()));
        }

        if(res.getResponse().size() != count){
            throw new Exception(String.format("sid服务返回请求失败，count=%s, res.size=%s", count, res.getResponse().size()));
        }

        List<Long> result = new ArrayList<>();
        for(idinfo item : res.getResponse()){
            result.add(item.getId());
        }
        return result;
    }
}
