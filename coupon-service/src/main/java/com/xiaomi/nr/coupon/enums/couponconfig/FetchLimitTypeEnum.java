package com.xiaomi.nr.coupon.enums.couponconfig;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;

/**
 * 限领类型
 *
 * <AUTHOR>
 * @date 2024/1/4
 */
@Getter
@AllArgsConstructor
public enum FetchLimitTypeEnum {

    /**
     * 1: 限领
     */
    LIMIT(1, "限领"),

    /**
     * 2: 不限领
     */
    NO_LIMIT(2, "不限领"),
    ;

    /**
     * 枚举code
     */
    private final Integer code;

    /**
     * 枚举描述
     */
    private final String desc;

    private static final HashMap<Integer, FetchLimitTypeEnum> MAPPING = new HashMap<>();

    static {
        for (FetchLimitTypeEnum e : FetchLimitTypeEnum.values()) {
            MAPPING.put(e.getCode(), e);
        }
    }

    public static FetchLimitTypeEnum valueOf(Integer code) {
        return MAPPING.get(code);
    }


    public static boolean isFetchLimitType(Integer code) {
        return LIMIT.getCode().equals(code);
    }
}
