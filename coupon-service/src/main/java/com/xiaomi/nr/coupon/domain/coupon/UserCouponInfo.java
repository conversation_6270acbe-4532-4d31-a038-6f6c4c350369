package com.xiaomi.nr.coupon.domain.coupon;

import com.xiaomi.nr.coupon.api.dto.coupon.info.CouponInfoItemDto;
import com.xiaomi.nr.coupon.api.dto.coupon.info.CouponInfoResponse;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.common.UserCouponPush;
import com.xiaomi.nr.coupon.domain.common.model.CouponUsePushContext;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponConvert;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponFormatCommon;
import com.xiaomi.nr.coupon.domain.coupon.model.CouponInfoModel;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave.CouponInfoMapper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 优惠券信息
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class UserCouponInfo {

    @Resource
    private CouponInfoMapper couponInfoMapper;

    @Resource
    private CouponFormatCommon couponFormatCommon;

    @Resource
    private CouponConvert couponConvert;

    @Resource
    private UserCouponPush userCouponPush;


    /**
     * 查用户可用券总数
     *
     * @param userId    Long
     * @param couponIds List<Long>
     * @return CouponInfoResponse
     */
    public CouponInfoResponse getCouponInfo(Long userId, List<Long> couponIds) throws BizError {
        CouponInfoResponse result = new CouponInfoResponse();
        result.setData(Collections.emptyMap());
        List<CouponPo> list;
        if (userId == null) {
            list = couponInfoMapper.getCouponInfoCouponIds(couponIds);
        } else {
            list = couponInfoMapper.batchGetCouponInfo(userId, couponIds);
        }

        if (list == null || list.size() == 0) {
            return result;
        }

        //修正状态：lock的也算是已使用状态
        list.forEach(item -> {
            String status = item.getStat();
            if (CouponStatusEnum.LOCKED.getValue().equals(status)) {
                status = CouponStatusEnum.USED.getValue();
            }
            item.setStat(status);
        });

        List<Integer> bizPlatform = Lists.newArrayList(BizPlatformEnum.RETAIL.getCode(), BizPlatformEnum.CAR.getCode());

        //格式化优惠券信息(po -> model)
        List<CouponInfoModel> modelList = couponFormatCommon.formatCouponList(bizPlatform, list);

        //model -> dto
        Map<Long, CouponInfoItemDto> data = new HashMap<>();
        for (CouponInfoModel item : modelList) {
            CouponInfoItemDto info = couponConvert.convert2CouponInfoDto(item, CouponInfoItemDto::new);
            if (info == null) {
                continue;
            }
            data.put(item.getId(), info);
        }

        result.setData(data);
        return result;
    }

    /**
     * 券使用变动消息,异步处理，防止调用方超时影响主流程
     */
    public void couponUsePush(Long userId, List<Long> couponIds, Integer status, Long modifyTime, Long orderId) {
        log.info("CouponInfo couponUsePush couponId:{}", couponIds);
        List<CouponPo> couponPos = couponInfoMapper.batchGetCouponInfo(userId, couponIds);
        if (CollectionUtils.isEmpty(couponPos)) {
            log.warn("CouponInfo couponUsePush no coupon found couponId:{}", couponIds);
            return;
        }

        CouponUsePushContext couponUsePushContext = new CouponUsePushContext(userId, null, couponPos, status, modifyTime, orderId);
        userCouponPush.couponUsePush(couponUsePushContext);
    }
}
