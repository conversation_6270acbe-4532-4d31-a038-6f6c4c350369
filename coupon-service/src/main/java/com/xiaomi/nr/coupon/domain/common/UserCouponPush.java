package com.xiaomi.nr.coupon.domain.common;

import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.common.model.CouponUsePushContext;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mq.CarCouponUsePushMqProducer;
import com.xiaomi.nr.coupon.infrastructure.repository.mq.CouponUsePushMqProducer;
import com.xiaomi.nr.coupon.infrastructure.repository.mq.model.CouponUsePushMqHeader;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.support.RocketMQHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 优惠券信息
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class UserCouponPush {

    @Autowired
    private CouponConfigRepository couponConfigRepository;


    @Autowired
    private CouponUsePushMqProducer couponUsePushMqProducer;

    @Value("${rocketmq.couponUsePush.topic}")
    private String couponUsePushTopic;

    @Autowired
    private CarCouponUsePushMqProducer carCouponUsePushMqProducer;

    @Value("${rocketmq.carCouponUsePush.topic}")
    private String carCouponUsePushTopic;


    @Value("${spring.profiles.active}")
    private String profile;

    @Async("asyncExecutor")
    public void couponUsePush(CouponUsePushContext context) {

        Long userId = context.getUserId() ;
        String vid = context.getVid() ;
        List<CouponPo> couponPos = context.getCouponPos();
        Integer status = context.getStatus();
        Long modifyTimeMillis = context.getModifyTimeMillis();
        Long orderId = context.getOrderId();

        if (CollectionUtils.isEmpty(couponPos)) {
            log.warn("CouponInfo couponUsePush no coupon found couponPos:{}", couponPos);
            return;
        }

        for (CouponPo couponPo : couponPos) {
            if (couponPo == null) {
                log.warn("CouponInfo couponUsePush no coupon found couponId");
                return;
            }
            Long couponId = couponPo.getId();
            CouponConfigItem couponConfigItem = couponConfigRepository.getCouponConfig(couponPo.getTypeId());
            // 3C场景
            if (!"7C783BEBB0D1C882E09DA5031B8EAEBF".equals(couponConfigItem.getCouponConfigInfo().getSendScene())
                    && !"8D5D96C7D9E680EF29FF1F2FC87B8DF8".equals(couponConfigItem.getCouponConfigInfo().getSendScene())
                    // 服务包场景
                    && !"08B818C8A63BD1849391A0430CDDCBA8".equals(couponConfigItem.getCouponConfigInfo().getSendScene())
                    && !"136A560EF374DC3F6945EB3325AE28CD".equals(couponConfigItem.getCouponConfigInfo().getSendScene())) {
                return;
            }

            Map<String, Object> msgMap = new HashMap<>();
            CouponUsePushMqHeader couponUsePushMqHeader = new CouponUsePushMqHeader();
            couponUsePushMqHeader.setScene(couponConfigItem.getCouponConfigInfo().getSendScene());
            couponUsePushMqHeader.setProfile(profile);
            msgMap.put("header", couponUsePushMqHeader);

            Map<String, Object> bodyMap = new HashMap<>();
            long modifyTime = modifyTimeMillis / 1000;
            bodyMap.put("unikey", couponId + "_" + modifyTime);
            if (Objects.nonNull(userId) && userId > 0) {
                bodyMap.put("userId", userId);
            }
            if (StringUtils.isNotBlank(vid)) {
                bodyMap.put("vid", vid);
            }
            bodyMap.put("configId", couponPo.getTypeId());
            bodyMap.put("couponId", couponId);
            bodyMap.put("status", status);
            bodyMap.put("modifyTimeMillis", modifyTimeMillis);
            bodyMap.put("modifyTime", modifyTime);
            bodyMap.put("orderId", orderId);
            bodyMap.put("bizPlatform", couponPo.getBizPlatform());
            msgMap.put("body", bodyMap);

            Message message = MessageBuilder.withPayload(GsonUtil.toJson(msgMap)).setHeader(RocketMQHeaders.KEYS, couponId + "_" + modifyTimeMillis).build();
            BizPlatformEnum bizPlatformEnum = BizPlatformEnum.valueOf(couponPo.getBizPlatform());
            switch (bizPlatformEnum) {
                case RETAIL:
                case CAR:
                    couponUsePush(couponId, message);
                    break;
                case CAR_AFTER_SALE:
                    carCouponUsePush(couponId, message);
            }
        }
    }

    private void couponUsePush(Long couponId, Message message) {
        try {
            couponUsePushMqProducer.send(couponUsePushTopic, message);
        } catch (Exception e) {
            log.error("CouponInfo  error couponId:{}", couponId, e);
            couponUsePushMqProducer.send(message);
        }
    }

    private void carCouponUsePush(Long couponId, Message message) {
        try {
            carCouponUsePushMqProducer.send(carCouponUsePushTopic, message);
        } catch (Exception e) {
            log.error("CouponInfo carCouponUsePush error couponId:{}", couponId, e);
            carCouponUsePushMqProducer.send(message);
        }
    }
}
