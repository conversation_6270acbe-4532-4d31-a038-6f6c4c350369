package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po;

import lombok.Data;

import java.io.Serializable;

/**
 * 汽车优惠券核销流水po
 *
 * <AUTHOR>
 * @date 2024/5/10 15:10
 */
@Data
public class CarCouponLogPo implements Serializable {
    private static final long serialVersionUID = 8572957535987584020L;

    /**
     * id
     */
    private Long id;

    /**
     * 汽车券id
     */
    private Long couponId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 车辆vid
     */
    private String vid;

    /**
     * 日志类型，1-核销，2-退还
     */
    private Integer logType;

    /**
     * 日志描述
     */
    private String logDesc;

    /**
     * 使用次数变化，使用-，退还+
     */
    private Integer changeTimes;

    /**
     * 所属业务，0-3c零售，3-汽车零售，4-汽车售后
     */
    private Integer bizPlatform;

    /**
     * 所属业务，0-3c零售，3-汽车零售，4-汽车售后
     */
    private Long addTime;
}
