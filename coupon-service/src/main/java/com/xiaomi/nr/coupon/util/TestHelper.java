package com.xiaomi.nr.coupon.util;

import static com.xiaomi.nr.coupon.util.NumberUtil.inRange;

/**
 * 压测测试辅助
 *
 * <AUTHOR>
 * @date 2021/4/25
 */
public class TestHelper {

    /**
     * 是否是压测用户
     *
     * @param userId 用户ID
     * @return true/false
     */
    public static boolean isLoadRunnerUser(Long userId) {
        return isNewLoad(userId);
    }

    /**
     * 是否新压测用户
     *
     * @param userId 用户ID
     * @return 是否
     */
    private static boolean isNewLoad(Long userId) {
        long pre = userId / 10;
        long last = userId % 10;
        if (inRange(pre, 120462513L, 120512512L) && last == 4L) {
            return true;
        }
        if (inRange(pre, 120412513L, 120462512L) && last == 4L) {
            return true;
        }
        return false;
    }
}
