package com.xiaomi.nr.coupon.domain.couponconfig.model;

import com.xiaomi.nr.coupon.enums.couponconfig.ConfigStatusEnum;
import lombok.Data;

/**
 * 优惠券描述
 */
@Data
public class CouponConfigInfo {

    /**
     * 配置名称
     */
    private String name;

    /**
     * 状态 1:上线, 2:下线, 3:终止
     */
    private Integer status;

    /**
     * 使用范围描述
     */
    private String couponDesc;

    /**
     * 投放场景
     */
    private String sendScene;

    /**
     * 可领取的开始时间
     */
    private Long startFetchTime;

    /**
     * 可领取的结束时间
     */
    private Long endFetchTime;

    /**
     * 使用有效期的类型
     */
    private Integer useTimeType;

    /**
     * 可使用的开始时间
     */
    private Long startUseTime;

    /**
     * 可使用的结束时间
     */
    private Long endUseTime;

    /**
     * 有效时长(单位小时)
     */
    private Integer useDuration;

    /**
     * 门槛类型 1满元 2满件 3每满元 4每满件
     */
    private Integer bottomType;

    /**
     * 满元门槛值（单位分）
     */
    private Integer bottomPrice;

    /**
     * 满件门槛值（单位个）
     */
    private Integer bottomCount;

    /**
     * 优惠值（单位个/分 折）
     */
    private Long promotionValue;

    /**
     * 最大减免金额（单位分）
     */
    private Integer maxReduce;

    /**
     * 可发放的总数量
     */
    private Integer applyCount;

    /**
     * 每人限领的数量
     */
    private Integer fetchLimit;

    /**
     * 发放渠道
     */
    private String sendChannel;

    /**
     * 优惠券类型  1: 商品券 2: 运费券
     */
    private Integer couponType = 1;

    /**
     * 履约方式 -1: 所有方式 139: 门店闪送 (这个跟交易中台统一定义)
     */
    private Integer shipmentId = -1;

    /**
     * 服务场景：基础保养、漆面修复、上门补胎
     */
    private Integer serviceScene;

    /**
     * 限领类型 1限领 2不限领
     */
    private Integer fetchLimitType;

    /**
     * 使用次数限制，1-限制，2-不限制
     */
    private Integer timesLimit = 1;

    /**
     * 公开推广
     */
    private Integer publicPromotion = 2;

    /**
     * 是否为上线状态
     * @return
     */
    public Boolean isOnline() {
        return status.equals(ConfigStatusEnum.Online.getValue());
    }

    /**
     * 判断优惠券领取有效期 （当前可用）
     * @param currentTime
     * @return
     */
    public Boolean isFetchAblePeriod(long currentTime) {
        return startFetchTime < currentTime && endFetchTime > currentTime;
    }

    /**
     * 判断优惠券领取有效期 （当前可用 + 未来可用）
     * @param currentTime 当前时间
     * @param futureUsable 是否未来可用
     * @return
     */
    public Boolean isFetchAblePeriod(long currentTime, boolean withFuture) {
        if (withFuture) {
            return endFetchTime > currentTime;
        } else {
            return startFetchTime < currentTime && endFetchTime > currentTime;
        }
    }
}
