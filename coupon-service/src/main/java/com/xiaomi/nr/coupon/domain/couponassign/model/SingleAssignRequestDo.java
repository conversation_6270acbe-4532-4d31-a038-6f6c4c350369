package com.xiaomi.nr.coupon.domain.couponassign.model;

import lombok.Data;

/**
 * 单张券发放参数
 *
 * <AUTHOR>
 */
@Data
public class SingleAssignRequestDo {

    /**
     * 请求id（长度限制在40个字符内，非常重要，要求单用户级的全局唯一）
     */
    private String requestId;

    /**
     * 用户id（发给此用户）
     */
    private Long userId;

    /**
     * 业务领域
     */
    private Integer bizPlatform;

    /**
     * vid，发放汽车售后服务券且sendMode=2时必填
     */
    private String vid;

    /**
     * 投放场景编码（不同的场景编码不同）
     */
    private String sceneCode;

    /**
     * 优惠券配置ID
     */
    private Long configId;

    /**
     * 券活动ID
     */
    private String activityId;

    /**
     * 发放方式  1:外部系统发券（默认）, 2:内部系统灌券
     */
    private Integer assignMode = 1;

    /**
     * 分享人小米ID
     */
    private Long shareUserId;

    /**
     * 门店ID
     */
    private String orgCode;

    /**
     * 发放关联的订单号
     */
    private Long orderId;

    /**
     * appId
     */
    private String appId;

    /**
     * 券开始使用时间（指定有效期需要传）
     * 已有场景：服务包场景
     */
    private Long startUseTime;

    /**
     * 券结束使用时间（指定有效期需要传）
     * 已有场景：服务包场景
     */
    private Long endUseTime;
}
