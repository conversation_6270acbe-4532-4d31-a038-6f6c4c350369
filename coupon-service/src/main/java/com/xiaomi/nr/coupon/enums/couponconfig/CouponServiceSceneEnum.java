package com.xiaomi.nr.coupon.enums.couponconfig;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Objects;

/**
 * 券服务场景枚举
 *
 * <AUTHOR>
 * @date 2024/1/2
 */
@Getter
@AllArgsConstructor
public enum CouponServiceSceneEnum {
    /**
     * 1: 基础保养
     */
    BASIC_MAINTENANCE(1, "基础保养"),

    /**
     * 2: 漆面修复
     */
    PAINT_REPAIR(2, "漆面修复"),

    /**
     * 3: 上门补胎
     */
    TO_DOOR_TIRE_REPAIR(3, "上门补胎"),
    ;

    /**
     * 枚举code
     */
    private final Integer code;

    /**
     * 枚举描述
     */
    private final String name;

    private static final HashMap<Integer, CouponServiceSceneEnum> MAPPING = new HashMap<>();

    static {
        for (CouponServiceSceneEnum e : CouponServiceSceneEnum.values()) {
            MAPPING.put(e.getCode(), e);
        }
    }

    public static CouponServiceSceneEnum valueOf(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        return MAPPING.get(code);
    }
}
