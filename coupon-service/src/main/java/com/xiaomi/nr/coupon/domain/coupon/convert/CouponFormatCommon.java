package com.xiaomi.nr.coupon.domain.coupon.convert;

import com.xiaomi.nr.coupon.api.dto.coupon.info.CustomDetailDto;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.constant.CommonConstant;
import com.xiaomi.nr.coupon.constant.CouponConfigConstant;
import com.xiaomi.nr.coupon.domain.coupon.model.CouponInfoModel;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigInfo;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.GoodScope;
import com.xiaomi.nr.coupon.enums.coupon.CouponUseChannelEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.*;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.util.NumberUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 优惠券信息格式化逻辑处理
 */
@Slf4j
@Service
public class CouponFormatCommon {

    @Autowired
    private CouponConvert couponConvert;

    @Autowired
    private CouponConfigRepository couponConfigRepository;


    /**
     * 用户优惠券统一格式化方法
     *
     * @param couponPoList 用户优惠券实体列表(DB)
     * @return List<>      优惠券基础信息(优惠券+券配置)
     * @throws BizError    业务异常
     */
    public List<CouponInfoModel> formatCouponList(List<Integer> bizPlatform, List<CouponPo> couponPoList) throws BizError {

        //优惠券实体(DB)合法性校验
        if (!checkCouponPo(couponPoList)) {
            return null;
        }

        //批量获取券配置信息
        List<Long> configIdList = new ArrayList<>(couponPoList.size());
        couponPoList.forEach(couponPo -> configIdList.add(couponPo.getTypeId()));
        Map<Long, CouponConfigItem> configCacheMap = getConfigCache(configIdList);

        //优惠券信息格式化(db+redis -> couponInfoModel)
        List<CouponInfoModel> couponInfoModelList = new ArrayList<>();
        for (CouponPo couponPo : couponPoList) {

            //取出券配置信息,并判断是否合法
            CouponConfigItem configCache = configCacheMap.get(couponPo.getTypeId());
            if (!checkCouponConfig(configCache, bizPlatform)) {
                continue;
            }

            //优惠券基础信息转换
            CouponInfoModel couponInfoModel = couponConvert.convertPoToModel(couponPo, configCache);
            //格式化优惠券扩展信息
            setCouponAIS(couponInfoModel);
            couponInfoModelList.add(couponInfoModel);
        }

        return couponInfoModelList;
    }



    /**
     * 格式化优惠券的扩展信息
     *
     * @param couponInfoModel  优惠券基础信息
     */
    public void setCouponAIS(CouponInfoModel couponInfoModel) {

        //格式化优惠券是否为单个商品可用券
        /*GoodScope goodsInclude = couponInfoModel.getDeductTargetGoods();
        if(Objects.isNull(goodsInclude)){
            log.info("coupon.CouponFormatCommon.setCouponAIS(), couponConfigId={}, goodsInclude=null", couponInfoModel.getConfigId());
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠券配置商品信息为空!");
        }*/

        //Boolean oneGoodsUse = isOneGoodsUse(goodsInclude);
        //OneGoodsUseInfoDto oneGoodsUseInfoDto = oneGoodsUse ? getOneGoodsUseInfo(goodsInclude) : null;

        //格式化useChannelGroup(AppTag)信息
        List<String> useChannelGroup = getUseChannelGroup(couponInfoModel.getConfigId(), couponInfoModel.getUseChannel());

        //格式化优惠券定制规则信息
        CustomDetailDto customDetailDto = getCustomDetail(couponInfoModel);

        //couponInfoModel.setIsOneGoodsUse(oneGoodsUse);
        //couponInfoModel.setOneGoodsUseInfoDto(oneGoodsUseInfoDto);
        couponInfoModel.setUseChannelGroup(useChannelGroup);
        couponInfoModel.setUseChannelDesc(getUseChannelDesc(couponInfoModel.getUseChannel(), couponInfoModel.getCouponType(), couponInfoModel.getShipmentId(), couponInfoModel.getBizPlatform()));
        couponInfoModel.setCustomRuleDetail(customDetailDto);

    }



    /**
     * 格式化优惠券规则名称
     *
     * @return configItem  优惠券基础信息
     */
    public CustomDetailDto getCustomDetail(CouponInfoModel configItem) {

        PromotionType useTypeEnum = PromotionType.getPromotionType(configItem.getUseType());
        if (Objects.isNull(useTypeEnum)) {
            log.warn("coupon.CouponCommonServiceImpl.getCustomDetail(), 非法的券配置类型, useType={}", configItem.getUseType());
            return null;
        }

        Long reduceMoney = configItem.getReduceMoney();
        Integer quotaType = configItem.getQuotaType();
        Integer quotaCount = configItem.getQuotaCount();
        Integer quotaMoney = configItem.getQuotaMoney();
        Long reduceDiscount = configItem.getReduceDiscount();
        Integer couponType = configItem.getCouponType();
        Integer shipmentId = configItem.getShipmentId();

        switch (useTypeEnum) {
            //满减券
            case ConditionReduce:
            case DirectReduce:
                return getCouponCashRuleName(reduceMoney, quotaType, quotaCount, quotaMoney, couponType, shipmentId);

            //折扣券
            case ConditionDiscount:
                return getCouponDiscountRuleName(reduceDiscount, quotaType, quotaCount, quotaMoney);

            //兑换券
            case NyuanBuy:
                return getCouponDeductibleRuleName(quotaType, quotaCount, quotaMoney);

            default:
                return null;
        }

    }

    public CustomDetailDto getCustomDetail(CouponConfigItem couponConfig, BottomTypeEnum bottomType) {
        PromotionType promotionType = couponConfig.getPromotionType();
        if (Objects.isNull(promotionType)) {
            log.warn("coupon.CouponCommonServiceImpl.getCustomDetail(), 非法的券配置类型, promotionType={}", promotionType);
            return null;
        }
        CouponConfigInfo configInfo = couponConfig.getCouponConfigInfo();
        switch (promotionType) {
            //现金券
            case ConditionReduce:
            case DirectReduce:
                return getCouponCashRuleName(configInfo.getPromotionValue(), bottomType.getCode(), configInfo.getBottomCount(), configInfo.getBottomPrice(), configInfo.getCouponType(), configInfo.getShipmentId());

            //折扣券
            case ConditionDiscount:
                return getCouponDiscountRuleName(configInfo.getPromotionValue(), bottomType.getCode(), configInfo.getBottomCount(), configInfo.getBottomPrice());

            //抵扣券
            case NyuanBuy:
                return getCouponDeductibleRuleName(bottomType.getCode(), configInfo.getBottomCount(), configInfo.getBottomPrice());

            default:
                return null;
        }

    }


    /**
     * 获取每张券的UseChannelGroup(AppTag)信息
     *
     * @param useChannelList 优惠券使用渠道列表
     * @return List<>        优惠券使用渠道分组列表
     */
    private List<String> getUseChannelGroup(Long configId, List<String> useChannelList) {
        if(CollectionUtils.isEmpty(useChannelList)){
            log.warn("coupon.CouponCommonServiceImpl.getUseChannelGroup(), couponConfigId={}, 优惠券使用范围为null!", configId);
            return Collections.emptyList();
        }

        //根据useChannel匹配对应的使用渠道分组
        List<String> useChannelGroupList = new ArrayList<>();
        useChannelList.forEach(useChannel -> {
            String appTag = getUseChannelGroup(useChannel);
            if (StringUtils.isNotEmpty(appTag)) {
                useChannelGroupList.add(appTag);
            }
        });

        return useChannelGroupList.stream().distinct().collect(Collectors.toList());
    }


    /**
     * 获取每张券的UseChannelName(AppTagInfo)信息
     *
     * @param useChannelList 优惠券使用渠道列表
     * @return String        优惠券使用渠道信息
     */
    public String getUseChannelDesc(List<String> useChannelList, Integer couponType, Integer shipmentId, Integer bizPlatform) {

        // 整车返回"仅购车可用"
        if (BizPlatformEnum.CAR.getCode().equals(bizPlatform)) {
            return "仅购车可用";
        }

        if(CouponTypeEnum.PostFee.getValue().equals(couponType)) {
            if(ShipmentIdEnum.Lightning.getValue().equals(shipmentId)) {
                return "仅小米之家门店闪送可用";
            }
        }

        if (CollectionUtils.isEmpty(useChannelList)) {
            return CommonConstant.EMPTY_STR;
        }

        //文案排序：小米商城 > 小米之家 > 授权店
        Map<String ,Integer> sortMap = new HashMap<>();
        useChannelList.forEach(useChannel -> sortMap.put(useChannel, CouponConfigConstant.USECHANNEL_SORT.get(useChannel)));
        List<String> useChanneSortlList = sortMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());

        //根据useChannel匹配对应的useChannelName信息
        List<String> useChannelDescList = new LinkedList<>();
        useChanneSortlList.forEach(useChannel -> useChannelDescList.add(CouponUseChannelEnum.findNameByValue(useChannel)));
        if(useChanneSortlList.contains(CouponUseChannelEnum.MI_SHOP.getValue())
                && !useChanneSortlList.contains(CouponUseChannelEnum.MI_HOME_ZY.getValue())
                && !useChanneSortlList.contains(CouponUseChannelEnum.MI_HOME_ZM.getValue())){
          useChannelDescList.set(useChannelDescList.indexOf(CouponUseChannelEnum.MI_SHOP.getName()), CouponConfigConstant.MISHOP_ONLINE_DESC);
        }

        return "仅" + StringUtils.join(useChannelDescList.toArray(), "/") + "可用";
    }


    /**
     * CouponPo 合法性校验
     *
     * @param couponPoList 用户优惠券实体列表
     * @return bool        true:合法，false:不合法
     */
    private Boolean checkCouponPo(List<CouponPo> couponPoList) {
        if (CollectionUtils.isEmpty(couponPoList)) {
            log.info("coupon.CouponCommonServiceImpl.checkCouponPo(), couponPoList信息为null, couponPoList={}", couponPoList);
            return false;
        }

        for (CouponPo couponPo : couponPoList) {
            if (Objects.isNull(couponPo)) {
                log.info("coupon.CouponCommonServiceImpl.checkCouponPo(), coupon信息不合法, couponPo=null");
                continue;
            }
            if (couponPo.getId() == null || couponPo.getId() < CommonConstant.ONE_LONG) {
                log.info("coupon.CouponCommonServiceImpl.checkCouponPo(), couponId不合法, couponPo={}", couponPo);
                continue;
            }
            if (couponPo.getTypeId() == null || couponPo.getTypeId() < CommonConstant.ONE_LONG) {
                log.info("coupon.CouponCommonServiceImpl.checkCouponPo(), configId不合法, couponPo={}", couponPo);
            }
        }

        return true;
    }


    /**
     * CouponPo 合法性校验
     *
     * @param configCache 用户优惠券实体
     * @return bool       true:合法，false:不合法
     */
    private Boolean checkCouponConfig(CouponConfigItem configCache, List<Integer> bizPlatform) {

        if (Objects.isNull(configCache)) {
            log.info("coupon.CouponCommonServiceImpl.checkCouponConfig(), 券配置信息为null, couponConfigCache=null");
            return false;
        }

        if (configCache.getConfigId() < CommonConstant.ONE_LONG) {
            log.info("coupon.CouponCommonServiceImpl.checkCouponConfig(), 券配置ID不合法, couponConfigCache={}", configCache);
            return false;
        }

        if (!bizPlatform.contains(configCache.getBizPlatform())) {
            return false;
        }

        return true;
    }


    /**
     * 从缓存获取券类型配置信息
     *
     * @param configIdList       券配置(类型)id列表
     * @return ConfigCacheItemPo 券配置缓存信息
     */
    private Map<Long, CouponConfigItem> getConfigCache(List<Long> configIdList) throws BizError {
        //调用本地缓存，获取券类型配置信息
        Map<Long, CouponConfigItem> couponConfigCacheMap = couponConfigRepository.getCouponConfigsOnlyBaseInfo(configIdList);
        if (MapUtils.isEmpty(couponConfigCacheMap)) {
            log.info("coupon.CouponFormatCommon.getConfigCache(), configIdList={}, couponConfigCacheMap=null", configIdList);
            throw ExceptionHelper.create(ErrCode.COUPON, "本地缓存获取券配置信息为null!");
        }
        return couponConfigCacheMap;
    }


    /**
     * 判断使用渠道(mi_shop,mi_home,mi_authorized)
     *
     * @param useChannel 优惠券使用渠道
     * @return String    优惠券使用渠道分组信息
     */
    private String getUseChannelGroup(String useChannel) {
        if (CouponConfigConstant.USECHANNEL_GROUP_MAP.containsKey(useChannel)) {
            return CouponConfigConstant.USECHANNEL_GROUP_MAP.get(useChannel);
        }
        return CommonConstant.EMPTY_STR;
    }


    /**
     * 判断该优惠券是否为单个商品可用
     *
     * @param goodsInclude 可用商品信息
     * @return Boolean     true:单个商品可用, false:非单个商品可用
     */
    private Boolean isOneGoodsUse(GoodScope goodsInclude) {
        int count = CommonConstant.ZERO_INT;
        Set<Long> skuMap = goodsInclude.getSkus();
        if (!CollectionUtils.isEmpty(skuMap) && skuMap.size() == CommonConstant.ONE_INT) {
            count++;
        }

        Set<Long> goodsMap = goodsInclude.getGoods();
        if (!CollectionUtils.isEmpty(goodsMap) && goodsMap.size() == CommonConstant.ONE_INT) {
            count++;
        }

        Set<Long> packageMap = goodsInclude.getPackages();
        if (!CollectionUtils.isEmpty(packageMap) && packageMap.size() == CommonConstant.ONE_INT) {
            count++;
        }

        return count == CommonConstant.ONE_INT;
    }


    /**
     * 获取抵扣券规则名称
     *
     * @param quotaType      配额类型(满元/满件)
     * @param quotaCount     满*件
     * @param quotaMoney     满*元
     * @return CustomDetailDto  优惠券规则信息
     */
    private CustomDetailDto getCouponDeductibleRuleName(Integer quotaType, Integer quotaCount, Integer quotaMoney) {
        String threshold = "";
        String thresholdUnit = "";

        //满件
        if (Objects.equals(QuotaTypeEnum.Count.getCode(), quotaType)) {
            threshold = quotaCount.toString();
            thresholdUnit = "件";
        }

        //满元
        if (Objects.equals(QuotaTypeEnum.Money.getCode(), quotaType)) {
            threshold = NumberUtil.centToYuan(quotaMoney, 2, RoundingMode.UP);
            thresholdUnit = "元";
        }

        CustomDetailDto customDetailDto = new CustomDetailDto();
        customDetailDto.setDesc("兑换券");
        customDetailDto.setThreshold(threshold);
        customDetailDto.setThresholdUnit(thresholdUnit);
        customDetailDto.setBenefit("商品兑换");
        customDetailDto.setBenefitPre("");
        customDetailDto.setBenefitUnit("");
        customDetailDto.setUseTypeDesc("商品兑换");
        customDetailDto.setCouponRuleDesc("商品兑换券");
        customDetailDto.setRuleIndex("商品兑换券");
        return customDetailDto;
    }


    /**
     * 格式化折扣券规则名称
     *
     * @param reduceDiscount 优惠折扣
     * @param quotaType      配额类型(满元/满件)
     * @param quotaCount     满*件
     * @param quotaMoney     满*元
     * @return CustomDetailDto 优惠券规则信息
     */
    private CustomDetailDto getCouponDiscountRuleName(Long reduceDiscount, Integer quotaType, Integer quotaCount, Integer quotaMoney) {
        CustomDetailDto customDetailDto = new CustomDetailDto();
        if (Objects.isNull(reduceDiscount) || reduceDiscount < CommonConstant.ZERO_LONG) {
            log.info("coupopn.CouponCommonServiceImpl.getCouponCashRuleName(), 错误的折扣, reduceDiscount={}", reduceDiscount);
            return customDetailDto;
        }

        String reduceDiscountStr = NumberUtil.tenCentToYuan(reduceDiscount,2, RoundingMode.DOWN);
        StringBuilder desc = new StringBuilder();
        StringBuilder ruleIndex = new StringBuilder();
        String threshold = "";
        String thresholdUnit = "";

        //满件
        if (Objects.equals(QuotaTypeEnum.Count.getCode(), quotaType)) {
            if (quotaCount == CommonConstant.ONE_LONG) {
                desc.append("无门槛");
                ruleIndex.append("下单享").append(reduceDiscountStr).append("折");
            } else {
                desc.append("满").append(quotaCount).append("件可用");
                ruleIndex.append("满").append(quotaCount).append("件享").append(reduceDiscountStr).append("折");
            }
            threshold = quotaCount.toString();
            thresholdUnit = "件";
        }

        //满元
        if (Objects.equals(QuotaTypeEnum.Money.getCode(), quotaType)) {
            String quotaMoneyStr = NumberUtil.centToYuan(quotaMoney, 2, RoundingMode.UP);
            desc.append("满").append(quotaMoneyStr).append("元可用");
            ruleIndex.append("满").append(quotaMoneyStr).append("享").append(reduceDiscountStr).append("折");
            threshold = quotaMoneyStr;
            thresholdUnit = "元";
        }

        customDetailDto.setDesc(String.valueOf(desc));
        customDetailDto.setThreshold(threshold);
        customDetailDto.setThresholdUnit(thresholdUnit);
        customDetailDto.setBenefit(reduceDiscountStr);
        customDetailDto.setBenefitPre("");
        customDetailDto.setBenefitUnit("折");
        customDetailDto.setUseTypeDesc("限时折扣");
        customDetailDto.setCouponRuleDesc(reduceDiscountStr + "折优惠券");
        customDetailDto.setRuleIndex(String.valueOf(ruleIndex));
        return customDetailDto;
    }


    /**
     * 格式化现金券规则名称
     *
     * @param reduceMoney 优惠金额
     * @param quotaType   配额类型(满元/满件)
     * @param quotaCount  满*件
     * @param quotaMoney  满*元
     * @return CustomDetailDto 优惠券规则信息
     */
    private CustomDetailDto getCouponCashRuleName(Long reduceMoney, Integer quotaType, Integer quotaCount, Integer quotaMoney, Integer couponType, Integer shipmentId) {
        CustomDetailDto customDetailDto = new CustomDetailDto();
        if (Objects.isNull(reduceMoney) || reduceMoney < CommonConstant.ZERO_LONG) {
            log.info("coupopn.CouponCommonServiceImpl.getCouponCashRuleName(), 错误的减*元,reduceMoney={}", reduceMoney);
            return customDetailDto;
        }

        String reduceMoneyStr = NumberUtil.centToYuan(reduceMoney,2 , RoundingMode.DOWN);
        String quotaMoneyStr = NumberUtil.centToYuan(quotaMoney,2, RoundingMode.UP);

        String desc = "";
        String couponRuleDesc = "";
        String useTypeDesc = "限时满减";
        String ruleIndex = "";
        String threshold = "";
        String thresholdUnit = "";

        //满件
        if (Objects.equals(QuotaTypeEnum.Count.getCode(), quotaType)) {
            if (quotaCount == CommonConstant.ONE_LONG) {
                if(CouponTypeEnum.PostFee.getValue().equals(couponType)) {
                    desc = "运费立减";
                    String ruleIndexPre = "";
                    if(ShipmentIdEnum.Lightning.getValue().equals(shipmentId)) {
                        ruleIndexPre = "闪送";
                    }
                    ruleIndex = ruleIndexPre + "运费减" + reduceMoneyStr;

                    useTypeDesc = "限时特惠";
                    couponRuleDesc = reduceMoneyStr + "元运费券";
                    if(Objects.equals(ShipmentIdEnum.Lightning.getValue(), shipmentId)){
                        couponRuleDesc = reduceMoneyStr + "元运费券，仅小米之家门店闪送可用";
                    }
                } else {
                    desc = "立减券";
                    ruleIndex = reduceMoneyStr + "元立减券";
                    couponRuleDesc = "满" + quotaCount + "件减" + reduceMoneyStr + "元优惠券";
                }
            } else {
                desc = "满" + quotaCount + "件可用";
                ruleIndex = "满" + quotaCount + "件减" + reduceMoneyStr;
                couponRuleDesc = "满" + quotaCount + "件减" + reduceMoneyStr + "元优惠券";
            }
            threshold = quotaCount.toString();
            thresholdUnit = "件";
        }

        //满元
        if (Objects.equals(QuotaTypeEnum.Money.getCode(), quotaType)) {
            desc = "满" + quotaMoneyStr + "元可用";
            couponRuleDesc = "满" + quotaMoneyStr + "元减" + reduceMoneyStr + "元优惠券";
            ruleIndex = "满" + quotaMoneyStr + "减" + reduceMoneyStr;
            threshold = quotaMoneyStr;
            thresholdUnit = "元";
        }

        customDetailDto.setDesc(desc);
        customDetailDto.setThreshold(threshold);
        customDetailDto.setThresholdUnit(thresholdUnit);
        customDetailDto.setBenefit(reduceMoneyStr);
        customDetailDto.setBenefitPre("￥");
        customDetailDto.setBenefitUnit("元");
        customDetailDto.setUseTypeDesc(useTypeDesc);
        customDetailDto.setCouponRuleDesc(couponRuleDesc);
        customDetailDto.setRuleIndex(ruleIndex);
        return customDetailDto;
    }

}
