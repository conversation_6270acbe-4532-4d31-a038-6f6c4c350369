package com.xiaomi.nr.coupon.domain.coupontrade.tradecheck;

import com.xiaomi.nr.coupon.enums.couponconfig.CouponServiceTypeEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/11 14:43
 */
@Component
public class AfterSaleCouponConsumeToolsFactory {
    /**
     * AfterSaleCouponConsumeTools 工厂
     * Map<CouponServiceTypeEnum, AfterSaleCouponConsumeTools>
     */
    private static final Map<CouponServiceTypeEnum, AfterSaleCouponConsumeTools> AFTER_SALE_COUPON_CONSUME_TOOLS_MAP = new HashMap<>();

    public static void register(CouponServiceTypeEnum serviceTypeEnum, AfterSaleCouponConsumeTools afterSaleCouponConsumeTools) {
        if (Objects.nonNull(serviceTypeEnum)) {
            AFTER_SALE_COUPON_CONSUME_TOOLS_MAP.put(serviceTypeEnum, afterSaleCouponConsumeTools);
        }
    }

    public AfterSaleCouponConsumeTools getAfterSaleCouponConsumeTools(CouponServiceTypeEnum serviceTypeEnum) {
        return AFTER_SALE_COUPON_CONSUME_TOOLS_MAP.get(serviceTypeEnum);
    }
}
