package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.po.CouponScenePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description: 优惠券场景mapper
 * @author: heji<PERSON><PERSON>
 * @Date 2022/2/26 9:39 下午
 * @Version: 1.0
 **/
@Mapper
@Component
public interface CouponSceneMapper {

    /**
     * 获取优惠券场景
     *
     */
    @Select("select id, scene_code, send_mode, assign_mode, status from nr_coupon_scene")
    List<CouponScenePo> getAllCouponScene();


}
