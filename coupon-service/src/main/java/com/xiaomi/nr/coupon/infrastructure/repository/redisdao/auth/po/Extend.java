package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.auth.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import java.io.Serializable;

/**
 * Client扩展信息
 */
@Data
public class Extend implements Serializable {

    private static final long serialVersionUID = -8571245859854614916L;

    @SerializedName("discountMin")
    private String discountMin;

    @SerializedName("shipmentExpense")
    private String shipmentExpense;

    @SerializedName("defaultPayId")
    private String defaultPayId;

    @SerializedName("displayCurrency")
    private String displayCurrency;

    @SerializedName("country_name")
    private String countryName;

    @SerializedName("wms_name")
    private String wmsName;

    @SerializedName("order_from")
    private String orderFrom;

    @SerializedName("xmss_channel")
    private String xmssChannel;

    @SerializedName("currency")
    private String currency;

    @SerializedName("CODExpense")
    private String codexpense;

    @SerializedName("is_online")
    private Integer isOnline;

    @SerializedName("client_group")
    private Long clientGroup;

    @SerializedName("display_name")
    private String displayName;
}
