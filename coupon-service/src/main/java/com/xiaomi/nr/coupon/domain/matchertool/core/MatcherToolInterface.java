package com.xiaomi.nr.coupon.domain.matchertool.core;

import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherBaseReqDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.enums.matchertool.MatcherToolTypeEnum;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

/**
 * 匹配器工具
 *
 * <AUTHOR>
 * @date 2023/7/13
 */
public interface MatcherToolInterface {

    /**
     * 匹配器工具类型枚举
     *
     * @return MatcherToolTypeEnum
     */
    MatcherToolTypeEnum getMatcherToolTypeEnum();

    /**
     * 主执行方法
     *
     * @param request MatcherBaseReqDo
     * @return MatcherContextDo
     * @throws BizError .
     */
    MatcherContextDo execute(MatcherBaseReqDo request) throws BizError;

}