package com.xiaomi.nr.coupon.domain.couponconfig.convert;

import com.xiaomi.nr.coupon.api.dto.couponconfig.CouponTypeInfoDto;
import com.xiaomi.nr.coupon.api.dto.couponconfig.GoodsItem;
import com.xiaomi.nr.coupon.api.dto.couponconfig.MissionDto;
import com.xiaomi.nr.coupon.api.dto.couponconfig.PromotionVO;
import com.xiaomi.nr.coupon.constant.CouponConfigConstant;
import com.xiaomi.nr.coupon.constant.CouponConstant;
import com.xiaomi.nr.coupon.domain.common.FormatCouponUtil;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponFormatCommon;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigInfo;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.GoodScope;
import com.xiaomi.nr.coupon.enums.couponconfig.BottomTypeEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.ConfigStatusEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.OutputStatusEnum;
import com.xiaomi.nr.coupon.enums.couponmission.MissionTypeEnum;
import com.xiaomi.nr.coupon.enums.couponmission.TimeTypeEnum;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.po.MissionCacheItemPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CouponConfigConvert {

    @Resource
    private CouponFormatCommon couponFormatCommon;

    /**
     * 构建券配置DTO信息，configCache、missionCache -> CouponTypeInfoDto
     *
     * @param configCache      券配置缓存信息
     * @return CouponTypeInfoDto 券配置接口返回信息
     */
    public CouponTypeInfoDto initCouponTypeInfo(CouponConfigItem configCache, boolean withProductInfo) throws BizError {
        if (Objects.isNull(configCache)) {
            log.warn("initCouponTypeInfo fail, param error ConfigCacheItemPo is null");
            return null;
        }

        //检验是否是本地缓存的空对象
        Integer configId = configCache.getConfigId();
        if (configId == null) {
            return null;
        }

        CouponConfigInfo couponConfigInfo = configCache.getCouponConfigInfo();

        String useTypeDesc = configCache.getPromotionType().getName();
        Map<String, String> showMap = FormatCouponUtil.formatCouponShowValue(configCache.getPromotionType().getCode(), couponConfigInfo.getPromotionValue());
        String showValue = showMap.get(CouponConfigConstant.SHOW_VALUE);
        String showUnit = showMap.get(CouponConfigConstant.SHOW_UNIT);
        BigDecimal reduceMaxPrice = convertReduceMaxPrice(couponConfigInfo.getMaxReduce().longValue());

        BottomTypeEnum bottomType = BottomTypeEnum.findByCode(couponConfigInfo.getBottomType());

        String quotaValue = convertQuotaValue(bottomType, couponConfigInfo.getBottomPrice(), couponConfigInfo.getBottomCount());

        CouponTypeInfoDto couponTypeInfoDto = new CouponTypeInfoDto();
        couponTypeInfoDto.setConfigId(configId.longValue());
        couponTypeInfoDto.setConfigName(couponConfigInfo.getName());
        couponTypeInfoDto.setCouponType(couponConfigInfo.getCouponType());
        couponTypeInfoDto.setShipmentId(couponConfigInfo.getShipmentId());
        couponTypeInfoDto.setTypeCode(configCache.getPromotionType().getValue());
        couponTypeInfoDto.setTypeCodeDesc(useTypeDesc);
        couponTypeInfoDto.setUseType(configCache.getPromotionType().getValue());
        couponTypeInfoDto.setUseTypeDesc(useTypeDesc);
        couponTypeInfoDto.setQuotaType(bottomType.getValue());
        couponTypeInfoDto.setQuotaValue(quotaValue);
        couponTypeInfoDto.setShowValue(showValue);
        couponTypeInfoDto.setShowUnit(showUnit);
        couponTypeInfoDto.setReduceMaxPrice(reduceMaxPrice);
        couponTypeInfoDto.setStatus(OutputStatusEnum.Approved.getMysqlValue());
        couponTypeInfoDto.setStatusDesc(OutputStatusEnum.Approved.getName());
        couponTypeInfoDto.setSendChannel(couponConfigInfo.getSendChannel());
        couponTypeInfoDto.setRangeDesc(couponConfigInfo.getCouponDesc());
        couponTypeInfoDto.setAddTime(0L);
        couponTypeInfoDto.setOnlineStatus(configCache.getCouponConfigInfo().getStatus());
        couponTypeInfoDto.setStartFetchTime(couponConfigInfo.getStartFetchTime());
        couponTypeInfoDto.setEndFetchTime(couponConfigInfo.getEndFetchTime());
        if (MapUtils.isNotEmpty(configCache.getUseChannelStore())) {
            couponTypeInfoDto.setUseChannels((new ArrayList<>(configCache.getUseChannelStore().keySet())));
        }
        couponTypeInfoDto.setSendScene(couponConfigInfo.getSendScene());
        couponTypeInfoDto.setUserFetchLimit(couponConfigInfo.getFetchLimit());
        if (withProductInfo) {
            couponTypeInfoDto.setGoodItems(getProductInfo(configCache.getGoodScope()));
        }
        //格式化优惠券定制规则信息
        couponTypeInfoDto.setCustomDetail(couponFormatCommon.getCustomDetail(configCache, bottomType));
        couponTypeInfoDto.setPromotionVO(convertToPromotionVO(configCache));
        return couponTypeInfoDto;
    }

    private PromotionVO convertToPromotionVO(CouponConfigItem configCache) {
        PromotionVO promotionVO = new PromotionVO();
        promotionVO.setPromotionType(configCache.getPromotionType().getCode());
        promotionVO.setPromotionValue(configCache.getCouponConfigInfo().getPromotionValue());
        promotionVO.setBottomType(configCache.getCouponConfigInfo().getBottomType());
        promotionVO.setBottomPrice(configCache.getCouponConfigInfo().getBottomPrice());
        promotionVO.setBottomCount(configCache.getCouponConfigInfo().getBottomCount());
        promotionVO.setMaxReduce(configCache.getCouponConfigInfo().getMaxReduce());
        return promotionVO;
    }


    /**
     * 封装商品信息
     *
     * @param goodScope
     * @return
     */
    private List<GoodsItem> getProductInfo(GoodScope goodScope) {
        List<GoodsItem> goodsItems = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(goodScope.getSkus())) {
            for (Long sku : goodScope.getSkus()) {
                GoodsItem goodsItem = new GoodsItem();
                goodsItem.setId(sku);
                goodsItem.setLevel(GoodsLevelEnum.Sku.getValue());
                goodsItems.add(goodsItem);
            }
        }
        if (CollectionUtils.isNotEmpty(goodScope.getPackages())) {
            for (Long pack : goodScope.getPackages()) {
                GoodsItem goodsItem = new GoodsItem();
                goodsItem.setId(pack);
                goodsItem.setLevel(GoodsLevelEnum.Package.getValue());
                goodsItems.add(goodsItem);
            }
        }
        if (CollectionUtils.isNotEmpty(goodScope.getSsus())) {
            for (Long ssu : goodScope.getSsus()) {
                GoodsItem goodsItem = new GoodsItem();
                goodsItem.setId(ssu);
                goodsItem.setLevel(GoodsLevelEnum.Ssu.getValue());
                goodsItems.add(goodsItem);
            }
        }
        return goodsItems;
    }

    /**
     * 配额输出转换
     *
     * @param bottomType String
     * @param quotaMoney Long
     * @param quotaCount Long
     * @return String
     */
    private String convertQuotaValue(BottomTypeEnum bottomType, Integer quotaMoney, Integer quotaCount) {
        if (BottomTypeEnum.Money == bottomType || BottomTypeEnum.EveMoney == bottomType) {
            BigDecimal showMoney = new BigDecimal(String.valueOf(quotaMoney));
            return showMoney.divide(new BigDecimal("100"), 2, BigDecimal.ROUND_UP).stripTrailingZeros().toPlainString();
        }

        if (BottomTypeEnum.Count == bottomType || BottomTypeEnum.EveCount == bottomType) {
            return String.valueOf(quotaCount);
        }

        //超出已知范围了
        return "";
    }

    /**
     * 转换抵扣券最大可抵扣金额（单位元）
     *
     * @param maxPrice Long
     * @return BigDecimal
     */
    public BigDecimal convertReduceMaxPrice(Long maxPrice) {
        BigDecimal money = new BigDecimal(maxPrice);
        return money.divide(new BigDecimal("100"), 2, BigDecimal.ROUND_DOWN).stripTrailingZeros();
    }

    /**
     * missionCache -> missionDto
     *
     * @param missionCache 券发放任务缓存信息
     * @return MissionDto  券发放任务Dto信息
     */
    public MissionDto convertMission(MissionCacheItemPo missionCache) {
        if (Objects.isNull(missionCache)) {
            return null;
        }
        String timeType = missionCache.getTimeType();

        MissionDto missionDto = new MissionDto();
        missionDto.setMissionId(missionCache.getId());
        missionDto.setMissionName(missionCache.getName());
        missionDto.setMissionType(missionCache.getMissionType());
        missionDto.setSendNumLimit(missionCache.getMaxSendNum());
        missionDto.setTimeType(timeType);
        missionDto.setTimeTypeDesc(formatTimeTypeDesc(timeType));
        missionDto.setCouponStartTime(missionCache.getCouponStartTime());
        missionDto.setCouponEndTime(missionCache.getCouponEndTime());
        missionDto.setCouponDays(missionCache.getDays());
        missionDto.setCouponHours(missionCache.getHours());
        missionDto.setAddTime(missionCache.getAddTime());

        return missionDto;
    }

    /**
     * missionCache -> missionDto
     *
     * @param couponConfigItem 券发放任务缓存信息
     * @return MissionDto  券发放任务Dto信息
     */
    public MissionDto convertMission(CouponConfigItem couponConfigItem) {

        Integer days = couponConfigItem.getCouponConfigInfo().getUseDuration();
        String timeType = days == 0 ? TimeTypeEnum.SECTION.getRedisValue() : TimeTypeEnum.DAYS.getRedisValue();
        Long id = Long.parseLong(String.valueOf(couponConfigItem.getConfigId()));
        String name = "";

        String missionType = MissionTypeEnum.findRedisValueBymysqlValue(1);

        MissionDto missionDto = new MissionDto();
        missionDto.setMissionId(id);
        missionDto.setMissionName(name);
        missionDto.setMissionType(missionType);
        missionDto.setSendNumLimit(couponConfigItem.getCouponConfigInfo().getApplyCount().longValue());
        missionDto.setTimeType(timeType);
        missionDto.setTimeTypeDesc(formatTimeTypeDesc(timeType));
        missionDto.setCouponStartTime(couponConfigItem.getCouponConfigInfo().getStartUseTime());
        missionDto.setCouponEndTime(couponConfigItem.getCouponConfigInfo().getEndUseTime());
        missionDto.setCouponDays(days / 24);
        missionDto.setCouponHours(days % 24);
        missionDto.setAddTime(0L);

        return missionDto;
    }


    /**
     * 批量转换发放任务信息 cache -> dto
     *
     * @param missionCacheList 发放任务缓存列表
     * @return List<MissionDto> 发放任务返回信息列表
     */
    public List<MissionDto> getValidMissionDtoList(List<MissionCacheItemPo> missionCacheList) {
        List<MissionDto> missionDtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(missionCacheList)) {
            return missionDtoList;
        }

        missionCacheList = missionCacheList.stream().sorted(Comparator.comparing(MissionCacheItemPo::getSendStartTime)).collect(Collectors.toList());

        for (MissionCacheItemPo missionCacheItemPo : missionCacheList) {
            MissionDto missionDto = convertMission(missionCacheItemPo);
            if (Objects.isNull(missionDto)) {
                continue;
            }
            missionDtoList.add(missionDto);
        }
        return missionDtoList;
    }


    /**
     * 批量转换发放任务信息 cache -> dto
     *
     * @param couponConfigItem
     * @return List<MissionDto> 发放任务返回信息列表
     */
    public List<MissionDto> getValidMissionDtoList(CouponConfigItem couponConfigItem) {

        List<MissionDto> missionDtoList = new ArrayList<>();

        if (Objects.isNull(couponConfigItem)) {
            return missionDtoList;
        }

        missionDtoList.add(convertMission(couponConfigItem));

        return missionDtoList;
    }

    /**
     * 券的真实有效期类别描述
     *
     * @param timeType 券有效期类型
     * @return String  券有效期类型描述
     */
    public String formatTimeTypeDesc(String timeType) {
        if (StringUtils.equals(TimeTypeEnum.SECTION.getRedisValue(), timeType)) {
            return TimeTypeEnum.SECTION.getName();
        }

        if (StringUtils.equals(TimeTypeEnum.DAYS.getRedisValue(), timeType)) {
            return TimeTypeEnum.DAYS.getName();
        }
        return "";
    }
}
