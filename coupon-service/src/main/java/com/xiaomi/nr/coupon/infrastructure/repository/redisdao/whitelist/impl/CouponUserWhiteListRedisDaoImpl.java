package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.whitelist.impl;

import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.whitelist.CouponUserWhiteListRedisDao;
import com.xiaomi.nr.coupon.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description 
 * <AUTHOR>
 * @date 2025-04-01 15:03
*/
@Slf4j
@Component
public class CouponUserWhiteListRedisDaoImpl implements CouponUserWhiteListRedisDao {

    private static final String WHITE_LIST_SHARD_KEY = "car:coupon:user:whitelist:{shardkey}";

    private static final int SHARD_COUNT = 64;

    @Autowired
    @Qualifier("whiteListRedisTemplate")
    private RedisTemplate<String, Number> redisTemplate;


    @Override
    public List<Long> getWhiteList() {
        SetOperations<String, Number> operations = redisTemplate.opsForSet();
        Set<Number> whiteList = new HashSet<>();
        try {
            for (int i = 0; i < SHARD_COUNT; i++) {
                String key = StringUtil.formatContent(WHITE_LIST_SHARD_KEY, String.valueOf(i));
                Set<Number> members = Optional.ofNullable(operations.members(key)).orElse(new HashSet<>());
                whiteList.addAll(members);
            }
        } catch (Exception e) {
            log.error("CouponUserWhiteListRedisDaoImpl.getWhiteList error. err:", e);
        }

        return whiteList.stream().map(Number::longValue).collect(Collectors.toList());
    }
}
