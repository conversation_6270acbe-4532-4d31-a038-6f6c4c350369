package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponassign;

import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;
import java.util.Map;

/**
 * 优惠券发放
 *
 * <AUTHOR>
 */
public interface CouponAssignRedisDao {

    /**
     * 获取用户已领取某个券的数量
     *
     * @param userId   long
     * @param configId long
     * @return int
     */
    int getUserAssignCount(long userId, long configId) throws Exception;

    /**
     * 获取vid已领取某个券的数量
     *
     * @param vid      String
     * @param configId long
     * @return int
     */
    int getVidAssignCount(String vid, long configId) throws Exception;

    /**
     * 获取某个券已领取总数量
     *
     * @param configId long
     * @return int
     */
    int getCouponAssignCount(long configId) throws Exception;

    /**
     * 用户已领取某个券的数量加1，并校验是否超过最大发放数量
     *
     * @param userId       long
     * @param configId     long
     * @param maxCount     int
     * @param endFetchTime long
     * @throws BizError .
     */
    void incrUserAssignCount(long userId, long configId, int maxCount, long endFetchTime) throws BizError;

    /**
     * vid已领取某个券的数量加1，并校验是否超过最大发放数量
     *
     * @param vid          String
     * @param configId     long
     * @param maxCount     int
     * @param endFetchTime long
     * @throws BizError .
     */
    void incrVidAssignCount(String vid, long configId, int maxCount, long endFetchTime) throws BizError;

    /**
     * 用户已领取某个券的数量减1
     *
     * @param userId   long
     * @param configId long
     */
    void decrUserAssignCount(long userId, long configId);

    /**
     * 用户已领取某个券的数量减1
     *
     * @param vid      String
     * @param configId long
     */
    void decrVidAssignCount(String vid, long configId);

    /**
     * 某个券已领取总数量加1，并校验是否超过最大发放数量
     *
     * @param configId long
     * @param maxCount int
     * @throws BizError .
     */
    void incrCouponAssignCount(Long userId, long configId, int maxCount) throws BizError;

    /**
     * 某个券已领取总数量减1
     *
     * @param configId long
     */
    void decrCouponAssignCount(long userId, long configId);

    /**
     * 批量获取领券数量
     *
     * @param uid
     * @param configIdList
     * @return
     */
    Map<Long, Integer> batchGetFetchCount(long uid, List<Long> configIdList) throws BizError;

    /**
     * 批量券的发放数量
     *
     * @param configIdList
     * @return
     */
    Map<Long, Integer> batchGetInventory(List<Long> configIdList) throws BizError;

}
