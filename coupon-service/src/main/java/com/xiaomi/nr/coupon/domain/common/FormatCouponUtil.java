package com.xiaomi.nr.coupon.domain.common;

import com.xiaomi.nr.coupon.constant.CommonConstant;
import com.xiaomi.nr.coupon.constant.CouponConfigConstant;
import com.xiaomi.nr.coupon.enums.couponconfig.PromotionType;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.util.NumberUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;

import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 优惠券、券配置公共格式化逻辑
 */
@Slf4j
public class FormatCouponUtil {


    /**
     * 格式化展示showValue、showUnit
     *
     * @param useType        优惠券类型
     * @param promotionValue    优惠金额/折扣力度
     * @return 格式化后的showValue、showUnit
     * @throws BizError      业务异常
     */
    public static Map<String, String> formatCouponShowValue(Integer useType, Long promotionValue) throws BizError {

        PromotionType promotionType = PromotionType.getPromotionType(useType);
        if(Objects.isNull(promotionType)){
            log.warn("coupon.FormatCouponUtil.FormatCouponShowValue(), 非法的优惠券类型:useType={}", useType);
            throw ExceptionHelper.create(ErrCode.COUPON, "非法的优惠券类型!");
        }

        Map<String, String> showMap = new HashMap<>(2);
        switch (promotionType) {
            case ConditionReduce:
            case DirectReduce:
                showMap.put(CouponConfigConstant.SHOW_VALUE, NumberUtil.centToYuan(promotionValue,2, RoundingMode.DOWN));
                showMap.put(CouponConfigConstant.SHOW_UNIT, CouponConfigConstant.COUPON_CASH_UNIT);
                return showMap;
            case ConditionDiscount:
                showMap.put(CouponConfigConstant.SHOW_VALUE, NumberUtil.centToYuan(promotionValue,2, RoundingMode.DOWN));
                showMap.put(CouponConfigConstant.SHOW_UNIT, CouponConfigConstant.COUPON_DISCOUNT_UNIT);
                return showMap;
            case NyuanBuy:
                showMap.put(CouponConfigConstant.SHOW_VALUE, NumberUtil.centToYuan(promotionValue,2, RoundingMode.DOWN));
                showMap.put(CouponConfigConstant.SHOW_UNIT, CommonConstant.EMPTY_STR);
                return showMap;
            default:
                showMap.put(CouponConfigConstant.SHOW_VALUE, CommonConstant.EMPTY_STR);
                showMap.put(CouponConfigConstant.SHOW_UNIT, CommonConstant.EMPTY_STR);
                return showMap;
        }
    }

}
