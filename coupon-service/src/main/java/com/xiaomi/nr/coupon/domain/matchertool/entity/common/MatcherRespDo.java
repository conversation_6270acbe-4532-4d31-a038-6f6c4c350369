package com.xiaomi.nr.coupon.domain.matchertool.entity.common;

import com.xiaomi.nr.coupon.domain.coupon.model.CouponInfoModel;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import lombok.Data;

import java.util.*;

/**
 * NoCodeMatcherRespDo
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Data
public class MatcherRespDo {

    /**
     * 匹配结果：校验结果
     */
    private List<MatcherRespItemDo> checkResp = new ArrayList<>();

    /**
     * 区配结果：可用商品，取传入商品与券配置可用商品的交集，商品信息=>券配置ID列表
     */
    private Map<MatcherGoodsItemDo, List<Long>> validGoods = new HashMap<>();

    /**
     * 券配置信息（原始），券配置ID=>券配置信息
     */
    private Map<Long, CouponConfigItem> configs = new HashMap<>();

    /**
     * 券配置信息（转换后的），券配置ID=>券配置信息
     */
    private Map<Long, CouponInfoModel> configModels = new HashMap<>();

}
