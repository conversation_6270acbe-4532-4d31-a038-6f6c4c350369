package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.po;

import lombok.Data;

import java.io.Serializable;

/**
 * 券发放任务id和券配置id间的映射关系实体
 */
@Data
public class MissionMapType implements Serializable {

    private static final long serialVersionUID = 8122249723395674335L;

    /**
     * 券发放任务id
     */
    private  Long missionId;

    /**
     * 券配置id
     */
    private Long couponConfigId;

    public MissionMapType(){}

    public MissionMapType(Long missionId,Long couponConfigId){
        this.missionId = missionId;
        this.couponConfigId = couponConfigId;
    }
}
