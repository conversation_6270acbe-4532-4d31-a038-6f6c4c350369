package com.xiaomi.nr.coupon.domain.couponconfig.model;

import lombok.Data;

import java.util.HashSet;
import java.util.Set;

/**
 * @description: 使用渠道实体
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/2/27 8:39 下午
 * @Version: 1.0
 **/
@Data
public class UseChannel {

    /**
     * 范围 true:全部门店 false:部分门店
     */
    private Boolean all;

    /**
     * 门店ID列表 如果门店范围是全部门店，则此列表为空
     */
    private Set<String> limitIds = new HashSet<>();
}
