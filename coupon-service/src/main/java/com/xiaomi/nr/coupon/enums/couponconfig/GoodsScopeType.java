package com.xiaomi.nr.coupon.enums.couponconfig;

import lombok.Getter;

@Getter
public enum GoodsScopeType {
    /**
     * 商品
     */
    Goods(1, "商品券"),
    /**
     * 分类
     */
    Categories(2, "品类券"),


    ;


    private final int value;
    private final String name;

    GoodsScopeType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static GoodsScopeType getGoodsScopeType(int code) {
        for (GoodsScopeType goodsScopeType : GoodsScopeType.values()) {
            if (goodsScopeType.value == code) {
                return goodsScopeType;
            }
        }
        return null;
    }

}
