package com.xiaomi.nr.coupon.domain.common.model;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * 消息推送上下文
 *
 * <AUTHOR>
 * @date 2024/5/31
 */
@Data
@AllArgsConstructor
public class CouponUsePushContext {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * vid
     */
    private String vid;

    /**
     * 优惠券列表
     */
    private List<CouponPo> couponPos;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 修改时间
     */
    private Long modifyTimeMillis;

    /**
     * 订单号
     */
    private Long orderId;
}
