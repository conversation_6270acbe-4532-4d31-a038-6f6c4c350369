package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.po.CouponConfigPO;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.po.*;

import java.util.List;
import java.util.Map;

/**
 * 优惠券配置redis缓存操作对象
 *
 * <AUTHOR>
 */
public interface CouponConfigRedisDao {

    /**
     * 获取单个券配置信息缓存
     * @param configId Long
     * @return ConfigCacheItemPo
     */
    ConfigCacheItemPo get(Long configId);

    /**
     * 批量获取券配置缓存
     *
     * @param configIds List<Long>
     * @return Map<Long, ConfigCacheItemPo>
     */
    Map<Long, ConfigCacheItemPo> get(List<Long> configIds);

    /**
     * 获取券全部信息
     *
     * @param configId
     * @return
     */
    CouponConfigPO getAllCouponInfo(Integer configId);

    /**
     * 获取券配置基本信息
     *
     * @param configId
     * @return
     */
    CouponConfigPO getOnlyBaseInfo(Integer configId);

    /**
     * 获取券配置商品信息
     *
     * @param configId
     * @return
     */
    String getOnlyGoodInfo(Integer configId);

    /**
     * 获取券配置基本信息
     *
     * @param configIds
     * @return
     */
    Map<Integer, CouponConfigPO> batchGetOnlyBaseInfo(List<Integer> configIds);

    /**
     * 所有－有效的券配置ID列表读redis
     *
     * @return List<Long>
     */
    ConfigIdListCachePo getNoCodeValidConfigIdList();

}

