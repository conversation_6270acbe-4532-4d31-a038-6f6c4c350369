package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.po;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: 优惠券场景PO
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/2/26 9:41 下午
 * @Version: 1.0
 **/
@Data
public class ServiceScenePo implements Serializable {

    private static final long serialVersionUID = -3684118840409811709L;

    /**
     * 投放场景id
     */
    private Integer id;

    /**
     * 投放场景编码
     */
    private String name;

    /**
     * 优惠券类型 1:商品券 2:运费券 3：超级补贴券  11：服务抵扣券 12：不限次服务卡
     */
    private Integer couponType;

    /**
     * 抵扣规则: 1-全部抵扣; 2-部分抵扣
     */
    private Integer deductRule;

    /**
     * 互斥规则：1-互斥; 2-叠加
     */
    private Integer mutualRule;
}
