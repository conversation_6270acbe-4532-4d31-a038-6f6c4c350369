package com.xiaomi.nr.coupon.domain.common.mqpush.service;

import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.common.model.MessageBuildContext;
import com.xiaomi.nr.coupon.domain.common.mqpush.CouponUsePushFactory;
import com.xiaomi.nr.coupon.domain.common.mqpush.CouponUsePushService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025-01-08 20:27
 */
@Slf4j
@Component
public class CarPushService implements CouponUsePushService {

    private final Set<String> sceneSet = new HashSet<>();

    @PostConstruct
    public void init() {
        CouponUsePushFactory.register(getBizType(), this);
    }

    @Override
    public void sendMessage(Message<?> message, Long couponId, String sceneCode) {
        log.info("Car platform sending message for couponId: {}, sceneCode: {}", couponId, sceneCode);
    }

    @Override
    public Message<?> buildMessage(MessageBuildContext context) {
        log.info("Building message for car platform with context: {}", context);
        return null;
    }

    @Override
    public Set<String> getSceneSet() {
        return this.sceneSet;
    }

    @Override
    public BizPlatformEnum getBizType() {
        return BizPlatformEnum.CAR;
    }
}
