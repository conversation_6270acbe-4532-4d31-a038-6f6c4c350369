package com.xiaomi.nr.coupon.domain.couponconfig;

import com.xiaomi.nr.coupon.api.dto.couponconfig.ValidConfigListRequest;
import com.xiaomi.nr.coupon.api.dto.couponconfig.ValidConfigListResponse;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

public interface GoodsCouponConfigService {

    /**
     * 获取商品(sku|package)有效券列表
     * @param request ValidConfigListRequest
     * @return ValidConfigListResponse
     */
    ValidConfigListResponse getGoodsValidCoupon(ValidConfigListRequest request) throws BizError;
}
