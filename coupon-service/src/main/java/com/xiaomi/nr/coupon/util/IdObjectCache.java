package com.xiaomi.nr.coupon.util;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 由于本地缓存以及接口实现等，包含大量sku与couponConfig对应关系，会创建大量的Long对象（大约是对应id的100倍），
 * 为减少对象数量，减轻GC压力和减少停顿时间，这里将id的Object对象缓存
 * 注意：因调用方法频次会很高，这里提供的方法不会包含空等校验，也不会String.trim
 */
public class IdObjectCache {
    private static Map<Long, Long> configIdObjectCache = new ConcurrentHashMap<>();

    private static Map<Long, Long> skuObjectCache = new ConcurrentHashMap<>();

    public static Long getConfigId(Long configId){
        Long ret = configIdObjectCache.get(configId);
        if(ret == null){
            configIdObjectCache.put(configId,configId);
            return configId;
        }
        return ret;
    }

    public static Long getConfigId(String couponIdStr){
        Long configId = Long.parseLong(couponIdStr);
        return getConfigId(configId);
    }

    public static Long getGood(Long sku){
        Long ret = skuObjectCache.get(sku);
        if(ret == null){
            skuObjectCache.put(sku,sku);
            return sku;
        }
        return ret;
    }

    public static Long getGood(String skuStr){
        Long sku = Long.parseLong(skuStr);
        return getGood(sku);
    }
}
