package com.xiaomi.nr.coupon.domain.couponconfig;

import com.xiaomi.nr.coupon.api.dto.couponconfig.CouponTypeInfoDto;
import com.xiaomi.nr.coupon.api.dto.couponconfig.CouponTypeInfoListResponse;
import com.xiaomi.nr.coupon.api.dto.couponconfig.GoodsItem;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ValidCouponConfigService {


    /**
     * 根据SKU/套装ID、发放渠道查询有效券配置信息列表接口 (批量)
     *
     * @param sendChannel    券发放渠道
     * @param goodsItemList  SKU/套装ID信息
     * @return List<>        券配置信息列表返回值
     * @throws BizError      业务异常
     */
    List<CouponTypeInfoListResponse> getCouponTypeInfo(String sendChannel, List<GoodsItem> goodsItemList) throws BizError;



    /**
     * 根据券配置ID获取券配置信息(批量)
     *
     * @param configIdList 券配置ID列表
     * @return List<>      券配置信息列表
     * @throws BizError
     */
    List<CouponTypeInfoDto> getCouponTypeInfoById(List<Long> configIdList, boolean withProductInfo) throws BizError;
}
