/**
 * Autogenerated by Thrift Compiler (0.9.2)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.xiaomi.nr.coupon.infrastructure.rpc.sid.sdk;

import org.mi.thrift.EncodingUtils;
import org.mi.thrift.protocol.TTupleProtocol;
import org.mi.thrift.scheme.IScheme;
import org.mi.thrift.scheme.SchemeFactory;
import org.mi.thrift.scheme.StandardScheme;
import org.mi.thrift.scheme.TupleScheme;

import javax.annotation.Generated;
import java.util.*;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.2)", date = "2021-11-15")
public class Result implements org.mi.thrift.TBase<Result, Result._Fields>, java.io.Serializable, Cloneable, Comparable<Result> {
    private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("Result");

    private static final org.mi.thrift.protocol.TField RESPONSE_FIELD_DESC = new org.mi.thrift.protocol.TField("response", org.mi.thrift.protocol.TType.LIST, (short)1);
    private static final org.mi.thrift.protocol.TField ERROR_NO_FIELD_DESC = new org.mi.thrift.protocol.TField("errorNo", org.mi.thrift.protocol.TType.I32, (short)2);
    private static final org.mi.thrift.protocol.TField ERROR_MSG_FIELD_DESC = new org.mi.thrift.protocol.TField("errorMsg", org.mi.thrift.protocol.TType.STRING, (short)3);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new ResultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new ResultTupleSchemeFactory());
    }

    public List<idinfo> response; // required
    public int errorNo; // required
    public String errorMsg; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.mi.thrift.TFieldIdEnum {
        RESPONSE((short)1, "response"),
        ERROR_NO((short)2, "errorNo"),
        ERROR_MSG((short)3, "errorMsg");

        private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

        static {
          for (_Fields field : EnumSet.allOf(_Fields.class)) {
            byName.put(field.getFieldName(), field);
          }
        }

        /**
         * Find the _Fields constant that matches fieldId, or null if its not found.
         */
        public static _Fields findByThriftId(int fieldId) {
            switch(fieldId) {
                case 1: // RESPONSE
                  return RESPONSE;
                case 2: // ERROR_NO
                  return ERROR_NO;
                case 3: // ERROR_MSG
                  return ERROR_MSG;
                default:
                  return null;
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, throwing an exception
         * if it is not found.
         */
        public static _Fields findByThriftIdOrThrow(int fieldId) {
          _Fields fields = findByThriftId(fieldId);
          if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
          return fields;
        }

        /**
         * Find the _Fields constant that matches name, or null if its not found.
         */
        public static _Fields findByName(String name) {
          return byName.get(name);
        }

        private final short _thriftId;
        private final String _fieldName;

        _Fields(short thriftId, String fieldName) {
          _thriftId = thriftId;
          _fieldName = fieldName;
        }

        public short getThriftFieldId() {
          return _thriftId;
        }

        public String getFieldName() {
          return _fieldName;
        }
    }

    // isset id assignments
    private static final int __ERRORNO_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
        Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.mi.thrift.meta_data.FieldMetaData>(_Fields.class);
        tmpMap.put(_Fields.RESPONSE, new org.mi.thrift.meta_data.FieldMetaData("response", org.mi.thrift.TFieldRequirementType.DEFAULT, 
                new org.mi.thrift.meta_data.ListMetaData(org.mi.thrift.protocol.TType.LIST, 
                        new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, idinfo.class))));
        tmpMap.put(_Fields.ERROR_NO, new org.mi.thrift.meta_data.FieldMetaData("errorNo", org.mi.thrift.TFieldRequirementType.DEFAULT, 
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I32)));
        tmpMap.put(_Fields.ERROR_MSG, new org.mi.thrift.meta_data.FieldMetaData("errorMsg", org.mi.thrift.TFieldRequirementType.DEFAULT, 
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.STRING)));
        metaDataMap = Collections.unmodifiableMap(tmpMap);
        org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(Result.class, metaDataMap);
    }

    public Result() {
    }

    public Result(
        List<idinfo> response,
        int errorNo,
        String errorMsg)
    {
        this();
        this.response = response;
        this.errorNo = errorNo;
        setErrorNoIsSet(true);
        this.errorMsg = errorMsg;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public Result(Result other) {
        __isset_bitfield = other.__isset_bitfield;
        if (other.isSetResponse()) {
            List<idinfo> __this__response = new ArrayList<idinfo>(other.response.size());
            for (idinfo other_element : other.response) {
                __this__response.add(new idinfo(other_element));
            }
            this.response = __this__response;
        }
        this.errorNo = other.errorNo;
        if (other.isSetErrorMsg()) {
            this.errorMsg = other.errorMsg;
        }
    }

    public Result deepCopy() {
      return new Result(this);
    }

    @Override
    public void clear() {
        this.response = null;
        setErrorNoIsSet(false);
        this.errorNo = 0;
        this.errorMsg = null;
    }

    public int getResponseSize() {
        return (this.response == null) ? 0 : this.response.size();
    }

    public java.util.Iterator<idinfo> getResponseIterator() {
        return (this.response == null) ? null : this.response.iterator();
    }

    public void addToResponse(idinfo elem) {
        if (this.response == null) {
            this.response = new ArrayList<idinfo>();
        }
        this.response.add(elem);
    }

    public List<idinfo> getResponse() {
        return this.response;
    }

    public Result setResponse(List<idinfo> response) {
        this.response = response;
        return this;
    }

    public void unsetResponse() {
        this.response = null;
    }

    /** Returns true if field response is set (has been assigned a value) and false otherwise */
    public boolean isSetResponse() {
        return this.response != null;
    }

    public void setResponseIsSet(boolean value) {
        if (!value) {
          this.response = null;
        }
    }

    public int getErrorNo() {
        return this.errorNo;
    }

    public Result setErrorNo(int errorNo) {
        this.errorNo = errorNo;
        setErrorNoIsSet(true);
        return this;
    }

    public void unsetErrorNo() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ERRORNO_ISSET_ID);
    }

    /** Returns true if field errorNo is set (has been assigned a value) and false otherwise */
    public boolean isSetErrorNo() {
        return EncodingUtils.testBit(__isset_bitfield, __ERRORNO_ISSET_ID);
    }

    public void setErrorNoIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ERRORNO_ISSET_ID, value);
    }

    public String getErrorMsg() {
        return this.errorMsg;
    }

    public Result setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
        return this;
    }

    public void unsetErrorMsg() {
        this.errorMsg = null;
    }

    /** Returns true if field errorMsg is set (has been assigned a value) and false otherwise */
    public boolean isSetErrorMsg() {
        return this.errorMsg != null;
    }

    public void setErrorMsgIsSet(boolean value) {
        if (!value) {
          this.errorMsg = null;
        }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
        case RESPONSE:
            if (value == null) {
              unsetResponse();
            } else {
              setResponse((List<idinfo>)value);
            }
            break;

        case ERROR_NO:
            if (value == null) {
              unsetErrorNo();
            } else {
              setErrorNo((Integer)value);
            }
            break;

        case ERROR_MSG:
            if (value == null) {
              unsetErrorMsg();
            } else {
              setErrorMsg((String)value);
            }
            break;

      }
    }

    public Object getFieldValue(_Fields field) {
        switch (field) {
        case RESPONSE:
            return getResponse();

        case ERROR_NO:
            return Integer.valueOf(getErrorNo());

        case ERROR_MSG:
            return getErrorMsg();

        }
        throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
        if (field == null) {
          throw new IllegalArgumentException();
        }

        switch (field) {
        case RESPONSE:
            return isSetResponse();
        case ERROR_NO:
            return isSetErrorNo();
        case ERROR_MSG:
            return isSetErrorMsg();
        }
        throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
        if (that == null)
          return false;
        if (that instanceof Result)
          return this.equals((Result)that);
        return false;
    }

    public boolean equals(Result that) {
        if (that == null)
          return false;

        boolean this_present_response = true && this.isSetResponse();
        boolean that_present_response = true && that.isSetResponse();
        if (this_present_response || that_present_response) {
            if (!(this_present_response && that_present_response))
              return false;
            if (!this.response.equals(that.response))
              return false;
        }

        boolean this_present_errorNo = true;
        boolean that_present_errorNo = true;
        if (this_present_errorNo || that_present_errorNo) {
            if (!(this_present_errorNo && that_present_errorNo))
              return false;
            if (this.errorNo != that.errorNo)
              return false;
        }

        boolean this_present_errorMsg = true && this.isSetErrorMsg();
        boolean that_present_errorMsg = true && that.isSetErrorMsg();
        if (this_present_errorMsg || that_present_errorMsg) {
            if (!(this_present_errorMsg && that_present_errorMsg))
              return false;
            if (!this.errorMsg.equals(that.errorMsg))
              return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        List<Object> list = new ArrayList<Object>();

        boolean present_response = true && (isSetResponse());
        list.add(present_response);
        if (present_response)
          list.add(response);

        boolean present_errorNo = true;
        list.add(present_errorNo);
        if (present_errorNo)
          list.add(errorNo);

        boolean present_errorMsg = true && (isSetErrorMsg());
        list.add(present_errorMsg);
        if (present_errorMsg)
          list.add(errorMsg);

        return list.hashCode();
    }

    @Override
    public int compareTo(Result other) {
        if (!getClass().equals(other.getClass())) {
          return getClass().getName().compareTo(other.getClass().getName());
        }

        int lastComparison = 0;

        lastComparison = Boolean.valueOf(isSetResponse()).compareTo(other.isSetResponse());
        if (lastComparison != 0) {
          return lastComparison;
        }
        if (isSetResponse()) {
          lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.response, other.response);
          if (lastComparison != 0) {
            return lastComparison;
          }
        }
        lastComparison = Boolean.valueOf(isSetErrorNo()).compareTo(other.isSetErrorNo());
        if (lastComparison != 0) {
          return lastComparison;
        }
        if (isSetErrorNo()) {
          lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.errorNo, other.errorNo);
          if (lastComparison != 0) {
            return lastComparison;
          }
        }
        lastComparison = Boolean.valueOf(isSetErrorMsg()).compareTo(other.isSetErrorMsg());
        if (lastComparison != 0) {
          return lastComparison;
        }
        if (isSetErrorMsg()) {
          lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.errorMsg, other.errorMsg);
          if (lastComparison != 0) {
            return lastComparison;
          }
        }
        return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.mi.thrift.protocol.TProtocol iprot) throws org.mi.thrift.TException {
        schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.mi.thrift.protocol.TProtocol oprot) throws org.mi.thrift.TException {
        schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("Result(");
        boolean first = true;

        sb.append("response:");
        if (this.response == null) {
          sb.append("null");
        } else {
            sb.append(this.response);
        }
        first = false;
        if (!first) sb.append(", ");
        sb.append("errorNo:");
        sb.append(this.errorNo);
        first = false;
        if (!first) sb.append(", ");
        sb.append("errorMsg:");
        if (this.errorMsg == null) {
          sb.append("null");
        } else {
            sb.append(this.errorMsg);
        }
        first = false;
        sb.append(")");
        return sb.toString();
    }

    public void validate() throws org.mi.thrift.TException {
        // check for required fields
        // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
      } catch (org.mi.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
      } catch (org.mi.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class ResultStandardSchemeFactory implements SchemeFactory {
        public ResultStandardScheme getScheme() {
            return new ResultStandardScheme();
        }
    }

    private static class ResultStandardScheme extends StandardScheme<Result> {

        public void read(org.mi.thrift.protocol.TProtocol iprot, Result struct) throws org.mi.thrift.TException {
            org.mi.thrift.protocol.TField schemeField;
            iprot.readStructBegin();
            while (true)
            {
                schemeField = iprot.readFieldBegin();
                if (schemeField.type == org.mi.thrift.protocol.TType.STOP) { 
                    break;
                }
                switch (schemeField.id) {
                    case 1: // RESPONSE
                        if (schemeField.type == org.mi.thrift.protocol.TType.LIST) {
                            {
                                org.mi.thrift.protocol.TList _list0 = iprot.readListBegin();
                                struct.response = new ArrayList<idinfo>(_list0.size);
                                idinfo _elem1;
                                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                                {
                                    _elem1 = new idinfo();
                                    _elem1.read(iprot);
                                    struct.response.add(_elem1);
                                }
                                iprot.readListEnd();
                            }
                            struct.setResponseIsSet(true);
                        } else { 
                          org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 2: // ERROR_NO
                        if (schemeField.type == org.mi.thrift.protocol.TType.I32) {
                            struct.errorNo = iprot.readI32();
                            struct.setErrorNoIsSet(true);
                        } else { 
                          org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 3: // ERROR_MSG
                        if (schemeField.type == org.mi.thrift.protocol.TType.STRING) {
                            struct.errorMsg = iprot.readString();
                            struct.setErrorMsgIsSet(true);
                        } else { 
                          org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    default:
                      org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                }
                iprot.readFieldEnd();
            }
            iprot.readStructEnd();

            // check for required fields of primitive type, which can't be checked in the validate method
            struct.validate();
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot, Result struct) throws org.mi.thrift.TException {
            struct.validate();

            oprot.writeStructBegin(STRUCT_DESC);
            if (struct.response != null) {
                oprot.writeFieldBegin(RESPONSE_FIELD_DESC);
                {
                    oprot.writeListBegin(new org.mi.thrift.protocol.TList(org.mi.thrift.protocol.TType.STRUCT, struct.response.size()));
                    for (idinfo _iter3 : struct.response)
                    {
                        _iter3.write(oprot);
                    }
                    oprot.writeListEnd();
                }
                oprot.writeFieldEnd();
            }
            oprot.writeFieldBegin(ERROR_NO_FIELD_DESC);
            oprot.writeI32(struct.errorNo);
            oprot.writeFieldEnd();
            if (struct.errorMsg != null) {
                oprot.writeFieldBegin(ERROR_MSG_FIELD_DESC);
                oprot.writeString(struct.errorMsg);
                oprot.writeFieldEnd();
            }
            oprot.writeFieldStop();
            oprot.writeStructEnd();
        }

    }

    private static class ResultTupleSchemeFactory implements SchemeFactory {
        public ResultTupleScheme getScheme() {
            return new ResultTupleScheme();
        }
    }

    private static class ResultTupleScheme extends TupleScheme<Result> {

        @Override
        public void write(org.mi.thrift.protocol.TProtocol prot, Result struct) throws org.mi.thrift.TException {
            TTupleProtocol oprot = (TTupleProtocol) prot;
            BitSet optionals = new BitSet();
            if (struct.isSetResponse()) {
                optionals.set(0);
            }
            if (struct.isSetErrorNo()) {
                optionals.set(1);
            }
            if (struct.isSetErrorMsg()) {
                optionals.set(2);
            }
            oprot.writeBitSet(optionals, 3);
            if (struct.isSetResponse()) {
                {
                    oprot.writeI32(struct.response.size());
                    for (idinfo _iter4 : struct.response)
                    {
                        _iter4.write(oprot);
                    }
                }
            }
            if (struct.isSetErrorNo()) {
                oprot.writeI32(struct.errorNo);
            }
            if (struct.isSetErrorMsg()) {
                oprot.writeString(struct.errorMsg);
            }
        }

        @Override
        public void read(org.mi.thrift.protocol.TProtocol prot, Result struct) throws org.mi.thrift.TException {
            TTupleProtocol iprot = (TTupleProtocol) prot;
            BitSet incoming = iprot.readBitSet(3);
            if (incoming.get(0)) {
                {
                    org.mi.thrift.protocol.TList _list5 = new org.mi.thrift.protocol.TList(org.mi.thrift.protocol.TType.STRUCT, iprot.readI32());
                    struct.response = new ArrayList<idinfo>(_list5.size);
                    idinfo _elem6;
                    for (int _i7 = 0; _i7 < _list5.size; ++_i7)
                    {
                        _elem6 = new idinfo();
                        _elem6.read(iprot);
                        struct.response.add(_elem6);
                    }
                }
                struct.setResponseIsSet(true);
            }
            if (incoming.get(1)) {
                struct.errorNo = iprot.readI32();
                struct.setErrorNoIsSet(true);
            }
            if (incoming.get(2)) {
                struct.errorMsg = iprot.readString();
                struct.setErrorMsgIsSet(true);
            }
        }
    }

}

