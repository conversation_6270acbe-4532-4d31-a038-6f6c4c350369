package com.xiaomi.nr.coupon.util;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.text.StringSubstitutor;
import org.apache.commons.text.lookup.StringLookup;
import org.assertj.core.util.Lists;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 字符串操作对象
 *
 * <AUTHOR>
 */
public class StringUtil {
    /**
     * 占位符前缀
     */
    private static final String KEY_PREFIX = "{";
    /**
     * 占位符后缀
     */
    private static final String KEY_SUFFIX = "}";
    /**
     * 转义符
     */
    private static final char KEY_ESCAPE = '0';

    /**
     * 文本内容格式化
     * <code>
     * String content = "my name is {name}, and age is {age}";
     * Map<String, String> valuesMap = new HashMap<>();
     * valuesMap.put("name", "Tom");
     * valuesMap.put("age", "8");
     * String result = StringUtil.formatContent(content, valuesMap);
     * </code>
     * <p>
     * result is: my name is Tom, and age is 8
     * </p>
     *
     * @param content   包含占位符的字符串
     * @param valuesMap 数据值
     * @return 格式化好的结果
     */
    public static String formatContent(String content, Map<String, String> valuesMap) {
        StringSubstitutor sub = initStringSubstitutor();
        sub.setVariableResolver(new MapLookup(content, valuesMap));
        return sub.replace(content);
    }

    /**
     * 文本内容格式化
     * <code>
     * String content = "my name is {name}, and age is {age}";
     * String result = StringUtil.formatContent(content, "Tom", "8);
     * </code>
     * <p>
     * result is: my name is Tom, and age is 8
     * </p>
     *
     * @param content 包含占位符的字符串
     * @param values  数据值
     * @return 格式化好的结果
     */
    public static String formatContent(String content, String... values) {
        StringSubstitutor sub = initStringSubstitutor();
        sub.setVariableResolver(new StringUtil.ArrayLookup(content, values));
        return sub.replace(content);
    }

    /**
     * 提供默认的构造实现
     *
     * @return StringSubstitutor
     */
    private static StringSubstitutor initStringSubstitutor() {
        StringSubstitutor sub = new StringSubstitutor();
        sub.setVariablePrefix(KEY_PREFIX);
        sub.setVariableSuffix(KEY_SUFFIX);
        sub.setEscapeChar(KEY_ESCAPE);
        return sub;
    }

    /**
     * 根据数组元素来进行查找
     */
    private static class ArrayLookup implements StringLookup {
        private final String content;
        private final String[] values;
        private int index = 0;

        public ArrayLookup(String content, String[] values) {
            this.content = content;
            this.values = values;
        }

        @Override
        public String lookup(String var) {
            if (this.values == null) {
                throw new IllegalArgumentException(this.content + " parameters must not be empty");
            }
            if (this.values.length <= this.index) {
                throw new IllegalArgumentException(this.content + " incorrect number of parameters");
            }
            String value = this.values[this.index];
            if (value == null) {
                throw new IllegalArgumentException(this.content + " parameter value is incorrect");
            } else {
                ++this.index;
                return value;
            }
        }
    }

    /**
     * 根据Map元素来进行查找
     */
    private static class MapLookup implements StringLookup {
        private final String content;
        private final Map<String, String> valuesMap;

        public MapLookup(String content, Map<String, String> valuesMap) {
            this.content = content;
            this.valuesMap = valuesMap;
        }

        @Override
        public String lookup(String var) {
            if (this.valuesMap == null) {
                throw new IllegalArgumentException(this.content + " parameters must not be empty");
            }
            String value = this.valuesMap.get(var);
            if (value == null) {
                throw new IllegalArgumentException(this.content + " parameter value is incorrect");
            } else {
                return value;
            }
        }
    }



    public static <X> String makeToken(Class<X> cls, String appId, String secret) throws Exception {
        Field[] fields = cls.getFields();
        List<String> paramNameList = new ArrayList<>();
        for(int i=0; i< fields.length; i++){
            paramNameList.add(fields[i].getName());
        }

        Map<String,String> paramMap = new HashMap<>();

        for(String fieldName : paramNameList){
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String getter = "get" + firstLetter + fieldName.substring(1);
            Method method = cls.getMethod(getter);
            String value = method.invoke(cls).toString();
            paramMap.put(fieldName,value);
        }

        System.out.println(paramMap);

        return genToken(appId,secret,paramMap,paramNameList);
    }




    public static String genToken(String appId, String secret, Map<String, String> params, List<String> paramNames) {
        //按首字母排序
        Collections.sort(paramNames);

        //拼接生成所有token的字符串
        StringBuilder str = new StringBuilder();
        str.append("appId").append("=").append(appId).append("&");

        for (String key : paramNames) {
            if ("appId".equals(key) || "token".equals(key)) {
                continue;
            }
            str.append(key).append("=").append(params.get(key)).append("&");
        }


        str.append("secret=").append(secret);

        //md5加密生成验证token
        return DigestUtils.md5Hex(str.toString());
    }


    /**
     * 把逗号分割的长整型数字列表字符串恢复成长整型List
     *
     * @param str
     * @return
     */
    public static List<Long> convertToLongList(String str) {
        List<Long> list = Lists.newArrayList();
        if (StringUtils.isEmpty(str)) {
            return list;
        } else {
            list = Arrays.stream(str.split(","))
                    .map(String::trim)
                    .filter(NumberUtils::isParsable)
                    .map(s -> Long.parseLong(s.trim()))
                    .collect(Collectors.toList());
        }
        return list;
    }


    public static String genNewToken(String appId, String secret) {
        //拼接生成所有token的字符串
        StringBuilder str = new StringBuilder().append("appId").append("=").append(appId).append("&").append("secret=").append(secret);

        //md5加密生成验证token
        return DigestUtils.md5Hex(str.toString());
    }
}

