package com.xiaomi.nr.coupon.domain.couponinvalid;

import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.couponinvalid.impl.CarAfterSaleCouponInvalidService;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 券作废流程工厂
 *
 * <AUTHOR>
 * @date 2024/5/19
 */
@Component
public class CouponInvalidFlowFactory {

    @Autowired
    private CarAfterSaleCouponInvalidService carAfterSaleCouponInvalidService;

    public CouponInvalidService getCouponInvalidService(Integer bizPlatform) throws BizError {

        BizPlatformEnum bizPlatformEnum = BizPlatformEnum.valueOf(bizPlatform);
        if (Objects.isNull(bizPlatformEnum)) {
            throw ExceptionHelper.create(GeneralCodes.NotFound, String.format("未找到匹配的业务领域：%d", bizPlatform));
        }

        switch (bizPlatformEnum) {
            case CAR_AFTER_SALE:
                return carAfterSaleCouponInvalidService;
            default:
                throw ExceptionHelper.create(GeneralCodes.ParamError, String.format("作废券不支持当前业务领域：%d", bizPlatform));
        }
    }
}
