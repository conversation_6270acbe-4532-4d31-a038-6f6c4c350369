package com.xiaomi.nr.coupon.domain.common;

import com.xiaomi.nr.coupon.enums.couponconfig.SendChannelEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.localcache.LocalCacheCommon;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.auth.po.AppAuthInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 通用方法
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class Common {

    @Resource
    private LocalCacheCommon localCacheCommon;

    /**
     * 检查appID与投放渠道是否匹配
     * 投放渠道是券可以在哪个渠道进行发放，appID是具体发放系统的标识，投放渠道承载于系统之上
     * 一个appID对应多个投放渠道
     * 一个投放渠道也可能对应多个appID
     *
     * @param appId   String
     * @param channel String
     * @return Boolean
     */
    @Deprecated
    public Boolean checkAppIdChannel(String appId, String channel) {
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(channel)) {
            log.error("checkAppIdChannel param fail, appId={},channel{}", appId, channel);
            return false;
        }
        AppAuthInfo appAuthInfo = localCacheCommon.getSingleAppAuth(appId);
        if(Objects.isNull(appAuthInfo)){
            return false;
        }
        return appAuthInfo.getChannelList().contains(channel);

    }

    /**
     * 下单赠券同时要兼容老的可发放券的appId（多个）
     *
     * @param configAppId String
     * @return boolean
     */
    @Deprecated
    public boolean equalsStoreOrderGiftAppId(String configAppId) {
        //下面这些appId都是老下单赠券支持可发放的券对应的发放系统appId，等什么时候统一按营销系统配置要发放的券时，这里就不需要这个兼容了
        if ("XM2106".equals(configAppId)) {
            return true;
        }else if ("1011".equals(configAppId)) {
            return true;
        } else if ("XM2057".equals(configAppId)) {
            return true;
        } else if ("XM2058".equals(configAppId)) {
            return true;
        } else if ("XM2059".equals(configAppId)) {
            return true;
        }
        return false;
    }

    /**
     * 是否需要转为其他渠道的渠道
     *
     * @param channel String
     * @return Boolean
     */
    @Deprecated
    public Boolean isTransOtherSendChannel(String channel) {
        List<String> list = new ArrayList<>();
        list.add(SendChannelEnum.StoreOrderGift.getRedisValue());
        return list.contains(channel);
    }

    /**
     * 是否为兼容老版本计数的渠道
     *
     * @param channel String
     * @return Boolean
     */
    @Deprecated
    public Boolean isCompatibleOldSendChannel(String channel) {
        return SendChannelEnum.StoreOrderGift.getRedisValue().equals(channel) || SendChannelEnum.Others.getRedisValue().equals(channel);
    }

    /**
     * 是否为店长券渠道
     *
     * @param channel String
     * @return Boolean
     */
    @Deprecated
    public Boolean isStoreManageSendChannel(String channel) {
        return SendChannelEnum.StoreManager.getRedisValue().equals(channel);
    }

    /**
     * 是否为线下投放渠道
     *
     * @param channel String
     * @return Boolean
     */
    @Deprecated
    public Boolean isOfflineSendChannel(String channel) {
        List<String> configs = new ArrayList<>();
        configs.add(SendChannelEnum.StoreOrderGift.getRedisValue());
        configs.add(SendChannelEnum.StoreManager.getRedisValue());
        configs.add(SendChannelEnum.DiffBusiness.getRedisValue());
        return configs.contains(channel);
    }

    /**
     * 获取原因
     *
     * @param msg String
     * @return String
     */
    public String getReason(String msg) {
        if (msg == null) {
            return null;
        }
        String[] list = msg.split("==>");
        if (list.length > 0) {
            return list[0];
        }
        return msg;
    }



}
