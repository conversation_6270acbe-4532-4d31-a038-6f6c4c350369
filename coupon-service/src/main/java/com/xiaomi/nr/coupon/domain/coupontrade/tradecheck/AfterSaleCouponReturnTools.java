package com.xiaomi.nr.coupon.domain.coupontrade.tradecheck;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/13 16:15
 */
public abstract class AfterSaleCouponReturnTools {
    /**
     * 优惠券退还校验 + 幂等性校验
     *
     * @param couponPos
     * @param orderId
     * @param vid
     * @return 是否幂等，券退还校验失败时抛出异常
     */
    public abstract boolean couponListReturnCheck(List<CouponPo> couponPos, long orderId, String vid) throws BizError;


    /**
     * 优惠券回退
     *
     * @param vid
     * @param userId
     * @param orderId
     * @param offline
     * @param couponPos
     */
    public abstract void returnCoupon(String vid, long userId, long orderId, Integer offline, List<CouponPo> couponPos) throws BizError;
}
