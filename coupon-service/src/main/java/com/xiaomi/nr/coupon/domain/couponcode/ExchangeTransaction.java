package com.xiaomi.nr.coupon.domain.couponcode;

import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CouponLogMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CouponMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponLogPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponcode.CouponCodeUpdateMapper;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 优惠券码兑换事务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ExchangeTransaction {

    @Resource
    private CouponMapper couponMapper;

    @Resource
    private CouponCodeUpdateMapper couponCodeUpdateMapper;

    /**
     * 落库事务
     *
     * @param index   md5索引
     * @param id      明码券唯一id值
     * @param orgCode 门店ID
     * @param coupon  券信息
     * @throws Exception
     */
    @Transactional(transactionManager = "xmPulseWriteTransactionManager", rollbackFor = {Exception.class})
    public void single(String index, Long id, String orgCode, CouponPo coupon) throws Exception {
        long nowTime = TimeUtil.getNowUnixSecond();

        try {
            //优惠券表
            Integer insertCouponCount = couponMapper.insert(coupon);
            if (insertCouponCount != 1) {
                log.warn("couponCode.singleExchange, 写入优惠券信息失败, userId={}, configId={}, index={}", coupon.getUserId(), coupon.getTypeId(), index);
                throw new Exception("优惠码无法兑换");
            }
        } catch (DuplicateKeyException e) {
            log.info("couponCode.singleExchange, 写入优惠券信息幂等处理, userId={}, configId={}, index={}", coupon.getUserId(), coupon.getTypeId(), index);
        }

        //优惠券码表
        try {
            exchange(index, id, orgCode, coupon, nowTime);
        } catch (Exception e) {
            log.warn("couponCode.singleExchange, 修改优惠券码失败, userId={}, configId={}, index={}, id={}", coupon.getUserId(), coupon.getTypeId(), index, id);
            throw new Exception("优惠码无法兑换");
        }
    }

    /**
     * 落库事务 - 优惠券码变更
     *
     * @param index   md5索引
     * @param id      明码券唯一id值
     * @param orgCode 门店ID
     * @param coupon  券信息
     * @param nowTime 当前时间
     * @throws Exception
     */
    @Transactional(transactionManager = "xmPulseWriteTransactionManager", rollbackFor = {Exception.class})
    public void exchange(String index, Long id, String orgCode, CouponPo coupon, long nowTime) throws Exception {
        Integer exchangeCount = couponCodeUpdateMapper.exchange(index, id, coupon.getUserId(), orgCode, coupon.getId(), nowTime);
        if (exchangeCount != 1) {
            throw new Exception("优惠码无法兑换");
        }
    }
}
