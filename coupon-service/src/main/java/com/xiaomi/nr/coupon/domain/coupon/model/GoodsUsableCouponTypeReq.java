package com.xiaomi.nr.coupon.domain.coupon.model;

import com.xiaomi.nr.coupon.api.dto.coupon.ProductInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
public class GoodsUsableCouponTypeReq implements Serializable {

    private static final long serialVersionUID = -6883528290656495373L;

    /**
     * 客户端id
     */
    private Long clientId;

    /**
     * 券配置id
     */
    private Set<Long> configIdList;

    /**
     * 商品信息列表
     */
    private List<ProductInfo> goodsInfo;

    /**
     * sku列表
     */
    private List<Long> goods;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 商品类型
     */
    private Integer goodsType;

    /**
     * 门店编码
     */
    private String orgCode;

}
