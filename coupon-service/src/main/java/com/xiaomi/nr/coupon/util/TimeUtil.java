package com.xiaomi.nr.coupon.util;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.time.temporal.TemporalQueries;
import java.util.Date;

/**
 * 日期时间工具类
 *
 * <AUTHOR>
 */
public class TimeUtil {
    /**
     * 默认时间格式化
     */
    public static final String DATE_TIME_FORMAT_DEFAULT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 获取当前日期格式（默认格式）
     * @return String
     */
    public static String getNowDateTime() {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DATE_TIME_FORMAT_DEFAULT);
        LocalDateTime dt = LocalDateTime.ofInstant(Instant.ofEpochSecond(getNowUnixSecond()), ZoneId.systemDefault());
        return df.format(dt);
    }

    /**
     * 日期格式转时间戳（默认格式）
     * @param second 10位时间戳(秒）
     * @return String
     */
    public static String formatSecond(Long second) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DATE_TIME_FORMAT_DEFAULT);
        LocalDateTime dt = LocalDateTime.ofInstant(Instant.ofEpochSecond(second), ZoneId.systemDefault());
        return df.format(dt);
    }

    /**
     * 时间戳转日期格式（默认格式）
     *
     * @param dateTime 时间
     * @return 10位时间戳(秒）
     */
    public static Long parseDateTime(String dateTime) {
        long mills = parseByPattern(dateTime, DATE_TIME_FORMAT_DEFAULT);
        return mills / 1000;
    }

    /**
     * @param mills   13位时间戳(毫秒）
     * @param pattern 格式
     * @return
     */
    public static String formatByPattern(Long mills, String pattern) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(pattern);
        LocalDateTime dt = LocalDateTime.ofInstant(Instant.ofEpochMilli(mills), ZoneId.systemDefault());
        return df.format(dt);
    }

    /**
     * 格式化时间
     *
     * @param dateTime 时间
     * @param pattern  格式
     * @return 13位时间戳(毫秒 ）
     */
    public static Long parseByPattern(String dateTime, String pattern) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(pattern);
        TemporalAccessor temporal = df.parse(dateTime);
        LocalDateTime localDateTime = null;
        if (temporal instanceof LocalDateTime) {
            localDateTime = (LocalDateTime) temporal;
        } else if (temporal instanceof ZonedDateTime) {
            localDateTime = ((ZonedDateTime) temporal).toLocalDateTime();
        } else if (temporal instanceof OffsetDateTime) {
            localDateTime = ((OffsetDateTime) temporal).toLocalDateTime();
        } else {
            LocalDate localDate = temporal.query(TemporalQueries.localDate());
            LocalTime localTime = temporal.query(TemporalQueries.localTime());
            if (localTime == null) {
                localTime = LocalTime.MIN;
            }
            localDateTime = localDate.atTime(localTime);
        }
        Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
        return instant.toEpochMilli();
    }

    /**
     * 获取当前日期
     *
     * @return 年-月-日
     */
    public static String getNowDay() {
        Date date = new Date();
        SimpleDateFormat ft = new SimpleDateFormat("yyyy-MM-dd");
        return ft.format(date.getTime());

    }

    /**
     * 获取当前时间戳（秒）
     *
     * @return long
     */
    public static long getNowUnixSecond() {
        return System.currentTimeMillis() / 1000;
    }

    /**
     * 获取当前时间戳（毫秒）
     *
     * @return long
     */
    public static long getNowUnixMillis() {
        return System.currentTimeMillis();
    }

    /**
     * 获取当前时间戳(纳秒)
     *
     * @return long
     */
    public static long getNowUnixNano() {
        return System.nanoTime();
    }

    /**
     * 计算时间差（毫秒）
     *
     * @return long
     */
    public static long sinceMillis(long millis) {
        return System.currentTimeMillis() - millis;
    }
}
