package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CarCouponLogPo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2024/5/11 16:50
 */
@Mapper
@Repository
public interface CarCouponLogMapper {
    /**
     * 插入核销流水记录
     *
     * @param carCouponLogPo
     * @return
     */
    @Insert("insert into tb_car_coupon_log " +
            "(id, coupon_id, user_id, order_id, vid, log_type, log_desc, change_times, biz_platform, add_time) " +
            "values(#{carCouponLogPo.id}, #{carCouponLogPo.couponId}, #{carCouponLogPo.userId}, #{carCouponLogPo.orderId}, #{carCouponLogPo.vid}, " +
            "#{carCouponLogPo.logType}, #{carCouponLogPo.logDesc}, #{carCouponLogPo.changeTimes}, #{carCouponLogPo.bizPlatform}, " +
            "#{carCouponLogPo.addTime})")
    Integer insert(@Param("carCouponLogPo") CarCouponLogPo carCouponLogPo);

    /**
     * 查询核销流水记录
     *
     * @param vid
     * @param couponId
     * @param orderId
     * @param logType
     * @return
     */
    @Select("select * from tb_car_coupon_log where vid = #{vid} and coupon_id = #{couponId} and order_id = #{orderId} and log_type = #{logType}")
    CarCouponLogPo getByLogType(@Param("vid") String vid, @Param("couponId") Long couponId, @Param("orderId") Long orderId, @Param("logType") Integer logType);
}
