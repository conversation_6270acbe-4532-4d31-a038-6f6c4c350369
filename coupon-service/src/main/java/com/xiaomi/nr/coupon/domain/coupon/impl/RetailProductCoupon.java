package com.xiaomi.nr.coupon.domain.coupon.impl;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.xiaomi.nr.coupon.api.dto.coupon.GoodsItemDto;
import com.xiaomi.nr.coupon.api.dto.coupon.ProductUsableCouponRequest;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.constant.CommonConstant;
import com.xiaomi.nr.coupon.domain.coupon.AbstractProductCoupon;
import com.xiaomi.nr.coupon.domain.coupon.model.ProductUsableCouponContext;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigInfo;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherToolInterface;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherConfigItemDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherGoodsItemDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.nocodefetchable.NoCodeFetchableReqDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.nocodeusable.NoCodeUsableReqDo;
import com.xiaomi.nr.coupon.enums.couponconfig.UseTimeTypeEnum;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.enums.goods.SaleModeEnum;
import com.xiaomi.nr.coupon.enums.matchertool.MatcherToolTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Strings;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 3C产品站优惠券服务
 *
 * <AUTHOR>
 * @date 2024/9/12
 */
@Component
public class RetailProductCoupon extends AbstractProductCoupon {

    /**
     * 获取业务领域
     *
     * @return BizPlatformEnum
     */
    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.RETAIL;
    }

    /**
     * 入参校验
     * @param req  req
     */
    public void checkReq(ProductUsableCouponRequest req) throws BizError {

        super.checkReq(req);

        // 商品校验
        for (GoodsItemDto goodsInfo : req.getGoodsList()) {

            if (!GoodsLevelEnum.Sku.getValue().equals(goodsInfo.getLevel()) && !GoodsLevelEnum.Package.getValue().equals(goodsInfo.getLevel()) && !GoodsLevelEnum.Ssu.getValue().equals(goodsInfo.getLevel())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "所传商品品级只能为sku或package或ssu");
            }
            if (Objects.isNull(goodsInfo.getSalePrice()) || goodsInfo.getSalePrice() < 0) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "所传商品销售价不能为空");
            }
            if (Objects.isNull(goodsInfo.getMarketPrice()) || goodsInfo.getMarketPrice() < 0) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "所传商品市场价不能为空");
            }
            if (Objects.isNull(goodsInfo.getVirtual())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "所传是否为虚拟商品不能为空");
            }
            if (Strings.isNullOrEmpty(goodsInfo.getSaleMode())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "所传商品销售模式不能为空");
            }
            if (Objects.isNull(goodsInfo.getBusinessType()) || goodsInfo.getBusinessType() <= 0) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "所传商品类型必须大于0");
            }
            if (SaleModeEnum.BOOKING.getValue().equals(goodsInfo.getSaleMode()) && (
                    Objects.isNull(goodsInfo.getFinalStartTime()) ||
                            Objects.isNull(goodsInfo.getFinalEndTime()) ||
                            goodsInfo.getFinalStartTime() >= goodsInfo.getFinalEndTime()
            )) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "定金预售模式必须传有效的尾款支付时间");
            }
        }
    }

    /**
     * 获取可领券
     *
     * @param req     req
     * @param context context
     */
    @Override
    @SentinelResource(value = "com.xiaomi.nr.coupon.domain.coupon.impl.ProductCouponServiceImpl.getProductFetchableCoupon", fallback = "getProductFetchableCouponSentinelDowngrade")
    public void getProductFetchableCoupon(ProductUsableCouponRequest req, ProductUsableCouponContext context) throws BizError {
        List<MatcherConfigItemDo> configs = CollectionUtils.isNotEmpty(req.getConfigList()) ? req.getConfigList().stream()
                .filter(e -> Objects.nonNull(e) && e.getConfigId() > 0)
                .map(e -> {
                    MatcherConfigItemDo item = new MatcherConfigItemDo();
                    item.setConfigId(e.getConfigId());
                    return item;
                }).collect(Collectors.toList()) : Collections.emptyList();


        NoCodeFetchableReqDo fetchReq = new NoCodeFetchableReqDo();
        fetchReq.setUserId(req.getUserId());
        fetchReq.setBizPlatform(Lists.newArrayList(BizPlatformEnum.RETAIL.getCode()));
        fetchReq.setClientId(req.getClientId());
        fetchReq.setOrgCode(req.getOrgCode());
        fetchReq.setConfigList(configs);
        fetchReq.setGoodsList(convertReqDtoToMatcherGoodsItemDo(req.getGoodsList()));

        fetchReq.setCheckBizPlatform(true);
        fetchReq.setCheckConfigStatus(true);
        fetchReq.setCheckConfigFetchTime(true);

        fetchReq.setCheckUserChannelAndOrgCode(true);
        fetchReq.setCheckConfigGoodsInclude(true);
        fetchReq.setCheckGlobalExcludeGoods(true);
        fetchReq.setCheckGlobalCouponExcludeGoods(true);

        MatcherToolInterface fetchMatcher = matcherToolFactory.getProvider(MatcherToolTypeEnum.PRODUCT_NOCODE_FETCHABLE);
        MatcherContextDo fetchResp = fetchMatcher.execute(fetchReq);
        if (Objects.nonNull(fetchResp) && CollectionUtils.isNotEmpty(fetchResp.getKeyResp())) {
            context.fill(fetchResp);
        }
    }

    /**
     * 获取可用券
     *
     * @param req     req
     * @param context context
     */
    @SentinelResource(value = "com.xiaomi.nr.coupon.domain.coupon.impl.ProductCouponServiceImpl.getProductUserUsableCoupon", fallback = "getProductUserUsableCouponSentinelDowngrade")
    @Override
    public void getProductUserUsableCoupon(ProductUsableCouponRequest req, ProductUsableCouponContext context) throws BizError {
        NoCodeUsableReqDo usableReq = new NoCodeUsableReqDo();
        usableReq.setUserId(req.getUserId());
        usableReq.setBizPlatform(Lists.newArrayList(BizPlatformEnum.RETAIL.getCode()));
        usableReq.setClientId(req.getClientId());
        usableReq.setOrgCode(req.getOrgCode());
        usableReq.setGoodsList(convertReqDtoToMatcherGoodsItemDo(req.getGoodsList()));

        usableReq.setCheckBizPlatform(true);
        usableReq.setCheckUserCouponStatus(true);
        usableReq.setCheckUserCouponTime(true);
        usableReq.setCheckConfigRegion(true);
        usableReq.setCheckUserCouponSpecialStore(true);

        usableReq.setCheckUserChannelAndOrgCode(true);
        usableReq.setCheckConfigGoodsInclude(true);
        usableReq.setCheckGlobalExcludeGoods(true);
        usableReq.setCheckGlobalCouponExcludeGoods(true);

        MatcherToolInterface usableMatcher = matcherToolFactory.getProvider(MatcherToolTypeEnum.PRODUCT_NOCODE_USABLE);
        MatcherContextDo usableResp = usableMatcher.execute(usableReq);
        if (Objects.nonNull(usableResp) && CollectionUtils.isNotEmpty(usableResp.getKeyResp())) {
            context.fill(usableResp);
        }
    }

    /**
     * 针对销售模式为定金预售商品，保证尾款可用券排在前面
     * 1、对于可领的券，将当下可用的有效期与尾款期无交集的后置，真实情况还和按用户领取后的可用时间为准
     * 2、对于用户已领的券，将有效期与尾款期无交集的后置
     *
     * @param goods         MatcherGoodsItemDo
     * @param keyResp       List<MatcherRespItemDo>
     * @param configInfoMap Map<Long, CouponConfigItem>
     * @param userCouponMap Map<Long, CouponPo>
     * @return List<MatcherRespItemDo>
     */
    protected List<MatcherRespItemDo> specialSort(MatcherGoodsItemDo goods,
                                                  List<MatcherRespItemDo> keyResp,
                                                  Map<Long, CouponConfigItem> configInfoMap,
                                                  Map<Long, CouponPo> userCouponMap) {
        if (!SaleModeEnum.BOOKING.getValue().equals(goods.getSaleMode())) {
            return keyResp;
        }

        long nowTime = TimeUtil.getNowUnixSecond();
        List<MatcherRespItemDo> result = new ArrayList<>();
        List<MatcherRespItemDo> noIntersections = new ArrayList<>();
        for (MatcherRespItemDo r : keyResp) {
            //已领券
            if (Objects.nonNull(r.getUserCouponId()) && r.getUserCouponId() > 0) {
                String useStartTimeStr = userCouponMap.get(r.getUserCouponId()).getStartTime();
                String useEndTimeStr = userCouponMap.get(r.getUserCouponId()).getEndTime();
                long useStartTime = Long.parseLong(Strings.isNullOrEmpty(useStartTimeStr) ? CommonConstant.ZERO_STR : useStartTimeStr);
                long useEndTime = Long.parseLong(Strings.isNullOrEmpty(useEndTimeStr) ? CommonConstant.ZERO_STR : useEndTimeStr);
                if (useEndTime <= goods.getFinalStartTime() || useStartTime >= goods.getFinalEndTime()) {
                    noIntersections.add(r);
                } else {
                    result.add(r);
                }
                continue;
            }

            //可领券
            if (!configInfoMap.containsKey(r.getConfigId())) {
                continue;
            }
            CouponConfigItem configItem = configInfoMap.get(r.getConfigId());
            CouponConfigInfo config = configItem.getCouponConfigInfo();

            long useEndTime = 0L;
            if (UseTimeTypeEnum.Relative.getValue().equals(config.getUseTimeType())) {
                if (config.getUseDuration() == null || config.getUseDuration() <= 0) {
                    continue;
                }
                useEndTime = nowTime + config.getUseDuration() * 3600;
            } else if (UseTimeTypeEnum.Fixed.getValue().equals(config.getUseTimeType())) {
                if (config.getStartUseTime() == null || config.getStartUseTime() <= 0 ||
                        config.getEndUseTime() == null || config.getEndUseTime() <= 0 ||
                        config.getStartUseTime() > config.getEndUseTime()
                ) {
                    continue;
                }
                useEndTime = config.getEndUseTime();
            }

            if (useEndTime <= goods.getFinalStartTime() || nowTime >= goods.getFinalEndTime()) {
                noIntersections.add(r);
            } else {
                result.add(r);
            }
        }
        result.addAll(noIntersections);
        return result;
    }
}
