package com.xiaomi.nr.coupon.domain.common.mqpush;

import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.common.model.MessageBuildContext;
import org.springframework.messaging.Message;

import java.util.Set;

/**
 * @description 券核销消息推送服务
 * <AUTHOR>
 * @date 2025-01-08 20:16
*/
public interface CouponUsePushService {

    /**
     * 发送消息
     */
    void sendMessage(Message<?> message, Long couponId, String sceneCode);

    /**
     * 构建消息
     */
    Message<?> buildMessage(MessageBuildContext context);

    /**
     * 获取需要推送的场景
     */
    Set<String> getSceneSet();

    /**
     * 获取业务平台类型
     */
    BizPlatformEnum getBizType();
}
