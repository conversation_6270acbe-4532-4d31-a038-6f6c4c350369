package com.xiaomi.nr.coupon.enums.notify;

import org.apache.commons.lang3.StringUtils;

/**
 * 缓存类型 枚举
 *
 * <AUTHOR>
 */
public enum CacheTypeEnum {

    /**
     * 券配置缓存
     */
    CouponConfig("couponConfig", "券配置缓存"),

    /**
     * 商品对应券配置列表的缓存
     */
    GoodsRelationCouponConfig("goodsRelationCouponConfig", "商品对应券配置列表的缓存"),

    /**
     * 券发放任务
     */
    CouponMission("couponMission", "券发放任务");

    private final String value;
    private final String name;

    CacheTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static boolean findByValue(String value) {
        CacheTypeEnum[] values = CacheTypeEnum.values();
        for (CacheTypeEnum item : values) {
            if (StringUtils.equals(item.getValue(), value)) {
                return true;
            }
        }
        return false;
    }
}
