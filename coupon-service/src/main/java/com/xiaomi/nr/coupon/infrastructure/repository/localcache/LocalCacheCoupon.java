package com.xiaomi.nr.coupon.infrastructure.repository.localcache;

import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.po.MissionCacheItemPo;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


/**
 * 本地缓存
 *
 * <AUTHOR>
 */
@Deprecated
@Component
public interface LocalCacheCoupon {

    /**
     * 批量从localCache里取优惠券发放任务信息
     *
     * @param missionIds List<Long>
     * @return Map<Long, MissionCacheItemPo>
     */
    Map<Long, MissionCacheItemPo> getCouponMission(List<Long> missionIds);

}
