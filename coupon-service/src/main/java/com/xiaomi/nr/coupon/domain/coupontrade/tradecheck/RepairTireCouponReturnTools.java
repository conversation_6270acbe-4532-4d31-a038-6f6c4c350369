package com.xiaomi.nr.coupon.domain.coupontrade.tradecheck;

import com.xiaomi.nr.coupon.enums.coupon.CarCouponLogTypeEnum;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.CouponServiceTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.CarCouponRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CarCouponLogPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 汽车售后服务-补胎券 券退还校验工具
 *
 * <AUTHOR>
 * @date 2024/5/13 16:31
 */
@Slf4j
@Component
public class RepairTireCouponReturnTools extends AfterSaleCouponReturnTools {
    @Resource
    private CarCouponRepository carCouponRepository;

    @PostConstruct
    public void register() {
        AfterSaleCouponReturnToolsFactory.register(CouponServiceTypeEnum.REPAIR_TAIR, this);
    }


    /**
     * 优惠券退还校验 + 幂等性校验
     *
     * @param couponPos
     * @param orderId
     * @param vid
     * @return 是否幂等，券退还校验失败时抛出异常
     */
    @Override
    public boolean couponListReturnCheck(List<CouponPo> couponPos, long orderId, String vid) throws BizError {
        // 补胎券只能使用一张
        CouponPo couponPo = couponPos.get(0);

        // 获取退还流水
        CarCouponLogPo couponLogPo = carCouponRepository.getCouponLogPoByLogType(vid, couponPo.getId(), orderId, CarCouponLogTypeEnum.RETURN.getCode());
        if (null != couponLogPo) {
            // 幂等
            return true;
        }

        // 状态校验，补胎券状态需为-未使用、已使用
        List<String> validStats = Lists.newArrayList(CouponStatusEnum.UNUSED.getValue(), CouponStatusEnum.USED.getValue());
        if (!validStats.contains(couponPo.getStat())) {
            throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券状态不正确");
        }

        return false;
    }

    /**
     * 优惠券回退
     *
     * @param vid
     * @param userId
     * @param orderId
     * @param offline
     * @param couponPos
     */
    @Override
    public void returnCoupon(String vid, long userId, long orderId, Integer offline, List<CouponPo> couponPos) throws BizError {
        if (CollectionUtils.isEmpty(couponPos)) {
            return;
        }

        // 补胎券只能使用一张
        CouponPo couponPo = couponPos.get(0);
        Long couponId = couponPo.getId();

        // 查询核销流水
        CarCouponLogPo consumeLogPo = carCouponRepository.getCouponLogPoByLogType(vid, couponId, orderId, CarCouponLogTypeEnum.CONSUME.getCode());
        if (consumeLogPo == null) {
            // 核销流水不存在
            log.error("RepairTireCouponReturnTools.returnCoupon 核销流水不存在, vid = {}, orderId = {}", vid, orderId);
        } else {
            // 补胎券退还，使用次数-1
            Integer oldUsedTimes = couponPo.getUsedTimes();
            Integer newUsedTimes = oldUsedTimes - 1;
            List<String> statList = Lists.newArrayList(CouponStatusEnum.UNUSED.getValue(), CouponStatusEnum.USED.getValue());
            carCouponRepository.updateCouponUsedTimes(vid, orderId, couponId, oldUsedTimes, newUsedTimes, statList);

            // 记录退还流水
            carCouponRepository.insertCarCouponLogPo(vid, userId, orderId, couponPo, CarCouponLogTypeEnum.RETURN, oldUsedTimes, newUsedTimes);
        }
    }
}
