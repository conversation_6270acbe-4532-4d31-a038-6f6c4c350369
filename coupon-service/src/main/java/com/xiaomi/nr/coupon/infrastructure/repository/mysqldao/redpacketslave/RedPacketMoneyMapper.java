package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.redpacketslave;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Mapper
@Component
public interface RedPacketMoneyMapper {

    /**
     * 查用户可用券总数
     *
     * @param userId  long
     * @param nowTime long
     * @return Integer
     */
    @Select("select IFNULL(sum(balance), 0) from tb_rp_redpacket where user_id=#{userId} and start_time<#{nowTime} and end_time>#{nowTime}")
    long getValidMoney(@Param("userId") long userId, @Param("nowTime") long nowTime);
}
