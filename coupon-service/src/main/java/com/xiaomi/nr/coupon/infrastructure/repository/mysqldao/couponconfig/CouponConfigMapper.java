package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.po.CouponConfigPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 券配置操作mapper
 */
@Mapper
@Component
public interface CouponConfigMapper {

    /**
     * 根据id集合批量获取券配置
     * @param ids
     * @return
     */
    @Select("<script>select * from nr_coupon_config  where id in <foreach collection='ids' item='id' index='index' open='(' close=')' separator=','>#{id}</foreach></script>")
    List<CouponConfigPO> getByIds(@Param("ids") List<Integer> ids);

    /**
     * 根据使用时间查询有效券id
     *
     * @param validFinalTime
     * @param lastVersion
     * @return
     */
    @Select("select id from nr_coupon_config where (end_use_time>#{validFinalTime} or end_fetch_time>#{validFinalTime}) and update_time> #{lastVersion}")
    List<Integer> getValidConfigIdByUseTime(@Param("validFinalTime") long validFinalTime, @Param("lastVersion") String lastVersion);

    /**
     * 根据使用时间查询券id
     *
     * @param beginFinalTime
     * @param endFinalTime
     * @return
     */
    @Select("select id from nr_coupon_config where end_use_time>#{beginFinalTime} and end_use_time<=#{endFinalTime} and update_time> #{lastVersion}")
    List<Integer> getInvalidConfigIdByUseTime(@Param("beginFinalTime") long beginFinalTime, @Param("endFinalTime") long endFinalTime, @Param("lastVersion") String lastVersion);

    /**
     * 根据使用时间查询有效券id
     *
     * @param validFinalTime
     * @return
     */
    @Select("select id from nr_coupon_config where end_use_time>#{validFinalTime} or end_fetch_time>#{validFinalTime}")
    List<Integer> getValidConfigIdByUseTimeV2(@Param("validFinalTime") long validFinalTime);

}
