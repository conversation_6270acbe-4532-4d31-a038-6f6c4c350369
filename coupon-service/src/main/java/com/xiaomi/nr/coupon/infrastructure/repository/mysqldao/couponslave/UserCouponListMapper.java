package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户优惠券mapper
 *
 * <AUTHOR>
 */
@Mapper
@Repository
public interface UserCouponListMapper {

    String SELECT_PARAMS = " id, user_id, type_id, activity_id, start_time, end_time,days, stat, order_id, use_time, " +
            "expire_time, is_pass, admin_id, admin_name,add_time, send_type, from_order_id, replace_money, invalid_time, " +
            "last_update_time, offline, reduce_express, parent_id, send_channel, biz_platform ";

    //所有券都从这个时间2020-05-25开始查起
    String ADD_TIME = "1590336000";


    /**
     * 查询该用户下状态 stat=unused 的优惠券列表
     *
     * @param userId    用户id
     * @param nowTime   当前时间
     * @param sendChannel 优惠券发放渠道
     * @return List<>   券信息列表
     * */
    @Select("<script>" +
            "select "+SELECT_PARAMS+ " from tb_coupon " +
            " where user_id=#{userId} " +
            " and stat='unused' " +
            " and end_time &gt; #{nowTime} " +
            " and add_time &gt; "+ ADD_TIME +
            " and biz_platform in <foreach collection='bizPlatformList' item='bizPlatform' index='index' open='(' close=')' separator=','>#{bizPlatform}</foreach> " +
            "<if test=\"sendChannel != null and sendChannel !=''\"> and send_channel = #{sendChannel,jdbcType=VARCHAR} </if>" +
            " order by add_time desc" +
            "</script>")
    List<CouponPo> getCouponByUnused(@Param("userId") long userId, @Param("nowTime") long nowTime,
                                     @Param("sendChannel") String sendChannel, @Param("bizPlatformList") List<Integer> bizPlatformList);




    /**
     * 查询该用户下状态 stat in (used,presented,received) 的优惠券列表
     *
     * @param userId   用户id
     * @param nowTime  当前时间
     * @param lastId   分页lastId
     * @param pageSize 页面大小
     * @return List<>   券信息列表
     */
    @Select("<script>" +
            "select "+SELECT_PARAMS+ " from tb_coupon " +
            " where user_id=#{userId} " +
            " and stat in('used','presented','received') " +
            " and end_time &gt; #{nowTime} " +
            " and add_time &gt; "+ ADD_TIME +
            " and biz_platform in <foreach collection='bizPlatformList' item='bizPlatform' index='index' open='(' close=')' separator=','>#{bizPlatform}</foreach> " +
            "<if test=\"sendChannel != null and sendChannel !=''\"> and send_channel = #{sendChannel,jdbcType=VARCHAR} </if>" +
            "<if test=\"lastId != null and lastId != 0\"> and id &lt; #{lastId,jdbcType=INTEGER} </if>" +
            " order by add_time desc, id desc limit #{pageSize} "+
            "</script>")
    List<CouponPo> getCouponByUsedPreRec(@Param("userId") long userId, @Param("nowTime") long nowTime,
                                         @Param("sendChannel") String sendChannel, @Param("lastId") long lastId, @Param("pageSize") int pageSize,
                                         @Param("bizPlatformList") List<Integer> bizPlatformList);



    /**
     * 查询该用户下状态 stat in (used,locked) 的优惠券列表
     *
     * @param userId   用户id
     * @param lastId   分页lastId
     * @param pageSize 页面大小
     * @return List<>  优惠券信息列表
     */
    @Select("<script>" +
            "select "+SELECT_PARAMS+ " from tb_coupon " +
            " where user_id=#{userId} " +
            " and add_time &gt; "+ ADD_TIME +
            " and stat in('used','locked') "+
            " and biz_platform in <foreach collection='bizPlatformList' item='bizPlatform' index='index' open='(' close=')' separator=','>#{bizPlatform}</foreach> " +
            "<if test=\"sendChannel != null and sendChannel !=''\"> and send_channel = #{sendChannel,jdbcType=VARCHAR} </if>" +
            "<if test=\"lastId != null and lastId != 0\"> and id &lt; #{lastId,jdbcType=INTEGER} </if>" +
            " order by add_time desc, id desc limit #{pageSize}"+
            "</script>")
    List<CouponPo> getCouponByUsedLocked(@Param("userId") long userId, @Param("sendChannel") String sendChannel,
                                         @Param("lastId") long lastId, @Param("pageSize") int pageSize,
                                         @Param("bizPlatformList") List<Integer> bizPlatformList);



    /**
     * 查询该用户下状态 stat = expired 的优惠券列表
     *
     * @param userId     用户id
     * @param nowTime    当前时间
     * @param lastId     分页lastId
     * @param pageSize   页面大小
     * @param nearlyDaysTime 返回90天内已过期的券
     * @return List<>        优惠券信息列表
     */
    @Select("<script>" +
            "select "+SELECT_PARAMS+ " from tb_coupon " +
            " where user_id=#{userId} " +
            " and (stat='expired' or (stat='unused' and end_time &lt;= #{nowTime})) " +
            " and end_time &gt; #{nearlyDaysTime} " +
            " and add_time &gt; "+ ADD_TIME +
            "<if test=\"sendChannel != null and sendChannel !=''\"> and send_channel = #{sendChannel,jdbcType=VARCHAR} </if>" +
            "<if test=\"lastId != null and lastId != 0\"> and id &lt; #{lastId,jdbcType=INTEGER} </if>" +
            " and biz_platform in <foreach collection='bizPlatformList' item='bizPlatform' index='index' open='(' close=')' separator=','>#{bizPlatform}</foreach> " +
            " order by add_time desc, id desc limit #{pageSize}"+
            "</script>")
    List<CouponPo> getCouponByExpired(@Param("userId") long userId, @Param("nearlyDaysTime") long nearlyDaysTime,
                                      @Param("nowTime")long nowTime, @Param("sendChannel") String sendChannel,
                                      @Param("lastId") long lastId, @Param("pageSize") int pageSize,
                                      @Param("bizPlatformList") List<Integer> bizPlatformList);




    /**
     * 查询该用户下状态 stat not in(invalied,cancel) 的优惠券列表
     *
     * @param userId    用户id
     * @return List<>   券信息列表
     * */
    @Select("<script>" +
            "select "+ SELECT_PARAMS + " from tb_coupon " +
            " where user_id=#{userId} " +
            " and add_time &gt; "+ ADD_TIME +
            " and end_time &gt; #{nearlyDaysTime} "+
            "<if test=\"sendChannel != null and sendChannel !=''\"> and send_channel = #{sendChannel,jdbcType=VARCHAR} </if>" +
            " and stat not in('invalid','cancel') "+
            " and biz_platform in <foreach collection='bizPlatformList' item='bizPlatform' index='index' open='(' close=')' separator=','>#{bizPlatform}</foreach> " +
            " order by add_time desc"+
            "</script>")
    List<CouponPo> getCouponByAll(@Param("userId") long userId, @Param("sendChannel") String sendChannel, @Param("nearlyDaysTime") long nearlyDaysTime, @Param("bizPlatformList") List<Integer> bizPlatformList);


    /**
     * 根据用户ID和状态查询用户券信息
     *
     * @param userId  用户id
     * @param status  券状态
     * @return List<> 优惠券信息列表
     */
    @Select("<script>" +
            "select "+SELECT_PARAMS+ " from tb_coupon " +
            " where user_id=#{userId} " +
            " and add_time &gt; "+ ADD_TIME +
            " and stat = #{status} "+
            " and biz_platform in <foreach collection='bizPlatformList' item='bizPlatform' index='index' open='(' close=')' separator=','>#{bizPlatform}</foreach> " +
            "<if test=\"sendChannel != null and sendChannel !=''\"> and send_channel = #{sendChannel,jdbcType=VARCHAR} </if>" +
            "<if test=\"lastId != null and lastId != 0\"> and id &lt; #{lastId,jdbcType=INTEGER} </if>" +
            " order by add_time desc, id desc limit #{pageSize}"+
            "</script>")
    List<CouponPo> getCouponByStat(@Param("userId") long userId, @Param("status") String status, @Param("sendChannel") String sendChannel,
                                   @Param("lastId") long lastId, @Param("pageSize") int pageSize, @Param("bizPlatformList") List<Integer> bizPlatformList);


    @Select("<script>" +
            "select * from tb_coupon where user_id=#{userId} and " +
            "type_id in <foreach collection='configIdList' item='id' index='index' open='(' close=')' separator=','>#{id}</foreach> " +
            "</script>")
    List<CouponPo> getCouponByUidConfigIdList(@Param("userId") long userId, @Param("configIdList") List<Long> configIdList);

    /**
     * 获取用户未使用的券
     * @param userId .
     * @param stat .
     * @param nowTime .
     * @return
     */
    @Select("<script>" +
            "select id, user_id, type_id, start_time, end_time, stat, order_id, add_time, from_order_id, parent_id, biz_platform " +
            " from tb_coupon " +
            " where user_id=#{userId} and stat=#{stat} and end_time &gt; #{nowTime} " +
            " and biz_platform in <foreach collection='bizPlatformList' item='bizPlatform' index='index' open='(' close=')' separator=','>#{bizPlatform}</foreach>" +
            " order by add_time desc" +
            "</script>")
    List<CouponPo> getUserCoupon(@Param("userId") Long userId, @Param("stat") String stat, @Param("nowTime") long nowTime, @Param("bizPlatformList") List<Integer> bizPlatformList);

    /**
     * 获取用户未使用的券（产品站）
     * @param userId .
     * @param bizPlatformList 业务领域
     * @param nowTime .
     * @return
     */
    @Select("<script>" +
            "select id, user_id, type_id, start_time, end_time, stat, add_time, extend_info, biz_platform " +
            " from tb_coupon " +
            " where user_id=#{userId} and stat='unused' and end_time &gt; #{nowTime} " +
            " and biz_platform in <foreach collection='bizPlatformList' item='bizPlatform' index='index' open='(' close=')' separator=','>#{bizPlatform}</foreach>" +
            "</script>")
    List<CouponPo> getProductUserCoupon(@Param("userId") long userId, @Param("bizPlatformList") List<Integer> bizPlatformList, @Param("nowTime") long nowTime);

    /**
     * 通过couponId查询券信息
     *
     * @param userId .
     * @param couponIdList .
     * @return .
     */
    @Select("<script>" +
            " select id, user_id, type_id, start_time, end_time, stat, add_time, extend_info, biz_platform " +
            " from tb_coupon " +
            " where user_id=#{userId} " +
            " and id in <foreach collection='couponIdList' item='id' index='index' open='(' close=')' separator=','>#{id}</foreach> " +
            "</script>")
    List<CouponPo> getCouponByUidCouponIdList(@Param("userId") long userId, @Param("couponIdList") List<Long> couponIdList);


}
