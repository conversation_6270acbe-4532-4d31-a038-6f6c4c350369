package com.xiaomi.nr.coupon.enums.coupon;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 优惠券使用范围枚举
 */
public enum CouponUseChannelEnum {

    /**
     * 小米商城
     */
    MI_SHOP(1, "mi_shop", "小米商城"),

    /**
     * 小米之家 直营店
     */
    MI_HOME_ZY(2, "mi_home", "小米之家"),

    /**
     * 小米之家 专卖店
     */
    MI_HOME_ZM(3, "mi_home", "小米之家"),

    /**
     * 授权店
     */
    MI_AUTHORIZED(4, "mi_authorized", "授权店"),

    /**
     * 堡垒店
     */
    MI_FORTRESS(5, "mi_fortress" , "堡垒店"),

    /**
     * 车商城
     */
    CAR_SHOP(6, "car_shop", "车商城"),

    ;



    private final int id;
    private final String value;
    private final String name;
    CouponUseChannelEnum(int id, String value, String name) {
        this.id = id;
        this.value = value;
        this.name = name;
    }

    public int getId() {
        return this.id;
    }
    public String getValue() {
        return this.value;
    }
    public String getName() {
        return name;
    }

    public static String findNameByValue(String value) {
        CouponUseChannelEnum[] values = CouponUseChannelEnum.values();
        for (CouponUseChannelEnum item : values) {
            if (StringUtils.equals(item.getValue(), value)) {
                return item.getName();
            }
        }
        return null;
    }

    public static CouponUseChannelEnum findByValue(String value) {
        CouponUseChannelEnum[] values = CouponUseChannelEnum.values();
        for (CouponUseChannelEnum item : values) {
            if (StringUtils.equals(item.getValue(), value)) {
                return item;
            }
        }
        return null;
    }

    public static String findValueById(int id) {
        CouponUseChannelEnum[] values = CouponUseChannelEnum.values();
        for (CouponUseChannelEnum item : values) {
            if (Objects.equals(item.getId(), id)) {
                return item.getValue();
            }
        }
        return null;
    }

}
