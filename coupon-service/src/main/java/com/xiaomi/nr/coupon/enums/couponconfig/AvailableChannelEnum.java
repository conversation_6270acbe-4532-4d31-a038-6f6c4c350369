package com.xiaomi.nr.coupon.enums.couponconfig;

public enum AvailableChannelEnum {
    /**
     * 小米商城渠道
     */
    Mi_SHOP(1, "MISHOP","小米商城"),

    /**
     * 小米之家渠道
     */
    Mi_HOME(2, "MIHOM<PERSON>","小米之家");


    private final int code;
    private final String value;
    private final String name;

    AvailableChannelEnum(int code, String value, String name) {
        this.code = code;
        this.value = value;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static AvailableChannelEnum findByCode(int code) {
        AvailableChannelEnum[] values = AvailableChannelEnum.values();
        for (AvailableChannelEnum item : values) {
            if (item.getCode() == code) {
                return item;
            }
        }
        return null;
    }

    public static AvailableChannelEnum findByValue(String value) {
        AvailableChannelEnum[] values = AvailableChannelEnum.values();
        for (AvailableChannelEnum item : values) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }
}