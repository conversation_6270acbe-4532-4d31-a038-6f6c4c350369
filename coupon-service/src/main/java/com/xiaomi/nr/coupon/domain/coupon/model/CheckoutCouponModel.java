package com.xiaomi.nr.coupon.domain.coupon.model;

import com.xiaomi.nr.coupon.api.dto.coupon.checkout.GoodsInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 结算券
 *
 * <AUTHOR>
 */
@Data
public class CheckoutCouponModel implements Serializable {

    private static final long serialVersionUID = -3190362231434410111L;

    /**
     * 是否为价保请求
     */
    private boolean fromPriceProtect = false;

    /**
     * 用户id
     */
    private Long userId = 0L;

    /**
     * 车辆vid
     */
    private String vid;

    /**
     * 应用id
     */
    private Long clientId = 0L;

    /**
     * 门店id
     */
    private String orgCode = "";

    /**
     * 城市id
     */
    private Long cityId = 0L;

    /**
     * 履约id
     */
    private Integer shipmentId;

    /**
     * 购物模式，0-默认值，1-物流，2-现场购，3-物流和现场购混合
     */
    private Long shoppingMode = 0L;

    /**
     * 商品列表
     */
    private List<GoodsInfo> skuPackageList = new ArrayList<>();

    /**
     * 门店类型(1：直营店，2：专卖店，3：授权店)
     */
    private Integer orgType;

    /**
     * 外部传入的所属业务场景
     */
    private Integer bizPlatform;

    /**
     * 优惠类型筛选
     */
    private List<Integer> promotionTypeList;
}