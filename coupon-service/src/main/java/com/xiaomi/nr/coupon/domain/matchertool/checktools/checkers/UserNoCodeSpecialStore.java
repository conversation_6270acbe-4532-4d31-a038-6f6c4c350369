package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.ExtPropInfo;
import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 校验专店专用
 *
 * <AUTHOR>
 * @date 2023/7/24
 */
@Component
public class UserNoCodeSpecialStore extends CheckerAbstract {

    @Override
    public boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {
        if(!ctx.isCheckUserCouponSpecialStore()) {
            return true;
        }

        if (Objects.isNull(respItem.getUserCouponId())) {
            errCtx.setErrCode(ErrCode.USE_COUPON_NOT_FOUND.getCode());
            errCtx.setErrMsg("无效的券");
            return false;
        }

        CouponConfigItem config = ctx.getConfigInfoMap().get(respItem.getConfigId());
        CouponPo coupon = ctx.getUserCouponMap().get(respItem.getUserCouponId());

        if (Objects.isNull(coupon)) {
            errCtx.setErrCode(ErrCode.USE_COUPON_ORGCODE_UNUSED.getCode());
            errCtx.setErrMsg("优惠券不可用");
            return false;
        }

        ExtPropInfo extPropInfo = config.getExtPropInfo();
        if (Objects.isNull(extPropInfo) || !extPropInfo.specialStore()) {
            return true;
        }

        String couponOrgCode = getCouponOrgCode(coupon.getExtendInfo());
        if (StringUtils.isEmpty(couponOrgCode)) {
            return true;
        }

        if (!couponOrgCode.equals(ctx.getOrgCode())) {
            errCtx.setErrCode(ErrCode.USE_COUPON_ORGCODE_UNUSED.getCode());
            errCtx.setErrMsg("优惠券仅限领取门店使用");
            return false;
        }

        return true;
    }

}
