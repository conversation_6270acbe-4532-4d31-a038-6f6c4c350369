package com.xiaomi.nr.coupon.domain.fetchcheck;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponSceneItem;
import com.xiaomi.nr.coupon.domain.fetchcheck.model.CouponFetchCheckInfo;
import com.xiaomi.nr.coupon.domain.fetchcheck.model.CouponFetchReqParam;
import com.xiaomi.nr.coupon.enums.couponconfig.ConfigStatusEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.SceneStatusEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.UseTimeTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.localcache.CouponSceneCache;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponassign.CouponAssignRedisDao;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: 领券校验
 * @Date: 2022.05.18 15:09
 */
@Slf4j
@Service
public class CouponFetchCheckService implements CouponFetchCheck {

    @Resource
    private CouponConfigRepository couponConfigRepository;

    @Resource
    private CouponSceneCache couponSceneCache;

    @Resource
    private CouponAssignRedisDao assignRedisDao;


    public Map<Long, CouponFetchCheckInfo> couponFetchCheck(CouponFetchReqParam couponFetchReqParam) throws Exception {
        Long uid = couponFetchReqParam.getUid();
        List<Long> configList = couponFetchReqParam.getConfigIdList();
        String appId = couponFetchReqParam.getAppId();
        Integer bizPlatForm = couponFetchReqParam.getBizPlatform();
        Map<Long, CouponFetchCheckInfo> couponFetchCheckInfoMap = new HashMap<>();
        //数据准备
        Map<Long, CouponConfigItem> couponConfigItemMap = couponConfigRepository.getCouponConfigs(configList);
        CouponSceneItem couponSceneItem = couponSceneCache.getCouponSceneByCode(couponFetchReqParam.getSceneCode());
        Map<Long, Integer> fetchCountMap = assignRedisDao.batchGetFetchCount(uid, configList);
        Map<Long, Integer> inventoryMap = assignRedisDao.batchGetInventory(configList);
        //条件校验
        for (Long configId : configList) {
            CouponFetchCheckInfo couponFetchCheckInfo = new CouponFetchCheckInfo();
            //已领信息
            if (MapUtils.isNotEmpty(fetchCountMap) && fetchCountMap.get(configId) > 0) {
                couponFetchCheckInfo.setFetched(true);
            } else {
                couponFetchCheckInfo.setFetched(false);
            }
            //校验可领信息
            try {
                CouponConfigItem couponConfigItem = couponConfigItemMap.get(configId);
                checkData(couponConfigItem, couponSceneItem);
                checkBizPlatform(bizPlatForm, couponConfigItem);
                checkSendScene(appId, couponSceneItem, couponConfigItem);
                checkStatus(couponConfigItem);
                checkTime(couponConfigItem);
                checkFetchLimit(fetchCountMap.get(configId), couponConfigItem.getCouponConfigInfo().getFetchLimit());
                checkCouponInventory(inventoryMap.get(configId), couponConfigItem.getCouponConfigInfo().getApplyCount());
                couponFetchCheckInfo.setFetchAble(true);
            } catch (BizError bizError) {
                couponFetchCheckInfo.setFetchAble(false);
                couponFetchCheckInfo.setInvalidCode(bizError.getCode());
                couponFetchCheckInfo.setInvalidReason(bizError.getMsg());
            }
            couponFetchCheckInfoMap.put(configId, couponFetchCheckInfo);
        }
        return couponFetchCheckInfoMap;
    }


    /**
     * 校验必要的配置信息
     * @param couponConfigItem 券配置信息
     * @param couponSceneItem 场景信息
     * @throws BizError
     */
    @Override
    public void checkData(CouponConfigItem couponConfigItem, CouponSceneItem couponSceneItem) throws BizError {
        if (couponConfigItem == null) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_NOT_FOUND, "未获取到券的配置信息！");
        }
        if (couponSceneItem == null) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_SCENE_INVALID, "未找到场景配置信息，请五分钟后重试！");
        }
    }

    /**
     * 业务领域校验
     */
    @Override
    public void checkBizPlatform(Integer bizPlatform, CouponConfigItem couponConfigItem) throws BizError {
        if (!Objects.equals(bizPlatform, couponConfigItem.getBizPlatform())) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_SCENE_OFFLINE, "业务领域不匹配");
        }
    }

    /**
     * 场景校验
     *
     * @param appId            调用方appId
     * @param couponSceneItem  场景配置
     * @param couponConfigItem 券配置
     */
    @Override
    public void checkSendScene(String appId, CouponSceneItem couponSceneItem, CouponConfigItem couponConfigItem) throws BizError {
        if (!couponSceneItem.getAppIds().contains(appId)) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_APPID_NOAUTH, "此系统无此场景发券权限！");
        }
        if (!SceneStatusEnum.Online.getValue().equals(couponSceneItem.getSceneStatus())) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_SCENE_OFFLINE, "投放场景非上线状态！");
        }
        if (!couponConfigItem.getCouponConfigInfo().getSendScene().equals(couponSceneItem.getSceneCode())) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_SCENE_MISMATCH, "发券场景不匹配！");
        }
    }

    /**
     * 校验优惠券状态
     * @param couponConfigItem 券配置
     * @throws BizError
     */
    @Override
    public void checkStatus(CouponConfigItem couponConfigItem) throws BizError {
        if (!ConfigStatusEnum.Online.getValue().equals(couponConfigItem.getCouponConfigInfo().getStatus())) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_STATUS_OFFLINE, "优惠券非上线状态！");
        }
    }

    /**
     * 校验时间
     * @param couponConfigItem 券配置
     */
    @Override
    public void checkTime(CouponConfigItem couponConfigItem) throws BizError {
        long nowTime = TimeUtil.getNowUnixSecond();

        if (couponConfigItem.getCouponConfigInfo().getStartFetchTime() == null || couponConfigItem.getCouponConfigInfo().getStartFetchTime() <= 0 ||
                couponConfigItem.getCouponConfigInfo().getEndFetchTime() == null || couponConfigItem.getCouponConfigInfo().getEndFetchTime() <= 0 ||
                couponConfigItem.getCouponConfigInfo().getStartFetchTime() >= couponConfigItem.getCouponConfigInfo().getEndFetchTime()
        ) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_LOSE_EFFICACY, "券配置的发放时间不符合要求");
        }

        long useEndTime = 0L;
        if (UseTimeTypeEnum.Relative.getValue().equals(couponConfigItem.getCouponConfigInfo().getUseTimeType())) {
            if (couponConfigItem.getCouponConfigInfo().getUseDuration() == null || couponConfigItem.getCouponConfigInfo().getUseDuration() <= 0) {
                throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_LOSE_EFFICACY, "券的有效使用时间不符合要求");
            }
            useEndTime = nowTime + couponConfigItem.getCouponConfigInfo().getUseDuration() * 3600;
        } else if (UseTimeTypeEnum.Fixed.getValue().equals(couponConfigItem.getCouponConfigInfo().getUseTimeType())) {
            if (couponConfigItem.getCouponConfigInfo().getStartUseTime() == null || couponConfigItem.getCouponConfigInfo().getStartUseTime() <= 0 ||
                    couponConfigItem.getCouponConfigInfo().getEndUseTime() == null || couponConfigItem.getCouponConfigInfo().getEndUseTime() <= 0 ||
                    couponConfigItem.getCouponConfigInfo().getStartUseTime() > couponConfigItem.getCouponConfigInfo().getEndUseTime()
            ) {
                throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_LOSE_EFFICACY, "券的有效使用时间区间不符合要求");
            }
            useEndTime = couponConfigItem.getCouponConfigInfo().getEndUseTime();
        }

        if (couponConfigItem.getCouponConfigInfo().getStartFetchTime() > nowTime) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_NOSTART_TIME, "券可领取时间未开始");
        }

        if (couponConfigItem.getCouponConfigInfo().getEndFetchTime() < nowTime) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_OVER_TIME, "券可领取时间已结束");
        }

        if (useEndTime <= nowTime) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_OVER_TIME, "不能发放已过期的券");
        }
    }

    /**
     * 校验个人领券限制
     * @param fetchCount 已领数量
     * @param limit 限领数量
     */
    @Override
    public void checkFetchLimit(Integer fetchCount, Integer limit) throws BizError {
        if (fetchCount >= limit) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_USER_LIMITED, "用户已领券数量超过限制");
        }
    }

    /**
     * 校验券库存
     * @param count 已发数量
     * @param limit 限发数量
     */
    @Override
    public void checkCouponInventory(Integer count, Integer limit) throws BizError {
        if (count >= limit) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_OVER_LIMITED, "此优惠券已领完");
        }
    }
}
