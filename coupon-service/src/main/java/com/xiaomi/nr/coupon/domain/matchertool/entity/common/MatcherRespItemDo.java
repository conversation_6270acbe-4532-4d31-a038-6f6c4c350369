package com.xiaomi.nr.coupon.domain.matchertool.entity.common;

import lombok.Data;

/**
 * MatcherRespItemDo
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Data
public class MatcherRespItemDo {

    /**
     * 匹配不通过的原因
     */
    private MatcherErrContextDo errCtx;

    /**
     * 用户券ID
     */
    private Long userCouponId;

    /**
     * 券配置ID
     */
    private Long configId;

    /**
     * 可领校验参数：投放场景ID，如果不是可领匹配，则为null
     */
    private String sceneCode;

    /**
     * 可领校验参数：分配为投放系统的appid（pls里分配的），如果不是可领匹配，则为null
     */
    private String appId;

}
