package com.xiaomi.nr.coupon.enums.matchertool;

import org.apache.commons.lang3.StringUtils;

/**
 * 匹配器校验工具类型 枚举
 *
 * <AUTHOR>
 */
public enum MatcherCheckerToolTypeEnum {

    /**
     * 无码券可领校验工具
     */
    NOCODE_FETCHABLE("无码券可领校验工具"),

    /**
     * 无码券可用校验工具
     */
    NOCODE_USABLE("无码券可用校验工具"),

    ;

    private final String name;

    MatcherCheckerToolTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static MatcherCheckerToolTypeEnum findByName(String name) {
        MatcherCheckerToolTypeEnum[] items = MatcherCheckerToolTypeEnum.values();
        for (MatcherCheckerToolTypeEnum item : items) {
            if (StringUtils.equals(item.getName(), name)) {
                return item;
            }
        }
        return null;
    }
}

