package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.enums.couponconfig.CouponTypeEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.ShipmentIdEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import org.springframework.stereotype.Component;

/**
 * 校验履约方式
 *
 * <AUTHOR>
 * @date 2023/7/24
 */
@Component
public class ConfigShipmentId extends CheckerAbstract {

    @Override
    public boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {
        if (!ctx.isCheckShipmentId()) {
            return true;
        }

        errCtx.setInvalidConfig(true);

        CouponConfigItem config = ctx.getConfigInfoMap().get(respItem.getConfigId());

        //校验是否满足运费券专属券的可用渠道
        if (CouponTypeEnum.PostFee.getValue().equals(config.getCouponType()) && !ShipmentIdEnum.Lightning.getValue().equals(ctx.getShipmentId())) {
            errCtx.setErrCode(ErrCode.USE_COUPON_SHIPMENT_MISMATCH.getCode());
            errCtx.setErrMsg("仅小米之家门店闪送可用");
            return false;
        }

        if (!ctx.getShipmentId().equals(config.getCouponConfigInfo().getShipmentId())) {
            errCtx.setErrCode(ErrCode.USE_COUPON_SHIPMENT_MISMATCH.getCode());
            errCtx.setErrMsg("不适用于当前履约方式");
            return false;
        }
        return true;
    }

}
