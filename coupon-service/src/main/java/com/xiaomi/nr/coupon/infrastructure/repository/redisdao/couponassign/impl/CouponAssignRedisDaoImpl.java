package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponassign.impl;

import com.xiaomi.nr.coupon.constant.CommonConstant;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.nacosconfig.NacosSwitchConfig;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponassign.CouponAssignRedisDao;
import com.xiaomi.nr.coupon.util.StringUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CouponAssignRedisDaoImpl implements CouponAssignRedisDao {

    /**
     * 缓存操作对象
     */
    @Autowired
    @Qualifier("stringNewCouponRedisTemplate")
    private StringRedisTemplate redisTemplate;

    @Autowired
    @Qualifier("numberNewCouponRedisTemplate")
    private RedisTemplate numberRedis;

    @Autowired
    private NacosSwitchConfig nacosSwitchConfig;

    /**
     * 检查某个券已领取总数量
     */
    private static final String KEY_COUPON_SEND_COUNT = "nr:coupon:sendCount:{configId}";

    /**
     * 用户已领取某个券的总数量
     */
    private static final String KEY_USER_CONFIG_SEND_COUNT = "nr:user:sendCount:{configId}:{userId}";

    /**
     * 用户已领券key的过期时间 = 券配置上的领券过期时间 - 当前时间 + 30天(调整结束时间的缓冲期)
     */
    private static final long KEY_USER_EXPIRE_TIME = 2592000L;

    /**
     * 操作redis超过50ms的打日志
     */
    private static final long RUN_COST_TIME_WARN = 50L;

    private static final String INCR_SCRIPT =
            " local sendCount = redis.call('INCRBY', KEYS[1], 1)" +
                    " if sendCount>tonumber(ARGV[1]) then" +
                    " redis.call('DECRBY', KEYS[1], 1)" +
                    " return 'failed'" +
                    " end" +
                    " if sendCount==1 and tonumber(ARGV[2])~=nil and tonumber(ARGV[2])>0 then" +
                    " redis.call('EXPIRE', KEYS[1], ARGV[2])" +
                    " end" +
                    " return 'success'";
    private static final String DECR_SCRIPT = "redis.call('DECRBY', KEYS[1], 1)";
    private static final String SCRIPT_SUCCESS = "success";

    /**
     * 用户领券计数缓存
     */
    private static final String KEY_USER_COUPON_SEND_COUNT = "nr:user:count:{userId}";

    /**
     * VID领券计数缓存
     */
    private static final String KEY_VID_COUPON_SEND_COUNT = "nr:vid:count:{vid}";


    /**
     * 获取用户已领取某个券的数量
     *
     * @param userId   long
     * @param configId long
     * @return int
     */
    @Override
    public int getUserAssignCount(long userId, long configId) throws Exception {

        //读取新缓存计数
        if (nacosSwitchConfig.isReadNewCouponCount() && configId > nacosSwitchConfig.getOnlineCouponConfigId()) {

            HashOperations<String, String, Integer> ops = numberRedis.opsForHash();

            Integer count = ops.get(StringUtil.formatContent(KEY_USER_COUPON_SEND_COUNT, String.valueOf(userId)), String.valueOf(configId));

            log.info("coupon.singleAssign, 获取用户已领取某个券的数量, userId={}, configId={}, count={}", userId, configId, count);

            return Objects.isNull(count) ? CommonConstant.ZERO_INT : count;
        }

        //读取老缓存计数
        long runStartTime = TimeUtil.getNowUnixMillis();
        String key = StringUtil.formatContent(KEY_USER_CONFIG_SEND_COUNT, String.valueOf(configId), String.valueOf(userId));
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        String countStr = operations.get(key);
        long runCostTime = TimeUtil.sinceMillis(runStartTime);
        if (runCostTime > RUN_COST_TIME_WARN) {
            log.info("coupon.singleAssign, 获取用户已领取某个券的数量耗时, runTime={}毫秒, userId={}, configId={}, count={}", runCostTime, userId, configId, countStr);
        }
        if (countStr == null || countStr.isEmpty()) {
            return 0;
        }
        try {
            return Integer.parseInt(countStr);
        } catch (Exception e) {
            throw new Exception(String.format("获取用户已领取某个券的数量转成Long失败:%s", countStr));
        }
    }

    /**
     * 获取vid已领取某个券的数量
     *
     * @param vid      String
     * @param configId long
     * @return int
     */
    @Override
    public int getVidAssignCount(String vid, long configId) throws Exception {
        long runStartTime = TimeUtil.getNowUnixMillis();

        HashOperations<String, String, Integer> ops = numberRedis.opsForHash();

        Integer count = ops.get(StringUtil.formatContent(KEY_VID_COUPON_SEND_COUNT, vid), String.valueOf(configId));

        long runCostTime = TimeUtil.sinceMillis(runStartTime);
        if (runCostTime > RUN_COST_TIME_WARN) {
            log.info("coupon.singleAssign, 获取vid已领取某个券的数量耗时, runTime={}毫秒, vid={}, configId={}, count={}", runCostTime, vid, configId, count);
        }

        return Objects.isNull(count) ? CommonConstant.ZERO_INT : count;
    }

    /**
     * 获取某个券已领取总数量
     *
     * @param configId long
     * @return int
     */
    @Override
    public int getCouponAssignCount(long configId) throws Exception {
        long runStartTime = TimeUtil.getNowUnixMillis();
        String key = StringUtil.formatContent(KEY_COUPON_SEND_COUNT, String.valueOf(configId));
        ValueOperations<String, Integer> operations = numberRedis.opsForValue();
        Integer count = operations.get(key);
        long runCostTime = TimeUtil.sinceMillis(runStartTime);
        if (runCostTime > RUN_COST_TIME_WARN) {
            log.info("coupon.singleAssign, 获取某个券已领取总数量耗时, runTime={}毫秒, configId={}, count={}", runCostTime, configId, count);
        }
        if (count == null) {
            return 0;
        }
        try {
            return count;
        } catch (Exception e) {
            throw new Exception(String.format("获取某个券已领取总数量转成Long失败:%s", count));
        }
    }

    /**
     * 用户已领取某个券的数量加1，并校验是否超过最大发放数量
     *
     * @param userId       long
     * @param configId     long
     * @param maxCount     int
     * @param endFetchTime long
     */
    @Override
    public void incrUserAssignCount(long userId, long configId, int maxCount, long endFetchTime) throws BizError {
        long runStartTime = TimeUtil.getNowUnixSecond();

        //用户领券老缓存结构计数
        if (nacosSwitchConfig.isWriteOldUserCouponCount() || configId <= nacosSwitchConfig.getOnlineCouponConfigId()) {
            DefaultRedisScript<String> redisScript = new DefaultRedisScript<>(INCR_SCRIPT, String.class);
            List<String> keys = new ArrayList<>();
            List<String> args = new ArrayList<>();
            keys.add(StringUtil.formatContent(KEY_USER_CONFIG_SEND_COUNT, String.valueOf(configId), String.valueOf(userId)));
            args.add(String.valueOf(maxCount));
            //券可领结束时间-当前时间+30天 = key的过期时间
            long expireTime = KEY_USER_EXPIRE_TIME;
            if (endFetchTime > runStartTime) {
                expireTime = endFetchTime - runStartTime + KEY_USER_EXPIRE_TIME;
            }
            args.add(String.valueOf(expireTime));
            String r = redisTemplate.execute(redisScript, keys, args.toArray());


            if (!SCRIPT_SUCCESS.equals(r)) {
                throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_USER_LIMITED, "用户可得券数量已达限制");
            }
        }

        //用户领券新缓存结构计数
        if (nacosSwitchConfig.getOnlineCouponConfigId() != 0L && configId > nacosSwitchConfig.getOnlineCouponConfigId()) {

            HashOperations<String, String, Integer> redisHashOps = numberRedis.opsForHash();

            Long userCount = redisHashOps.increment(StringUtil.formatContent(KEY_USER_COUPON_SEND_COUNT, String.valueOf(userId)), String.valueOf(configId), CommonConstant.ONE_LONG);

            if (nacosSwitchConfig.isReadNewCouponCount()) {
                log.info("coupon.singleAssign, 获取用户已领取某个券的数量, userId={}, configId={}, count={}", userId, configId, userCount);
                if (userCount.intValue() > maxCount) {
                    throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_USER_LIMITED, "用户可得券数量已达限制");
                }
            }
        }
    }

    /**
     * vid已领取某个券的数量加1，并校验是否超过最大发放数量
     *
     * @param vid          String
     * @param configId     long
     * @param maxCount     int
     * @param endFetchTime long
     */
    @Override
    public void incrVidAssignCount(String vid, long configId, int maxCount, long endFetchTime) throws BizError {
        long runStartTime = TimeUtil.getNowUnixSecond();

        HashOperations<String, String, Integer> redisHashOps = numberRedis.opsForHash();

        Long sendCount = redisHashOps.increment(StringUtil.formatContent(KEY_VID_COUPON_SEND_COUNT, String.valueOf(vid)), String.valueOf(configId), CommonConstant.ONE_LONG);

        long runCostTime = TimeUtil.sinceMillis(runStartTime);
        if (runCostTime > RUN_COST_TIME_WARN) {
            log.info("coupon.singleAssign, vid已领取某个券的数量加1耗时, runTime={}ms, vid={}, configId={}, count={}", runCostTime, vid, configId, sendCount);
        }

        if (sendCount.intValue() > maxCount) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_USER_LIMITED, "vid可得券数量已达限制");
        }
    }

    /**
     * 某个券已领取总数量加1，并校验是否超过最大发放数量
     *
     * @param configId long
     * @param maxCount int
     */
    @Override
    public void incrCouponAssignCount(Long userId, long configId, int maxCount) throws BizError {

        ValueOperations<String, Integer> operations = numberRedis.opsForValue();

        Long sendCount = operations.increment(StringUtil.formatContent(KEY_COUPON_SEND_COUNT, String.valueOf(configId)), CommonConstant.ONE_LONG);

        if (!Objects.isNull(sendCount) && sendCount > maxCount) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_OVER_LIMITED, "发放数量已达限制");
        }

        // 防止超发 高并发下双保险 读写分离情况下可能会失效
        Integer count = operations.get(StringUtil.formatContent(KEY_COUPON_SEND_COUNT, String.valueOf(configId)));
        if (!Objects.isNull(count) && count > maxCount) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_OVER_LIMITED, "发放数量已达限制");
        }
    }

    /**
     * 用户已领取某个券的数量减1
     *
     * @param userId   long
     * @param configId long
     */
    @Override
    public void decrUserAssignCount(long userId, long configId) {

        long runStartTime = TimeUtil.getNowUnixMillis();

        //老缓存计数还库存
        if (nacosSwitchConfig.isWriteOldUserCouponCount() || configId <= nacosSwitchConfig.getOnlineCouponConfigId()) {
            DefaultRedisScript<String> redisScript = new DefaultRedisScript<>(DECR_SCRIPT, String.class);
            List<String> keys = new ArrayList<>();
            keys.add(StringUtil.formatContent(KEY_USER_CONFIG_SEND_COUNT, String.valueOf(configId), String.valueOf(userId)));
            redisTemplate.execute(redisScript, keys);
        }

        //新缓存计数还库存
        if (nacosSwitchConfig.getOnlineCouponConfigId() != 0L && configId > nacosSwitchConfig.getOnlineCouponConfigId()) {
            HashOperations<String, String, Integer> redisHashOps = numberRedis.opsForHash();
            redisHashOps.increment(StringUtil.formatContent(KEY_USER_COUPON_SEND_COUNT, String.valueOf(userId)), String.valueOf(configId), -CommonConstant.ONE_LONG);
        }

        long runCostTime = TimeUtil.sinceMillis(runStartTime);
        if (runCostTime > RUN_COST_TIME_WARN) {
            log.info("coupon.singleAssign, 用户已领取某个券的数量减1耗时, runTime={}ms, userId={}, configId={}", runCostTime, userId, configId);
        }
    }

    /**
     * vid已领取某个券的数量减1
     *
     * @param vid   String
     * @param configId long
     */
    @Override
    public void decrVidAssignCount(String vid, long configId) {
        long runStartTime = TimeUtil.getNowUnixMillis();

        HashOperations<String, String, Integer> redisHashOps = numberRedis.opsForHash();
        Long sendCount = redisHashOps.increment(StringUtil.formatContent(KEY_VID_COUPON_SEND_COUNT, vid), String.valueOf(configId), -CommonConstant.ONE_LONG);

        long runCostTime = TimeUtil.sinceMillis(runStartTime);
        if (runCostTime > RUN_COST_TIME_WARN) {
            log.info("coupon.singleAssign, vid已领取某个券的数量减1耗时, runTime={}ms, vid={}, configId={}, count={}", runCostTime, vid, configId, sendCount);
        }
    }

    /**
     * 某个券已领取总数量减1
     *
     * @param userId   long
     * @param configId long
     */
    @Override
    public void decrCouponAssignCount(long userId, long configId) {
        long runStartTime = TimeUtil.getNowUnixMillis();

        ValueOperations<String, String> operations = numberRedis.opsForValue();

        operations.decrement(StringUtil.formatContent(KEY_COUPON_SEND_COUNT, String.valueOf(configId)), CommonConstant.ONE_LONG);

        long runCostTime = TimeUtil.sinceMillis(runStartTime);
        if (runCostTime > RUN_COST_TIME_WARN) {
            log.info("coupon.singleAssign, 某个券已领取总数量减1耗时, runTime={}毫秒, userId={}, configId={}", runCostTime, userId, configId);
        }
    }

    /**
     * 批量获取领券数量
     *
     * @param uid
     * @param configIdList
     * @return
     */
    @Override
    public Map<Long, Integer> batchGetFetchCount(long uid, List<Long> configIdList) throws BizError {
        try {

            Map<Long, Integer> inventoryMap = new HashMap<>();
            List<Long> oldConfigIdList = Lists.newArrayList();

            if (nacosSwitchConfig.isReadNewCouponCount()) {
                List<Long> newConfigIdList = Lists.newArrayList();
                long onlineCouponConfigId = nacosSwitchConfig.getOnlineCouponConfigId();
                for (Long configId : configIdList) {
                    if (onlineCouponConfigId != 0L && configId > onlineCouponConfigId) {
                        newConfigIdList.add(configId);
                        continue;
                    }
                    oldConfigIdList.add(configId);
                }

                //取新缓存用户券计数
                List<String> configStrIds = newConfigIdList.stream().map(String::valueOf).collect(Collectors.toList());
                HashOperations ops = numberRedis.opsForHash();
                List<Integer> countList = ops.multiGet(StringUtil.formatContent(KEY_USER_COUPON_SEND_COUNT, String.valueOf(uid)), configStrIds);
                for (int i = 0; i < newConfigIdList.size(); i++) {
                    inventoryMap.put(newConfigIdList.get(i), Objects.isNull(countList.get(i)) ? CommonConstant.ZERO_INT : countList.get(i));
                }

            } else {
                oldConfigIdList = configIdList;
            }

            if (CollectionUtils.isEmpty(oldConfigIdList)) {
                return inventoryMap;
            }

            //取老缓存用户券计数
            ValueOperations<String, Number> valueOperations = numberRedis.opsForValue();
            List<String> keys = oldConfigIdList.stream().map(configId -> StringUtil.formatContent(KEY_USER_CONFIG_SEND_COUNT, String.valueOf(configId), String.valueOf(uid))).collect(Collectors.toList());
            List<Number> fetchCount = valueOperations.multiGet(keys);
            for (int i = 0; i < keys.size(); i++) {
                int finalI = i;
                final Integer inventory = Optional.ofNullable(fetchCount).map(inv -> inv.get(finalI)).map(Number::intValue).orElse(0);
                inventoryMap.put(oldConfigIdList.get(i), inventory);
            }
            return inventoryMap;
        } catch (Exception e) {
            log.error("batchGetFetchCount error configId:{} uid:{}", configIdList, uid, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "获取领券数量失败", e);
        }
    }

    /**
     * 批量券的发放数量
     *
     * @param configIdList
     * @return
     */
    @Override
    public Map<Long, Integer> batchGetInventory(List<Long> configIdList) throws BizError {
        try {
            ValueOperations<String, Number> redisClusterValueOps = numberRedis.opsForValue();
            Map<Long, Integer> inventoryMap = new HashMap<>();
            List<String> keys = configIdList.stream().map(configId -> StringUtil.formatContent(KEY_COUPON_SEND_COUNT, String.valueOf(configId))).collect(Collectors.toList());
            List<Number> inventories = redisClusterValueOps.multiGet(keys);
            for (int i = 0; i < keys.size(); i++) {
                int finalI = i;
                final Integer inventory = Optional.ofNullable(inventories).map(inv -> inv.get(finalI)).map(Number::intValue).orElse(0);
                inventoryMap.put(configIdList.get(i), inventory);
            }
            return inventoryMap;
        } catch (Exception e) {
            log.error("batchGetInventory error configId:{}", configIdList, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "获取库存失败", e);
        }
    }

}
