package com.xiaomi.nr.coupon.domain.matchertool.checktools;

import com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers.*;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherCheckToolAbstract;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * 车商城无码券可领校验工具
 *
 * <AUTHOR>
 * @date 2024/9/6
 */
@Component
public class CarShopProductNoCodeFetchableCheckTool extends MatcherCheckToolAbstract {

    private final List<String> toolList = new ArrayList<>();

    @PostConstruct
    public void init() {
        toolList.add(BaseInfo.class.getName());
        toolList.add(ConfigBizPlatform.class.getName());
        toolList.add(ProductConfigUseChannel.class.getName());
        toolList.add(PublicPromotion.class.getName());
        toolList.add(ConfigStatus.class.getName());
        toolList.add(ConfigFetchTime.class.getName());
        //toolList.add(SceneStatus.class.getName());
        toolList.add(ConfigFetchLimit.class.getName());
        toolList.add(UserFetchLimit.class.getName());
    }

    @Override
    public List<String> getTools() {
        return toolList;
    }

}
