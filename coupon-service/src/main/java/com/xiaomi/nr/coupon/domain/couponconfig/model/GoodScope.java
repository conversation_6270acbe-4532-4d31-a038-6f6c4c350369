package com.xiaomi.nr.coupon.domain.couponconfig.model;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.*;

/**
 * @description: 商品可使用范围
 * @author: heji<PERSON><PERSON>
 * @Date 2022/2/27 7:35 下午
 * @Version: 1.0
 **/
@Data
public class GoodScope {

    /**
     * SKU列表
     */
    @SerializedName("s")
    private Set<Long> skus = new HashSet<>();

    /**
     * 货品列表
     */
    @SerializedName("gi")
    private Set<Long> goods = new HashSet<>();

    /**
     * 套装列表
     */
    @SerializedName("pi")
    private Set<Long> packages = new HashSet<>();

    /**
     * ssu列表
     */
    @SerializedName("ssu")
    private Set<Long> ssus = new HashSet<>();

    /**
     * 扩展信息（ssu => 扩展信息）
     */
    @SerializedName("ext")
    private Map<Long, GoodExt> goodsExt = new HashMap<>();

    /**
     * 批量验证券可用sku
     * @param skuList
     * @return
     */
    public List<Long> batchCheckValidSkus(List<Long> skuList) {
        return batchCheckValidGood(skuList, skus);
    }

    /**
     * 单个验证券可用sku
     * @param skuId
     * @return
     */
    public boolean singleCheckValidSku(Long skuId) {
        return skus.contains(skuId);
    }

    /**
     * 批量验证券可用货品
     * @param gidList
     * @return
     */
    public List<Long> batchCheckValidGid(List<Long> gidList) {
        return batchCheckValidGood(gidList, goods);
    }

    /**
     * 单个验证券可用sku
     * @param gid
     * @return
     */
    public boolean singleCheckValidGid(Long gid) {
        return goods.contains(gid);
    }


    /**
     * 验证可用套装
     * @param packageList
     * @return
     */
    public List<Long> batchCheckValidPackage(List<Long> packageList) {
        return batchCheckValidGood(packageList, packages);
    }

    /**
     * 单个验证券可用套装
     * @param packageId
     * @return
     */
    public boolean singleCheckValidPackage(Long packageId) {
        return packages.contains(packageId);
    }

    /**
     * 批量验证券可用整车ssu
     * @param ssuList
     * @return
     */
    public List<Long> batchCheckValidCarSsu(List<Long> ssuList) {
        return batchCheckValidGood(ssuList, ssus);
    }

    /**
     * 单个验证券可用整车ssu
     * @param ssuId
     * @return
     */
    public boolean singleCheckValidSsu(Long ssuId) {
        return ssus.contains(ssuId);
    }

    /**
     * 批量校验可用商品私有方法
     * @param goodList
     * @param configGood
     * @return
     */
    private List<Long> batchCheckValidGood(List<Long> goodList, Set<Long> configGood) {
        List<Long> validList = new ArrayList<>();
        if (configGood.size() == 0) {
            return validList;
        }
        for (Long goodId : goodList) {
            if (configGood.contains(goodId)) {
                validList.add(goodId);
            }
        }
        return validList;
    }
}
