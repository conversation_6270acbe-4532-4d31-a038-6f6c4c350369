package com.xiaomi.nr.coupon.domain.couponcode;

import com.xiaomi.nr.coupon.domain.couponcode.model.SingleExchangeRequestDo;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.enums.couponcode.StatusEnum;
import com.xiaomi.nr.coupon.enums.couponcode.UseModeEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.ConfigStatusEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponcode.po.CouponCodePo;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponcode.CouponCodeRedisDao;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 券码兑换检查
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ExchangeChecker {

    @Resource
    private CouponCodeRedisDao couponCodeRedisDao;

    private static final char[] CHARS = new char[]{'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N',
            'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '2', '3', '4', '5', '6', '7', '8', '9'};
    private static final int TOTAL_LEN = 16;
    private static final String FIRST = "M";

    /**
     * 参数校验
     *
     * @param request SingleExchangeRequestDo
     * @throws BizError .
     */
    public void paramsChecker(SingleExchangeRequestDo request) throws BizError {
        if (request.getUserId() == null || request.getUserId() <= 0) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_PARAM, "小米ID不符合要求");
        }

        if (StringUtils.isEmpty(request.getCouponCode())) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_PARAM, "优惠码不能为空");
        }

        if (!couponCodeChecker(request.getCouponCode())) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_ILLEGAL, "优惠码不存在");
        }

        if (request.getAppId() == null || "".equals(request.getAppId()) || request.getAppId().trim().length() == 0) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_PARAM, "appID不符合要求");
        }
    }

    /**
     * 优惠码的本身校验
     *
     * @param code String
     * @return bool
     */
    public boolean couponCodeChecker(String code) {
        if (StringUtils.isEmpty(code)) {
            return false;
        }

        if (StringUtils.length(code) != TOTAL_LEN) {
            return false;
        }

        String first = code.substring(0, 1);
        if (!FIRST.equals(first)) {
            return false;
        }

        int checkIndex = (code.charAt(4) | code.charAt(9) ^ code.charAt(13)) % 32;
        return CHARS[checkIndex] == code.charAt(TOTAL_LEN - 1);
    }

    /**
     * 兑换条件校验
     *
     * @param codeInfo         CouponCodePo
     * @param configInfo       CouponConfigItem
     * @param useMode          Integer 使用方式（兑换/结算）
     * @param fromPriceProtect bool 是否来自价保
     * @throws BizError .
     */
    public void conditionChecker(CouponCodePo codeInfo, CouponConfigItem configInfo, Integer useMode, boolean fromPriceProtect) throws BizError {
        //状态检查
        statusChecker(codeInfo, useMode, fromPriceProtect);

        //券配置状态检查
        configStatusChecker(configInfo);

        //券配置时间检查
        configTimeChecker(codeInfo, configInfo, useMode);
    }

    /**
     * 检查单位时间内，用户兑换次数是否已达限制
     *
     * @param userId   long
     * @param maxCount int
     * @throws BizError .
     */
    public void userExchangeCountChecker(long userId, int maxCount) throws BizError {
        try {
            couponCodeRedisDao.incrUserExchangeCount(userId, maxCount);
        } catch (BizError e) {
            throw e;
        } catch (Exception e) {
            log.error("coupon.singleExchange, 检查用户兑券频率是否已达限制遇到异常, error={}, userId={}", e.getMessage(), userId);
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_EXCHANGE_FAIL, "兑换失败");
        }
    }

    /**
     * 状态检查
     *
     * @param codeInfo         CouponCodePo
     * @param useMode          Integer 使用方式
     * @param fromPriceProtect bool 是否来自价保
     * @throws BizError .
     */
    private void statusChecker(CouponCodePo codeInfo, Integer useMode, boolean fromPriceProtect) throws BizError {
        if (StatusEnum.EXPIRED.getValue().equals(codeInfo.getStat())) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_EXPIRE, "优惠码已过期");
        }

        //如果是价保，则必须为已使用
        if (fromPriceProtect) {
            if (!StatusEnum.USED.getValue().equals(codeInfo.getStat())) {
                throw ExceptionHelper.create(ErrCode.COUPON_CODE_INVALID, "优惠码无效");
            }
            return;
        }

        if (StatusEnum.USED.getValue().equals(codeInfo.getStat())) {
            if (UseModeEnum.EXCHANGE.getValue().equals(useMode)) {
                throw ExceptionHelper.create(ErrCode.COUPON_CODE_USED, "优惠码已兑换");
            }
            if (UseModeEnum.CHECKOUT.getValue().equals(useMode)) {
                throw ExceptionHelper.create(ErrCode.COUPON_CODE_USED, "优惠码已使用");
            }
        }

        if (!StatusEnum.UNUSED.getValue().equals(codeInfo.getStat())) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_INVALID, "优惠码无效");
        }
    }

    /**
     * 券配置状态校验
     *
     * @param configInfo CouponConfigItem
     */
    private void configStatusChecker(CouponConfigItem configInfo) throws BizError {
        if (!ConfigStatusEnum.Online.getValue().equals(configInfo.getCouponConfigInfo().getStatus())) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_INVALID, "优惠码无效");
        }
    }

    /**
     * 时间校验(有效期的校验)
     *
     * @param codeInfo 优惠码信息
     * @param configInfo CouponConfigItem
     * @param useMode    Integer 使用模式（兑换/结算）
     */
    private void configTimeChecker(CouponCodePo codeInfo, CouponConfigItem configInfo, Integer useMode) throws BizError {
        long nowTime = TimeUtil.getNowUnixSecond();
        if (configInfo.getCouponConfigInfo().getStartFetchTime() == null || configInfo.getCouponConfigInfo().getStartFetchTime() <= 0L) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_INVALID, "优惠码无效");
        }
        if (configInfo.getCouponConfigInfo().getEndFetchTime() == null || configInfo.getCouponConfigInfo().getEndFetchTime() <= 0L) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_INVALID, "优惠码无效");
        }
        if (configInfo.getCouponConfigInfo().getStartUseTime() == null || configInfo.getCouponConfigInfo().getStartUseTime() <= 0L) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_INVALID, "优惠码无效");
        }
        if (codeInfo.getEndTime() == null || codeInfo.getEndTime() <= 0L) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_INVALID, "优惠码无效");
        }
        if (UseModeEnum.EXCHANGE.getValue().equals(useMode)) {
            if (configInfo.getCouponConfigInfo().getStartFetchTime() > nowTime) {
                throw ExceptionHelper.create(ErrCode.COUPON_CODE_TIME_NO_START, "未到可兑换时间");
            }
            if (configInfo.getCouponConfigInfo().getEndFetchTime() <= nowTime) {
                throw ExceptionHelper.create(ErrCode.COUPON_CODE_EXPIRE, "优惠码已过期");
            }
        } else if (UseModeEnum.CHECKOUT.getValue().equals(useMode)) {
            if (configInfo.getCouponConfigInfo().getStartUseTime() > nowTime) {
                throw ExceptionHelper.create(ErrCode.COUPON_CODE_TIME_NO_START, "未到可用时间");
            }
        }
        if (codeInfo.getEndTime() <= nowTime) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_EXPIRE, "优惠码已过期");
        }
    }
}
