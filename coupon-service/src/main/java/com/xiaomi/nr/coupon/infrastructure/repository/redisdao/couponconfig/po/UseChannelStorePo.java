package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/3/1 4:44 下午
 * @Version: 1.0
 **/
@Data
public class UseChannelStorePo implements Serializable {

    /**
     * 门店范围 1:全部门店 2:部分门店
     */
    @SerializedName("s")
    private Integer scope;

    /**
     * 门店ID列表 如果门店范围是全部门店，则此列表为空
     */
    @SerializedName("si")
    private List<String> storeIds;
}
