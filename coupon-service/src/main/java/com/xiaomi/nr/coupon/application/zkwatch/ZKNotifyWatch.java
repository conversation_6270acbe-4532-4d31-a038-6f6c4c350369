package com.xiaomi.nr.coupon.application.zkwatch;

import com.xiaomi.miliao.zookeeper.ZKClient;
import com.xiaomi.miliao.zookeeper.ZKFacade;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: ZK节点变更监控
 * @author: hejiapeng
 * @Date 2022/3/1 8:39 下午
 * @Version: 1.0
 **/
@Slf4j
@Order(2)
@Component
public class ZKNotifyWatch implements ApplicationRunner {

    private final ZKClient zkClient = ZKFacade.getAbsolutePathClient();

    private static final String COUPON_CONFIG_CHANGE_ZK_PATH = "/cnzone/config/nr-mid-platform/coupon/record/coupon_config_refresh_version";
    @Resource
    private CouponConfigRepository couponConfigRepository;

    /**
     * 券变动增量通知
     */
    private void watchCouponConfigVersion() {
        zkClient.registerDataChanges(String.class, COUPON_CONFIG_CHANGE_ZK_PATH, (path, data) -> {
            try {
                long runStartTime = TimeUtil.getNowUnixMillis();
                log.info("ZKNotifyWatch.watchCouponConfigVersion increment update  data：{}", data);
                long currentSeqId = couponConfigRepository.loadIncrCouponConfigToCache();
                log.info("ZKNotifyWatch.watchCouponConfigVersion, 监控刷新券配置任务结束, currentSeqId:{}, runTime={}毫秒", currentSeqId, TimeUtil.sinceMillis(runStartTime));
            } catch (Exception e) {
                log.warn("CouponConfig increase load execute error", e);
            }
        });
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        watchCouponConfigVersion();
    }
}
