package com.xiaomi.nr.coupon.infrastructure.annotation.aspect;

import com.google.common.util.concurrent.RateLimiter;
import com.xiaomi.nr.coupon.infrastructure.annotation.MethodLimit;
import com.xiaomi.nr.coupon.infrastructure.error.ErrInfo;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class MethodLimitAop {

    /**
     * 定义每个方法对应的限流器
     */
    private final ConcurrentHashMap<String, RateLimiter> rateLimiterMap = new ConcurrentHashMap();

    @Around(value = "@annotation(com.xiaomi.nr.coupon.infrastructure.annotation.MethodLimit)")
    public Object around(ProceedingJoinPoint joinPoint) {

        //拦截目标方法,获取拦截的方法名
        MethodSignature methodSignature = (MethodSignature)(joinPoint.getSignature());
        //获取目标方法上限流注解的参数
        MethodLimit methodLimit = methodSignature.getMethod().getDeclaredAnnotation(MethodLimit.class);

        //获取方法对应的 RateLimiter 没有则创建
        RateLimiter rateLimiter = rateLimiterMap.get(methodLimit.name());
        if (rateLimiter == null) {
            rateLimiter = RateLimiter.create(methodLimit.frequency(), methodLimit.timeout(),methodLimit.timeUnit());
            rateLimiterMap.put(methodLimit.name(), rateLimiter);
        }

        //接口限流控制
        if (!rateLimiter.tryAcquire()) {
            log.warn("coupon.admin.CurrentLimitAop, 该接口访问频率过快，请稍后访问，methodName={}",methodLimit.name());
            return Result.fromException(ErrInfo.ERR_COUPON_CONFIG_INTERFACE_BUZY);
        }

        //执行目标方法 返回目标方法结果
        Object result;
        try {
            result = joinPoint.proceed();
        } catch (Throwable throwable) {
            log.warn("coupon.admin.CurrentLimitAop, Aspect error，methodName={}, err={}",methodLimit.name(),throwable.getMessage());
            return Result.fromException(ErrInfo.ERR_COUPON_CONFIG_INTERFACE_BUZY);
        }
        return result;
    }

}

