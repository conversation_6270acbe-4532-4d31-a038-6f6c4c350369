package com.xiaomi.nr.coupon.domain.matchertool.entity.common;

import lombok.Data;

/**
 * MatcherConfigItemDo
 *
 * <AUTHOR>
 * @date 2023/7/31
 */
@Data
public class MatcherConfigItemDo {

    /**
     * 可领校验参数：券配置ID
     */
    private Long configId;

    /**
     * 可领校验参数：投放场景ID
     */
    private String sceneCode;

    /**
     * 可领校验参数：分配为投放系统的appid（pls里分配的）
     */
    private String appId;

}
