package com.xiaomi.nr.coupon.domain.coupon;

import com.xiaomi.nr.coupon.api.dto.PageBeanStream;
import com.xiaomi.nr.coupon.api.dto.coupon.list.UserCouponListDto;
import com.xiaomi.nr.coupon.api.dto.coupon.list.UserCouponListRequest;
import com.xiaomi.nr.coupon.constant.CommonConstant;
import com.xiaomi.nr.coupon.constant.CouponConfigConstant;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponConvert;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponFormatCommon;
import com.xiaomi.nr.coupon.domain.coupon.model.CouponInfoModel;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.utils.profiler.JProfilerUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.profiler.Profiler;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Slf4j
public abstract class AbstractUserCouponList {

    @Autowired
    private CouponFormatCommon couponFormatCommon;

    @Autowired
    private CouponConvert couponConvert;

    public PageBeanStream<UserCouponListDto> getUserCouponList(UserCouponListRequest req) throws BizError {

        Profiler profiler = JProfilerUtil.createNestedProfiler("getUserCouponList");
        log.info("getUserCouponList. req:{}", GsonUtil.toJson(req));
        // 1、初始化入参
        initRequest(req);
        JProfilerUtil.startNestedPoint(profiler,"userCouponList-getUserCouponList");

        // 2、获取用户优惠券信息(db -> po)
        Pair<Integer, List<CouponPo>> couponPoPair = getCouponPoList(req);
        int poSize = couponPoPair.getKey();
        List<CouponPo> couponPoList = couponPoPair.getValue();
        JProfilerUtil.startNestedPoint(profiler,"userCouponList-getCouponPoList");

        // 3、格式化优惠券信息(po -> model)
        List<CouponInfoModel> couponInfoModelList = formatCouponList(req.getBizPlatform(), couponPoList);
        JProfilerUtil.startNestedPoint(profiler,"userCouponList-formatCouponList");

        // 4、过滤
        couponInfoModelList = filterCoupon(req, couponInfoModelList);

        // 5、构建用户优惠券列表返回值(model -> dto)
        List<UserCouponListDto> userCouponListDtoList = convert2UserCouponListDto(couponInfoModelList);

        // 6、构建出参
        int pageSize = req.getPageSize();
        int total = userCouponListDtoList.size();
        long lastId = poSize > CommonConstant.ZERO_INT ? couponPoList.get(poSize - CommonConstant.ONE_INT).getId() : CommonConstant.ZERO_LONG;

        JProfilerUtil.stopNestedPoint(profiler);
        //全量返回(排序)
        if (!isPageStatus(req.getStatus())) {
            return new PageBeanStream<>(lastId, Math.max(pageSize, total), true, couponSort(userCouponListDtoList));
        }

        //分页返回
        return new PageBeanStream<>(lastId, pageSize, pageSize > poSize, userCouponListDtoList);
    }

    /**
     * 初始化请求参数(兼容分页与不分页请求)
     *
     * @param req 请求参数
     * @return 初始化之后的值
     */
    private void initRequest(UserCouponListRequest req) {
        if (Objects.isNull(req.getPageSize())) {
            req.setPageSize(CommonConstant.PAGESIZE_DEFAULT);
        }

        if (req.getPageSize() > CommonConstant.PAGESIZE_MAX) {
            req.setPageSize(CommonConstant.PAGESIZE_MAX);
        }

        if (Objects.isNull(req.getLastId())) {
            req.setLastId(CommonConstant.ZERO_LONG);
        }
    }

    /**
     * 从DB获取用户优惠券列表初始信息
     *
     * @param req            用户优惠券列表请求参数
     * @return Pair<Integer, List<CouponPo>>   (页大小, 过滤后po)
     */
    protected abstract Pair<Integer, List<CouponPo>> getCouponPoList(UserCouponListRequest req);

    /**
     * 格式化用户优惠券列表
     *
     * @param couponPoList 优惠券列表基础信息(DB数据, 不包含券配置信息)
     * @return List<>      优惠券列表详细信息,包含券配置缓存信息
     */
    private List<CouponInfoModel> formatCouponList(List<Integer> bizPlatform, List<CouponPo> couponPoList) throws BizError {
        if (CollectionUtils.isEmpty(couponPoList)) {
            return Collections.emptyList();
        }

        //调用公共的format方法, 获取优惠券详细信息
        return couponFormatCommon.formatCouponList(bizPlatform, couponPoList);
    }

    protected List<CouponInfoModel> filterCoupon(UserCouponListRequest req, List<CouponInfoModel> couponInfoList) {
        return couponInfoList;
    }

    /**
     * 构建用户优惠券列表返回值 (model -> dto)
     *
     * @param couponInfoModelList 优惠券列表详细信息
     * @return List<>             用户优惠券列表接口返回值
     */
    private List<UserCouponListDto> convert2UserCouponListDto(List<CouponInfoModel> couponInfoModelList) {
        if (CollectionUtils.isEmpty(couponInfoModelList)) {
            return Collections.emptyList();
        }

        //调用公共转换方法
        List<UserCouponListDto> userCouponListDtoList = new ArrayList<>(couponInfoModelList.size());
        couponInfoModelList.forEach(couponInfoModel -> {
            UserCouponListDto userCouponListDto = couponConvert.convert2UserCouponListDto(couponInfoModel);
            if (!Objects.isNull(userCouponListDto)) {
                userCouponListDtoList.add(userCouponListDto);
            }
        });
        return userCouponListDtoList;
    }

    /**
     * 优惠券状态校验(判断是否分页返回)
     *
     * @param status 优惠券状态
     * @return bool  true:分页返回，false:全量返回
     */
    private boolean isPageStatus(String status) {
        return CouponConfigConstant.COUPON_STATUS_PAGEMAP.containsKey(status);
    }

    /**
     * 优惠券列表排序，按力度大小，是否已用排序(目前排序只针对于unused、all状态的查询进行排序)
     *
     * @param couponList 用户优惠券列表
     * @return List<>    按规则排序后的列表
     */
    private List<UserCouponListDto> couponSort(List<UserCouponListDto> couponList) {
        if (CollectionUtils.isEmpty(couponList)) {
            return Collections.emptyList();
        }

        //未使用状态
        List<UserCouponListDto> couponUnUsedList = new ArrayList<>();
        //其他状态
        List<UserCouponListDto> couponUsedList = new ArrayList<>();
        couponList.forEach(coupon -> {
            if (StringUtils.equals(coupon.getStatus(), CouponStatusEnum.UNUSED.getValue())) {
                couponUnUsedList.add(coupon);
            }else {
                couponUsedList.add(coupon);
            }});

        //按value值从大到小排序返回
        List<UserCouponListDto> sortResult = sortList(couponUnUsedList);
        sortResult.addAll(sortList(couponUsedList));
        return sortResult;
    }

    /**
     * 优惠券列表信息按value倒序排列
     *
     * @param userCouponListDtoList    初始优惠券列表
     * @return List<UserCouponListDto> 倒排后优惠券列表
     */
    private List<UserCouponListDto> sortList(List<UserCouponListDto> userCouponListDtoList) {

        //先按showValue倒序排列，showValue相同时再按addTime倒序排列
        userCouponListDtoList = userCouponListDtoList.stream().sorted((c1,c2) -> {
            String beforeV = c1.getShowValue();
            String afterV = c2.getShowValue();
            if(StringUtils.equals(beforeV,afterV)){
                return -c1.getAddTime().compareTo(c2.getAddTime());
            }
            BigDecimal c1V = new BigDecimal(CommonConstant.EMPTY_STR.equals(beforeV) ? CommonConstant.ZERO_STR : beforeV);
            BigDecimal c2V = new BigDecimal(CommonConstant.EMPTY_STR.equals(afterV) ? CommonConstant.ZERO_STR : afterV);
            return -c1V.compareTo(c2V);

        }).collect(Collectors.toList());

        return userCouponListDtoList;
    }



}
