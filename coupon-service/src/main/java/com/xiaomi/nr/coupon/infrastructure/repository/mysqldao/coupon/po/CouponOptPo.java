package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po;

import lombok.Data;

/**
 * @description: 用户券数据库操作表
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2023/1/9 9:45 上午
 * @Version: 1.0
 **/
@Data
public class CouponOptPo {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 车辆VID
     */
    private String vid;

    /**
     * 订单编号
     */
    private Long orderId;

    /**
     * 操作状态 1 锁定 2 回滚 3 核销
     */
    private Integer optType;

    /**
     * 添加时间
     */
    private Long addTime;

}
