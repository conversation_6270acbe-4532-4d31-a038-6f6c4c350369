package com.xiaomi.nr.coupon.enums.store;

import java.util.Objects;

public enum StoreTypeEnum {
    /**
     * 直营店
     */
    ORG_TYPE_DIRECT(1),
    /**
     * 专卖店
     */
    ORG_TYPE_SPECIALTY(2),
    /**
     * 授权店
     */
    ORG_TYPE_AUTHORIZED(3);

    private final int orgType;

    StoreTypeEnum(int orgType) {
        this.orgType = orgType;
    }

    public int getOrgType() {
        return orgType;
    }

    /**
     * 是否是直营店
     *
     * @param orgType 门店类型
     * @return true/false
     */
    public static boolean isDirect(int orgType) {
        return ORG_TYPE_DIRECT.getOrgType() == orgType;
    }

    /**
     * 是否是专卖店
     *
     * @param orgType 门店类型
     * @return true/false
     */
    public static boolean isSpecialty(int orgType) {
        return ORG_TYPE_SPECIALTY.getOrgType() == orgType;
    }

    /**
     * 是否是授权店
     *
     * @param orgType 门店类型
     * @return true/false
     */
    public static boolean isAuthorized(int orgType) {
        return ORG_TYPE_AUTHORIZED.getOrgType() == orgType;
    }

    /**
     * 是否是合法的门店枚举类型
     * @param value
     * @return
     */
    public static boolean isStoreType(Integer value) {

        if(value == null){
            return false;
        }

        StoreTypeEnum[] values = StoreTypeEnum.values();
        for (StoreTypeEnum item : values) {
            if (Objects.equals(item.getOrgType(), value)) {
                return true;
            }
        }
        return false;
    }

}
