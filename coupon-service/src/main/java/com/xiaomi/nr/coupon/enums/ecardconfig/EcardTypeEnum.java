package com.xiaomi.nr.coupon.enums.ecardconfig;

/**
 * 礼品卡类型 枚举
 *
 * <AUTHOR>
 */

public enum EcardTypeEnum {
    // TypeIDRecycle 爱回收卡类型
    TypeIDRecycleAHS(9,""),
    TypeIDRecycleExtendAHS(26,""),
    TypeIDRecycleAHSMiCare(29,"micare 换新券"),
    TypeIDRecycleAHSMiCareExtend(32,"micare 换新补贴券"),
    // TypeIDExpress 快递补偿卡类型
    TypeIDExpress(14,""),
    TypeIDRecycleStand(24,"标准"),
    TypeIDRecycleExtend(25,"补贴"),
    TypeIDBeijingShoppingCoupon(28,"北京消费劵"),
    TypeIDRecycleMiCare(30,"micare 换新券"),
    TypeIDRecycleMiCareExtend(31,"micare 换新补贴券"),
    TypeIDBeijingSpringCoupon(40,"北京市年货劵"),

    /**
     * 标准换新现金券
     */
    recycle(41, "标准换新现金券"),

    AirConditionerRecycle(43, "空调换新现金券"),

    /**
     * 给用户开票，不给电信开票
     */
    CoinTelecomDoor(44, "米金电信上门服务现金券"),

    /**
     * 不给用户开票，给电信开票
     */
    CoinTelecomDoorInvoice(46, "米金电信上门服务现金券"),




    ;


    private final Integer value;
    private final String name;

    EcardTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static EcardTypeEnum findByValue(Integer value) {
        EcardTypeEnum[] values = EcardTypeEnum.values();
        for (EcardTypeEnum ecardTypeEnum : values) {
            if (value.equals(ecardTypeEnum.value)) {
                return ecardTypeEnum;
            }
        }
        return null;
    }
}