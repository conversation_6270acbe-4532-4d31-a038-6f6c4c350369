package com.xiaomi.nr.coupon.domain.coupon;

import com.xiaomi.nr.coupon.api.dto.coupon.NearDaysListByTypeIdsCouponInfoDto;
import com.xiaomi.nr.coupon.api.dto.coupon.NearDaysListByTypeIdsItemDto;
import com.xiaomi.nr.coupon.api.dto.coupon.OneGoodsUseInfoDto;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.GoodScope;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave.UserCouponIdsMapper;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户近*天优惠券列表（部分信息）
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class NearDaysList {

    @Resource
    private UserCouponIdsMapper userCouponIdsMapper;

    @Autowired
    private CouponConfigRepository couponConfigRepository;


    /**
     * 根据券配置ID批量查询用户近*天的券列表（支持批量）
     *
     * @param userId          Long
     * @param addTime         Long
     * @return Map<String, NearDaysListByTypeIdsItemDto>
     */
    public Map<String, NearDaysListByTypeIdsItemDto> getNearDaysListByTypeIds(Long userId, List<Long> configIds, Long addTime) throws BizError {
        if(CollectionUtils.isEmpty(configIds)){
            return Collections.emptyMap();
        }
        Map<String, NearDaysListByTypeIdsItemDto> result = new HashMap<>(configIds.size());

        List<CouponPo> coupons = userCouponIdsMapper.getListByConfigIds(userId, configIds, addTime);
        if (CollectionUtils.isEmpty(coupons)) {
            return result;
        }
        Map<Long, List<CouponPo>> typeIdCoupons = coupons.stream().collect(Collectors.groupingBy(CouponPo::getTypeId));
        Map<Long, CouponConfigItem> couponConfigItemMap =   couponConfigRepository.getCouponConfigs(configIds);

        typeIdCoupons.forEach((configId,couponPos)->{
            NearDaysListByTypeIdsItemDto typeItem = new NearDaysListByTypeIdsItemDto();
            CouponConfigItem couponConfigItem = couponConfigItemMap.get(configId);
            if(couponConfigItem == null) {
                log.warn("NearDaysList getNearDaysListByTypeIds couponConfigItem is null, userId={}, configId={}", userId, configId);
                return;
            }
            if(BizPlatformEnum.RETAIL.getCode() != couponConfigItem.getBizPlatform()){
                return;
            }
            GoodScope goodScope = couponConfigItem.getGoodScope();
            if((goodScope.getGoods().size()+goodScope.getPackages().size()) ==1){
                typeItem.setIsOneGoodsUse(true);
                if(goodScope.getGoods().size()==1){
                    goodScope.getGoods().forEach((k)->{
                        typeItem.setOneGoodsUseInfo(new OneGoodsUseInfoDto(k, GoodsLevelEnum.Goods.getValue()));
                    });
                }else if(goodScope.getPackages().size()==1){
                    goodScope.getPackages().forEach((k)->{
                        typeItem.setOneGoodsUseInfo(new OneGoodsUseInfoDto(k, GoodsLevelEnum.Package.getValue()));
                    });
                }
            }

            typeItem.setCoupons(couponPos.stream().map(x->makeCouponItem(x)).collect(Collectors.toList()));
            result.put(String.valueOf(configId), typeItem);
        });

        return result;
    }




    /**
     * 构始建券item
     *
     * @param coupon CouponPo
     * @return NearDaysListByTypeIdsCouponInfoDto
     */
    private NearDaysListByTypeIdsCouponInfoDto makeCouponItem(CouponPo coupon) {
        NearDaysListByTypeIdsCouponInfoDto couponItem = new NearDaysListByTypeIdsCouponInfoDto();
        couponItem.setCouponId(coupon.getId());
        couponItem.setStatus(coupon.getStat());
        couponItem.setStatusDesc(CouponStatusEnum.findNameByValue(coupon.getStat()));

        //取消和作废的不返回
        if (couponItem.getStatus().contains(CouponStatusEnum.CANCEL.getValue()) || couponItem.getStatus().contains(CouponStatusEnum.INVALID.getValue())) {
            return null;
        }

        //开始时间
        try {
            couponItem.setStartTime(Long.parseLong(coupon.getStartTime()));
        } catch (NumberFormatException e) {
            log.warn("NearDaysList.getNearDaysListByTypeIds, 券的开始时间无法转换成long, userId={}, typeId={}, couponId={}, startTime={}", coupon.getUserId(), coupon.getTypeId(), coupon.getId(), coupon.getStartTime());
            return null;
        }

        //结束时间
        try {
            couponItem.setEndTime(Long.parseLong(coupon.getEndTime()));
        } catch (NumberFormatException e) {
            log.warn("NearDaysList.getNearDaysListByTypeIds, 券的结束时间无法转换成long, userId={}, typeId={}, couponId={}, startTime={}", coupon.getUserId(), coupon.getTypeId(), coupon.getId(), coupon.getStartTime());
            return null;
        }

        //修正过期的券状态:时间过期且状态为未使用的，要改成过期状态
        if (couponItem.getEndTime() <= TimeUtil.getNowUnixSecond() && couponItem.getStatus().contains(CouponStatusEnum.UNUSED.getValue())) {
            couponItem.setStatus(CouponStatusEnum.EXPIRED.getValue());
            couponItem.setStatusDesc(CouponStatusEnum.EXPIRED.getName());
        }
        //修正过期的券状态:锁定状态的为已使用状态
        if (couponItem.getStatus().contains(CouponStatusEnum.LOCKED.getValue())) {
            couponItem.setStatus(CouponStatusEnum.USED.getValue());
            couponItem.setStatusDesc(CouponStatusEnum.USED.getName());
        }
        return couponItem;
    }


}
