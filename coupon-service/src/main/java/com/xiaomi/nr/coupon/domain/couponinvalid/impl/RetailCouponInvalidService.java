package com.xiaomi.nr.coupon.domain.couponinvalid.impl;

import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponCheckDto;
import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponReq;
import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponResp;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.couponinvalid.CouponInvalidService;
import com.xiaomi.nr.coupon.domain.couponinvalid.constant.CouponInvalidConst;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.UserCouponRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.xiaomi.nr.coupon.domain.couponinvalid.constant.CouponInvalidConst.RETAIL_COUPON_BIZ_PLATFORM_INVALID;
import static com.xiaomi.nr.coupon.domain.couponinvalid.constant.CouponInvalidConst.RETAIL_COUPON_STATUS_INVALID;

/**
 * <AUTHOR>
 * @date 2025/2/24 16:51
 */
@Component
@Slf4j
public class RetailCouponInvalidService extends CouponInvalidService {
    @Autowired
    private UserCouponRepository userCouponRepository;

    /**
     * 券作废校验
     *
     * @param req req  InvalidCouponReq
     * @return 不可作废券列表
     */
    @Override
    public List<InvalidCouponCheckDto> invalidCouponCheck(InvalidCouponReq req) throws BizError {
        // 1、查询DB
        List<CouponPo> couponPoList = userCouponRepository.getCouponPoList(req.getUserId(), req.getCouponIdList());

        // 2、校验
        return checkCouponList(couponPoList);
    }

    /**
     * 检查优惠券列表中的无效优惠券
     *
     * @param couponPoList 优惠券列表
     * @return 无效优惠券检查结果列表
     */
    private List<InvalidCouponCheckDto> checkCouponList(List<CouponPo> couponPoList) {
        List<InvalidCouponCheckDto> cantInvalidCouponList = new ArrayList<>();

        for (CouponPo couponPo : couponPoList) {
            String couponStat = couponPo.getStat();
            // 券业务场景不可用
            if (!Objects.equals(couponPo.getBizPlatform(), BizPlatformEnum.RETAIL.getCode())) {
                String cantInvalidReason = String.format(RETAIL_COUPON_BIZ_PLATFORM_INVALID, couponPo.getBizPlatform());
                cantInvalidCouponList.add(new InvalidCouponCheckDto(couponPo.getId(), cantInvalidReason));
            }

            // 券状态非未使用
            if (!CouponStatusEnum.UNUSED.getValue().equals(couponStat)) {
                String cantInvalidReason = String.format(RETAIL_COUPON_STATUS_INVALID, couponStat);
                cantInvalidCouponList.add(new InvalidCouponCheckDto(couponPo.getId(), cantInvalidReason));
            }
        }

        return cantInvalidCouponList;
    }

    /**
     * 券作废
     *
     * @param req InvalidCouponReq
     * @return resp
     */
    @Override
    public InvalidCouponResp invalidCoupon(InvalidCouponReq req) throws BizError {
        InvalidCouponResp resp = new InvalidCouponResp();
        resp.setSuccess(false);

        // 1、查询DB
        Long userId = req.getUserId();
        List<Long> couponIdList = req.getCouponIdList();
        List<CouponPo> couponPoList = userCouponRepository.getCouponPoList(userId, couponIdList);

        // 2、幂等校验
        if (couponPoList.stream().allMatch(po -> CouponStatusEnum.INVALID.getValue().equals(po.getStat()))) {
            resp.setSuccess(true);
            resp.setIdempotent(true);
            return resp;
        }

        // 3、可作废校验
        List<InvalidCouponCheckDto> cantInvalidCouponList = checkCouponList(couponPoList);
        if (CollectionUtils.isNotEmpty(cantInvalidCouponList)) {
            InvalidCouponCheckDto invalidCouponCheckDto = cantInvalidCouponList.get(0);
            resp.setFailReason(String.format(CouponInvalidConst.FAIL_REASON_TEMP, invalidCouponCheckDto.getCouponId(), invalidCouponCheckDto.getReason()));
            return resp;
        }

        // 4、DB作废券操作
        userCouponRepository.invalidCoupon(userId, couponIdList);
        resp.setSuccess(true);

        return resp;
    }
}
