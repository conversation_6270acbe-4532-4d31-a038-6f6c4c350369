package com.xiaomi.nr.coupon.domain.fetchcheck;


import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponSceneItem;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;


/**
 * @Description: 券可领性校验
 * @Date: 2022.05.18 15:20
 */
public interface CouponFetchCheck {

    /**
     * 校验必要的配置信息
     * @param couponConfigItem 券配置信息
     * @param couponSceneItem 场景信息
     * @throws BizError
     */
    void checkData(CouponConfigItem couponConfigItem, CouponSceneItem couponSceneItem) throws BizError;

    /**
     * 校验业务领域
     * @param bizPlatform 业务领域
     * @param couponConfigItem 券配置
     * @throws BizError
     */
    void checkBizPlatform(Integer bizPlatform, CouponConfigItem couponConfigItem) throws BizError;

    /**
     * 场景校验
     * @param appId 调用方appId
     * @param couponSceneItem 场景配置
     * @param couponConfigItem 券配置
     */
    void checkSendScene(String appId, CouponSceneItem couponSceneItem, CouponConfigItem couponConfigItem) throws BizError;

    /**
     * 校验优惠券状态
     * @param couponConfigItem 券配置
     * @throws BizError
     */
    void checkStatus(CouponConfigItem couponConfigItem) throws BizError;

    /**
     * 校验时间
     * @param couponConfigItem 券配置
     */
    void checkTime(CouponConfigItem couponConfigItem) throws BizError;

    /**
     * 校验个人领券限制
     * @param fetchCount 已领数量
     * @param limit 限领数量
     */
    void checkFetchLimit(Integer fetchCount, Integer limit) throws BizError;

    /**
     * 校验券库存
     * @param count 已发数量
     * @param limit 限发数量
     */
    void checkCouponInventory(Integer count, Integer limit) throws BizError;
}
