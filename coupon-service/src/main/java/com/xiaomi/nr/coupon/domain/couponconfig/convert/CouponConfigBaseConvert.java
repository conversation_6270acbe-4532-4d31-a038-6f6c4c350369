package com.xiaomi.nr.coupon.domain.couponconfig.convert;

import com.xiaomi.nr.coupon.api.dto.couponconfig.CouponConfigBaseInfo;
import com.xiaomi.nr.coupon.api.dto.couponconfig.MissionDto;
import com.xiaomi.nr.coupon.constant.CouponConfigConstant;
import com.xiaomi.nr.coupon.domain.common.FormatCouponUtil;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponFormatCommon;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigInfo;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.enums.couponconfig.BottomTypeEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.OutputStatusEnum;
import com.xiaomi.nr.coupon.enums.couponmission.TimeTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.po.MissionCacheItemPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Supplier;

@Component
@Slf4j
public abstract class CouponConfigBaseConvert {

    @Resource
    private CouponFormatCommon couponFormatCommon;

    public CouponConfigBaseInfo convertCouponConfigBaseInfo(CouponConfigItem configCache, Supplier<? extends CouponConfigBaseInfo> supplier) throws BizError {
        if (Objects.isNull(configCache)) {
            log.warn("initCouponTypeInfo fail, param error ConfigCacheItemPo is null");
            return null;
        }

        //检验是否是本地缓存的空对象
        Integer configId = configCache.getConfigId();
        if (configId == null) {
            return null;
        }

        CouponConfigInfo couponConfigInfo = configCache.getCouponConfigInfo();
        String useTypeDesc = configCache.getPromotionType().getName();
        Map<String, String> showMap = FormatCouponUtil.formatCouponShowValue(configCache.getPromotionType().getCode(), couponConfigInfo.getPromotionValue());
        String showValue = showMap.get(CouponConfigConstant.SHOW_VALUE);
        String showUnit = showMap.get(CouponConfigConstant.SHOW_UNIT);
        BigDecimal reduceMaxPrice = convertReduceMaxPrice(couponConfigInfo.getMaxReduce().longValue());
        BottomTypeEnum bottomType = BottomTypeEnum.findByCode(couponConfigInfo.getBottomType());
        String quotaValue = convertQuotaValue(bottomType, couponConfigInfo.getBottomPrice(), couponConfigInfo.getBottomCount());

        CouponConfigBaseInfo couponConfigBaseInfo = supplier.get();
        couponConfigBaseInfo.setConfigId(configId.longValue());
        couponConfigBaseInfo.setConfigName(couponConfigInfo.getName());
        couponConfigBaseInfo.setTypeCode(configCache.getPromotionType().getValue());
        couponConfigBaseInfo.setTypeCodeDesc(useTypeDesc);
        couponConfigBaseInfo.setUseType(configCache.getPromotionType().getValue());
        couponConfigBaseInfo.setUseTypeDesc(useTypeDesc);
        couponConfigBaseInfo.setQuotaType(bottomType.getValue());
        couponConfigBaseInfo.setQuotaValue(quotaValue);
        couponConfigBaseInfo.setShowValue(showValue);
        couponConfigBaseInfo.setShowUnit(showUnit);
        couponConfigBaseInfo.setReduceMaxPrice(reduceMaxPrice);
        couponConfigBaseInfo.setStatus(OutputStatusEnum.Approved.getMysqlValue());
        couponConfigBaseInfo.setStatusDesc(OutputStatusEnum.Approved.getName());
        couponConfigBaseInfo.setSendChannel(couponConfigInfo.getSendChannel());
        couponConfigBaseInfo.setRangeDesc(couponConfigInfo.getCouponDesc());
        couponConfigBaseInfo.setAddTime(0L);
        couponConfigBaseInfo.setOnlineStatus(configCache.getCouponConfigInfo().getStatus());
        couponConfigBaseInfo.setStartFetchTime(couponConfigInfo.getStartFetchTime());
        couponConfigBaseInfo.setEndFetchTime(couponConfigInfo.getEndFetchTime());
        if (MapUtils.isNotEmpty(configCache.getUseChannelStore())) {
            couponConfigBaseInfo.setUseChannels((new ArrayList<>(configCache.getUseChannelStore().keySet())));
        }
        couponConfigBaseInfo.setSendScene(couponConfigInfo.getSendScene());

        //格式化优惠券定制规则信息
        couponConfigBaseInfo.setCustomDetail(couponFormatCommon.getCustomDetail(configCache, bottomType));
        couponConfigBaseInfo.setCouponType(configCache.getCouponType());
        return couponConfigBaseInfo;
    }

    /**
     * 转换抵扣券最大可抵扣金额（单位元）
     *
     * @param maxPrice Long
     * @return BigDecimal
     */
    public BigDecimal convertReduceMaxPrice(Long maxPrice) {
        BigDecimal money = new BigDecimal(maxPrice);
        return money.divide(new BigDecimal("100"), 2, BigDecimal.ROUND_DOWN).stripTrailingZeros();
    }

    /**
     * 配额输出转换
     *
     * @param bottomType String
     * @param quotaMoney Long
     * @param quotaCount Long
     * @return String
     */
    private String convertQuotaValue(BottomTypeEnum bottomType, Integer quotaMoney, Integer quotaCount) {
        if (BottomTypeEnum.Money == bottomType || BottomTypeEnum.EveMoney == bottomType) {
            BigDecimal showMoney = new BigDecimal(String.valueOf(quotaMoney));
            return showMoney.divide(new BigDecimal("100"), 2, BigDecimal.ROUND_UP).stripTrailingZeros().toPlainString();
        }

        if (BottomTypeEnum.Count == bottomType || BottomTypeEnum.EveCount == bottomType) {
            return String.valueOf(quotaCount);
        }

        //超出已知范围了
        return "";
    }


    /**
     * missionCache -> missionDto
     *
     * @param missionCache 券发放任务缓存信息
     * @return MissionDto  券发放任务Dto信息
     */
    protected MissionDto convertMission(MissionCacheItemPo missionCache) {
        if (Objects.isNull(missionCache)) {
            return null;
        }
        String timeType = missionCache.getTimeType();

        MissionDto missionDto = new MissionDto();
        missionDto.setMissionId(missionCache.getId());
        missionDto.setMissionName(missionCache.getName());
        missionDto.setMissionType(missionCache.getMissionType());
        missionDto.setSendNumLimit(missionCache.getMaxSendNum());
        missionDto.setTimeType(timeType);
        missionDto.setTimeTypeDesc(formatTimeTypeDesc(timeType));
        missionDto.setCouponStartTime(missionCache.getCouponStartTime());
        missionDto.setCouponEndTime(missionCache.getCouponEndTime());
        missionDto.setCouponDays(missionCache.getDays());
        missionDto.setAddTime(missionCache.getAddTime());
        missionDto.setCouponHours(missionCache.getHours());

        return missionDto;
    }




    /**
     * 券的真实有效期类别描述
     *
     * @param timeType 券有效期类型
     * @return String  券有效期类型描述
     */
    protected String formatTimeTypeDesc(String timeType) {
        if (StringUtils.equals(TimeTypeEnum.SECTION.getRedisValue(), timeType)) {
            return TimeTypeEnum.SECTION.getName();
        }

        if (StringUtils.equals(TimeTypeEnum.DAYS.getRedisValue(), timeType)) {
            return TimeTypeEnum.DAYS.getName();
        }
        return "";
    }


    /**
     * 批量转换发放任务信息 cache -> dto
     *
     * @param missionCacheList 发放任务缓存列表
     * @return List<MissionDto> 发放任务返回信息列表
     */
    protected List<MissionDto> getValidMissionDtoList(List<MissionCacheItemPo> missionCacheList) {
        List<MissionDto> missionDtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(missionCacheList)) {
            return missionDtoList;
        }

        for (MissionCacheItemPo missionCacheItemPo : missionCacheList) {
            MissionDto missionDto = convertMission(missionCacheItemPo);
            if (Objects.isNull(missionDto)) {
                continue;
            }
            missionDtoList.add(missionDto);
        }
        return missionDtoList;
    }


}
