package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponcode;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;


/**
 * 券码更新mapper
 *
 * <AUTHOR>
 */
@Mapper
@Component
public interface CouponCodeUpdateMapper {
    /**
     * 兑换券码
     *
     * @param index　String
     * @param id　Long
     * @param userId　Long
     * @param orgCode String
     * @param couponId　Long
     * @param useTime Long
     * @return Integer
     */
    @Update("update tb_codecoupon set stat='used', use_time=#{useTime}, user_id=#{userId}, org_code=#{orgCode}, coupon_id=#{couponId}, use_mode=2" +
            " where coupon_index=#{index} and id=#{id} and stat='unused'" +
            " limit 1")
    Integer exchange(@Param("index") String index, @Param("id") Long id, @Param("userId") Long userId, @Param("orgCode") String orgCode,
                     @Param("couponId") Long couponId, @Param("useTime") Long useTime);
}
