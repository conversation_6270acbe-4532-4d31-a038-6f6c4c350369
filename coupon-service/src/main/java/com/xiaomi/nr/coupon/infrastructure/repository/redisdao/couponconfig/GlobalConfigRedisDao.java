package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig;

import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.po.CompareGoodsItem;

public interface GlobalConfigRedisDao {

    /**
     * 获取全局排除商品
     *
     * @return 商品信息
     */
    CompareGoodsItem getGlobalInExclude();

    /**
     * 获取全局排除活动或券
     *
     * @return 商品信息
     */
    CompareGoodsItem getGlobalActInExclude();

    /**
     * 全局排除券
     *
     * @return 商品信息
     */
    CompareGoodsItem getGlobalCouponInExclude();
}

