package com.xiaomi.nr.coupon.domain.couponinvalid;

import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponCheckDto;
import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponReq;
import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponResp;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;

/**
 * 券作废抽象类
 *
 * <AUTHOR>
 * @date 2024/5/19
 */
public abstract class CouponInvalidService {

    /**
     * 券作废校验
     *
     * @param req  req  InvalidCouponReq
     * @return  不可作废券列表
     */
    public abstract List<InvalidCouponCheckDto> invalidCouponCheck(InvalidCouponReq req) throws BizError;

    /**
     * 券作废
     *
     * @param req  InvalidCouponReq
     * @return  resp
     */
    public abstract InvalidCouponResp invalidCoupon(InvalidCouponReq req) throws BizError;

}
