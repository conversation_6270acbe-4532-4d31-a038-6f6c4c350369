package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.impl;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.constant.CommonConstant;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.po.CouponConfigPO;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.CouponConfigRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.po.ConfigCacheItemPo;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.po.ConfigIdListCachePo;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.po.ConfigInfoCachePo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.StringUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 券配置的redis缓存操作对象
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CouponConfigRedisDaoImpl implements CouponConfigRedisDao {

    /**
     * 券配置cache key（180天以内创建且过期不超过3天）
     */
    private static final String KEY_COUPON_CONFIG_CACHE = "nr_coupon_config_info_cache_{configId}";

    /**
     * 所有－有效的无码券配置ID列表cache key（180天以内创建且未过期的）
     */
    private static final String KEY_COUPON_NO_CODE_CONFIG_VALID_LIST_CACHE_ALL = "nr_coupon_no_code_config_valid_id_list_cache";

    /**
     * 每次读取100个缓存
     */
    private static final int LIMIT_REDIS_GET_COUNT = 100;

    /**
     * 券配置cache key（180天以内创建且过期不超过3天）
     */
    private static final String KEY_COUPON_INFO_CACHE = "nr:coupon:info:{configId}";

    /**
     * 券配置cache key（180天以内创建且过期不超过3天）
     */
    private static final String KEY_COUPON_GOODS_CACHE = "nr:coupon:goods:{configId}";

    /**
     * 券配置商品cache key
     */
    private static final String KEY_COUPON_NEW_GOODS_CACHE = "nr:coupon:newGoods:{configId}";


    /**
     * 缓存操作对象
     */
    @Autowired
    @Qualifier("stringNewCouponRedisTemplate")
    private StringRedisTemplate redisTemplate;


    /**
     * 获取单个券配置信息缓存
     *
     * @param configId Long
     * @return ConfigCacheItemPo
     */
    @Override
    public ConfigCacheItemPo get(Long configId) {
        if (configId == null || configId <= 0) {
            log.error("CouponConfigRedisDao.get, 券配置ID不符合要求, configId={}", configId);
            return null;
        }

        List<Long> ids = new ArrayList<>();
        ids.add(configId);
        Map<Long, ConfigCacheItemPo> r = get(ids);

        if (r == null || r.isEmpty() || r.get(configId) == null) {
            return null;
        }

        if (configId.equals(r.get(configId).getId())) {
            return r.get(configId);
        }
        return null;
    }


    /**
     * 从redis里读取券配置缓存（支持批量）
     *
     * @param configIds List<Long>
     * @return Map<Long, ConfigCacheItemPo>
     */
    @Override
    public Map<Long, ConfigCacheItemPo> get(List<Long> configIds) {
        if (configIds == null || configIds.size() <= 0) {
            log.error("CouponConfigRedisDao.get, 券配置ID列表不符合要求, configIds={}", configIds);
            return Collections.emptyMap();
        }

        //券配置id去重
        configIds = configIds.stream().distinct().collect(Collectors.toList());

        long runStartTime = TimeUtil.getNowUnixMillis();

        Map<Long, ConfigCacheItemPo> result = new HashMap<>(configIds.size());
        ValueOperations<String, String> operations = redisTemplate.opsForValue();

        List<String> keys = new ArrayList<>();
        for (Long id : configIds) {
            if (id == null || id <= 0) {
                log.error("CouponConfigRedisDao.get, 券配置ID不符合要求, id={}", id);
                continue;
            }

            keys.add(StringUtil.formatContent(KEY_COUPON_CONFIG_CACHE, String.valueOf(id)));

            if (keys.size() < LIMIT_REDIS_GET_COUNT) {
                continue;
            }

            List<String> jsonStrList = operations.multiGet(keys);
            List<ConfigCacheItemPo> infos = decodeBaseInfo(keys, jsonStrList);
            if (infos.size() > 0) {
                infos.forEach(v -> result.put(v.getId(), v));
            }
            keys.clear();
        }

        if (!CollectionUtils.isEmpty(keys)) {
            List<String> jsonStrList = new ArrayList<>();
            if (keys.size() > 1) {
                jsonStrList = operations.multiGet(keys);
            } else {
                jsonStrList.add(operations.get(keys.get(0)));
            }
            List<ConfigCacheItemPo> infos = decodeBaseInfo(keys, jsonStrList);
            if (infos.size() > 0) {
                infos.forEach(v -> result.put(v.getId(), v));
            }
        }

        StringBuilder noFindIds = new StringBuilder();
        for (Long id : configIds) {
            if (!result.containsKey(id)) {
                noFindIds.append(id).append(",");
            }
        }
        if (!Strings.isNullOrEmpty(String.valueOf(noFindIds))) {
            log.info("CouponConfigRedisDao.get, 以下券配置ID在redis里未找到, noFindIds={}", noFindIds);
        }

        long runCostTime = TimeUtil.sinceMillis(runStartTime);
        if (runCostTime > CommonConstant.REDIS_TIME_OUT) {
            log.info("CouponConfigRedisDaoImpl.get, 获取券配置缓存时间比较长,runTime={}ms, configIds={}", runCostTime, configIds);
        }

        return result;
    }

    /**
     * 获取券批次PO全量信息
     *
     * @param configId
     * @return
     */
    @Override
    public CouponConfigPO getAllCouponInfo(Integer configId) {
        ValueOperations<String, String> operations = redisTemplate.opsForValue();

        List<String> keys = Lists.newArrayList();
        keys.add(getConfigInfoKey(configId));
        keys.add(getConfigGoodKey(configId));
        List<String> resultList = operations.multiGet(keys);

        if (CollectionUtils.isEmpty(resultList) || resultList.size() < 2) {
            log.error("CouponConfigRedisDao.getCouponConfigPo err, keys:{}", keys);
            return null;
        }

        if (Objects.isNull(resultList.get(0))) {
            log.error("CouponConfigRedisDao.getCouponConfigPo config info is null, keys:{}, data:{}", keys, resultList.get(0));
            return null;
        }

        CouponConfigPO couponConfigPO = GsonUtil.fromJson(resultList.get(0), CouponConfigPO.class);
        if (Objects.isNull(couponConfigPO)) {
            log.error("CouponConfigRedisDao.getCouponConfigPo fromJson err, keys:{}, data:{}", keys, resultList.get(0));
            return null;
        }

        couponConfigPO.setGoodsInclude(resultList.get(1));
        return couponConfigPO;

    }

    /**
     * 获取券批次PO基本信息
     *
     * @param configId
     * @return
     */
    @Override
    public CouponConfigPO getOnlyBaseInfo(Integer configId) {
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        String configInfo = operations.get(getConfigInfoKey(configId));
        return GsonUtil.fromJson(configInfo, CouponConfigPO.class);
    }

    /**
     * 获取券批次PO商品信息
     *
     * @param configId
     * @return
     */
    @Override
    public String getOnlyGoodInfo(Integer configId) {
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        String goodsInfo = operations.get(getConfigGoodKey(configId));
        return goodsInfo;
    }

    /**
     * 批量获取券批次PO基本信息
     *
     * @param configIds
     * @return
     */
    @Override
    public Map<Integer, CouponConfigPO> batchGetOnlyBaseInfo(List<Integer> configIds) {
        if (CollectionUtils.isEmpty(configIds)) {
            return MapUtils.EMPTY_MAP;
        }

        ValueOperations<String, String> operations = redisTemplate.opsForValue();

        List<String> keys = configIds.stream().map(this::getConfigInfoKey).collect(Collectors.toList());

        List<String> couponInfos = operations.multiGet(keys);

        Map<Integer, CouponConfigPO> configInfoCachePoMap = new HashMap<>(configIds.size());

        for (String couponInfo : couponInfos) {
            if (StringUtils.isEmpty(couponInfo)) {
                continue;
            }
            CouponConfigPO couponConfigPO = GsonUtil.fromJson(couponInfo, CouponConfigPO.class);
            if (!Objects.isNull(couponConfigPO.getId())) {
                configInfoCachePoMap.put(couponConfigPO.getId(), couponConfigPO);
            }
        }

        return configInfoCachePoMap;
    }


    /**
     * 解析券缓存基本信息
     *
     * @param keys List<String>
     * @param data List<String>
     * @return List<ConfigCacheItemPo>
     */
    private List<ConfigCacheItemPo> decodeBaseInfo(List<String> keys, List<String> data) {
        List<ConfigCacheItemPo> result = new ArrayList<>();
        if (data == null || data.size() == 0) {
            return result;
        }

        for (String resJson : data) {
            if (resJson == null || resJson.isEmpty()) {
                log.error("CouponConfigRedisDao.decodeBaseInfo, 从redis里取到的数据存在为空的情况, keys={}, resJson={}", keys, resJson);
                continue;
            }

            ConfigCacheItemPo info = GsonUtil.fromJson(resJson, ConfigCacheItemPo.class);
            if (info == null || info.getId() <= 0) {
                log.error("CouponConfigRedisDao.decodeBaseInfo, 从redis里取到的数据解析后发现有存在不符合要求的, keys={}, resJson={}", keys, resJson);
                continue;
            }
            result.add(info);
        }
        return result;
    }

    /**
     * 所有－有效的券配置ID列表读redis
     *
     * @return NoCodeConfigIdListCachePo
     */
    @Override
    public ConfigIdListCachePo getNoCodeValidConfigIdList() {
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        String resJson = operations.get(KEY_COUPON_NO_CODE_CONFIG_VALID_LIST_CACHE_ALL);

        if (resJson == null || resJson.isEmpty()) {
            return null;
        }

        ConfigIdListCachePo result = GsonUtil.fromJson(resJson, ConfigIdListCachePo.class);
        if (result == null || result.getConfigIds() == null) {
            log.error("CouponConfigRedisDao.getNoCodeValidConfigIdList, 从redis里取到的有效券配置ID列表解析后为空, resJson={}", resJson);
            return null;
        }
        return result;
    }

    /**
     * 券批次基本信息key拼接
     *
     * @param configId
     * @return
     */
    private String getConfigInfoKey(Integer configId) {
        return StringUtil.formatContent(KEY_COUPON_INFO_CACHE, String.valueOf(configId));
    }

    /**
     * 券批次商品key拼接
     *
     * @param configId
     * @return
     */
    private String getConfigGoodKey(Integer configId) {
        return StringUtil.formatContent(KEY_COUPON_NEW_GOODS_CACHE, String.valueOf(configId));
    }

}