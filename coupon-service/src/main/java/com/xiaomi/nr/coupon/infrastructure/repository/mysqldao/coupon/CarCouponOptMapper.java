package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponOptPo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * 汽车券操作记录写入
 *
 * <AUTHOR>
 */
@Mapper
@Repository
public interface CarCouponOptMapper {

    /**
     * 插入操作记录
     *
     * @param couponOptPo
     * @return
     */
    @Insert("insert into tb_car_coupon_opt " +
            " (id, vid, user_id, order_id, opt_type, add_time) " +
            " values " +
            " (#{couponOptPo.id},#{couponOptPo.vid},#{couponOptPo.userId},#{couponOptPo.orderId},#{couponOptPo.optType},#{couponOptPo.addTime})")
    Integer insert(@Param("couponOptPo") CouponOptPo couponOptPo);

    /**
     * 查询操作记录
     *
     * @param vid
     * @param orderId
     * @param opt
     * @return
     */
    @Select("select vid,user_id,order_id,opt_type " +
            " from tb_car_coupon_opt " +
            " where vid=#{vid} and order_id = #{orderId} and opt_type = #{opt} " +
            " for update")
    CouponOptPo getByCouponOpt(@Param("vid") String vid, @Param("orderId") long orderId, @Param("opt") long opt);
}