package com.xiaomi.nr.coupon.enums.couponconfig;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Objects;

/**
 * 优惠券类型 枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CouponTypeEnum {

    /**
     * 商品券
     */
    Goods(1, "商品券"),

    /**
     * 运费券
     */
    PostFee(2, "运费券"),

    /**
     * 超级补贴券
     */
    Subsidy(3, "超级补贴券"),

    /**
     * 11: 抵扣券
     */
    Deduction(11, "服务抵扣券"),

    /**
     * 12：不限次服务卡
     */
    ServiceCoupon(12, "不限次服务卡"),

    ;

    private final Integer value;
    private final String name;

    public Integer getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }



    private static final HashMap<Integer, CouponTypeEnum> MAPPING = new HashMap<>();

    static {
        for (CouponTypeEnum e : CouponTypeEnum.values()) {
            MAPPING.put(e.getValue(), e);
        }
    }

    public static CouponTypeEnum valueOf(Integer value) {
        if (Objects.isNull(value)) {
            return null;
        }
        return MAPPING.get(value);
    }

}

