package com.xiaomi.nr.coupon.infrastructure.repository.mq.model;

import lombok.Data;

/**
 * 优惠券灌券消息
 *
 * <AUTHOR>
 * @date 2024/12/13
 */
@Data
public class CouponAssignNotifyMsg {

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 券批次id
     */
    private Long configId;
    /**
     * 用户券id
     */
    private Long couponId;
    /**
     * 券状态：unused-未使用; used-已使用; expired-已过期; invalid-已作废
     */
    private String stat;
    /**
     * 开始使用时间戳（秒）
     */
    private Long startUseTime;
    /**
     * 结束使用时间戳（秒）
     */
    private Long endUseTime;
    /**
     * 发放时间戳（秒）
     */
    private Long assignTime;
    /**
     * 优惠类型：1-满减; 2-满折; 3-抵扣(N元购); 4-立减; 5-礼品券
     */
    private Integer promotionType;

}
