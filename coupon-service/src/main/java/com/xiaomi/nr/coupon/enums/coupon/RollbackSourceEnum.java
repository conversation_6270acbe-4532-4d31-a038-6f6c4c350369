package com.xiaomi.nr.coupon.enums.coupon;

public enum RollbackSourceEnum {
    /**
     * 订单回滚
     */
    ORDER(1),
    /**
     * 优惠定时补偿回滚
     */
    SCHEDULE(2);

    private int code;

    RollbackSourceEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static RollbackSourceEnum getByCode(int code) {
        for (RollbackSourceEnum rollbackSource : RollbackSourceEnum.values()) {
            if (rollbackSource.code == code) {
                return rollbackSource;
            }
        }
        return null;
    }

}
