package com.xiaomi.nr.coupon.infrastructure.repository;

import com.google.gson.reflect.TypeToken;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.couponconfig.model.*;
import com.xiaomi.nr.coupon.enums.couponconfig.*;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.po.CouponConfigPO;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 优惠券工厂
 * <AUTHOR>
 */
@Slf4j
@Component
public class CouponConfigItemFactory {

    //90天
    public static final int WEEK_TIMESTAMP = 7776000;

    /**
     * 创建优惠券领域实体工厂方法
     * @param couponConfig
     * @param onlyBaseInfo
     * @return
     */
    public CouponConfigItem buildCouponConfigItem(CouponConfigPO couponConfig, boolean onlyBaseInfo) {

        CouponConfigItem couponConfigItem = null;

        try {

            couponConfigItem = new CouponConfigItem();

            couponConfigItem.setConfigId(couponConfig.getId());

            couponConfigItem.setPromotionType(convertPromotionType(couponConfig.getPromotionType()));

            couponConfigItem.setDeductType(convertDeductType(couponConfig));

            couponConfigItem.setGoodsScopeType(convertGoodsScopeType(couponConfig.getScopeType()));

            //设置基本信息，领取信息，使用规则（不含适用商品信息）
            couponConfigItem.setCouponConfigInfo(convertCouponConfigInfo(couponConfig));

            couponConfigItem.setUseChannelStore(convertUseChannel(couponConfig));

            couponConfigItem.setAssignArea(convertAssignArea(couponConfig.getAreaIds()));

            couponConfigItem.setExtPropInfo(convertExtPropInfo(couponConfig.getExtProp()));

            couponConfigItem.setOnlyBaseInfo(onlyBaseInfo);

            couponConfigItem.setExpireTime(convertExpireTime(Math.max(couponConfig.getEndFetchTime(), couponConfig.getEndUseTime())));

            couponConfigItem.setCouponType(couponConfig.getCouponType());

            couponConfigItem.setBizPlatform(couponConfig.getBizPlatform());

            //设置适用商品相关信息
            if (!onlyBaseInfo) {
                couponConfigItem.setGoodScope(convertGoodScope(couponConfig));
            }

            couponConfigItem.setBudgetApplyNo(couponConfig.getBudgetApplyNo());

            couponConfigItem.setLineNum(couponConfig.getLineNum());

            couponConfigItem.setServiceType(couponConfig.getServiceType());

            couponConfigItem.setTimesLimit(couponConfig.getTimesLimit());

        } catch (Exception e) {
            log.error("CouponConfigItem build error. config:{}", couponConfig, e);
            return null;
        }

        return couponConfigItem;
    }

    /**
     * 基本信息转换
     * @param couponConfig
     * @return
     */
    private CouponConfigInfo convertCouponConfigInfo(CouponConfigPO couponConfig) {
        CouponConfigInfo couponConfigInfo = new CouponConfigInfo();
        BeanUtils.copyProperties(couponConfig, couponConfigInfo);
        couponConfigInfo.setServiceScene(couponConfig.getServiceType());
        if(couponConfig.getPromotionType() == PromotionType.DirectReduce.getCode()){
            couponConfigInfo.setBottomType(BottomTypeEnum.Count.getCode());
            couponConfigInfo.setBottomCount(1);
        }
        return couponConfigInfo;
    }

    /**
     * 填充商品范围类型
     *
     * @param scopeType
     */
    private GoodsScopeType convertGoodsScopeType(int scopeType) throws BizError {
        GoodsScopeType goodsScopeType = GoodsScopeType.getGoodsScopeType(scopeType);
        if (goodsScopeType == null) {
            throw ExceptionHelper.create(ErrCode.COUPON, "无法获取有效的券商品类型");
        }
        return goodsScopeType;
    }

    /**
     * 填充优惠类型
     *
     * @param type 优惠类型
     */
    private PromotionType convertPromotionType(int type) throws BizError {
        PromotionType promotionType = PromotionType.getPromotionType(type);
        if(promotionType == null) {
            throw ExceptionHelper.create(ErrCode.COUPON, "无法获取有效的券优惠类型");
        }
        return promotionType;
    }


    /**
     * n元券抵扣类型转换
     * @param couponConfig
     * @return
     */
    private DeductTypeEnum convertDeductType(CouponConfigPO couponConfig) {
        DeductTypeEnum deductType = DeductTypeEnum.OneCent;
        if(PromotionType.NyuanBuy.getCode() == couponConfig.getPromotionType() && couponConfig.getPromotionValue() <= 0) {
            deductType = DeductTypeEnum.Zero;
        }
        return deductType;
    }

    /**
     * 使用渠道转换
     * @param couponConfig
     * @return
     * @throws BizError
     */
    private Map<Integer, UseChannel> convertUseChannel(CouponConfigPO couponConfig) throws BizError {
        String useChannelStr = couponConfig.getUseChannel();
        if(StringUtils.isEmpty(useChannelStr) && !couponConfig.getBizPlatform().equals(BizPlatformEnum.CAR.getCode())) {
            throw ExceptionHelper.create(ErrCode.COUPON, "无法获取有效的使用渠道");
        }

        Map<Integer, UseChannel> useChannelMap = new HashMap<>();

        List<Integer> useChannelList = Arrays.stream(StringUtils.split(useChannelStr, ","))
                .filter(NumberUtils::isParsable)
                .map(Integer::parseInt)
                .collect(Collectors.toList());


        Map<String, UseChannel> usePlatformMap = GsonUtil.fromJson(couponConfig.getUsePlatform(), new TypeToken<Map<String, UseChannel>>(){}.getType());
        Map<String, UseChannel> useStorePOMap = GsonUtil.fromJson(couponConfig.getUseStore(), new TypeToken<Map<String, UseChannel>>(){}.getType());

        for (Integer useChannelId : useChannelList) {
            UseChannel useChannel;
            if(useChannelId == 1){
                useChannel = usePlatformMap.get(String.valueOf(useChannelId));
            } else {
                useChannel = useStorePOMap.get(String.valueOf(useChannelId));

            }
            useChannelMap.put(useChannelId, useChannel);
        }

        return useChannelMap;
    }

    /**
     * 区域限制转换
     * @param areaIds
     * @return
     */
    private Map<Integer, List<Integer>> convertAssignArea(String areaIds) {
        Map<Integer, List<Integer>> assignArea = new HashMap<>();
        if(StringUtils.isEmpty(areaIds)) {
            return MapUtils.EMPTY_MAP;
        }

        List<Integer> areaList = Arrays.stream(StringUtils.split(areaIds, ",")).map(Integer::parseInt).collect(Collectors.toList());

        assignArea.put(2, areaList);
        return assignArea;
    }

    /**
     * 可用商品转换
     * @param couponConfig
     * @return
     * @throws BizError
     */
    private GoodScope convertGoodScope(CouponConfigPO couponConfig) throws BizError {
        if(StringUtils.isEmpty(couponConfig.getGoodsInclude())){
            throw ExceptionHelper.create(ErrCode.COUPON, "无法获取有效的券可用商品");
        }
        GoodScope goodScope =  GsonUtil.fromJson(couponConfig.getGoodsInclude(), GoodScope.class);
        return goodScope;

    }

    /**
     * 扩展信息转换
     * @param extProp
     * @return
     */
    private ExtPropInfo convertExtPropInfo(String extProp){
        ExtPropInfo extPropInfo = new ExtPropInfo();
        if(StringUtils.isEmpty(extProp)) {
            return extPropInfo;
        }

        extPropInfo = GsonUtil.fromJson(extProp, ExtPropInfo.class);
        if(extPropInfo.getShare() == YesNoEnum.Undefined.getValue()){
            extPropInfo.setShare(YesNoEnum.No.getValue());
        }
        if(extPropInfo.getPostFree() == YesNoEnum.Undefined.getValue()){
            extPropInfo.setPostFree(YesNoEnum.No.getValue());
        }
        if(extPropInfo.getArea() == YesNoEnum.Undefined.getValue()){
            extPropInfo.setArea(YesNoEnum.No.getValue());
        }
        if (extPropInfo.getProMember() == IsProMemberEnum.Undefined.getMysqlValue()) {
            extPropInfo.setProMember(IsProMemberEnum.No.getMysqlValue());
        }
        if(extPropInfo.getSpecialStore() == YesNoEnum.Undefined.getValue()){
            extPropInfo.setSpecialStore(YesNoEnum.No.getValue());
        }
        if(extPropInfo.getCheckoutStage() == YesNoEnum.Undefined.getValue()){
            extPropInfo.setCheckoutStage(CheckoutStage.BOOKING.getValue());
        }

        return extPropInfo;
    }

    /**
     * 过期时间转换
     * @param endTime
     * @return
     */
    private Long convertExpireTime(Long endTime){
        return endTime - TimeUtil.getNowUnixSecond() + WEEK_TIMESTAMP;
    }
}
