package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.po;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 单个优惠券缓存信息（最新的）
 *
 * <AUTHOR>
 */
@Data
public class ConfigCacheItemPo implements Serializable {

    private static final long serialVersionUID = 5373483353589469297L;

    /**
     * 优惠券配置ID
     */
    private Long id;

    /**
     * 优惠券类型名称
     */
    private String name;

    /**
     * 券配置的有效开始时间
     */
    private Long globalStartTime;

    /**
     * 券配置的有效结束时间
     */
    private Long globalEndTime;

    /**
     * 优惠券状态 add：创建 approving：审核中 approved：审核通过 reject：已拒绝 cancel：已终止
     */
    private String status;

    /**
     * 　模式类型　code：有码券　no_code：无码券
     */
    private String modeType;

    /**
     * 使用类型 cash：现金券，discount：折扣券，deductible：抵扣券
     */
    private String useType;

    /**
     * 是否可分享　true：可分享 false：不可分享
     */
    private Boolean isShare;

    /**
     * 是否包邮 true：包邮 false：不包邮
     */
    private Boolean isPostFree;

    /**
     * 是否调价商品可用 true：是 false：否
     */
    private Boolean isCheckPrice;

    /**
     * 是否套装部分商品可用券 true：是 false：否
     */
    private Boolean isCheckPackage;

    /**
     * 可使用渠道类型 online：仅线上使用 offline：仅线下使用 online_offline：线上线下均可使用
     */
    private String useChannelType;

    /**
     * 使用范围说明
     */
    private String rangeDesc;

    /**
     * 优惠券发放渠道，store_manager：店长券，store_order_gift：下单赠券，空：其他渠道
     */
    private String sendChannel;

    /**
     * 优惠券配置的创建时间
     */
    private Long addTime;

    /**
     * 优惠券配置的变更时间
     */
    private Long modifyTime;


    /**
     * 配额类型 money：满元 count：满件 money_count：满元且满件 eve_money：每满元 eve_count：每满件
     */
    private String quotaType;

    /**
     * 满件配额（单位件）
     */
    private Long quotaCount;

    /**
     * 满元配额（单位分）
     */
    private Long quotaMoney;

    /**
     * 满减券的满减金额（单位分）
     */
    private Long reduceMoney;

    /**
     * 折扣券的折扣（9折就是90）
     */
    private Long reduceDiscount;

    /**
     * 折扣券的最大减免金额（单位分）
     */
    private Long reduceMaxPrice;

    /**
     * 抵扣券可抵扣的货品SKU
     */
    private List<ConfigCacheTargetGoodsItem> deductTargetGoods;

    /**
     * 抵扣类型　zero_cent：0元抵扣 one_cent：1分钱抵扣
     */
    private String deductType;


    /**
     * 可用的client_id列表
     */
    private List<String> clients;

    /**
     * 可用商品明细，已经将品类下的sku筛出来了
     */
    private ConfigCacheGoods goodsInclude;

    /**
     * 优惠券使用渠道（1～N个），mi_shop：小米商城，mi_home：小米之家，mi_authorized：授权店
     */
    private List<String> useChannel;


    /**
     * 优惠券缓存的创建时间
     */
    private String cacheCreateTime;

}
