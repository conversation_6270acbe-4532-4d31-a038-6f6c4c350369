package com.xiaomi.nr.coupon.util;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 数据/金额(换算)工具类
 */
@Slf4j
public class NumberUtil {

    /**
     * 金额单位转换(分转元)
     *
     * @param value        单位是分的金额
     * @param roundingMode 舍入模式 (RoundingMode.UP/RoundingMode.DOWN)
     * @return String      单位是元的金额
     */
    public static String centToYuan(long value, int scale, RoundingMode roundingMode){
        try{
            BigDecimal showValue = new BigDecimal(String.valueOf(value));
            return showValue.divide(new BigDecimal("100"), scale, roundingMode).stripTrailingZeros().toPlainString();
        }catch (Exception e){
            log.warn("coupon.NumberUtil.centToYuan(), Exception error:", e);
        }
        return null;
    }

    /**
     * 金额单位转换(分转元)，保留小数点后00
     *
     * @param value        单位是分的金额
     * @param roundingMode 舍入模式 (RoundingMode.UP/RoundingMode.DOWN)
     * @return String      单位是元的金额
     */
    public static String centToYuanV2(long value, int scale, RoundingMode roundingMode){
        try{
            BigDecimal showValue = new BigDecimal(String.valueOf(value));
            return showValue.divide(new BigDecimal("100"), scale, roundingMode).toPlainString();
        }catch (Exception e){
            log.warn("coupon.NumberUtil.centToYuan(), Exception error:", e);
        }
        return null;
    }


    /**
     * 金额单位转换(角转元)
     *
     * @param value        单位是角的金额
     * @param roundingMode 舍入模式 (RoundingMode.UP/RoundingMode.DOWN)
     * @return String      单位是元的金额
     */
    public static String tenCentToYuan(long value, int scale, RoundingMode roundingMode){
        try{
            BigDecimal showValue = new BigDecimal(String.valueOf(value));
            return showValue.divide(new BigDecimal("10"), scale, roundingMode).stripTrailingZeros().toPlainString();
        }catch (Exception e){
            log.warn("coupon.NumberUtil.tenCentToYuan(), Exception error:", e);
        }
        return null;
    }

    /**
     * 金额单位转换(元转分)
     *
     * @param value        单位是元的金额
     * @param roundingMode 舍入模式 (RoundingMode.UP/RoundingMode.DOWN)
     * @return String      单位是分的金额
     */
    public static String yuanToCent(String value, int newScale, RoundingMode roundingMode){
        try{
            BigDecimal showValue = new BigDecimal(value);
            return showValue.multiply(new BigDecimal("100")).setScale(newScale, roundingMode).stripTrailingZeros().toPlainString();
        }catch (Exception e){
            log.warn("coupon.NumberUtil.yuanToCent(), Exception error:", e);
        }

        return null;
    }


    /**
     * 金额单位转换(元转角)
     *
     * @param value        单位是元的金额
     * @param roundingMode 舍入模式 (RoundingMode.UP/RoundingMode.DOWN)
     * @return String      单位是角的金额
     */
    public static String yuanToTenCent(String value, int newScale, RoundingMode roundingMode){
        try{
            BigDecimal showValue = new BigDecimal(value);
            return showValue.multiply(new BigDecimal("10")).setScale(newScale, roundingMode).stripTrailingZeros().toPlainString();
        }catch (Exception e){
            log.warn("coupon.NumberUtil.yuanToCent(), Exception error:", e);
        }
        return null;
    }

    /**
     * 折扣单位转换
     * @param value
     * @param scale
     * @param roundingMode
     * @return
     */
    public static String convertDiscount(long value, int scale, RoundingMode roundingMode) {
        try{
            BigDecimal showValue = new BigDecimal(String.valueOf(value));
            return showValue.divide(new BigDecimal("100"), scale, roundingMode).toPlainString();
        }catch (Exception e){
            log.warn("coupon.NumberUtil.centToYuan(), Exception error:", e);
        }
        return null;
    }

    /**
     * 所有数据进行汇总Long
     *
     * @param nums 数据集, 遇到NULL转为0
     * @return 汇总后
     */
    public static long sum(Long... nums) {
        if (nums == null || nums.length == 0) {
            return 0L;
        }
        long total = 0L;
        for (Long num : nums) {
            total += (num == null ? 0L : num);
        }
        return total;
    }

    /**
     * 所有数据进行汇总Integer
     *
     * @param nums 数据集, 遇到NULL转为0
     * @return 汇总后
     */
    public static int sum(Integer... nums) {
        if (nums == null || nums.length == 0) {
            return 0;
        }
        int total = 0;
        for (Integer num : nums) {
            total += (num == null ? 0 : num);
        }
        return total;
    }

    /**
     * 是否在范围内
     *
     * @param target 目标数
     * @param begin  开始
     * @param end    结束
     * @return 左闭右闭
     */
    public static boolean inRange(Long target, long begin, long end) {
        if (target < begin) {
            return false;
        }
        if (target > end) {
            return false;
        }
        return true;
    }

    public static boolean isPositive(Long num) {
        return num != null && num > 0;
    }

    public static boolean isNegative(Long num) {
        return !isPositive(num);
    }

    public static boolean isPositive(Integer num) {
        return num != null && num > 0;
    }

    public static boolean isNegative(Integer num) {
        return !isPositive(num);
    }


}
