package com.xiaomi.nr.coupon.enums.couponconfig;

/**
 * 优惠券发放方式 枚举
 *
 * <AUTHOR>
 */
public enum SceneAssignModeEnum {

    /**
     * 接口发放
     */
    Interface(1,"接口发放"),

    /**
     * 灌券
     */
    Fill(2,"灌券");

    private final Integer value;
    private final String name;

    SceneAssignModeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static String findNameByValue(Integer value) {
        SceneAssignModeEnum[] values = SceneAssignModeEnum.values();
        for (SceneAssignModeEnum item : values) {
            if (item.getValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }
}

