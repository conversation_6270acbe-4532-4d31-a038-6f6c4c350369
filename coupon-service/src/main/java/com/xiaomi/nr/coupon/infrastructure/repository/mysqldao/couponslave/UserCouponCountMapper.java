package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户优惠券mapper
 *
 * <AUTHOR>
 */
@Mapper
@Repository
public interface UserCouponCountMapper {

    /**
     * 查用户可用券总数
     *
     * @param userId  long
     * @param bizPlatformList 业务领域
     * @param nowTime long
     * @return Integer
     */
    @Select("<script>" +
            "select count(1) " +
            " from tb_coupon " +
            " where user_id=#{userId} and stat='unused' and end_time &gt; #{nowTime} " +
            " and biz_platform in <foreach collection='bizPlatformList' item='bizPlatform' index='index' open='(' close=')' separator=','>#{bizPlatform}</foreach>" +
            "</script>")
    Integer getValidCount(@Param("userId") long userId, @Param("bizPlatformList") List<Integer> bizPlatformList, @Param("nowTime") long nowTime);

    /**
     * 查用户可用券总数
     *
     * @param userId  long
     * @param bizPlatformList 业务领域
     * @param nowTime long
     * @return Integer
     */
    @Select("<script>" +
            "select id, type_id " +
            " from tb_coupon " +
            " where user_id=#{userId} and stat='unused' and end_time &gt; #{nowTime} " +
            " and biz_platform in <foreach collection='bizPlatformList' item='bizPlatform' index='index' open='(' close=')' separator=','>#{bizPlatform}</foreach>" +
            "</script>")
    List<CouponPo> getValidCouponIds(@Param("userId") long userId, @Param("bizPlatformList") List<Integer> bizPlatformList, @Param("nowTime") long nowTime);
}
