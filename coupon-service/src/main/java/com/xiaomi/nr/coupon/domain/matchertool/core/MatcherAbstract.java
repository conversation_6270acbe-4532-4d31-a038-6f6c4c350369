package com.xiaomi.nr.coupon.domain.matchertool.core;

import com.alibaba.nacos.common.utils.StringUtils;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherBaseReqDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Objects;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * MatcherAbstract
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Slf4j
public abstract class MatcherAbstract extends MatcherBaseMethod implements MatcherInterface, MatcherToolInterface {

    /**
     * 主执行方法
     *
     * @param request MatcherBaseReqDo
     * @return MatcherContextDo
     * @throws BizError .
     */
    @Override
    public MatcherContextDo execute(MatcherBaseReqDo request) throws BizError {
        MatcherContextDo ctx = newContext(request);
        initResource(ctx);
        matching(ctx);
        filter(ctx);
        if (ctx.isSort()) {
            noCodeSort(ctx.getKeyResp(), ctx.getConfigInfoMap(), ctx.getUserCouponMap());
        }
        return ctx;
    }

    @Override
    public void initResource(MatcherContextDo ctx) throws BizError {

        // 业务领域独有逻辑
        specialInitResource(ctx);

        // 公共逻辑
        commonInitResource(ctx);
    }

    /**
     * 公共初始化资源
     *
     * @param ctx
     * @throws BizError
     */
    protected void commonInitResource(MatcherContextDo ctx) throws BizError {

        if (Objects.nonNull(ctx.getUserId()) && ctx.getUserId() <= 0) {
            ctx.setUserId(null);
        }

        if (CollectionUtils.isNotEmpty(ctx.getOuterGoodsList())) {
            ctx.setOuterGoodsList(ctx.getOuterGoodsList().stream().
                    filter(e -> Objects.nonNull(e) && Objects.nonNull(e.getId()) && Objects.nonNull(e.getLevel()))
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(e -> e.getId() + "-" + e.getLevel()))), ArrayList::new)));
        }

        if (ctx.getNowUnixSecond() <= 0) {
            ctx.setNowUnixSecond(TimeUtil.getNowUnixSecond());
        }

        // 3C领域Fetchable和Usable都需要，由入参决定，暂时放在这里
        if (StringUtils.isNotEmpty(ctx.getOrgCode()) && StringUtils.isNotBlank(ctx.getOrgCode())) {
            ctx.setOrgInfo(getOrgInfo(ctx.getOrgCode()));
        } else {
            ctx.setOrgCode(null);
        }

        if (ctx.isCheckGlobalExcludeGoods()) {
            ctx.setGlobalExcludeGoods(getGlobalInExclude());
        }

        if (ctx.isCheckGlobalCouponExcludeGoods()) {
            ctx.setGlobalCouponExcludeGoods(getGlobalCouponInExclude());
        }
    }

    /**
     * 业务领域初始化资源
     *
     * @param ctx           ctx
     * @throws BizError     bizError
     */
    protected abstract void specialInitResource(MatcherContextDo ctx) throws BizError;

    @Override
    public void filter(MatcherContextDo ctx) throws BizError {
        if (CollectionUtils.isEmpty(ctx.getKeyResp())) {
            return;
        }
        ctx.setKeyResp(ctx.getKeyResp().stream().filter(e -> Objects.isNull(e.getErrCtx())).collect(Collectors.toList()));
    }

}
