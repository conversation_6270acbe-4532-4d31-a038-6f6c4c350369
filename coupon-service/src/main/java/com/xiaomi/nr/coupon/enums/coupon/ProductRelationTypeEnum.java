package com.xiaomi.nr.coupon.enums.coupon;

import org.apache.commons.lang3.StringUtils;

/**
 * 产品站券关系类型
 *
 * <AUTHOR>
 * @date 2023/9/4
 */
public enum ProductRelationTypeEnum {

    /**
     * 可领券
     * */
    FETCHABLE("FETCHABLE","可领券"),

    /**
     * 用户已领券
     * */
    FETCHED("FETCHED","用户已领券"),

    ;

    private final String value;
    private final String name;

    ProductRelationTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static boolean findByValue(String value) {
        ProductRelationTypeEnum[] values = ProductRelationTypeEnum.values();
        for (ProductRelationTypeEnum item : values) {
            if (StringUtils.equals(item.getValue(),value)) {
                return true;
            }
        }
        return false;
    }
}
