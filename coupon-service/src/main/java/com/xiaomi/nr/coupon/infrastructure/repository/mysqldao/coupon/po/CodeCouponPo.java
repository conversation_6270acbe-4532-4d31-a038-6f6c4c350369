package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po;

import lombok.Data;

import java.util.Date;

/**
 * 有码优惠券
 *
 */
@Data
public class CodeCouponPo {
    /**
     * 优惠券编号
     */
    private Long id;

    /**
     * 加密code
     */
    private String couponCode;

    /**
     * 索引，code的md5
     */
    private String couponIndex;

    /**
     * 优惠券类型编号
     */
    private Long typeId;

    /**
     * 活动编号
     */
    private String batchId;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 过期时间
     */
    private Long endTime;

    /**
     * 状态: 线上locked 线下 used/unused
     */
    private String stat;

    /**
     * 是否可重复使用
     */
    private Integer multiplex;

    /**
     * 初始总次数
     */
    private Long totalCount;

    /**
     * 初始总金额
     */
    private Long totalMoney;

    /**
     * 实际抵用金额
     */
    private Long replaceMoney;

    /**
     * 实际减免邮费
     */
    private Long reduceExpress;

    /**
     * 剩余次数
     */
    private Long residueCount;

    /**
     * 剩余金额
     */
    private Long residueMoney;

    /**
     * 线下使用
     */
    private Integer offline;

    /**
     * 订单编号
     */
    private Long orderId;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 使用时间
     */
    private Long useTime;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 发送类型,
     */
    private String sendType;

    /**
     * 发券的订单编号
     */
    private String fromOrderId;

    /**
     * 作废时间
     */
    private Long invalidTime;

    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;
}
