package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.impl;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.GoodCouponsRedisDao;
import com.xiaomi.nr.coupon.util.StringUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: hejiapeng
 * @Date 2022/3/6 1:16 下午
 * @Version: 1.0
 **/
@Component
@Slf4j
public class GoodCouponsRedisDaoImpl implements GoodCouponsRedisDao {

    /**
     * 商品可用券关系缓存key
     */
    private static final String GOODS_COUPON_CONFIG_KEY = "nr:coupon:reverse:{level}_{id}";


    @Autowired
    @Qualifier("stringNewCouponRedisTemplate")
    private RedisTemplate<String,String> stringRedis;


    /**
     * 获取倒排
     *
     * @param goods
     * @param level
     * @return
     * @throws BizError
     */
    @Override
    public List<String> get(List<Long> goods, String level) throws BizError {
        try {
            if (CollectionUtils.isEmpty(goods)) {
                return Lists.newArrayList();
            }
            // 批量查询
            List<String> keyList = goods.stream().map(good -> getKey(good, level)).collect(Collectors.toList());
            ValueOperations<String, String> redisOps = stringRedis.opsForValue();
            return redisOps.multiGet(keyList);
        } catch (Exception e) {
            log.error("GoodCouponsRedisDao.get error. goods:{}, level:{}, err:{}", goods, level, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "获取倒排失败");
        }
    }

    /**
     * 获取key
     *
     * @param good
     * @param level
     * @return
     */
    private String getKey(Long good, String level) {
        return StringUtil.formatContent(GOODS_COUPON_CONFIG_KEY, level, String.valueOf(good));

    }

}
