package com.xiaomi.nr.coupon.constant;

import com.xiaomi.nr.coupon.enums.ecard.EcardStatEnum;


import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 礼品卡常量
 *
 * <AUTHOR>
 */
public class EcardConstant {

    /**
     * 一次最多查询多少张卡
     */
    public static final int GET_ECARD_BY_CARDIDS_MAX_SIZE = 100;

    /**
     * 标准换新现金券发放的最小金额
     * */
    public static final BigDecimal ASSIGN_ECARD_MONEY_MIN = new BigDecimal("0");

    /**
     * 标准换新现金券发放的最大金额
     * */
    public static final BigDecimal ASSIGN_ECARD_MONEY_MAX = new BigDecimal("10000");

    /**
     * SnLen 规则：[sku]/[9位随机数]
     */
    public static final int SN_LEN = 9;

    /**
     * CardIDLen id 规则：[1/2][4位面额/10][9位随机数]
     */
    public static final int CARDID_LEN = 9;

    /**
     * PasswordLen 规则：20位随机数字
     */
    public static final int PASSWORD_LEN = 20;

    /**
     * 发放标准现金券日志信息
     */
    public final static String ASSIGN_LOG_DESC = String.format("[%s]->[%s] 成功", "开卡", "绑定");

    /**
     * 作废标准现金券日志信息
     */
    public final static String CANCEL_LOG_DESC = String.format("[%s]->[%s] 成功", "绑定", "绑定作废");

    /**
     * 标准现金券，下发短信的 TemplateId
     */
    public final static String TEMPLATE_ID = "CI103064_100684";

    /**
     * 券类型redis key
     */
    public final static String ECARD_TYPE_KEY = "shopapi_pulsev3_ecard_type_worker";

    /**
     * 现金券生成任务ID默认是0
     */
    public final static String MISSION_ID = "0";

    /**
     * 随机数生成数字集
     *
     * @return int[]
     */
    public static int[] randNum() {
        return new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    }

    /**
     * 获取时间map
     *
     * @return Map<Integer, String>
     */
    public static Map<Integer, String> getTimeMap() {
        Map<Integer, String> timeMap = new HashMap<>();
        timeMap.put(EcardStatEnum.CreateCancel.getValue(), "invalid_time");
        timeMap.put(EcardStatEnum.ActiveCancel.getValue(), "invalid_time");
        timeMap.put(EcardStatEnum.BindCancel.getValue(), "invalid_time");
        timeMap.put(EcardStatEnum.Bind.getValue(), "bind_time");
        timeMap.put(EcardStatEnum.Active.getValue(), "active_time");
        return timeMap;
    }
}
