package com.xiaomi.nr.coupon.domain.coupon;

import com.xiaomi.nr.coupon.api.dto.coupon.AssignCouponItemDto;
import com.xiaomi.nr.coupon.api.dto.coupon.AssignMissionItemDto;
import com.xiaomi.nr.coupon.api.dto.coupon.AssignRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.AssignResponse;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.constant.CouponConstant;
import com.xiaomi.nr.coupon.domain.common.Common;
import com.xiaomi.nr.coupon.domain.couponassign.AssignCommon;
import com.xiaomi.nr.coupon.domain.couponassign.SingleAssigner;
import com.xiaomi.nr.coupon.domain.couponassign.model.SingleAssignRequestDo;
import com.xiaomi.nr.coupon.domain.couponassign.model.SingleAssignResponseDo;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.enums.coupon.CouponSendTypeEnum;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.*;
import com.xiaomi.nr.coupon.enums.couponmission.MissionStatusEnum;
import com.xiaomi.nr.coupon.enums.couponmission.MissionTypeEnum;
import com.xiaomi.nr.coupon.enums.couponmission.MissionVersionEnum;
import com.xiaomi.nr.coupon.enums.couponmission.TimeTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CouponMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponLogPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.ExtendInfo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.MissionMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponassign.CouponAssignRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.CouponConfigRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.po.ConfigCacheItemPo;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.MissionCacheDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.po.MissionCacheItemPo;
import com.xiaomi.nr.coupon.infrastructure.rpc.sid.SidProxy;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 发放优惠券
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Deprecated
public class Assign {

    @Resource
    private CouponConfigRedisDao couponConfigRedisDao;

    @Resource
    private MissionCacheDao missionCacheDao;

    @Resource
    private SidProxy sidProxy;

    @Resource
    private CouponMapper couponMapper;

    @Resource
    private MissionMapper missionMapper;

    @Resource
    private AssignTransaction assignTransaction;

    @Resource
    private Common common;

    @Resource
    private AssignCommon assignCommon;

    @Resource
    private SingleAssigner singleAssigner;

    @Resource
    private CouponAssignRedisDao assignRedisDao;


    /**
     * 一次最多发放1张
     */
    private final static int ASSIGN_MAX_COUNT = 1;

    /**
     * 接口发放
     *
     * @param request AssignRequest
     * @return AssignResponse
     * @throws BizError .
     */
    public AssignResponse send(AssignRequest request) throws BizError {
        //参数检查
        paramsChecker(request);

        //获取发放任务ID
        List<Long> missionIds = getMissionIds(request.getItems());

        //获取发放任务缓存信息
        Map<Long, MissionCacheItemPo> missionInfos = getMissionInfos(missionIds);
        if(missionInfos.isEmpty() || MissionVersionEnum.QIAN_KUN.getValue().equals(missionInfos.get(missionIds.get(0)).getVersion())) {
            Long configId = missionMapper.getConfigIdByMissionId(missionIds.get(0));
            if(configId == null || configId <= 0) {
                throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_NOT_FOUND, String.format("优惠券任务处于不可发放的状态==>任务[%s]", missionIds.get(0)));
            }

            //获取券配置信息
            CouponConfigItem configInfo = assignCommon.getConfigInfo(configId);
            if(configInfo == null || configInfo.getCouponConfigInfo() == null || configInfo.getCouponConfigInfo().getSendScene() == null || configInfo.getCouponConfigInfo().getSendScene().isEmpty()){
                throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_NOT_FOUND, String.format("优惠券处于不可发放的状态。==>券配置ID[%s]", configId));
            }

            //业务领域校验
            if (!BizPlatformEnum.RETAIL.getCode().equals(configInfo.getBizPlatform())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, String.format("业务领域不符合要求==>券配置ID[%s]", configId));
            }

            SingleAssignRequestDo reqDo = new SingleAssignRequestDo();
            reqDo.setAppId(request.getAppId());
            reqDo.setRequestId(request.getRequestId());
            reqDo.setConfigId(configId);
            reqDo.setUserId(request.getUserId());
            reqDo.setAssignMode(SceneAssignModeEnum.Interface.getValue());
            reqDo.setOrderId(request.getItems().get(0).getOrderId());
            reqDo.setOrgCode(request.getOrgCode());
            reqDo.setSceneCode(configInfo.getCouponConfigInfo().getSendScene());
            reqDo.setShareUserId(request.getShareUserId());
            reqDo.setActivityId(String.valueOf(configId));
            reqDo.setBizPlatform(request.getBizPlatform());

            SingleAssignResponseDo respDto = singleAssigner.assign(reqDo);
            return makeReturnDataForNew(respDto, missionIds.get(0), reqDo.getOrderId());
        }

        //获取券配置ID
        List<Long> configIds = getConfigIds(missionInfos);

        //获取发放任务缓存信息
        Map<Long, ConfigCacheItemPo> configInfos = getConfigInfos(configIds);

        //条件检查
        conditionChecker(request, configInfos, missionInfos);

        //从库里取幂等数据
        List<CouponPo> idemData = getIdemData(request);

        //判断是否存在幂等数据，如果幂等则直接组织数据并返回
        if (idemData != null && !idemData.isEmpty()) {
            return makeReturnData(idemData, true);
        }

        //取券ID
        List<Long> couponIds = getCouponIds(request.getUserId(), request.getItems().size());

        //构建券落库数据
        List<CouponPo> coupons = makeCouponData(request, configInfos, missionInfos, couponIds);

        //构建券日志落库数据
        List<CouponLogPo> couponLogs = makeCouponLogData(coupons);

        //落库
        core(request, missionInfos, coupons, couponLogs);

        //构建返回数据
        return makeReturnData(coupons, false);
    }

    /**
     * 从新方法结果构建返回数据
     *
     * @param data   SingleAssignResponseDo
     * @param missionId Long
     * @param orderId Long
     * @return List<CouponPo>
     */
    private AssignResponse makeReturnDataForNew(SingleAssignResponseDo data, Long missionId, Long orderId) {
        List<AssignCouponItemDto> items = new ArrayList<>();
        AssignCouponItemDto item = new AssignCouponItemDto();
        item.setCouponId(data.getCouponId());
        item.setMissionId(missionId);
        item.setOrderId(orderId);
        items.add(item);

        AssignResponse result = new AssignResponse();
        result.setIsIdempotent(data.getIsIdempotent());
        result.setItems(items);
        return result;
    }

    /**
     * 老版本的已发放量减1
     *
     * @param missionInfos Map<Long, MissionCacheItemPo>
     * @throws BizError .
     */
    private void decrOldSendCount(AssignRequest request, Map<Long, MissionCacheItemPo> missionInfos) throws BizError {
        List<Long> missionIds = new ArrayList<>();
        for (AssignMissionItemDto ri : request.getItems()) {
            MissionCacheItemPo mission = missionInfos.get(ri.getMissionId());
            if (mission == null) {
                throw ExceptionHelper.create(ErrCode.COUPON, String.format("无效的任务信息。==>任务[%s]", ri.getMissionId()));
            }
            missionIds.add(ri.getMissionId());
        }
        missionCacheDao.decrOldSendCount(missionIds);
    }

    /**
     * 已发放量减1
     *
     * @param missionInfos Map<Long, MissionCacheItemPo>
     * @throws BizError .
     */
    private void decrSendCount(AssignRequest request, Map<Long, MissionCacheItemPo> missionInfos) throws BizError {
        List<Long> missionIds = new ArrayList<>();
        for (AssignMissionItemDto ri : request.getItems()) {
            MissionCacheItemPo mission = missionInfos.get(ri.getMissionId());
            if (mission == null) {
                throw ExceptionHelper.create(ErrCode.COUPON, String.format("无效的任务信息。==>任务[%s]", ri.getMissionId()));
            }
            missionIds.add(ri.getMissionId());
        }
        missionCacheDao.decrSendCount(missionIds);
    }

    /**
     * 发放核心
     *
     * @param request      AssignRequest
     * @param missionInfos Map<Long, MissionCacheItemPo>
     * @param coupons      List<CouponPo>
     * @param couponLogs   List<CouponLogPo>
     * @throws BizError .
     */
    private void core(AssignRequest request, Map<Long, MissionCacheItemPo> missionInfos, List<CouponPo> coupons, List<CouponLogPo> couponLogs) throws BizError {
        //老版本计数是否成功
        boolean isOldIncr = false;

        //新版本计数是否成功
        boolean isIncr = false;

        //最新版本计数是否成功
        boolean isNewIncr = false;

        //获取券配置信息
        CouponConfigItem configInfo = assignCommon.getConfigInfo(coupons.get(0).getTypeId());
        if(configInfo == null || configInfo.getCouponConfigInfo() == null || configInfo.getCouponConfigInfo().getApplyCount() == null) {
            throw ExceptionHelper.create(ErrCode.COUPON, String.format("优惠券处于不可发放的状态。==>券配置ID[%s]", coupons.get(0).getTypeId()));
        }

        try {
            List<Long> missionIds = new ArrayList<>();
            List<Long> maxNums = new ArrayList<>();
            for (AssignMissionItemDto ri : request.getItems()) {
                MissionCacheItemPo mission = missionInfos.get(ri.getMissionId());
                if (mission == null) {
                    throw ExceptionHelper.create(ErrCode.COUPON, String.format("无效的任务信息！==>任务[%s]", ri.getMissionId()));
                }
                missionIds.add(ri.getMissionId());
                maxNums.add(mission.getMaxSendNum());
            }

            //最新版的
            assignRedisDao.incrCouponAssignCount(request.getUserId(), configInfo.getConfigId(), configInfo.getCouponConfigInfo().getApplyCount());
            isNewIncr = true;

            //已发放量加1并校验是否超过最大发放数量
            //如是是要兼容老版本计数的渠道，则老发放接口里计数还需要加上（不考虑失败回退计数），不然两边一起发的话有发超的风险
            if (common.isCompatibleOldSendChannel(request.getSendChannel())) {
                missionCacheDao.incrOldSendCountAndCheck(missionIds, maxNums);
                isOldIncr = true;
            }

            //新版的
            missionCacheDao.incrSendCountAndCheck(missionIds, maxNums);
            isIncr = true;

            assignTransaction.insertDb(coupons, couponLogs);
        } catch (Exception e) {
            if (isNewIncr) {
                assignRedisDao.decrCouponAssignCount(request.getUserId(), configInfo.getConfigId());
            }
            if (isOldIncr) {
                decrOldSendCount(request, missionInfos);
            }
            if (isIncr) {
                decrSendCount(request, missionInfos);
            }
            if (e instanceof BizError) {
                throw e;
            }
            log.error("assign.coupon, 遇到异常，优惠券发放失败, userId={}, requestId={}", request.getUserId(), request.getRequestId(), e);
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠券发放失败");
        }
    }

    /**
     * 构建返回数据
     *
     * @param data   List<CouponDo>
     * @param isIdem Boolean
     * @return List<CouponPo>
     */
    private AssignResponse makeReturnData(List<CouponPo> data, Boolean isIdem) {
        List<AssignCouponItemDto> items = new ArrayList<>();
        for (CouponPo v : data) {
            long missionId;
            try {
                missionId = Long.parseLong(v.getActivityId());
            } catch (Exception e) {
                log.error("assign.coupon, 用户优惠券表里的任务ID转成Long失败, userId={}, requestId={}, missionId={}", v.getUserId(), v.getRequestId(), v.getActivityId());
                continue;
            }
            AssignCouponItemDto item = new AssignCouponItemDto();
            item.setCouponId(v.getId());
            item.setMissionId(missionId);
            item.setOrderId(v.getOrderId());
            items.add(item);
        }

        AssignResponse result = new AssignResponse();
        result.setIsIdempotent(isIdem);
        result.setItems(items);
        return result;
    }

    /**
     * 获取幂等数据
     *
     * @param request AssignRequest
     * @return List<CouponPo>
     */
    private List<CouponPo> getIdemData(AssignRequest request) {
        return couponMapper.getIdemData(request.getUserId(), request.getSendChannel(), request.getRequestId(), request.getItems().get(0).getMissionId());
    }

    /**
     * 构建优惠券表数据
     *
     * @param request      AssignRequest
     * @param configInfos  Map<Long, ConfigCacheItemPo>
     * @param missionInfos Map<Long, MissionCacheItemPo>
     * @param couponIds    List<Long>
     * @return List<CouponPo>
     * @throws BizError .
     */
    private List<CouponPo> makeCouponData(AssignRequest request, Map<Long, ConfigCacheItemPo> configInfos, Map<Long, MissionCacheItemPo> missionInfos, List<Long> couponIds) throws BizError {
        List<CouponPo> result = new ArrayList<>();
        long nowTime = TimeUtil.getNowUnixSecond();

        int i = 0;
        for (AssignMissionItemDto ri : request.getItems()) {
            CouponPo coupon = new CouponPo();
            coupon.setId(couponIds.get(i));
            coupon.setUserId(request.getUserId());
            coupon.setSendChannel(request.getSendChannel());
            coupon.setRequestId(request.getRequestId());

            //mission
            for (Map.Entry<Long, MissionCacheItemPo> item : missionInfos.entrySet()) {
                MissionCacheItemPo v = item.getValue();
                if (!v.getId().equals(ri.getMissionId())) {
                    continue;
                }
                Long fromOrderId = ri.getOrderId();
                if (fromOrderId == null) {
                    fromOrderId = 0L;
                }
                coupon.setTypeId(v.getCouponConfigId());
                coupon.setActivityId(String.valueOf(ri.getMissionId()));
                coupon.setStartTime(String.valueOf(v.getCouponStartTime()));
                coupon.setEndTime(String.valueOf(v.getCouponEndTime()));
                coupon.setAdminId(v.getAdminId());
                coupon.setAdminName(v.getAdminName());
                coupon.setFromOrderId(String.valueOf(fromOrderId));
            }

            //config
            for (Map.Entry<Long, ConfigCacheItemPo> item : configInfos.entrySet()) {
                ConfigCacheItemPo v = item.getValue();
                if (!v.getId().equals(coupon.getTypeId())) {
                    continue;
                }

                String useChannelTypeStr = UseChannelType.findMysqlValueByRedisValue(v.getUseChannelType());
                if (useChannelTypeStr == null) {
                    throw ExceptionHelper.create(ErrCode.COUPON, String.format("可用渠道类型超出已知范围==>券配置[%s]，可使用渠道类型[%s]", coupon.getActivityId(), v.getUseChannelType()));
                }

                int useChannelType;
                try {
                    useChannelType = Integer.parseInt(useChannelTypeStr);
                } catch (NumberFormatException e) {
                    throw ExceptionHelper.create(ErrCode.COUPON, String.format("可用渠道类型有误==>券配置[%s]，可使用渠道类型[%s]", coupon.getActivityId(), v.getUseChannelType()));
                }
                coupon.setOffline(useChannelType);
                coupon.setSendType(CouponSendTypeEnum.EXTERNAL.getValue());
            }

            //default
            coupon.setStat(CouponStatusEnum.UNUSED.getValue());
            coupon.setDays(0);
            coupon.setOrderId(0L);
            coupon.setUseTime(0L);
            coupon.setExpireTime(0L);
            coupon.setIsPass(1);
            coupon.setAddTime(nowTime);
            coupon.setReplaceMoney(BigDecimal.valueOf(0));
            coupon.setInvalidTime(0L);
            coupon.setReduceExpress(BigDecimal.valueOf(0));
            coupon.setParentId(0L);

            //extend
            ExtendInfo extend = new ExtendInfo();
            if (request.getOrgCode() != null && !"".equals(request.getOrgCode())) {
                extend.setOrgCode(request.getOrgCode());
            }
            if (request.getShareUserId() != null && request.getShareUserId() > 0L) {
                extend.setShareUserId(request.getShareUserId());
            }
            extend.setAppId(request.getAppId());
            coupon.setExtendInfo(GsonUtil.toJson(extend));

            result.add(coupon);
            i++;
        }

        return result;
    }

    /**
     * 构建优惠券日志表数据
     *
     * @param coupons List<CouponPo>
     * @return List<CouponLogPo>
     */
    private List<CouponLogPo> makeCouponLogData(List<CouponPo> coupons) {
        List<CouponLogPo> result = new ArrayList<>();
        for (CouponPo coupon : coupons) {
            CouponLogPo couponLog = new CouponLogPo();
            couponLog.setCouponId(coupon.getId());
            couponLog.setUserId(coupon.getUserId());
            couponLog.setType(String.valueOf(coupon.getTypeId()));
            couponLog.setOldStat(CouponConstant.STATUS_SEND);
            couponLog.setNewStat(CouponStatusEnum.UNUSED.getValue());
            couponLog.setAdminId(coupon.getAdminId());
            couponLog.setAdminName("外部人员(" + coupon.getAdminName() + ")");
            couponLog.setOffline(coupon.getOffline());
            couponLog.setCouponDesc("外部接口调用");
            couponLog.setAddTime(coupon.getAddTime());
            result.add(couponLog);
        }
        return result;
    }

    /**
     * 获取优惠券ID
     *
     * @param count int
     * @return List<Long>
     * @throws BizError .
     */
    private List<Long> getCouponIds(Long userId, Integer count) throws BizError {
        //调sid服务批量生成优惠ID
        List<Long> couponIds;
        try {
            couponIds = sidProxy.get(userId, count);
        } catch (Exception e) {
            log.error("assign.coupon, 获取优惠券ID失败, userId={}, num={}, err={}", userId, count, e.getMessage());
            throw ExceptionHelper.create(ErrCode.COUPON, "无法连接SID服务");
        }
        return couponIds;
    }

    /**
     * 发放条件校验
     *
     * @param request      AssignRequest
     * @param configInfos  Map<Long, ConfigCacheItemPo>
     * @param missionInfos Map<Long, MissionCacheItemPo>
     * @throws BizError .
     */
    private void conditionChecker(AssignRequest request, Map<Long, ConfigCacheItemPo> configInfos, Map<Long, MissionCacheItemPo> missionInfos) throws BizError {
        List<Long> missionIds = new ArrayList<>();
        for (AssignMissionItemDto ri : request.getItems()) {
            missionIds.add(ri.getMissionId());

            //任务信息
            MissionCacheItemPo mission = missionInfos.get(ri.getMissionId());
            if (mission == null) {
                throw ExceptionHelper.create(ErrCode.COUPON, String.format("无效的优惠券发放任务==>任务[%s]", ri.getMissionId()));
            }

            //配置信息
            ConfigCacheItemPo config = configInfos.get(mission.getCouponConfigId());
            if (config == null) {
                throw ExceptionHelper.create(ErrCode.COUPON, String.format("无效的优惠券配置==>任务[%s]-券配置[%s]", ri.getMissionId(), mission.getCouponConfigId()));
            }

            //发放权限检查
            rightsChecker(request.getAppId(), request.getSendChannel(), mission);

            //投放渠道检查
            sendChannelChecker(request.getSendChannel(), config);

            //券模式类型检查
            modeTypeChecker(config);

            //状态检查
            statusChecker(config, mission);

            //任务发放类型检查
            missionTypeChecker(mission);

            //时间检查
            timeChecker(config, mission);
        }

        //数量检查
        countChecker(missionIds, missionInfos);
    }

    /**
     * 任务投放渠道检查
     *
     * @param sendChannel String
     * @param config      ConfigCacheItemPo
     * @throws BizError .
     */
    private void sendChannelChecker(String sendChannel, ConfigCacheItemPo config) throws BizError {
        //某些渠道只能发其他渠道投放的券，如果某个券是指定渠道的，则不能发放（以后会统一成为：只能发放对应投放渠道的券，不能跨渠道发）
        if (common.isTransOtherSendChannel(sendChannel)) {
            sendChannel = SendChannelEnum.Others.getRedisValue();
        }

        if (!config.getSendChannel().equals(sendChannel)) {
            throw ExceptionHelper.create(ErrCode.COUPON, String.format("不支持当前渠道发放==>券配置[%s],渠道[%s]", config.getId(), sendChannel));
        }
    }

    /**
     * 数量检查
     *
     * @param missionIds   List<Long>
     * @param missionInfos List<MissionCacheItemPo>
     * @throws BizError .
     */
    private void countChecker(List<Long> missionIds, Map<Long, MissionCacheItemPo> missionInfos) throws BizError {
        Map<Long, Long> counts = missionCacheDao.getSendCount(missionIds);
        for (Map.Entry<Long, MissionCacheItemPo> mission : missionInfos.entrySet()) {
            if (counts.get(mission.getValue().getId()) == null || mission.getValue().getMaxSendNum() <= counts.get(mission.getValue().getId())) {
                throw ExceptionHelper.create(ErrCode.COUPON, String.format("发放数量已达限制==>任务[%s]", mission.getValue().getId()));
            }
        }
    }

    /**
     * 任务发放类型检查
     *
     * @param mission MissionCacheItemPo
     * @throws BizError .
     */
    private void missionTypeChecker(MissionCacheItemPo mission) throws BizError {
        if (!mission.getMissionType().equals(MissionTypeEnum.INTERFACE.getRedisValue())) {
            throw ExceptionHelper.create(ErrCode.COUPON, String.format("发放任务不支持当前发放方式==>任务[%s]", mission.getId()));
        }
    }

    /**
     * 券模式类型检查
     *
     * @param config ConfigCacheItemPo
     * @throws BizError .
     */
    private void modeTypeChecker(ConfigCacheItemPo config) throws BizError {
        if (!config.getModeType().equals(ModeTypeEnum.NoCode.getRedisValue())) {
            throw ExceptionHelper.create(ErrCode.COUPON, String.format("非无码券类型不可发放==>券配置[%s]", config.getId()));
        }
    }

    /**
     * 状态检查
     *
     * @param config  ConfigCacheItemPo
     * @param mission MissionCacheItemPo
     * @throws BizError .
     */
    private void statusChecker(ConfigCacheItemPo config, MissionCacheItemPo mission) throws BizError {
        if (!mission.getStatus().equals(MissionStatusEnum.Approved.getRedisValue())) {
            throw ExceptionHelper.create(ErrCode.COUPON, String.format("发放任务已过期==>任务[%s]", mission.getId()));
        }

        if (!config.getStatus().equals(OutputStatusEnum.Approved.getRedisValue())) {
            throw ExceptionHelper.create(ErrCode.COUPON, String.format("券配置已过期==>券配置[%s]", config.getId()));
        }
    }

    /**
     * 时间校验(券的真实有效时间)
     *
     * @param config  ConfigCacheItemPo
     * @param mission MissionCacheItemPo
     */
    private void timeChecker(ConfigCacheItemPo config, MissionCacheItemPo mission) throws BizError {
        long nowTime = TimeUtil.getNowUnixSecond();

        if (config.getGlobalStartTime() == null || config.getGlobalStartTime() <= 0 ||
                config.getGlobalEndTime() == null || config.getGlobalEndTime() <= 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, String.format("券配置的发放时间不符合要求==>券配置[%s], 券配置开始时间[%s] - 券配置结束时间[%s]", config.getId(), config.getGlobalStartTime(), config.getGlobalEndTime()));
        }

        if (TimeTypeEnum.DAYS.getRedisValue().equals(mission.getTimeType()) && (mission.getDays() == null || mission.getDays() < 0 || mission.getHours() == null || mission.getHours() < 0)) {
            throw ExceptionHelper.create(ErrCode.COUPON, String.format("发放任务的有效时间不符合要求==>任务[%s], [%s]天，[%s]小时，任务开始时间[%s] - 任务结束时间[%s]", mission.getId(), mission.getDays(), mission.getHours(), mission.getCouponStartTime(), mission.getCouponEndTime()));
        }

        if (TimeTypeEnum.SECTION.getRedisValue().equals(mission.getTimeType()) &&
                (mission.getCouponStartTime() == null || mission.getCouponStartTime() <= 0 ||
                        mission.getCouponEndTime() == null || mission.getCouponEndTime() <= 0)) {
            throw ExceptionHelper.create(ErrCode.COUPON, String.format("发放任务的有效时间区间不符合要求==>任务[%s], [%s]天，任务开始时间[%s] - 任务结束时间[%s]", mission.getId(), mission.getDays(), mission.getCouponStartTime(), mission.getCouponEndTime()));
        }

        if (TimeTypeEnum.DAYS.getRedisValue().equals(mission.getTimeType())) {
            mission.setCouponStartTime(nowTime);
            mission.setCouponEndTime(nowTime + mission.getDays() * 86400 + mission.getHours() * 3600);
        }

        if (mission.getCouponEndTime() <= nowTime) {
            throw ExceptionHelper.create(ErrCode.COUPON, String.format("不能发放已过期的券==>任务[%s], 任务的券真实结束时间[%s]", mission.getId(), mission.getCouponEndTime()));
        }

        if (mission.getCouponStartTime() >= mission.getCouponEndTime()) {
            throw ExceptionHelper.create(ErrCode.COUPON, String.format("发放任务的有效开始时间不符合要求==>任务[%s], 任务开始时间[%s] - 任务结束时间[%s]", mission.getId(), mission.getCouponStartTime(), mission.getCouponEndTime()));
        }

        if (mission.getCouponStartTime() < config.getGlobalStartTime() || mission.getCouponEndTime() > config.getGlobalEndTime()) {
            throw ExceptionHelper.create(ErrCode.COUPON, String.format("券的有效时间超出配置的时间范围==>任务[%s], 券配置开始时间[%s] - 券配置结束时间[%s]", mission.getId(), config.getGlobalStartTime(), config.getGlobalStartTime()));
        }
    }

    /**
     * 发放权限检查
     *
     * @param appId   String
     * @param mission MissionCacheItemPo
     * @throws BizError .
     */
    private void rightsChecker(String appId, String sendChannel, MissionCacheItemPo mission) throws BizError {
        String depart = mission.getDepartmentId();
        if (depart == null || depart.isEmpty()) {
            throw ExceptionHelper.create(ErrCode.COUPON, String.format("发放任务无权限使用！==>任务[%s]", mission.getId()));
        }
        String[] split = StringUtil.split(mission.getDepartmentId(), ":");
        if (split.length == 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, String.format("发放任务无权限使用～==>任务[%s]", mission.getId()));
        }
        if (!split[0].equals(appId)) {
            //因为下单赠券可以发放其他appid的券，所以不能限制
            if (!common.equalsStoreOrderGiftAppId(split[0])) {
                throw ExceptionHelper.create(ErrCode.COUPON, String.format("发放任务无权限使用==>任务[%s]", mission.getId()));
            }
        }
        if (!common.checkAppIdChannel(appId, sendChannel)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "没有权限发放此渠道的券");
        }
    }

    /**
     * 获取券配置缓存信息
     *
     * @param ids List<Long>
     * @return Map<Long, ConfigCacheItemPo>
     * @throws BizError .
     */
    private Map<Long, ConfigCacheItemPo> getConfigInfos(List<Long> ids) throws BizError {
        Map<Long, ConfigCacheItemPo> caches = couponConfigRedisDao.get(ids);
        if (caches == null) {
            throw ExceptionHelper.create(ErrCode.COUPON, "无法获取有效的券配置信息");
        }

        if(caches.isEmpty()) {
            return caches;
        }

        if (caches.size() == ids.size()) {
            return caches;
        }

        for (Long id : ids) {
            if (caches.get(id) == null || caches.get(id).getId() <= 0) {
                throw ExceptionHelper.create(ErrCode.COUPON, String.format("优惠券处于不可发放的状态==>券配置[%s]", id));
            }
        }
        return caches;
    }

    /**
     * 获取发放任务缓存信息
     *
     * @param ids List<Long>
     * @return Map<Long, MissionCacheItemPo>
     * @throws BizError .
     */
    private Map<Long, MissionCacheItemPo> getMissionInfos(List<Long> ids) throws BizError {
        Map<Long, MissionCacheItemPo> caches = missionCacheDao.get(ids);
        if (caches == null) {
            throw ExceptionHelper.create(ErrCode.COUPON, "无法获取有效的发放任务");
        }

        if (caches.size() == ids.size()) {
            return caches;
        }

        for (Long id : ids) {
            if (caches.get(id) == null || caches.get(id).getId() == null || caches.get(id).getId() <= 0) {
                return new HashMap<>();
            }
        }
        return caches;
    }

    /**
     * 获取券配置ID
     *
     * @param data Map<Long, MissionCacheItemPo>
     */
    private List<Long> getConfigIds(Map<Long, MissionCacheItemPo> data) {
        List<Long> result = new ArrayList<>();
        for (Map.Entry<Long, MissionCacheItemPo> item : data.entrySet()) {
            if (result.contains(item.getValue().getCouponConfigId())) {
                continue;
            }
            result.add(item.getValue().getCouponConfigId());
        }
        return result;
    }

    /**
     * 获取发放任务ID
     *
     * @param items List<AssignItemDto>
     */
    private List<Long> getMissionIds(List<AssignMissionItemDto> items) {
        List<Long> result = new ArrayList<>();
        for (AssignMissionItemDto item : items) {
            if (result.contains(item.getMissionId())) {
                continue;
            }
            result.add(item.getMissionId());
        }
        return result;
    }

    /**
     * 参数校验
     *
     * @param request AssignRequest
     * @throws BizError .
     */
    private void paramsChecker(AssignRequest request) throws BizError {
        if (request.getUserId() == null || request.getUserId() <= 0) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "小米ID不符合要求");
        }

        if (Strings.isEmpty(request.getAppId())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "appID不符合要求");
        }

        if (Strings.isEmpty(request.getRequestId())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "请求ID不符合要求");
        } else if (request.getRequestId().trim().length() > 40) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "请求ID的长度不能超过40个字符");
        }

        if (Strings.isEmpty(request.getSendChannel())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "投放渠道不符合要求");
        }

        if (common.isOfflineSendChannel(request.getSendChannel())) {
            if (common.isStoreManageSendChannel(request.getSendChannel())) {
                if (request.getShareUserId() == null || request.getShareUserId() <= 0) {
                    throw ExceptionHelper.create(GeneralCodes.ParamError, "分享人小米ID不符合要求");
                }
            }
            if (Strings.isEmpty(request.getOrgCode())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "门店ID不能为空");
            }
        }

        if (request.getItems() == null || request.getItems().isEmpty()) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "发放任务不能为空");
        }

        if (request.getItems().size() > ASSIGN_MAX_COUNT) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "每次只能发放1张券");
        }

        List<Long> missionIds = new ArrayList<>();
        for (AssignMissionItemDto item : request.getItems()) {
            if (item.getMissionId() == null || item.getMissionId() <= 0) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, String.format("发放任务不符合发放要求==>[%s]", item.getMissionId()));
            }

            if (missionIds.contains(item.getMissionId())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, String.format("单次发放的任务不能重复==>[%s]", item.getMissionId()));
            }

            missionIds.add(item.getMissionId());

            if (item.getOrderId() != null && item.getOrderId() <= 0) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, String.format("发放关联的订单号不符合要求==>[%s]", item.getMissionId()));
            }
        }
    }
}
