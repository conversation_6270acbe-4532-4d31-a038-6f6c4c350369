package com.xiaomi.nr.coupon.constant;

import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.coupon.CouponUseChannelEnum;

import java.util.*;

/**
 * 优惠券相关常量
 *
 * <AUTHOR>
 */
public class CouponConfigConstant {

    //根据券配置id获取券的类型和券的面额所需常量
    public static final String COUPON_CASH_UNIT = "元";
    public static final String COUPON_DISCOUNT_UNIT = "折";

    //券优惠力度展示常量showValue、showUnit字段
    public final static String SHOW_VALUE = "showValue";
    public final static String SHOW_UNIT = "showUnit";

    //优惠券使用渠道范围分组
    public static final String MISHOP_ONLINE = "MISHOP_ONLINE";
    public static final String MIHOME_OFFLINE = "MIHOME_OFFLINE";
    public static final String CAR_SHOP = "CAR_SHOP";

    //文案优化
    public static final String MISHOP_ONLINE_DESC = "小米商城(商城配送)";

    //优惠券使用渠道范围分组映射
    public static final Map<String, String> USECHANNEL_GROUP_MAP = new HashMap<>(CommonConstant.FOUR_INT);
    static {
        USECHANNEL_GROUP_MAP.put(CouponUseChannelEnum.MI_SHOP.getValue(), MISHOP_ONLINE);
        USECHANNEL_GROUP_MAP.put(CouponUseChannelEnum.MI_HOME_ZY.getValue(), MIHOME_OFFLINE);
        USECHANNEL_GROUP_MAP.put(CouponUseChannelEnum.MI_HOME_ZM.getValue(), MIHOME_OFFLINE);
        USECHANNEL_GROUP_MAP.put(CouponUseChannelEnum.MI_AUTHORIZED.getValue(), MIHOME_OFFLINE);
        USECHANNEL_GROUP_MAP.put(CouponUseChannelEnum.MI_FORTRESS.getValue(), MIHOME_OFFLINE);
        USECHANNEL_GROUP_MAP.put(CouponUseChannelEnum.CAR_SHOP.getValue(), CAR_SHOP);
    }

    //useChannelDesc文案排序用
    public static final Map<String, Integer> USECHANNEL_SORT = new HashMap<>(CommonConstant.FOUR_INT);
    static {
        USECHANNEL_SORT.put(CouponUseChannelEnum.MI_SHOP.getValue(), 1);
        USECHANNEL_SORT.put(CouponUseChannelEnum.MI_HOME_ZY.getValue(), 2);
        USECHANNEL_SORT.put(CouponUseChannelEnum.MI_HOME_ZM.getValue(), 3);
        USECHANNEL_SORT.put(CouponUseChannelEnum.MI_AUTHORIZED.getValue(), 4);
        USECHANNEL_SORT.put(CouponUseChannelEnum.MI_FORTRESS.getValue(), 5);
        USECHANNEL_SORT.put(CouponUseChannelEnum.CAR_SHOP.getValue(), 6);
    }

    //分页状态校验
    public static final Map<String, Integer> COUPON_STATUS_PAGEMAP = new HashMap<>(5);
    static {
        COUPON_STATUS_PAGEMAP.put(CouponStatusEnum.USED.getValue(), 1);
        COUPON_STATUS_PAGEMAP.put(CouponStatusEnum.EXPIRED.getValue(), 1);
        COUPON_STATUS_PAGEMAP.put(CouponStatusEnum.PRESENTED.getValue(), 1);
        COUPON_STATUS_PAGEMAP.put(CouponStatusEnum.RECEIVED.getValue(), 1);
        COUPON_STATUS_PAGEMAP.put("used_presented_received", 1);
    }

    // 米家对应的clientID
    public static final long MI_HOME_CLIENT_ID = 180100041075L;
    // 授权店对应的clientID
    public static final long AUTHORIZED_CLIENT_ID = 180100041157L;
}
