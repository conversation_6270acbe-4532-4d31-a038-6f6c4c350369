package com.xiaomi.nr.coupon.domain.coupon.impl;

import com.xiaomi.nr.coupon.api.dto.coupon.info.CouponInfoRequest;
import com.xiaomi.nr.coupon.domain.coupon.AbstractUserCouponInfo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave.CouponInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/21
 */
@Component
@Slf4j
public class UserCouponInfoImpl extends AbstractUserCouponInfo {

    @Resource
    private CouponInfoMapper couponInfoMapper;

    /**
     * 获取优惠券信息
     *
     * @param req 入参
     * @return 优惠券信息
     */
    @Override
    public List<CouponPo> getCouponInfo(CouponInfoRequest req) {
        Long userId = req.getUserId();
        List<Long> couponIds = req.getCouponIds();

        return Objects.isNull(userId)
                ? couponInfoMapper.getCouponInfoCouponIds(couponIds)
                : couponInfoMapper.batchGetCouponInfo(userId, couponIds);
    }
}
