package com.xiaomi.nr.coupon.domain.coupontrade.tradecheck;

import com.xiaomi.nr.coupon.api.dto.trade.CouponLockItem;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/10 15:25
 */
public abstract class AfterSaleCouponLockTools {
    /**
     * 优惠券锁定校验 + 幂等性校验
     *
     * @param couponPos
     * @param orderId
     * @param vid
     * @param timeNow
     * @return 是否幂等，券锁定校验失败时抛出异常
     */
    public abstract boolean couponListLockCheck(List<CouponPo> couponPos, long orderId, String vid, long timeNow) throws BizError;

    /**
     * 优惠券锁定
     *
     * @param vid
     * @param userId
     * @param orderId
     * @param offline
     * @param couponLockItems
     */
    public abstract void lockCoupon(String vid, long userId, long orderId, Integer offline, List<CouponLockItem> couponLockItems) throws BizError;
}
