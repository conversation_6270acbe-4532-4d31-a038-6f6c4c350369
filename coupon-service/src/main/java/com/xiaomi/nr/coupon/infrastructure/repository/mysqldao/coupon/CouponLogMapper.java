package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponLogPo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 券日志写入
 * <AUTHOR>
 */
@Mapper
@Repository
public interface CouponLogMapper {

    /**
     * 插入日志
     * @param log
     * @return
     */
    @Insert("insert into tb_coupon_log (" +
            "coupon_id, user_id, type, old_stat, new_stat, admin_id, admin_name, coupon_desc, add_time, offline" +
            ") values (" +
            "#{log.couponId},#{log.userId},#{log.type},#{log.oldStat},#{log.newStat},#{log.adminId},#{log.adminName},#{log.couponDesc}," +
            "#{log.addTime},#{log.offline})")
    Integer insert(@Param("log") CouponLogPo log);

    /**
     * 批量写入日志
     * @param couponLogPos
     * @return
     */
    @Insert("<script>insert into tb_coupon_log (" +
            "coupon_id, user_id, type, old_stat, new_stat, admin_id, admin_name, coupon_desc, add_time, offline) values " +
            "<foreach item='log' index='index' collection='couponLogPos' open='' separator=',' close=''>" +
            "(#{log.couponId},#{log.userId},#{log.type},#{log.oldStat},#{log.newStat},#{log.adminId},#{log.adminName},#{log.couponDesc}," +
            "#{log.addTime},#{log.offline})</foreach></script>")
    Integer batchInsert(@Param("couponLogPos") List<CouponLogPo> couponLogPos);
}