package com.xiaomi.nr.coupon.application.dubbo;

import com.xiaomi.nr.coupon.api.dto.trade.*;
import com.xiaomi.nr.coupon.api.service.CouponTradeDubboService;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponConvert;
import com.xiaomi.nr.coupon.domain.coupontrade.CouponTradeCheckService;
import com.xiaomi.nr.coupon.domain.coupontrade.UserCouponFlowFactory;
import com.xiaomi.nr.coupon.domain.coupontrade.UserCouponService;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Optional;

@Component
@Slf4j
@Service(group = "${dubbo.group}", version = "1.0", timeout = 3000, delay = 10000)
public class CouponTradeDubboServiceImpl implements CouponTradeDubboService {

    @Autowired
    private UserCouponFlowFactory userCouponFlowFactory;

    @Autowired
    private CouponTradeCheckService couponTradeCheckService;

    @Resource
    private CouponConvert couponConvert;

    @Override
    public Result<LockCouponResponse> lockUserCoupon(@Valid LockCouponRequest request) {
        try {

            couponTradeCheckService.checkLockRequestParam(request);

            String couponCode = StringUtils.EMPTY;
            if(CollectionUtils.isNotEmpty(request.getCouponItems())){
                couponCode = request.getCouponItems().get(0).getCouponCode();
            }

            UserCouponService userCouponService = userCouponFlowFactory.getUserCouponService(couponCode, request.getSubmitType());

            return Result.success(userCouponService.lockUserCoupon(request));

        } catch (BizError bizError) {
            log.error("CouponTradeDubboService.lockUserCoupon bizError request:{}", request, bizError);
            return Result.fromException(bizError, bizError.getMessage());
        } catch (Exception e) {
            log.error("CouponTradeDubboService.lockUserCoupon Exception request:{}", request, e);
            return Result.fromException(e, "优惠券锁定失败");
        }
    }

    @Override
    public Result<ConsumeCouponResponse> consumeUserCoupon(@Valid ConsumeCouponRequest request) {
        try {

            couponTradeCheckService.CheckCommonRequestParam(request.getUserId(), request.getOrderId(), request.getOffline());

            UserCouponService userCouponService = userCouponFlowFactory.getUserCouponService(request.getCouponCode(), request.getSubmitType());

            return Result.success(userCouponService.consumeUserCoupon(request));

        } catch (BizError bizError) {
            log.error("CouponTradeDubboService.consumeUserCoupon bizError request:{}", request, bizError);
            return Result.fromException(bizError, bizError.getMessage());
        }catch (Exception e) {
            log.error("CouponTradeDubboService.consumeUserCoupon Exception request:{}", request, e);
            return Result.fromException(e, "优惠券核销失败");
        }
    }

    @Override
    public Result<RollbackCouponResponse> rollbackUserCoupon(@Valid RollbackCouponRequest request) {
        try {

            couponTradeCheckService.CheckCommonRequestParam(request.getUserId(), request.getOrderId(), request.getOffline());

            UserCouponService userCouponService = userCouponFlowFactory.getUserCouponService(request.getCouponCode(), request.getSubmitType());

            return Result.success(userCouponService.rollbackUserCoupon(request));

        } catch (BizError bizError) {
            log.error("CouponTradeDubboService.rollbackUserCoupon bizError request:{}", request, bizError);
            return Result.fromException(bizError, bizError.getMessage());
        }catch (Exception e) {
            log.error("CouponTradeDubboService.rollbackUserCoupon Exception request:{}", request, e);
            return Result.fromException(e, "优惠券退还失败");
        }
    }

    /**
     * 结算用券查询
     *
     * @param request GetCheckoutCouponListV2Request
     * @return GetCheckoutCouponListV2Response
     */
    @Override
    public Result<GetCheckoutCouponListV2Response> getCheckoutCouponListV2(@Valid GetCheckoutCouponListV2Request request) {
        try {
            couponTradeCheckService.checkCheckoutCouponListV2Param(request);

            CheckoutCouponReqModel req = couponConvert.convertCheckoutReq2CheckoutModel(request);
            UserCouponService userCouponService = userCouponFlowFactory.getUserCouponService(request.getCouponCode(), request.getSubmitType());
            CheckoutCouponResModel checkoutRes = userCouponService.checkoutCoupon(req);

            GetCheckoutCouponListV2Response response = new GetCheckoutCouponListV2Response();
            response.setNoCodeCoupons(checkoutRes.getNoCodeCoupons());
            response.setCodeCoupons(checkoutRes.getCodeCoupons());
            response.setCouponGroupInfoMap(checkoutRes.getCouponGroupInfoMap());
            return Result.success(response);
        } catch (BizError e) {
            log.warn("coupon.getCheckoutCouponListV2, request:{}, err:{}", request, e.getMessage());
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("coupon.getCheckoutCouponListV2, 结算校验券遇到异常，request:{}", request, e);
            return Result.fromException(e);
        }
    }

}
