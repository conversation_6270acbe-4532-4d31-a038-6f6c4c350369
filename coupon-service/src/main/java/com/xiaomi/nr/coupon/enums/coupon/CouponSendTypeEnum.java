package com.xiaomi.nr.coupon.enums.coupon;

import org.apache.commons.lang3.StringUtils;

/**
 * 优惠券发送类型 枚举
 * <AUTHOR>
 * */
public enum CouponSendTypeEnum {

    /**
     * 用户中心补发优惠券
     * */
    REISSUE("reissue","用户中心补发优惠券"),

    /**
     * 灌券
     * */
    MARKETING("marketing","活动灌券"),

    /**
     * 接口发券
     * */
    EXTERNAL("external","外部调用接口发券"),

    /**
     * 西瓜商超
     * */
    XIGUA_MARKET("xiguaMarket", "西瓜商超");

    private final String value;
    private final String name;

    CouponSendTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static boolean findByValue(String value) {
        CouponSendTypeEnum[] values = CouponSendTypeEnum.values();
        for (CouponSendTypeEnum item : values) {
            if (StringUtils.equals(item.getValue(),value)) {
                return true;
            }
        }
        return false;
    }
}
