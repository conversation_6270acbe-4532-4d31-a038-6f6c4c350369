package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.enums.couponconfig.ConfigStatusEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import org.springframework.stereotype.Component;

/**
 * 校验券配置上下线状态
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Component
public class ConfigStatus extends CheckerAbstract {

    @Override
    public boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {
        if (!ctx.isCheckConfigStatus()) {
            return true;
        }

        errCtx.setInvalidConfig(true);

        CouponConfigItem config = ctx.getConfigInfoMap().get(respItem.getConfigId());

        if (ConfigStatusEnum.Online.getValue().equals(config.getCouponConfigInfo().getStatus())) {
            return true;
        }

        errCtx.setErrCode(ErrCode.ASSIGN_COUPON_STATUS_OFFLINE.getCode());
        errCtx.setErrMsg("券配置已停止发放");
        return false;
    }
}
