package com.xiaomi.nr.coupon.domain.coupontrade.convert;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.api.dto.coupon.*;
import com.xiaomi.nr.coupon.api.dto.coupon.checkout.GoodsInfo;
import com.xiaomi.nr.coupon.constant.CommonConstant;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponFormatCommon;
import com.xiaomi.nr.coupon.domain.coupontrade.tradecheck.CouponInfoCheck;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.ExtPropInfo;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.coupon.CouponUseChannelEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.*;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponcode.po.CouponCodePo;
import com.xiaomi.nr.coupon.util.NumberUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 优惠券基本信息转换类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CouponBaseInfoConvert {

    @Resource
    private CouponInfoCheck couponInfoCheck;

    @Resource
    private CouponFormatCommon couponFormatCommon;

    /**
     * 转换无码券信息券配置至券基本信息
     *
     * @param coupon 优惠券信息
     * @param config 券配置缓存信息
     * @return CouponBaseInfo
     */
    public CouponBaseInfo convertNoCodeCouponPoConfig2BaseInfo(CouponPo coupon, CouponConfigItem config) {
        long endTime = convertLong(coupon.getEndTime());
        CouponBaseInfo result = new CouponBaseInfo();
        result.setCouponId(coupon.getId());
        result.setCouponName(config.getCouponConfigInfo().getName());
        result.setConfigId(coupon.getTypeId());
        result.setStatus(getStatus(coupon.getStat(), endTime));
        result.setStartTime(convertLong(coupon.getStartTime()));
        result.setEndTime(endTime);
        result.setCouponType(config.getCouponType());
        result.setCouponRangeDesc(config.getCouponConfigInfo().getCouponDesc());
        result.setCouponRuleDesc(getRuleDesc(config));
        result.setPromotionType(config.getPromotionType().getCode());
        result.setPromotionValue(convertPromotionValue(config.getPromotionType(), config.getCouponConfigInfo().getPromotionValue()));
        result.setMaxReduce(config.getCouponConfigInfo().getMaxReduce());
        result.setShowUnit(getShowUnit(config.getPromotionType()));
        result.setBottomType(config.getCouponConfigInfo().getBottomType());
        result.setBottomCount(config.getCouponConfigInfo().getBottomCount());
        result.setBottomPrice(config.getCouponConfigInfo().getBottomPrice());
        ExtPropInfo extPropInfo = config.getExtPropInfo();
        result.setPostFree(extPropInfo.getPostFree());
        result.setShare(extPropInfo.getShare());
        result.setCheckoutStage(extPropInfo.getCheckoutStage());
        result.setType(getType(config.getPromotionType()));
        result.setTypeCode(getTypeCode(config.getPromotionType()));
        result.setLimitUseRegion(couponInfoCheck.getCouponUseRegionId(config.getAssignArea()));
        result.setSendChannel(config.getCouponConfigInfo().getSendChannel());
        fillUseChannel(config, result);
        result.setBudgetApplyNo(config.getBudgetApplyNo());
        result.setLineNum(config.getLineNum());
        result.setBizPlatform(config.getBizPlatform());
        result.setServiceScene(config.getCouponConfigInfo().getServiceScene());
        result.setTimesLimit(config.getCouponConfigInfo().getTimesLimit());
        // 券抵扣的工时标准面数量写死10
        result.setWorkHourStandardPage(10);
        result.setSendScene(config.getCouponConfigInfo().getSendScene());
        return result;
    }

    /**
     * 转换明码券信息券配置至券基本信息
     *
     * @param coupon 优惠券信息
     * @param config 券配置缓存信息
     * @return CouponBaseInfo
     */
    public CouponBaseInfo convertCodeCouponPoConfig2BaseInfo(CouponCodePo coupon, CouponConfigItem config) {
        // 早期的明码券都是在券本身上记录开始时间的，最新的优惠码灌券已经不在券本身记录开始时间，只记录了结束时间
        long startTime = Objects.isNull(coupon.getStartTime()) || coupon.getStartTime().equals(0L) ? config.getCouponConfigInfo().getStartUseTime() : coupon.getStartTime();
        CouponBaseInfo result = new CouponBaseInfo();
        result.setCouponId(coupon.getId());
        result.setCouponName(config.getCouponConfigInfo().getName());
        result.setConfigId(coupon.getTypeId());
        result.setStatus(getStatus(coupon.getStat(), coupon.getEndTime()));
        result.setStartTime(startTime);
        result.setEndTime(coupon.getEndTime());
        result.setCouponType(config.getCouponType());
        result.setCouponRangeDesc(config.getCouponConfigInfo().getCouponDesc());
        result.setCouponRuleDesc(getRuleDesc(config));
        result.setPromotionType(config.getPromotionType().getCode());
        result.setPromotionValue(convertPromotionValue(config.getPromotionType(), config.getCouponConfigInfo().getPromotionValue()));
        result.setMaxReduce(config.getCouponConfigInfo().getMaxReduce());
        result.setShowUnit(getShowUnit(config.getPromotionType()));
        result.setBottomType(config.getCouponConfigInfo().getBottomType());
        result.setBottomCount(config.getCouponConfigInfo().getBottomCount());
        result.setBottomPrice(config.getCouponConfigInfo().getBottomPrice());
        ExtPropInfo extPropInfo = config.getExtPropInfo();
        result.setPostFree(extPropInfo.getPostFree());
        result.setShare(extPropInfo.getShare());
        result.setCheckoutStage(extPropInfo.getCheckoutStage());
        result.setType(getType(config.getPromotionType()));
        result.setTypeCode(getTypeCode(config.getPromotionType()));
        result.setLimitUseRegion(couponInfoCheck.getCouponUseRegionId(config.getAssignArea()));
        result.setSendChannel(config.getCouponConfigInfo().getSendChannel());
        fillUseChannel(config, result);
        return result;
    }

    /**
     * 填充使用渠道、使用渠道描述
     *
     * @param config    config
     * @param baseInfo  baseInfo
     */
    public void fillUseChannel(CouponConfigItem config, CouponBaseInfo baseInfo) {
        String useChannel = convertUseChannel(config);
        baseInfo.setUseChannel(useChannel);

        List<String> useChannelList = Lists.newArrayList(StringUtils.defaultIfBlank(useChannel, CommonConstant.EMPTY_STR).split(CommonConstant.COMMA));
        baseInfo.setUseChannelDesc(couponFormatCommon.getUseChannelDesc(useChannelList, baseInfo.getCouponType(), null, baseInfo.getBizPlatform()));

    }

    /**
     * 生成分组编号
     *
     * @param config 券配置信息
     * @return 分组编号
     * @throws Exception
     */
    public String genCouponGroupNo(CouponConfigItem config) throws Exception {
        if (Objects.isNull(config) || Objects.isNull(config.getCouponConfigInfo())) {
            throw new Exception("券配置信息有误");
        }
        Integer couponType = config.getCouponType();
        Integer shipmentId = config.getCouponConfigInfo().getShipmentId();
        if (Objects.isNull(couponType)) {
            throw new Exception("券类型不存在");
        }
        if (Objects.isNull(shipmentId)) {
            throw new Exception("券配置的履约方式不存在");
        }
        Integer sortVal = couponInfoCheck.getSortMapValByCouponType(couponType);
        return sortVal + "_" + couponType + "_" + shipmentId;
    }

    /**
     * 生成分组编号（售后服务）
     *
     * @param config 券配置信息
     * @return 分组编号
     * @throws Exception
     */
    public String genCouponGroupNoForSaleAfter(CouponConfigItem config) throws Exception {
        if (Objects.isNull(config) || Objects.isNull(config.getCouponConfigInfo())) {
            throw new Exception("券配置信息有误");
        }
        Integer couponType = config.getCouponType();
        Integer shipmentId = config.getCouponConfigInfo().getShipmentId();
        Integer serviceScene = config.getCouponConfigInfo().getServiceScene();
        if (Objects.isNull(couponType)) {
            throw new Exception("券类型不存在");
        }
        if (Objects.isNull(shipmentId)) {
            throw new Exception("券配置的履约方式不存在");
        }
        if (Objects.isNull(serviceScene)) {
            throw new Exception("券类型对应的服务场景值不存在");
        }
        Integer sortVal = couponInfoCheck.getSortMapValByCouponType(couponType);
        return sortVal + "_" + couponType + "_" + shipmentId + "_" + serviceScene;
    }

    /**
     * 获取可用套装列表
     *
     * @param config 券配置信息
     * @param goods  购物车商品
     * @return 套装ID列表
     */
    public List<Long> getValidPackageList(CouponConfigItem config, List<GoodsInfo> goods) {
        if (Objects.isNull(config) || Objects.isNull(config.getGoodScope()) || Objects.isNull(config.getGoodScope().getPackages())) {
            return null;
        }

        // 不传入商品则返回所有可用商品
        if (CollectionUtils.isEmpty(goods)) {
            return new ArrayList<>(config.getGoodScope().getPackages());
        }

        return goods.stream().filter(goodsInfo -> {
            return GoodsLevelEnum.Package.getValue().equals(goodsInfo.getLevel()) && config.getGoodScope().singleCheckValidPackage(goodsInfo.getId());
        }).map(GoodsInfo::getId).distinct().collect(Collectors.toList());
    }

    /**
     * 获取可用SKU列表
     *
     * @param config 券配置信息
     * @param goods  购物车商品
     * @return SKU列表
     */
    public List<Long> getValidSkuList(CouponConfigItem config, List<GoodsInfo> goods) {
        if (Objects.isNull(config) || Objects.isNull(config.getGoodScope()) || Objects.isNull(config.getGoodScope().getSkus())) {
            return null;
        }

        // 不传入商品则返回所有可用商品
        if (CollectionUtils.isEmpty(goods)) {
            return new ArrayList<>(config.getGoodScope().getSkus());
        }

        return goods.stream().filter(goodsInfo -> {
            return GoodsLevelEnum.Sku.getValue().equals(goodsInfo.getLevel()) && config.getGoodScope().singleCheckValidSku(goodsInfo.getId());
        }).map(GoodsInfo::getId).distinct().collect(Collectors.toList());
    }

    /**
     * 获取可用SSU列表
     *
     * @param config 券配置信息
     * @param goods  购物车商品
     * @return SKU列表
     */
    public List<Long> getValidSsuList(CouponConfigItem config, List<GoodsInfo> goods) {
        if (Objects.isNull(config) || Objects.isNull(config.getGoodScope()) || Objects.isNull(config.getGoodScope().getSsus())) {
            return null;
        }

        // 不传入商品则返回所有可用商品
        if (CollectionUtils.isEmpty(goods)) {
            return new ArrayList<>(config.getGoodScope().getSsus());
        }

        return goods.stream().filter(goodsInfo -> GoodsLevelEnum.Ssu.getValue().equals(goodsInfo.getLevel()) && config.getGoodScope().singleCheckValidSsu(goodsInfo.getId())).map(GoodsInfo::getId).distinct().collect(Collectors.toList());
    }

    /**
     * 转换优惠值（例：8.5折返回85，传入的是850）
     *
     * @param promotionType
     * @param promotionValue
     * @return
     */
    private Long convertPromotionValue(PromotionType promotionType, Long promotionValue) {
        if (PromotionType.ConditionDiscount.equals(promotionType)) {
            return promotionValue / 10;
        }
        return promotionValue;
    }

    /**
     * 转换使用渠道
     *
     * @param config 券配置信息
     * @return 渠道字符串（以逗号分隔）
     */
    private String convertUseChannel(CouponConfigItem config) {
        List<String> channels = new ArrayList<>();
        if (config.getUseChannelStore().containsKey(CouponUseChannelEnum.MI_SHOP.getId())) {
            channels.add(CouponUseChannelEnum.MI_SHOP.getValue());
        }
        if (config.getUseChannelStore().containsKey(CouponUseChannelEnum.MI_HOME_ZY.getId())) {
            channels.add(CouponUseChannelEnum.MI_HOME_ZY.getValue());
        }
        if (config.getUseChannelStore().containsKey(CouponUseChannelEnum.MI_HOME_ZM.getId())) {
            channels.add(CouponUseChannelEnum.MI_HOME_ZM.getValue());
        }
        if (config.getUseChannelStore().containsKey(CouponUseChannelEnum.MI_AUTHORIZED.getId())) {
            channels.add(CouponUseChannelEnum.MI_AUTHORIZED.getValue());
        }
        if (config.getUseChannelStore().containsKey(CouponUseChannelEnum.CAR_SHOP.getId())) {
            channels.add(CouponUseChannelEnum.CAR_SHOP.getValue());
        }
        return channels.isEmpty() ? "" : channels.stream().distinct().collect(Collectors.joining(","));
    }

    private String getTypeCode(PromotionType promotionType) {
        return PromotionType.getValueByCode(promotionType.getCode());
    }

    private Integer getType(PromotionType promotionType) {
        if (Objects.isNull(promotionType)) {
            return null;
        }
        String val = PromotionType.getValueByCode(promotionType.getCode());
        UseTypeEnum typeEnum = UseTypeEnum.findByValue(val);
        if (Objects.isNull(typeEnum)) {
            return null;
        }
        return typeEnum.getMysqlValue();
    }

    private String getShowUnit(PromotionType promotionType) {
        if (Objects.isNull(promotionType)) {
            return "";
        }

        switch (promotionType) {
            //满减券
            case ConditionReduce:
            case DirectReduce:
                return "元";

            //折扣券
            case ConditionDiscount:
                return "折";

            //兑换券
            case NyuanBuy:
                return "";

            default:
                return "";
        }
    }

    private String getRuleDesc(CouponConfigItem config) {
        if (Objects.isNull(config)) {
            return "";
        }

        switch (config.getPromotionType()) {
            //满减券
            case ConditionReduce:
            case DirectReduce:
                return getCouponCashRuleName(
                        config.getCouponConfigInfo().getPromotionValue(),
                        config.getCouponConfigInfo().getBottomType(),
                        config.getCouponConfigInfo().getBottomCount(),
                        config.getCouponConfigInfo().getBottomPrice(),
                        config.getCouponType(),
                        config.getCouponConfigInfo().getShipmentId()
                );

            //折扣券
            case ConditionDiscount:
                return getDiscountRuleName(config.getCouponConfigInfo().getPromotionValue());

            //兑换券
            case NyuanBuy:
                return getDeductibleRuleName();

            default:
                return "";
        }
    }

    private String getDeductibleRuleName() {
        return "商品兑换券";
    }

    private String getDiscountRuleName(Long reduceDiscount) {
        if (Objects.isNull(reduceDiscount)) {
            return "";
        }
        String reduceDiscountStr = NumberUtil.convertDiscount(reduceDiscount, 1, RoundingMode.DOWN);
        return reduceDiscountStr + "折优惠券";
    }

    private String getCouponCashRuleName(Long reduceMoney, Integer quotaType, Integer quotaCount, Integer quotaMoney, Integer couponType, Integer shipmentId) {
        if (Objects.isNull(reduceMoney)) {
            return "";
        }
        if (Objects.isNull(quotaMoney)) {
            return "";
        }
        String reduceMoneyStr = NumberUtil.centToYuan(reduceMoney, 2, RoundingMode.DOWN);
        String quotaMoneyStr = NumberUtil.centToYuan(quotaMoney, 2, RoundingMode.UP);
        String couponRuleDesc = "";

        //满件
        if (Objects.equals(QuotaTypeEnum.Count.getCode(), quotaType)) {
            if (quotaCount == CommonConstant.ONE_LONG) {
                if (CouponTypeEnum.PostFee.getValue().equals(couponType)) {
                    couponRuleDesc = reduceMoneyStr + "元运费券";
                    if (Objects.equals(ShipmentIdEnum.Lightning.getValue(), shipmentId)) {
                        couponRuleDesc = reduceMoneyStr + "元运费券，仅小米之家门店闪送可用";
                    }
                } else {
                    couponRuleDesc = "满" + quotaCount + "件减" + reduceMoneyStr + "元优惠券";
                }
            } else {
                couponRuleDesc = "满" + quotaCount + "件减" + reduceMoneyStr + "元优惠券";
            }

        }

        //满元
        if (Objects.equals(QuotaTypeEnum.Money.getCode(), quotaType)) {
            couponRuleDesc = "满" + quotaMoneyStr + "元减" + reduceMoneyStr + "元优惠券";
        }
        return couponRuleDesc;
    }

    private Long convertLong(String t) {
        if (Objects.isNull(t)) {
            return CommonConstant.ZERO_LONG;
        }
        return Long.parseLong(StringUtils.trim(t));
    }

    private String getStatus(String stat, Long endTime) {
        if (StringUtils.isEmpty(stat)) {
            return null;
        }
        if (stat.equals(CouponStatusEnum.LOCKED.getValue())) {
            return CouponStatusEnum.USED.getValue();
        }
        if (isExpired(stat, endTime)) {
            return CouponStatusEnum.EXPIRED.getValue();
        }
        return stat;
    }

    private Boolean isExpired(String stat, long endTime) {
        long nowTme = TimeUtil.getNowUnixSecond();
        return StringUtils.equals(CouponStatusEnum.EXPIRED.getValue(), stat) ||
                (StringUtils.equals(CouponStatusEnum.UNUSED.getValue(), stat) && endTime < nowTme);
    }

}
