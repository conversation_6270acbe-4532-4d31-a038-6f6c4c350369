package com.xiaomi.nr.coupon.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.compressors.deflate.DeflateCompressorInputStream;
import org.apache.commons.compress.compressors.deflate.DeflateCompressorOutputStream;
import org.apache.commons.compress.compressors.deflate.DeflateParameters;
import org.apache.commons.lang3.ArrayUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * lz4压缩、解缩工具
 *
 * <AUTHOR>
 */
@Slf4j
public class CompressUtil {

    /**
     * 预留4位表示版本号，目前为1
     */
    private static byte[] compressVersion = {1, 0, 0, 0};

    /**
     * deflate压缩
     * @param param String
     * @return byte[]
     * @throws IOException
     */
    public static byte[] compress(String param) throws IOException {
        byte[] srcBytes = param.getBytes(StandardCharsets.UTF_8);
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        DeflateParameters deflateParam = new DeflateParameters();
        deflateParam.setCompressionLevel(9);
        DeflateCompressorOutputStream bcos = new DeflateCompressorOutputStream(out, deflateParam);
        bcos.write(srcBytes);
        bcos.close();
        // 预留4位表示版本号
        return ArrayUtils.addAll(compressVersion, out.toByteArray());
    }

    /**
     * deflate解压
     * @param param byte[]
     * @return String
     * @throws Exception
     */
    public static String decompress(byte[] param) throws Exception {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        byte[] srcBytes = ArrayUtils.subarray(param, 4, param.length);
        ByteArrayInputStream in = new ByteArrayInputStream(srcBytes);
        try {
            DeflateCompressorInputStream inputStream = new DeflateCompressorInputStream(in);
            byte[] buffer = new byte[2048];
            int n;
            while ((n = inputStream.read(buffer)) >= 0) {
                out.write(buffer, 0, n);
            }
        } catch (IOException e) {
            log.error("deflateUncompress error ", e);
            throw e;
        }
        return new String(out.toByteArray(), StandardCharsets.UTF_8);
    }
}
