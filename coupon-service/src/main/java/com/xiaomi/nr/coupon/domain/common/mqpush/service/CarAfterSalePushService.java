package com.xiaomi.nr.coupon.domain.common.mqpush.service;

import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.common.model.MessageBuildContext;
import com.xiaomi.nr.coupon.domain.common.mqpush.CouponSendSceneEnum;
import com.xiaomi.nr.coupon.domain.common.mqpush.CouponUsePushFactory;
import com.xiaomi.nr.coupon.domain.common.mqpush.CouponUsePushService;
import com.xiaomi.nr.coupon.infrastructure.repository.mq.CarCouponUsePushMqProducer;
import com.xiaomi.nr.coupon.infrastructure.repository.mq.CarEquityPerformanceMqProducer;
import com.xiaomi.nr.coupon.infrastructure.repository.mq.convert.CouponUseMessageConvert;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025-01-08 20:28
 */
@Slf4j
@Component
public class CarAfterSalePushService implements CouponUsePushService {

    private final Set<String> sceneSet = new HashSet<>();

    private final Map<String, String> equityKeyMap = new HashMap<>();

    @Autowired
    private CarCouponUsePushMqProducer carCouponUsePushMqProducer;

    @Autowired
    private CarEquityPerformanceMqProducer carEquityPerformanceMqProducer;

    @Autowired
    private CouponUseMessageConvert messageConvert;

    @PostConstruct
    public void init() {
        List<String> sceneCodes = Arrays.stream(CouponSendSceneEnum.values())
                .filter(scene -> scene.getBizPlatform().equals(BizPlatformEnum.CAR_AFTER_SALE.getCode()))
                .map(CouponSendSceneEnum::getCode)
                .collect(Collectors.toList());
        sceneSet.addAll(sceneCodes);
        equityKeyMap.put(CouponSendSceneEnum.ULTRA_FIRST_OWNER_NEED_MAINTENANCE.getCode(), "BASE_MRS_MAINTAIN");
        equityKeyMap.put(CouponSendSceneEnum.ULTRA_FIRST_OWNER_TIRE_REPAIR.getCode(), "BASE_MRS_TIREREPAIR");
        equityKeyMap.put(CouponSendSceneEnum.CONSUMABLES_PACKAGE.getCode(), "SP_MRS_MAINTAIN");
        equityKeyMap.put(CouponSendSceneEnum.OPTIONAL_TRACK_PACKAGE_COUPON_DISTRIBUTION.getCode(), "");
        equityKeyMap.put(CouponSendSceneEnum.YU7_NEED_MAINTENANCE.getCode(), "");
        equityKeyMap.put(CouponSendSceneEnum.ULTRA_LIMIT_BOTTOM_PLATE.getCode(), "");
        CouponUsePushFactory.register(getBizType(), this);
    }

    @Override
    public void sendMessage(Message<?> message, Long couponId, String sceneCode) {
        if (!sceneSet.contains(sceneCode)) {
            return;
        }
        // 服务包券核销消息
        if (CouponSendSceneEnum.SERVICE_PACKAGE.getCode().equals(sceneCode)) {
            carCouponUsePushMqProducer.sendMessage(message, couponId, StringUtils.EMPTY);
            return;
        }
        // 权益中心权益核销消息
        if (CouponSendSceneEnum.CONSUMABLES_PACKAGE.getCode().equals(sceneCode)
                || CouponSendSceneEnum.ULTRA_FIRST_OWNER_NEED_MAINTENANCE.getCode().equals(sceneCode)
                || CouponSendSceneEnum.ULTRA_FIRST_OWNER_TIRE_REPAIR.getCode().equals(sceneCode)
                || CouponSendSceneEnum.OPTIONAL_TRACK_PACKAGE_COUPON_DISTRIBUTION.getCode().equals(sceneCode)
                || CouponSendSceneEnum.YU7_NEED_MAINTENANCE.getCode().equals(sceneCode)
                || CouponSendSceneEnum.ULTRA_LIMIT_BOTTOM_PLATE.getCode().equals(sceneCode)) {
            // 首任车主权益-按需保养券 + 首任车主权益-补胎券 + 耗材券 + 选配赛道包发券 + YU7按需保养券
            carEquityPerformanceMqProducer.sendMessage(message, couponId, "COUPON");
        }

    }

    @Override
    public Message<?> buildMessage(MessageBuildContext context) {
        String sceneCode = context.getSceneCode();
        CouponPo couponPo = context.getCouponPo();
        if (CouponSendSceneEnum.SERVICE_PACKAGE.getCode().equals(sceneCode)) {
            // 服务包
            return messageConvert.buildCouponUseMessage(context.getCouponUsePushContext(), couponPo, sceneCode, context.getProfile());
        } else if (CouponSendSceneEnum.CONSUMABLES_PACKAGE.getCode().equals(sceneCode)
                || CouponSendSceneEnum.ULTRA_FIRST_OWNER_NEED_MAINTENANCE.getCode().equals(sceneCode)
                || CouponSendSceneEnum.ULTRA_FIRST_OWNER_TIRE_REPAIR.getCode().equals(sceneCode)
                || CouponSendSceneEnum.OPTIONAL_TRACK_PACKAGE_COUPON_DISTRIBUTION.getCode().equals(sceneCode)
                || CouponSendSceneEnum.YU7_NEED_MAINTENANCE.getCode().equals(sceneCode)
                || CouponSendSceneEnum.ULTRA_LIMIT_BOTTOM_PLATE.getCode().equals(sceneCode)) {
            // 首任车主权益-按需保养券 + 首任车主权益-补胎券 + 耗材券 + 选配赛道包发券 + YU7按需保养券
            String equityKey = equityKeyMap.get(sceneCode);
            return messageConvert.buildCarEquityPerformanceMessage(context.getCouponUsePushContext(), couponPo, equityKey);
        } else {
            log.error("CarAfterSalePushService error. unknown sceneCode: {}, context: {}", sceneCode, GsonUtil.toJson(context));
            return null;
        }

    }

    @Override
    public Set<String> getSceneSet() {
        return sceneSet;
    }

    @Override
    public BizPlatformEnum getBizType() {
        return BizPlatformEnum.CAR_AFTER_SALE;
    }
}
