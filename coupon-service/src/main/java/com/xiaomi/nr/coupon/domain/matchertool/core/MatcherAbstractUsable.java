package com.xiaomi.nr.coupon.domain.matchertool.core;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.*;
import com.xiaomi.nr.coupon.domain.matchertool.entity.nocodeusable.NoCodeUsableReqDo;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave.UserCouponListMapper;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 3C领域
 *
 * <AUTHOR>
 * @date 2024/10/14
 */
@Slf4j
public abstract class MatcherAbstractUsable extends MatcherAbstract {

    @Resource
    protected UserCouponListMapper userCouponListMapper;

    /**
     * 匹配过程
     *
     * @param ctx .
     * @throws BizError .
     */
    @Override
    public void matching(MatcherContextDo ctx) {
        if (MapUtils.isEmpty(ctx.getUserCouponMap())) {
            return;
        }

        List<MatcherRespItemDo> keyResp = new ArrayList<>();
        Map<Long, List<MatcherGoodsItemDo>> validGoods = new HashMap<>();
        for (Map.Entry<Long, CouponPo> couponItem : ctx.getUserCouponMap().entrySet()) {
            try {
                MatcherRespItemDo respItem = new MatcherRespItemDo();
                respItem.setUserCouponId(couponItem.getValue().getId());
                respItem.setConfigId(couponItem.getValue().getTypeId());

                //如果确定券配置匹配不通过，则不再匹配
                if (ctx.getInvalidConfigIds().containsKey(respItem.getConfigId())) {
                    respItem.setErrCtx(ctx.getInvalidConfigIds().get(respItem.getConfigId()));
                    keyResp.add(respItem);
                    continue;
                }

                MatcherErrContextDo errCtx = getCheckTool().check(ctx, respItem);
                if (Objects.nonNull(errCtx)) {
                    respItem.setErrCtx(errCtx);
                    keyResp.add(respItem);
                    //如果是券配置匹配不通过，则记下券配置匹配不通过的原因
                    if (errCtx.isInvalidConfig()) {
                        if (Objects.isNull(ctx.getInvalidConfigIds())) {
                            ctx.setInvalidConfigIds(new HashMap<>());
                        }
                        ctx.getInvalidConfigIds().put(respItem.getConfigId(), errCtx);
                    }
                    continue;
                } else {
                    keyResp.add(respItem);
                }

                if (validGoods.containsKey(couponItem.getValue().getTypeId())) {
                    continue;
                }
                List<MatcherGoodsItemDo> goodsList = getValidGoods(ctx, respItem);
                if (CollectionUtils.isNotEmpty(goodsList)) {
                    validGoods.put(couponItem.getValue().getTypeId(), goodsList);
                }
            } catch (Exception e) {
                log.error("MatcherAbstract, matching execute error, coupon:{}, ctx:{}", GsonUtil.toJson(couponItem), GsonUtil.toJson(ctx), e);
            }
        }
        ctx.setKeyResp(keyResp);
        ctx.setValidGoods(validGoods);
    }

    @Override
    public MatcherContextDo newContext(MatcherBaseReqDo req) throws BizError {
        NoCodeUsableReqDo p = (NoCodeUsableReqDo) req;
        if (Objects.isNull(p)) {
            throw ExceptionHelper.create(GeneralCodes.InternalError, "可用匹配器入参不符合要求");
        }

        MatcherContextDo ctx = new MatcherContextDo();

        ctx.setUserId(p.getUserId());
        ctx.setBizPlatform(p.getBizPlatform());
        ctx.setClientId(p.getClientId());
        ctx.setOrgCode(p.getOrgCode());
        ctx.setOuterGoodsList(p.getGoodsList());
        ctx.setCheckBizPlatform(p.isCheckBizPlatform());
        ctx.setCheckUserChannelAndOrgCode(p.isCheckUserChannelAndOrgCode());
        ctx.setCheckConfigGoodsInclude(p.isCheckConfigGoodsInclude());
        ctx.setCheckGlobalExcludeGoods(p.isCheckGlobalExcludeGoods());
        ctx.setCheckGlobalCouponExcludeGoods(p.isCheckGlobalCouponExcludeGoods());
        ctx.setSort(p.isSort());

        ctx.setUserCouponIdList(p.getUserCouponIdList());
        ctx.setCityId(p.getCityId());
        ctx.setShoppingMode(p.getShoppingMode());
        ctx.setShipmentId(p.getShipmentId());

        ctx.setCheckShipmentId(p.isCheckShipmentId());
        ctx.setCheckUserCouponStatus(p.isCheckUserCouponStatus());
        ctx.setCheckUserCouponTime(p.isCheckUserCouponTime());
        ctx.setCheckConfigRegion(p.isCheckConfigRegion());
        ctx.setCheckUserCouponSpecialStore(p.isCheckUserCouponSpecialStore());
        return ctx;
    }

    @Override
    public void specialInitResource(MatcherContextDo ctx) throws BizError {
        if ((Objects.nonNull(ctx.getUserId()) && ctx.getUserId() <= 0) || Objects.isNull(ctx.getUserId())) {
            ctx.setUserId(null);
            return;
        }

        if (CollectionUtils.isNotEmpty(ctx.getUserCouponIdList())) {
            ctx.setUserCouponIdList(ctx.getUserCouponIdList().stream().filter(e -> Objects.nonNull(e) && e > 0).distinct().collect(Collectors.toList()));
        }

        List<CouponPo> userCouponList;
        if (CollectionUtils.isNotEmpty(ctx.getUserCouponIdList())) {
            userCouponList = userCouponListMapper.getCouponByUidCouponIdList(ctx.getUserId(), ctx.getUserCouponIdList());
        } else {
            userCouponList = userCouponListMapper.getProductUserCoupon(ctx.getUserId(), ctx.getBizPlatform(), ctx.getNowUnixSecond());
            if (CollectionUtils.isNotEmpty(userCouponList)) {
                userCouponList = userCouponList.stream().filter(e -> ctx.getBizPlatform().contains(e.getBizPlatform())).collect(Collectors.toList());
            }
        }

        genUserCouponInfo(ctx, userCouponList);
    }

    /**
     * 生成用户优惠券信息
     *
     * @param ctx               ctx
     * @param userCouponList    用户券列表
     * @throws BizError         bizError
     */
    protected void genUserCouponInfo(MatcherContextDo ctx, List<CouponPo> userCouponList) throws BizError {
        if (CollectionUtils.isNotEmpty(userCouponList)) {
            Map<Long, CouponPo> userCouponMap = userCouponList.stream().collect(Collectors.toMap(CouponPo::getId, e -> e));
            ctx.setUserCouponMap(userCouponMap);
            List<Long> configIds = userCouponList.stream().map(CouponPo::getTypeId).distinct().collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(configIds)) {
                Map<Long, CouponConfigItem> configInfoMap = getCouponConfigInfos(configIds);
                if (MapUtils.isEmpty(configInfoMap)) {
                    throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_NOT_FOUND, "无法获取到有效的券配置信息");
                }
                ctx.setConfigInfoMap(configInfoMap);
            }
        }
    }

}
