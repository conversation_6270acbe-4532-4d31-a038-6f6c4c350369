package com.xiaomi.nr.coupon.domain.coupontrade;

import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.api.dto.trade.CouponLockItem;
import com.xiaomi.nr.coupon.api.dto.trade.GetCheckoutCouponListV2Request;
import com.xiaomi.nr.coupon.api.dto.trade.LockCouponRequest;
import com.xiaomi.nr.coupon.enums.coupon.SubmitTypeEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.PromotionType;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @description: 入参校验
 * @author: hejiapeng
 * @Date 2023/1/11 10:43 上午
 * @Version: 1.0
 **/
@Component
@Slf4j
public class CouponTradeCheckService {

    /**
     * 校验优惠券锁定入参方法
     *
     * @param request
     * @throws BizError
     */
    public void checkLockRequestParam(LockCouponRequest request) throws BizError {

        CheckCommonRequestParam(request.getUserId(), request.getOrderId(), request.getOffline());

        if (Objects.isNull(BizPlatformEnum.valueOf(request.getBizPlatform()))) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "业务领域不能为空");
        }

        if (BizPlatformEnum.CAR_AFTER_SALE.getCode().equals(request.getBizPlatform()) && Strings.isEmpty(request.getVid())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "VID不能为空");
        }

        Integer submitType = Optional.ofNullable(request.getSubmitType()).orElse(0);
        if (CollectionUtils.isEmpty(request.getCouponItems()) && SubmitTypeEnum.REPLACE.getCode() != submitType) {
            log.warn("UserCouponService.lockUserCoupon request:{}, err:{}", request, "下单使用优惠券信息不正确");
            throw ExceptionHelper.create(GeneralCodes.ParamError, "下单使用优惠券信息不正确");
        }

        if (CollectionUtils.isNotEmpty(request.getCouponItems())) {
            for (CouponLockItem couponItem : request.getCouponItems()) {
                if (couponItem.getCouponId() == null || couponItem.getCouponId() <= 0) {
                    if (StringUtils.isEmpty(couponItem.getCouponCode())) {
                        throw ExceptionHelper.create(GeneralCodes.ParamError, "优惠券信息不正确");
                    }
                }
                if (couponItem.getReplaceMoney() == null || couponItem.getReduceExpress() == null) {
                    throw ExceptionHelper.create(GeneralCodes.ParamError, "优惠券信息不正确");
                }
            }
        }
        /*if (request.getClientId() == null || request.getClientId() <= 0) {
            log.warn("UserCouponService.lockUserCoupon request:{}, err:{}", request, "下单来源信息不正确");
            throw ExceptionHelper.create(GeneralCodes.ParamError, "下单来源信息不正确");
        }*/

        if (request.getOrgCode() == null) {
            request.setOrgCode(StringUtils.EMPTY);
        }
    }

    /**
     * 校验优惠券核销入参公共方法
     *
     * @param userId
     * @param orderId
     * @param offline
     * @throws BizError
     */
    public void CheckCommonRequestParam(long userId, long orderId, Integer offline) throws BizError {
        if (userId <= 0) {
            log.warn("UserCouponService.lockUserCoupon userId:{},orderId:{},err:{}", userId, orderId, "用户信息不正确");
            throw ExceptionHelper.create(GeneralCodes.ParamError, "用户信息不正确");
        }
        if (orderId <= 0 || offline == null) {
            log.warn("UserCouponService.lockUserCoupon userId:{},orderId:{},err:{}", userId, orderId, "订单信息不正确");
            throw ExceptionHelper.create(GeneralCodes.ParamError, "订单信息不正确");
        }
    }

    /**
     * 校验结算可用券入参
     *
     * @param request
     * @throws BizError
     */
    public void checkCheckoutCouponListV2Param(GetCheckoutCouponListV2Request request) throws BizError {
        if (request.getUserId() == null || request.getUserId() <= 0) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "小米ID不能为空");
        }

        if (CollectionUtils.isEmpty(request.getBizPlatform()) || request.getBizPlatform().size() > 1 || Objects.isNull(BizPlatformEnum.valueOf(request.getBizPlatform().get(0)))) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "业务领域不符合要求");
        }

        if (request.getBizPlatform().contains(BizPlatformEnum.CAR_AFTER_SALE.getCode()) && Strings.isBlank(request.getVid())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "必须传入VID");
        }

        if (Strings.isNotBlank(request.getVid()) && request.getVid().length() != 17) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "VID不符合要求");
        }

        if (request.getBizPlatform().contains(BizPlatformEnum.RETAIL.getCode())) {
            if (request.getClientId() == null || request.getClientId() <= 0) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "应用ID不能为空");
            }

            if (request.getShipmentId() == null) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "履约方式不能为空");
            }
        }

        if (CollectionUtils.isNotEmpty(request.getCouponIds()) && StringUtils.isNotEmpty(request.getCouponCode())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "优惠券ID和优惠码不能同时使用");
        }

        if (CollectionUtils.isNotEmpty(request.getCouponIds())) {
            for (Long couponId : request.getCouponIds()) {
                if (Objects.isNull(couponId) || couponId <= 0) {
                    throw ExceptionHelper.create(GeneralCodes.ParamError, "优惠券ID存在不合法的值");
                }
            }
        }

        if (StringUtils.isNotEmpty(request.getCouponCode()) && StringUtils.isBlank(request.getCouponCode())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "优惠码存不合法的值");
        }

        /*if (Objects.nonNull(request.getSubmitType()) && request.getSubmitType() == SubmitTypeEnum.REPLACE.getCode() && request.getUsedCoupon() == null) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "已用券ID不能为空");
        }*/

        // 优惠类型校验
        List<Integer> promotionTypeList = request.getPromotionTypeList();
        if (CollectionUtils.isNotEmpty(promotionTypeList)) {
            boolean illegalPromotionExists = promotionTypeList.stream()
                    .anyMatch(promotionType -> Objects.isNull(promotionType) || Objects.isNull(PromotionType.getPromotionType(promotionType)));
            if (illegalPromotionExists) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "优惠类型非法");
            }
        }
    }

}
