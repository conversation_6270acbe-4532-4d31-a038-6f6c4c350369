package com.xiaomi.nr.coupon.enums.ecard;


import lombok.Getter;

/**
 * 礼品卡业务场景
 */
@Getter
public enum EcardBusinessTypeEnum {

    AIRCONDITIONER_RENEW(1, "空调换新退单补发"),

    STANDARD_RENEW(2, "标准换新现金券"),

    /**
     * 给用户开票 ，不给电信开票
     */
    COIN_TELECOM_DOOR(3, "米网电信上门服务发券"),

    /**
     * 不给用户开票，给电信开票
     */
    COIN_TELECOM_DOOR_INVOICE(4, "米网电信上门服务发券"),

    ;




    private final Integer value;
    private final String name;

    EcardBusinessTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }


}
