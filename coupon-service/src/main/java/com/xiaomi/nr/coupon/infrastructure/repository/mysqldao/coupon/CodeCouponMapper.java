package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon;


import com.xiaomi.nr.coupon.domain.coupon.model.CouponCodeQueryParam;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponcode.po.CouponCodePo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * 码券Db访问接口
 *
 */
@Repository
public interface CodeCouponMapper {

    String QUERY_FIELD = "id, coupon_code, coupon_index, type_id, batch_id, start_time, end_time, stat, order_id, user_id, coupon_id, use_mode, use_time, replace_money, reduce_express";

    /**
     * 获取券码信息
     *
     * @param index String
     * @return List<TbCodecoupon>
     */
    @Select("select " + QUERY_FIELD +
            " from tb_codecoupon" +
            " where coupon_index=#{index}")
    List<CouponCodePo> getCouponCode(@Param("index") String index);

    /**
     * 获取券码信息
     *
     * @param index String
     * @return List<TbCodecoupon>
     */
    @Select("select " + QUERY_FIELD +
            " from tb_codecoupon" +
            " where user_id = #{userId} and coupon_index=#{index} and order_id = #{orderId}")
    CouponCodePo getCouponCodeByOrderId(@Param("index") String index, @Param("userId") Long userId, @Param("orderId") Long orderId);

    /**
     * 更新券状态
     *
     * @param id            券码id
     * @param userId        用户ID
     * @param statOld       原本状态
     * @param statNew       新状态
     * @param orderId       订单ID
     * @param replaceMoney  优惠金额
     * @param reduceExpress 邮费优惠金额
     * @param useTime       使用时间
     * @param offline       是否线下
     * @param orgCode       门店Code
     * @return 更新数
     */
    @Update("update tb_codecoupon " +
            "set stat=#{statNew}, " +
            "order_id=#{orderId}, " +
            "user_id=#{userId}, " +
            "replace_money=#{replaceMoney}, " +
            "reduce_express=#{reduceExpress}, " +
            "use_time=#{useTime}, " +
            "offline=#{offline}, " +
            "org_code=#{orgCode} " +
            "where id = #{id} and stat=#{statOld}")
    int updateStat(@Param("id") Long id, @Param("userId") Long userId,
                   @Param("statOld") String statOld, @Param("statNew") String statNew,
                   @Param("orderId") Long orderId, @Param("replaceMoney") BigDecimal replaceMoney,
                   @Param("reduceExpress") BigDecimal reduceExpress, @Param("useTime") Long useTime,
                   @Param("offline") Integer offline, @Param("orgCode") String orgCode);

    /**
     * 更新券状态
     *
     * @param id            券码编号
     * @param userId        用户ID
     * @param statOld       原本状态
     * @param statNew       新状态
     * @param orderId       订单ID
     * @return 更新数
     */
    @Update("update tb_codecoupon " +
            "set stat=#{statNew}, " +
            "order_id=#{orderId}, " +
            "user_id=#{userId} " +
            "where id=#{id} and stat=#{statOld}")
    int consumeCoupon(@Param("id") Long id, @Param("userId") Long userId, @Param("statOld") String statOld,
                   @Param("statNew") String statNew, @Param("orderId") Long orderId);

    /**
     * 获取优惠码
     *
     * @param queryParam queryParam
     * @return List<CouponCodePo>
     */
    @Select("<script>" +
            "select " + QUERY_FIELD +
            " from tb_codecoupon where id != 0 " +
            "<if test='couponIndex != null'> and coupon_index=#{couponIndex} </if>" +
            "<if test='stat != null'> and stat=#{stat} </if>" +
            "<if test='sendType != null'> and send_type=#{sendType} </if>" +
            "<if test='orderId != null'> and order_id=#{orderId} </if>" +
            "<if test='useMode != null'> and use_mode=#{useMode} </if>" +
            "</script>")
    List<CouponCodePo> getCouponCodeInfo(CouponCodeQueryParam queryParam);
}
