package com.xiaomi.nr.coupon.domain.matchertool.checktools;

import com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers.*;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherCheckToolAbstract;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/14
 */
@Component
public class CarShopProductNoCodeUsableCheckTool extends MatcherCheckToolAbstract {

    private final List<String> toolList = new ArrayList<>();

    @PostConstruct
    public void init() {
        toolList.add(BaseInfo.class.getName());
        toolList.add(ConfigBizPlatform.class.getName());
        toolList.add(ProductConfigUseChannel.class.getName());
        toolList.add(UserNoCodeProductUseTime.class.getName());
        toolList.add(UserNoCodeCouponStatus.class.getName());
        toolList.add(ConfigGoodsValid.class.getName());
        toolList.add(UserNoCodePromotionType.class.getName());
    }

    @Override
    public List<String> getTools() {
        return toolList;
    }

}
