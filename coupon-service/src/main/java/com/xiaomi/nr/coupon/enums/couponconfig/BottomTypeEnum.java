package com.xiaomi.nr.coupon.enums.couponconfig;

/**
 * 门槛类型 枚举
 *
 * <AUTHOR>
 */
public enum BottomTypeEnum {

    /**
     * 满元
     */
    Money(1, "money", "满元"),

    /**
     * 满件
     */
    Count(2, "count", "满件"),

    /**
     * 满件且满元
     */
    MoneyCount(5, "money_count", "满元且满件"),

    /**
     * 每满元
     */
    EveMoney(3, "eve_money", "每满元"),

    /**
     * 每满件
     */
    EveCount(4, "eve_count", "每满件");

    private final int code;
    private final String value;
    private final String name;

    BottomTypeEnum(int code, String value, String name) {
        this.code = code;
        this.value = value;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static BottomTypeEnum findByCode(int code) {
        BottomTypeEnum[] values = BottomTypeEnum.values();
        for (BottomTypeEnum item : values) {
            if (item.getCode() == code) {
                return item;
            }
        }
        return null;
    }

    public static BottomTypeEnum findByValue(String value) {
        BottomTypeEnum[] values = BottomTypeEnum.values();
        for (BottomTypeEnum item : values) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }
}

