package com.xiaomi.nr.coupon.infrastructure.repository.mq;

import com.xiaomi.nr.coupon.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ExtRocketMQTemplateConfiguration;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;

/**
 * @description 权益核销消息推送
 * <AUTHOR>
 * @date 2025-01-08 14:21
*/
@Slf4j
@ExtRocketMQTemplateConfiguration(group="${rocketmq.carEquityPerformance-producer.group}",nameServer="${rocketmq.carEquityPerformance.name-server}")
public class CarEquityPerformanceMqProducer extends RocketMQTemplate implements Produce<Message<?>>{

    @Value("${rocketmq.carEquityPerformance.topic}")
    private String carEquityPerformancePushTopic;

    @Override
    public void sendMessage(Message<?> message, Long couponId, String tag) {
        try {
            super.send(carEquityPerformancePushTopic + ":" + tag, message);
            log.info("CarEquityPerformanceMqProducer sendMessage. couponId: {}, tag: {}, message: {}", couponId, tag, GsonUtil.toJson(message));
        } catch (Exception e) {
            log.error("CarEquityPerformanceMqProducer send error couponId:{}", couponId, e);
            super.send(carEquityPerformancePushTopic + ":" + tag, message);
        }
    }

}
