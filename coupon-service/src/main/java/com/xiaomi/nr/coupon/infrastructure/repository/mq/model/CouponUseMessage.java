package com.xiaomi.nr.coupon.infrastructure.repository.mq.model;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-01-08 19:57
*/
@Data
@Builder
public class CouponUseMessage implements Serializable {
    private static final long serialVersionUID = 5715690682084296567L;

    private CouponUsePushMqHeader header;

    private Body body;

    @Data
    @Builder
    public static class Body implements Serializable {
        private static final long serialVersionUID = 8375650068733432492L;

        /**
         * 唯一键
         */
        private String unikey;

        /**
         * 用户ID
         */
        private Long userId;

        /**
         * VID
         */
        private String vid;

        /**
         * 配置ID
         */
        private Long configId;

        /**
         * 优惠券ID
         */
        private Long couponId;

        /**
         * 状态
         */
        private Integer status;

        /**
         * 修改时间（毫秒）
         */
        private Long modifyTimeMillis;

        /**
         * 修改时间（秒）
         */
        private Long modifyTime;

        /**
         * 订单ID
         */
        private Long orderId;

        /**
         * 业务平台
         */
        private Integer bizPlatform;
    }
}
