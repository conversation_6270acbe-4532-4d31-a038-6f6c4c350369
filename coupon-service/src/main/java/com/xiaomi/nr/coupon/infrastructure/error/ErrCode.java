package com.xiaomi.nr.coupon.infrastructure.error;

import com.xiaomi.youpin.infra.rpc.errors.ErrorCode;

/**
 * 错误码常量
 * <p>
 * 错误码值分三部分 400{scope}{internalCode},
 * 400:          固定值
 * scope:        定义级别 {@link com.xiaomi.youpin.infra.rpc.errors.Scopes}
 * internalCode：错误级别的确切问题
 *
 * </p>
 *
 * <AUTHOR>
 */
public class ErrCode {

    /**
     * 优惠券配置
     */
    public static final ErrorCode COUPON = ErrorCode.createOnce(ErrScope.COUPON, 100);

    /****************************************** 领券相关错误码 101-199 ***********************************/

    /**
     * 优惠券配置无效
     */
    public static final ErrorCode ASSIGN_COUPON_NOT_FOUND = ErrorCode.createOnce(ErrScope.COUPON, 101);

    /**
     * 优惠券配置已下线
     */
    public static final ErrorCode ASSIGN_COUPON_STATUS_OFFLINE = ErrorCode.createOnce(ErrScope.COUPON, 102);

    /**
     * 优惠券配置有误
     */
    public static final ErrorCode ASSIGN_COUPON_LOSE_EFFICACY = ErrorCode.createOnce(ErrScope.COUPON, 103);

    /**
     * 优惠券发放场景无效
     */
    public static final ErrorCode ASSIGN_COUPON_SCENE_INVALID = ErrorCode.createOnce(ErrScope.COUPON, 104);

    /**
     * 优惠券发放场景已下线
     */
    public static final ErrorCode ASSIGN_COUPON_SCENE_OFFLINE = ErrorCode.createOnce(ErrScope.COUPON, 105);

    /**
     * APPID无权发放该场景优惠券
     */
    public static final ErrorCode ASSIGN_COUPON_APPID_NOAUTH = ErrorCode.createOnce(ErrScope.COUPON, 106);

    /**
     * 与优惠券发放场景不匹配！
     */
    public static ErrorCode ASSIGN_COUPON_SCENE_MISMATCH = ErrorCode.createOnce(ErrScope.COUPON, 107);

    /**
     * 与券场景发放方式不匹配
     */
    public static final ErrorCode ASSIGN_COUPON_SENDMODE_MISMATCH = ErrorCode.createOnce(ErrScope.COUPON, 108);

    /**
     * 优惠券发放数量已达限制
     */
    public static final ErrorCode ASSIGN_COUPON_OVER_LIMITED = ErrorCode.createOnce(ErrScope.COUPON, 109);

    /**
     * 用户可领券数量已达限制
     */
    public static final ErrorCode ASSIGN_COUPON_USER_LIMITED = ErrorCode.createOnce(ErrScope.COUPON, 110);

    /**
     * 优惠券领取时间未到
     */
    public static final ErrorCode ASSIGN_COUPON_NOSTART_TIME = ErrorCode.createOnce(ErrScope.COUPON, 111);

    /**
     * 优惠券领取时间已结束
     */
    public static final ErrorCode ASSIGN_COUPON_OVER_TIME = ErrorCode.createOnce(ErrScope.COUPON, 112);


    /**
     * 与券场景投放方式不匹配
     */
    public static final ErrorCode ASSIGN_COUPON_ASSIGNMODE_MISMATCH = ErrorCode.createOnce(ErrScope.COUPON, 113);

    /**
     * 券配置上的投放场景码为空
     */
    public static ErrorCode ASSIGN_COUPON_SCENE_CODE_EMPTY = ErrorCode.createOnce(ErrScope.COUPON, 114);

    /**
     * 券配置上的所属业务领域不匹配
     */
    public static ErrorCode COUPON_BIZ_PLATFORM_MISMATCH = ErrorCode.createOnce(ErrScope.COUPON, 115);

    /**
     * 券作废失败
     */
    public static final ErrorCode INVALID_COUPON_FAIL = ErrorCode.createOnce(ErrScope.COUPON, 116);

    /**
     * 非公开推广普适券
     */
    public static ErrorCode NOT_PUBLIC_PROMOTION = ErrorCode.createOnce(ErrScope.COUPON, 117);


    /******************************************用券相关错误码 201-299 ***********************************/

    /**
     * 优惠劵类型错误
     */
    public static final ErrorCode USE_COUPON_TYPE_ERROR = ErrorCode.createOnce(ErrScope.COUPON, 201);

    /**
     * 优惠劵渠道错误
     */
    public static final ErrorCode USE_COUPON_CHANNEL_MISMATCH = ErrorCode.createOnce(ErrScope.COUPON, 202);

    /**
     * 优惠劵使用门店不匹配
     */
    public static final ErrorCode USE_COUPON_ORGCODE_MISMATCH = ErrorCode.createOnce(ErrScope.COUPON, 203);

    /**
     * 优惠劵包含商品错误
     */
    public static final ErrorCode USE_COUPON_GOODS_MISMATCH = ErrorCode.createOnce(ErrScope.COUPON, 204);

    /**
     * 优惠劵区域限制
     */
    public static final ErrorCode USE_COUPON_AREA_LIMIT = ErrorCode.createOnce(ErrScope.COUPON, 205);

    /**
     * 优惠劵配置失效
     */
    public static final ErrorCode USE_COUPON_LOSE_EFFICACY = ErrorCode.createOnce(ErrScope.COUPON, 206);

    /**
     * 优惠券有效时间错误
     */
    public static final ErrorCode USE_COUPON_TIME_INVALID = ErrorCode.createOnce(ErrScope.COUPON, 207);

    /**
     * 履约方式不匹配
     */
    public static final ErrorCode USE_COUPON_SHIPMENT_MISMATCH = ErrorCode.createOnce(ErrScope.COUPON, 208);

    /**
     * 专店专用不匹配
     */
    public static final ErrorCode USE_COUPON_ORGCODE_UNUSED = ErrorCode.createOnce(ErrScope.COUPON, 209);

    /**
     * 券不存在
     */
    public static final ErrorCode USE_COUPON_NOT_FOUND = ErrorCode.createOnce(ErrScope.COUPON, 210);

    /**
     * 券状态错误
     */
    public static final ErrorCode USE_COUPON_STAT_ERROR = ErrorCode.createOnce(ErrScope.COUPON, 211);

    /**
     * 券回滚失败
     */
    public static final ErrorCode USE_COUPON_RETURN_ERROR = ErrorCode.createOnce(ErrScope.COUPON, 212);

    /**
     * 无法获取有效门店信息
     */
    public static ErrorCode USE_COUPON_ORG_INFO_CACHE_NULL = ErrorCode.createOnce(ErrScope.COUPON, 213);

    /**
     * 优惠券使用时间未到
     */
    public static final ErrorCode USE_COUPON_NOSTART_TIME = ErrorCode.createOnce(ErrScope.COUPON, 214);

    /**
     * 优惠券使用时间已结束
     */
    public static final ErrorCode USE_COUPON_OVER_TIME = ErrorCode.createOnce(ErrScope.COUPON, 215);

    /**
     * 仅限指定车辆可用（VID）
     */
    public static final ErrorCode USE_COUPON_VID_NOT_EQUATION = ErrorCode.createOnce(ErrScope.COUPON, 216);

    /**
     * 业务领域
     */
    public static final ErrorCode USE_COUPON_BIZ_PLATFORM_MISMATCH = ErrorCode.createOnce(ErrScope.COUPON, 217);

    /**
     * 服务场景
     */
    public static final ErrorCode USE_COUPON_SERVICE_SCENE_MISMATCH = ErrorCode.createOnce(ErrScope.COUPON, 218);

    /**
     * 使用次数
     */
    public static final ErrorCode USE_COUPON_USED_TIMES_UPDATE_ERROR = ErrorCode.createOnce(ErrScope.COUPON, 219);

    /**
     * 核销流水
     */
    public static final ErrorCode USE_COUPON_LOG_ERROR = ErrorCode.createOnce(ErrScope.COUPON, 220);

    /**
     * 优惠类型错误
     */
    public static final ErrorCode USE_COUPON_PROMOTION_TYPE_ERROR = ErrorCode.createOnce(ErrScope.COUPON, 221);


    /****************************优惠码相关错误码 301-399 定好后不要动了，服务端有按码做相关标识处理 *****************************/

    /**
     * 兑换参数有误
     */
    public static final ErrorCode COUPON_CODE_PARAM = ErrorCode.createOnce(ErrScope.COUPON, 301);

    /**
     * 优惠码不存在
     */
    public static final ErrorCode COUPON_CODE_ILLEGAL = ErrorCode.createOnce(ErrScope.COUPON, 302);

    /**
     * 优惠码无效
     */
    public static final ErrorCode COUPON_CODE_INVALID = ErrorCode.createOnce(ErrScope.COUPON, 303);

    /**
     * 优惠码已过期
     */
    public static final ErrorCode COUPON_CODE_EXPIRE = ErrorCode.createOnce(ErrScope.COUPON, 304);

    /**
     * 优惠码未到可兑换时间
     */
    public static final ErrorCode COUPON_CODE_TIME_NO_START = ErrorCode.createOnce(ErrScope.COUPON, 305);

    /**
     * 获取优惠码信息失败
     */
    public static final ErrorCode COUPON_CODE_INFO_FAIL = ErrorCode.createOnce(ErrScope.COUPON, 306);

    /**
     * 兑换频率过快
     */
    public static final ErrorCode COUPON_CODE_USER_EXCHANGE_RATE = ErrorCode.createOnce(ErrScope.COUPON, 307);

    /**
     * 优惠码已被兑换
     */
    public static final ErrorCode COUPON_CODE_USED = ErrorCode.createOnce(ErrScope.COUPON, 308);

    /**
     * 优惠码兑换失败
     */
    public static final ErrorCode COUPON_CODE_EXCHANGE_FAIL = ErrorCode.createOnce(ErrScope.COUPON, 309);

    /**
     * 礼品卡
     */
    public static final ErrorCode ECARD = ErrorCode.createOnce(ErrScope.ECARD, 101);


}
