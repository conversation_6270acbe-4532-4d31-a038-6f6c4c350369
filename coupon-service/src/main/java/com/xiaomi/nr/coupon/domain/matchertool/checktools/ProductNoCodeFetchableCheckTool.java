package com.xiaomi.nr.coupon.domain.matchertool.checktools;

import com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers.*;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherCheckToolAbstract;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * 产品站-无码券可领校验工具
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Component
public class ProductNoCodeFetchableCheckTool extends MatcherCheckToolAbstract {

    private final List<String> toolList = new ArrayList<>();

    @PostConstruct
    public void init() {
        toolList.add(BaseInfo.class.getName());
        toolList.add(ConfigBizPlatform.class.getName());
        toolList.add(ConfigStatus.class.getName());
        toolList.add(ConfigFetchTime.class.getName());
        toolList.add(ConfigGoodsValid.class.getName());
        toolList.add(SceneCode.class.getName());
        toolList.add(SceneStatus.class.getName());
        toolList.add(SceneSendMode.class.getName());
        toolList.add(SceneAssignMode.class.getName());
        toolList.add(SceneAssignRights.class.getName());
        toolList.add(ConfigFetchLimit.class.getName());
        toolList.add(UserFetchLimit.class.getName());
        toolList.add(ProductConfigUseChannelOrgCode.class.getName());
    }

    @Override
    public List<String> getTools() {
        return toolList;
    }

}
