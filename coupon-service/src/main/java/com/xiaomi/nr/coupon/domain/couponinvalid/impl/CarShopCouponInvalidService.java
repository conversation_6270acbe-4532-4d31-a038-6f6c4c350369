package com.xiaomi.nr.coupon.domain.couponinvalid.impl;

import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponCheckDto;
import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponReq;
import com.xiaomi.nr.coupon.api.dto.couponinvalid.InvalidCouponResp;
import com.xiaomi.nr.coupon.domain.couponinvalid.CouponInvalidService;
import com.xiaomi.nr.coupon.domain.couponinvalid.constant.CouponInvalidConst;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.CouponServiceTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.UserCouponRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.xiaomi.nr.coupon.domain.couponinvalid.constant.CouponInvalidConst.CAR_AFTER_SALE_COUPON_CANT_INVALID_REASON;
import static com.xiaomi.nr.coupon.domain.couponinvalid.constant.CouponInvalidConst.CAR_SHOP_VIP_COUPON_CANT_INVALID_REASON;

/**
 * <AUTHOR>
 * @date 2024/5/19
 */
@Component
@Slf4j
public class CarShopCouponInvalidService extends CouponInvalidService {

    @Autowired
    private UserCouponRepository userCouponRepository;

    /**
     * 券作废校验
     *
     * @param req InvalidCouponReq
     * @return 不可作废券列表
     */
    @Override
    public List<InvalidCouponCheckDto> invalidCouponCheck(InvalidCouponReq req) throws BizError {

        // 1、查询DB
        List<CouponPo> couponPoList = userCouponRepository.getCouponPoList(req.getUserId(), req.getCouponIdList());

        // 2、状态、使用次数校验
        return checkCouponList(couponPoList);
    }

    /**
     * 券作废DB操作
     *
     * @param req InvalidCouponReq
     * @return resp
     */
    @Override
    public InvalidCouponResp invalidCoupon(InvalidCouponReq req) throws BizError {

        InvalidCouponResp resp = new InvalidCouponResp();
        resp.setSuccess(false);

        // 1、查询DB
        Long userId = req.getUserId();
        List<Long> couponIdList = req.getCouponIdList();
        List<CouponPo> couponPoList = userCouponRepository.getCouponPoList(userId, couponIdList);

        // 2、幂等校验
        if (couponPoList.stream().allMatch(po -> CouponStatusEnum.INVALID.getValue().equals(po.getStat()))) {
            resp.setSuccess(true);
            resp.setIdempotent(true);
            return resp;
        }

        // 3、可作废校验
        List<InvalidCouponCheckDto> cantInvalidCouponList = checkCouponList(couponPoList);
        if (CollectionUtils.isNotEmpty(cantInvalidCouponList)) {
            InvalidCouponCheckDto invalidCouponCheckDto = cantInvalidCouponList.get(0);
            resp.setFailReason(String.format(CouponInvalidConst.FAIL_REASON_TEMP, invalidCouponCheckDto.getCouponId(), invalidCouponCheckDto.getReason()));
            return resp;
        }


        // 4、DB作废操作
        userCouponRepository.invalidCoupon(userId, couponIdList);
        resp.setSuccess(true);

        return resp;
    }

    private List<InvalidCouponCheckDto> checkCouponList(List<CouponPo> couponPoList) {

        List<InvalidCouponCheckDto> cantInvalidCouponList = new ArrayList<>();
        for (CouponPo couponPo : couponPoList) {
            String stat = couponPo.getStat();
            // 状态非未使用
            if (!CouponStatusEnum.UNUSED.getValue().equals(stat) && !CouponStatusEnum.INVALID.getValue().equals(stat)) {
                String statName = Optional.ofNullable(CouponStatusEnum.findByValue(stat)).map(CouponStatusEnum::getName).orElse(stat);
                String cantInvalidReason = String.format(CAR_SHOP_VIP_COUPON_CANT_INVALID_REASON, couponPo.getTypeId(), statName);
                cantInvalidCouponList.add(new InvalidCouponCheckDto(couponPo.getId(), cantInvalidReason));
            }
        }
        return cantInvalidCouponList;
    }
}
