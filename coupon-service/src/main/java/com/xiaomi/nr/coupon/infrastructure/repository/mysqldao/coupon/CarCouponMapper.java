package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * 汽车专用券（目前只有汽车售后服务券在用）
 *
 * <AUTHOR>
 */
@Mapper
@Repository
public interface CarCouponMapper {

    String TB_COUPON_FIELD = "id, user_id, type_id, activity_id, start_time, end_time, days, stat, order_id, use_time, " +
            "expire_time, is_pass, admin_id, admin_name, add_time, send_type, from_order_id, replace_money, invalid_time, " +
            "offline, reduce_express, parent_id, send_channel, request_id, extend_info, biz_platform, vid, service_type, " +
            "times_limit, used_times";

    /**
     * 券写入
     *
     * @param coupon CouponPo
     * @return Integer
     */
    @Insert("insert into tb_car_coupon (" +
            "id, user_id, vid, type_id, activity_id, start_time, end_time, days, stat, order_id, use_time, " +
            "expire_time, is_pass, admin_id, admin_name, add_time, send_type, from_order_id, replace_money, invalid_time, " +
            "offline, reduce_express, parent_id, send_channel, biz_platform, request_id, extend_info, service_type, times_limit, used_times" +
            ") values(" +
            "#{coupon.id},#{coupon.userId},#{coupon.vid},#{coupon.typeId},#{coupon.activityId},#{coupon.startTime},#{coupon.endTime}," +
            "#{coupon.days},#{coupon.stat},#{coupon.orderId},#{coupon.useTime},#{coupon.expireTime},#{coupon.isPass}," +
            "#{coupon.adminId},#{coupon.adminName},#{coupon.addTime},#{coupon.sendType},#{coupon.fromOrderId},#{coupon.replaceMoney}," +
            "#{coupon.invalidTime},#{coupon.offline},#{coupon.reduceExpress},#{coupon.parentId},#{coupon.sendChannel},#{coupon.bizPlatform},#{coupon.requestId}," +
            "#{coupon.extendInfo},#{coupon.serviceType},#{coupon.timesLimit},#{coupon.usedTimes})")
    Integer insert(@Param("coupon") CouponPo coupon);

    /**
     * 获取幂等数据
     *
     * @param vid         String
     * @param sendChannel String
     * @param requestId   String
     * @param missionId   Long
     * @return List<CouponPo>
     */
    @Select("select id, user_id, type_id, activity_id, start_time, end_time, order_id, from_order_id, biz_platform, vid " +
            " from tb_car_coupon" +
            " where vid=#{vid} and send_channel=#{sendChannel} and request_id=#{requestId} and activity_id=#{missionId}")
    List<CouponPo> getIdemData(@Param("vid") String vid, @Param("sendChannel") String sendChannel, @Param("requestId") String requestId, @Param("missionId") Long missionId);


    /**
     * 根据vid和券Id获取用户券列表
     *
     * @param vid
     * @param couponIdList
     * @return
     */
    @Select("<script>select " + TB_COUPON_FIELD +
            " from tb_car_coupon " +
            " where vid=#{vid} " +
            " and id in <foreach collection='couponIdList' item='couponId' index='index' open='(' close=')' separator=','>#{couponId}</foreach> " +
            "</script>")
    List<CouponPo> getByCouponIdList(@Param("vid") String vid, @Param("couponIdList") List<Long> couponIdList);

    /**
     * 锁定用户券
     *
     * @param id            ID
     * @param vid           vid
     * @param statOld       原状态
     * @param orderId       订单ID
     * @param statNew       新状态
     * @param replaceMoney  抵扣钱
     * @param reduceExpress 抵扣邮费
     * @param offline       是否线下
     * @return 更新数
     */
    @Update("update tb_car_coupon " +
            "set stat=#{statNew}, " +
            "order_id=#{orderId}, " +
            "replace_money=#{replaceMoney}, " +
            "reduce_express=#{reduceExpress}, " +
            "offline=#{offline} " +
            "where id=#{id} and vid=#{vid} and stat=#{statOld}")
    int lockCoupon(@Param("id") Long id, @Param("vid") String vid, @Param("statOld") String statOld,
                   @Param("statNew") String statNew, @Param("orderId") Long orderId,
                   @Param("replaceMoney") BigDecimal replaceMoney, @Param("reduceExpress") BigDecimal reduceExpress,
                   @Param("offline") Integer offline);


    /**
     * 退还用户券
     *
     * @param couponIds     ID
     * @param vid           vid
     * @param statOld       原状态
     * @param orderId       订单ID
     * @param statNew       新状态
     * @param replaceMoney  抵扣钱
     * @param reduceExpress 抵扣邮费
     * @param offline       是否线下
     * @return 更新数
     */
    @Update("<script> " +
            "update tb_car_coupon " +
            "set stat=#{statNew}, " +
            "replace_money=#{replaceMoney}, " +
            "reduce_express=#{reduceExpress} " +
            "where vid = #{vid} and order_id = #{orderId} and stat = #{statOld} and id in " +
            "<foreach collection='couponIds' item='couponId' index='index' open='(' close=')' separator=','>#{couponId}</foreach> " +
            "</script>")
    int returnCoupon(@Param("couponIds") List<Long> couponIds, @Param("vid") String vid, @Param("statOld") String statOld,
                     @Param("statNew") String statNew, @Param("orderId") Long orderId,
                     @Param("replaceMoney") BigDecimal replaceMoney, @Param("reduceExpress") BigDecimal reduceExpress,
                     @Param("offline") Integer offline);

    /**
     * 核销用户券
     *
     * @param couponIds
     * @param vid
     * @param statOld
     * @param statNew
     * @param orderId
     * @param useTime
     * @return
     */
    @Update("<script> " +
            "update tb_car_coupon " +
            "set stat=#{statNew}, " +
            "use_time=#{useTime} " +
            "where vid=#{vid} and order_id=#{orderId} and stat=#{statOld} and id in " +
            "<foreach collection='couponIds' item='couponId' index='index' open='(' close=')' separator=','>#{couponId}</foreach> " +
            "</script>")
    int consumeCoupon(@Param("couponIds") List<Long> couponIds, @Param("vid") String vid, @Param("statOld") String statOld,
                      @Param("statNew") String statNew, @Param("orderId") Long orderId, @Param("useTime") Long useTime);



    @Update("<script> " +
            "update tb_car_coupon " +
            "set used_times = #{newUsedTimes} " +
            "where vid = #{vid} and used_times = #{oldUsedTimes} and id = #{couponId} and stat in " +
            "<foreach collection='statList' item='stat' index='index' open='(' close=')' separator=','>#{stat}</foreach>" +
            "</script>")
    int updateCouponUsedTimes(@Param("vid") String vid, @Param("couponId") Long couponId, @Param("oldUsedTimes") Integer oldUsedTimes, @Param("newUsedTimes") Integer newUsedTimes, @Param("statList") List<String> statList);

    /**
     * 获取所有已过期补胎券
     *
     * @param nowTime nowTime
     * @return List<CouponPo>
     */
    @Select("<script> " +
            "select * " +
            " from tb_car_coupon " +
            " where biz_platform = 4 and service_type = 3 and end_time &lt; #{nowTime} and stat = 'unused' " +
            "</script>")
    List<CouponPo> getExpireRepairTairCoupon(@Param("nowTime") long nowTime);

    /**
     * 更新已过期补胎券状态
     *
     * @param couponIds couponIds
     * @return int
     */
    @Update("<script> " +
            "update tb_car_coupon set stat = " +
            "CASE WHEN used_times &gt; 0 then 'used' " +
            "WHEN used_times = 0 then 'expired' END " +
            "where biz_platform = 4 and service_type = 3 and stat = 'unused' and id in " +
            "<foreach collection='couponIds' item='couponId' open='(' close=')' separator=','>#{couponId}</foreach> " +
            "</script> ")
    int updateRepairTairCouponStat(@Param("couponIds") List<Long> couponIds);

    @Update("update tb_car_coupon " +
            "set stat='invalid' " +
            "where vid=#{vid} and id in (${couponIds}) and stat='unused' and used_times=0")
    int invalidCoupon(@Param("vid") String vid, @Param("couponIds") String couponIds);
}
