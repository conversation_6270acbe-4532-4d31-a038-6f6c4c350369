package com.xiaomi.nr.coupon.enums.matchertool;

import org.apache.commons.lang3.StringUtils;

/**
 * 匹配器工具类型 枚举
 *
 * <AUTHOR>
 */
public enum MatcherToolTypeEnum {

    /**
     * 产品站-无码券可领匹配器工具
     */
    PRODUCT_NOCODE_FETCHABLE("产品站无码券可领匹配器工具"),

    /**
     * 产品站-无码券可用匹配器工具
     */
    PRODUCT_NOCODE_USABLE("产品站无码券可用匹配器工具"),

    /**
     * 无码券可领匹配器工具
     */
    NOCODE_FETCHABLE("无码券可领匹配器工具"),

    /**
     * 产品站-无码券可用匹配器工具
     */
    NOCODE_USABLE("无码券可用匹配器工具"),

    /**
     * 车商城产品站-无码券可领匹配器工具
     */
    CAR_SHOP_PRODUCT_NOCODE_FETCHABLE("车商城产品站无码券可领匹配器工具"),

    /**
     * 车商城产品站-无码券可用匹配器工具
     */
    CAR_SHOP_PRODUCT_NOCODE_USABLE("车商城产品站无码券可用匹配器工具"),

    ;

    private final String name;

    MatcherToolTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static MatcherToolTypeEnum findByName(String name) {
        MatcherToolTypeEnum[] items = MatcherToolTypeEnum.values();
        for (MatcherToolTypeEnum item : items) {
            if (StringUtils.equals(item.getName(), name)) {
                return item;
            }
        }
        return null;
    }
}

