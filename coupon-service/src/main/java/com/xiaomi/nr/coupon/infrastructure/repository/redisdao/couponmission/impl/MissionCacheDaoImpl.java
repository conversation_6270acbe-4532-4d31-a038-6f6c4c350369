package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.impl;

import com.google.common.collect.Lists;
import com.xiaomi.newretail.common.tools.base.exception.BaseException;
import com.xiaomi.nr.coupon.constant.CommonConstant;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.MissionCacheDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.po.MissionCacheItemPo;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.po.MissionMapType;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.StringUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MissionCacheDaoImpl implements MissionCacheDao {

    /**
     * 缓存操作对象
     */
    @Autowired
    @Qualifier("stringNewCouponRedisTemplate")
    private StringRedisTemplate redisTemplate;

    /**
     * 老pulse缓存操作对象
     */
    @Autowired
    @Qualifier("stringPulseTypeRedisTemplate")
    private StringRedisTemplate redisOldPulseTemplate;

    private static final String KEY_COUPON_MISSION_CACHE = "nr:coupon:mission:{id}";
    private static final String NEW_KEY_COUPON_MISSIONID_CACHE = "nr:coupon:valid:mission:{id}";

    /**
     * 任务已发放数量（必须放到同一slot里）
     * missionId
     */
    private static final String KEY_COUPON_MISSIONID_SEND_COUNT = "{nr_coupon_mission_send_count}_%d";

    /**
     * 任务已发放数量（老版本的）
     * missionId
     */
    private static final String KEY_COUPON_MISSIONID_OLD_SEND_COUNT = "coupon_count_%d";

    /**
     * 批量每次读取100个缓存
     */
    private static final int LIMIT_REDIS_GET_COUNT = 100;


    /**
     * 增加（不要动它）
     */
    private static final String luaIncrScript = " local incrKey={}" +
            " for i, v in ipairs(KEYS) do" +
            " local idKey = i*2-1" +
            " local countKey = i*2" +
            " local sendCount = redis.call('INCRBY', KEYS[i], 1)" +
            " incrKey[i] = KEYS[i]" +
            " if sendCount > tonumber(ARGV[countKey]) then" +
            " for ti, tv in ipairs(incrKey) do" +
            " redis.call('DECRBY', tv, 1)" +
            " end" +
            " return ARGV[1]" +
            " end" +
            " end" +
            " return ''";

    /**
     * 扣减（不要动它）
     */
    private static final String luaDecrScript = " for i, v in ipairs(KEYS) do" +
            " redis.call('DECRBY', v, 1)" +
            " end";



    /**
     * 根据发券任务id获取单个券发放任务信息
     *
     * @param missionId Long
     * @return MissionCacheItemPo
     */
    @Override
    public MissionCacheItemPo get(Long missionId) {
        if (missionId == null || missionId < 0) {
            return null;
        }

        Map<Long, MissionCacheItemPo> r = get(Lists.newArrayList(missionId));
        if (r == null) {
            return null;
        }
        return r.get(missionId);
    }


    /**
     * 根据发券任务id列表批量获取券发放任务信息
     *
     * @param missionIds List<Long>
     * @return Map<Long, MissionCacheItemPo>
     */
    @Override
    public Map<Long, MissionCacheItemPo> get(List<Long> missionIds) {
        if (CollectionUtils.isEmpty(missionIds)) {
            return Collections.emptyMap();
        }

        long runStartTime = TimeUtil.getNowUnixMillis();

        Map<Long, MissionCacheItemPo> result = new HashMap<>(missionIds.size());
        List<String> keys = new ArrayList<>(LIMIT_REDIS_GET_COUNT);
        ValueOperations<String, String> operations = redisTemplate.opsForValue();

        for (Long id : missionIds) {
            if (StringUtils.isEmpty(id.toString())) {
                continue;
            }

            keys.add(StringUtil.formatContent(KEY_COUPON_MISSION_CACHE, String.valueOf(id)));
            if (keys.size() < LIMIT_REDIS_GET_COUNT) {
                continue;
            }

            List<String> jsonStrList = operations.multiGet(keys);
            List<MissionCacheItemPo> infos = decodeBaseInfo(keys, jsonStrList);
            if (infos.size() > 0) {
                infos.forEach(v -> result.put(v.getId(), v));
            }
            keys.clear();
        }

        if (!CollectionUtils.isEmpty(keys)) {
            List<String> jsonStrList = new ArrayList<>();
            if (keys.size() > 1) {
                jsonStrList = operations.multiGet(keys);
            } else {
                jsonStrList.add(operations.get(keys.get(0)));
            }
            List<MissionCacheItemPo> infos = decodeBaseInfo(keys, jsonStrList);
            if (infos.size() > 0) {
                infos.forEach(v -> result.put(v.getId(), v));
            }
        }

        StringBuilder noFindIds = new StringBuilder();
        for(Long id : missionIds) {
            if(!result.containsKey(id)) {
                noFindIds.append(id).append(",");
            }
        }
        if(!Strings.isNullOrEmpty(String.valueOf(noFindIds))) {
            log.info("MissionCacheDaoImpl.get, 以下券任务ID在redis里未找到, noFindIds={}", noFindIds);
        }

        long runCostTime = TimeUtil.sinceMillis(runStartTime);
        if(runCostTime > CommonConstant.REDIS_TIME_OUT) {
            log.info("MissionCacheDaoImpl.get, 获取券发放任务缓存时间比较长, runTime={}ms, missionIds={}", runCostTime, missionIds);
        }

        return result;
    }


    /**
     * 获取当前有效发放任务id和券配置id的映射关系
     *
     * @return 发放任务id和券配置id的映射关系
     */
    @Override
    public Map<Long, List<Long>> getMissionIdMap(List<Long> validConfigIdList) {
        long runStartTime = TimeUtil.getNowUnixMillis();

        Map<Long, List<Long>> ret = new HashMap<>();

        ValueOperations<String, String> operations = redisTemplate.opsForValue();

        List<String> keys = validConfigIdList.stream().map(configId -> StringUtil.formatContent(NEW_KEY_COUPON_MISSIONID_CACHE, String.valueOf(configId))).collect(Collectors.toList());

        List<String> missionIds = operations.multiGet(keys);

        long runCostTime = TimeUtil.sinceMillis(runStartTime);
        if(runCostTime > 50L) {
            log.info("MissionCacheDaoImpl.multiGet, 获取当前有效发放任务id和券配置id的映射关系缓存时间比较长, missionIds={}, runTime={}毫秒", missionIds, runCostTime);
        }
        for (int i = 0; i < validConfigIdList.size(); i++) {
            if (null == missionIds.get(i)) {
                continue;
            }
            List<Long> missionId = Arrays.stream(StringUtils.split(missionIds.get(i), ",")).map(Long::parseLong).collect(Collectors.toList());

            ret.put(validConfigIdList.get(i), missionId);
        }
        return ret;
    }


    /**
     * 解析券缓存基本信息
     *
     * @param keyList  List<String>
     * @param dataList List<String>
     * @return List<ConfigCacheItemPo>
     */
    private List<MissionCacheItemPo> decodeBaseInfo(List<String> keyList, List<String> dataList) {
        List<MissionCacheItemPo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(dataList)) {
            log.info("couponmission.cache, 从redis里取到的数据为null, keyList={}", keyList);
            return result;
        }

        for (String resJson : dataList) {
            if (StringUtils.isEmpty(resJson)) {
                log.warn("couponmission.cache, 从redis里取到的数据存在为空的情况, keyList={}, resJson={}", keyList, resJson);
                continue;
            }

            MissionCacheItemPo info = GsonUtil.fromJson(resJson, MissionCacheItemPo.class);
            if (Objects.isNull(info) || info.getId() <= 0) {
                log.warn("couponmission.cache, 从redis里取到的数据解析后发现有存在不符合要求的, keyList={}, resJson={}", keyList, resJson);
                continue;
            }

            result.add(info);
        }
        return result;
    }


    /**
     * 批量获取任务已发放数量
     *
     * @param missionIds List<Long>
     * @return Map<Long, Long>
     */
    @Override
    public Map<Long, Long> getSendCount(List<Long> missionIds) {
        if (CollectionUtils.isEmpty(missionIds)) {
            return Collections.emptyMap();
        }

        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        List<String> keys = new ArrayList<>();
        for (Long id : missionIds) {
            keys.add(String.format(KEY_COUPON_MISSIONID_SEND_COUNT, id));
        }

        List<String> list = new ArrayList<>();
        if (keys.size() > 1) {
            list = operations.multiGet(keys);
        } else {
            list.add(operations.get(keys.get(0)));
        }

        if (list == null || list.isEmpty()) {
            return Collections.emptyMap();
        }

        //mget是按key的顺序返回的
        Map<Long, Long> result = new HashMap<>(missionIds.size());
        for (int i = 0; i < missionIds.size(); i++) {
            String countStr = list.get(i);
            if (countStr == null) {
                result.put(missionIds.get(i), 0L);
                continue;
            }

            long count;
            try {
                count = Long.parseLong(countStr);
            } catch (Exception e) {
                log.error("assign.coupon, 任务已发放数据转成Long失败, missionId={}", missionIds.get(i));
                continue;
            }
            result.put(missionIds.get(i), count);
        }
        return result;
    }

    /**
     * 老版本的，任务已发放数量批量加1，并校验是否超过最大发放数量
     *
     * @param missionIds List<Long>
     */
    @Override
    public void incrOldSendCountAndCheck(List<Long> missionIds, List<Long> maxNums) throws BizError {
        if (CollectionUtils.isEmpty(missionIds)) {
            return;
        }

        //lua老任务不支持多任务
        if(missionIds.size() > 1) {
            throw ExceptionHelper.create(ErrCode.COUPON, String.format("同时只能发放一个任务的券。==>任务[%s]", missionIds));
        }

        DefaultRedisScript<String> redisScript = new DefaultRedisScript<>(luaIncrScript, String.class);

        List<String> keys = new ArrayList<>();
        List<String> args = new ArrayList<>();
        for (int i = 0; i < missionIds.size(); i++) {
            keys.add(String.format(KEY_COUPON_MISSIONID_OLD_SEND_COUNT, missionIds.get(i)));
            args.add(missionIds.get(i).toString());
            args.add(maxNums.get(i).toString());
        }

        String missionId = redisOldPulseTemplate.execute(redisScript, keys, args.toArray());
        log.info("assign.coupon, 任务已发放数量加1老版本的结果，keys={}, missionIds={}, missionId={}", keys, missionIds, missionId);
        if (!"".equals(missionId)) {
            throw ExceptionHelper.create(ErrCode.COUPON, String.format("任务发放数量已达限制。==>任务[%s]", missionId));
        }
    }

    /**
     * 任务已发放数量批量加1，并校验是否超过最大发放数量
     *
     * @param missionIds List<Long>
     */
    @Override
    public void incrSendCountAndCheck(List<Long> missionIds, List<Long> maxNums) throws BizError {
        if (CollectionUtils.isEmpty(missionIds)) {
            return;
        }

        DefaultRedisScript<String> redisScript = new DefaultRedisScript<>(luaIncrScript, String.class);

        List<String> keys = new ArrayList<>();
        List<String> args = new ArrayList<>();
        for (int i = 0; i < missionIds.size(); i++) {
            keys.add(String.format(KEY_COUPON_MISSIONID_SEND_COUNT, missionIds.get(i)));
            args.add(missionIds.get(i).toString());
            args.add(maxNums.get(i).toString());
        }

        String missionId = redisTemplate.execute(redisScript, keys, args.toArray());
        log.info("assign.coupon, 任务已发放数量加1的结果，keys={}, missionIds={}, missionId={}", keys, missionIds, missionId);
        if (!"".equals(missionId)) {
            throw ExceptionHelper.create(ErrCode.COUPON, String.format("任务发放数量已达限制==>任务[%s]", missionId));
        }
    }


    /**
     * 老版本的，任务已发放数量批量减1
     *
     * @param missionIds List<Long>
     */
    @Override
    public void decrOldSendCount(List<Long> missionIds) {
        if (CollectionUtils.isEmpty(missionIds)) {
            return;
        }

        DefaultRedisScript<String> redisScript = new DefaultRedisScript<>(luaDecrScript, String.class);

        List<String> keys = new ArrayList<>();
        for (Long missionId : missionIds) {
            keys.add(String.format(KEY_COUPON_MISSIONID_OLD_SEND_COUNT, missionId));
        }

        try {
            redisOldPulseTemplate.execute(redisScript, keys);
            log.info("assign.coupon, 任务已发放数量减1老版本的结果，keys={}, missionIds={}", keys, missionIds);
        } catch (Exception e) {
            log.error("assign.coupon, 任务已发放数量减1老版本失败，missionIds={}", missionIds, e);
        }
    }

    /**
     * 任务已发放数量批量减1
     *
     * @param missionIds List<Long>
     */
    @Override
    public void decrSendCount(List<Long> missionIds) {
        if (CollectionUtils.isEmpty(missionIds)) {
            return;
        }

        DefaultRedisScript<String> redisScript = new DefaultRedisScript<>(luaDecrScript, String.class);

        List<String> keys = new ArrayList<>();
        for (Long missionId : missionIds) {
            keys.add(String.format(KEY_COUPON_MISSIONID_SEND_COUNT, missionId));
        }

        try {
            redisTemplate.execute(redisScript, keys);
            log.info("assign.coupon, 任务已发放数量减1的结果，keys={}, missionIds={}", keys, missionIds);
        } catch (Exception e) {
            log.error("assign.coupon, 任务已发放数量减1失败，missionIds={}", missionIds, e);
        }
    }

}
