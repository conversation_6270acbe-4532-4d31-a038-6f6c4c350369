package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @description: 优惠券redis缓存po
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @Date 2022/3/1 4:38 下午
 * @Version: 1.0
 **/
@Data
public class ConfigInfoCachePo implements Serializable {
    /**
     * 配置ID
     */
    @SerializedName("id")
    private Integer id;

    /**
     * 配置名称
     */
    @SerializedName("n")
    private String name;

    /**
     * 状态 1:上线, 2:下线, 3:终止
     */
    @SerializedName("s")
    private Integer status;

    /**
     * 使用范围描述
     */
    @SerializedName("cd")
    private String couponDesc;

    /**
     * 优惠类型 1:满减, 2:满折, 3:N元券, 4:立减
     */
    @SerializedName("pt")
    private Integer promotionType;

    /**
     * 可领取的开始时间
     */
    @SerializedName("sft")
    private Long startFetchTime;

    /**
     * 可领取的结束时间
     */
    @SerializedName("eft")
    private Long endFetchTime;

    /**
     * 使用有效期的类型
     */
    @SerializedName("utt")
    private Integer useTimeType;

    /**
     * 可使用的开始时间
     */
    @SerializedName("sut")
    private Long startUseTime;

    /**
     * 可使用的结束时间
     */
    @SerializedName("eut")
    private Long endUseTime;

    /**
     * 有效时长(单位小时)
     */
    @SerializedName("ud")
    private Integer useDuration;

    /**
     * 使用平台对应的门店 使用渠道类型(1:小米商城 2:直营店/专卖店 3:授权店 4:堡垒店) => 渠道可用门店配置
     */
    @SerializedName("ucs")
    private Map<Integer, UseChannelStorePo> useChannelStore;

    /**
     * 门槛类型 1满元 2满件 3每满元 4每满件
     */
    @SerializedName("bt")
    private Integer bottomType;

    /**
     * 满元门槛值（单位分）
     */
    @SerializedName("bp")
    private Integer bottomPrice;

    /**
     * 满件门槛值（单位个）
     */
    @SerializedName("bc")
    private Integer bottomCount;

    /**
     * 优惠值（单位个/分）
     */
    @SerializedName("pv")
    private Long promotionValue;

    /**
     * 最大减免金额（单位分）
     */
    @SerializedName("mr")
    private Integer maxReduce;

    /**
     * 商品范围类型 1 商品券 2 分类券
     */
    @SerializedName("gst")
    private Integer goodsScopeType;

    /**
     * 可发放的总数量
     */
    @SerializedName("ac")
    private Integer applyCount;

    /**
     * 每人限领的数量
     */
    @SerializedName("flc")
    private Integer fetchLimitCount;

    /**
     * 是否可分享　true：可分享 false：不可分享
     */
    @SerializedName("se")
    private Boolean share;

    /**
     * 是否包邮 true：包邮 false：不包邮
     */
    @SerializedName("pf")
    private Boolean postFree;

    /**
     * 是否指定地区可用 true：是 false：否
     */
    @SerializedName("aa")
    private Boolean assignArea;

    /**
     * 指定可用地区配置
     * 区域类型（1:省 2:市） => 区域ID列表
     */
    @SerializedName("aac")
    private Map<Integer, List<Integer>> assignAreaConfig;

    /**
     * 创建时间
     */
    @SerializedName("ct")
    private Long createTime;

    /**
     * 最后更新时间
     */
    @SerializedName("lut")
    private String lastUpdateTime;
}
