package com.xiaomi.nr.coupon.domain.coupontrade.tradecheck;

import com.google.common.collect.Maps;
import com.xiaomi.nr.coupon.api.dto.coupon.checkout.GoodsInfo;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.api.dto.trade.*;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.GoodExt;
import com.xiaomi.nr.coupon.domain.couponconfig.model.ServiceSceneItem;
import com.xiaomi.nr.coupon.domain.coupontrade.TradeCheckAbstract;
import com.xiaomi.nr.coupon.domain.coupontrade.convert.CouponBaseInfoConvert;
import com.xiaomi.nr.coupon.domain.coupontrade.entity.TradeCheckContext;
import com.xiaomi.nr.coupon.enums.coupon.OrderStatusEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.CouponServiceTypeEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.ModeTypeEnum;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.CarCouponRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponOptPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 销核结算-无码券-售后服务
 *
 * <AUTHOR>
 * @date 2024/1/5
 */
@Component
@Slf4j
public class NoCodeSaleAfterCheck extends TradeCheckAbstract {

    @Resource
    private CouponBaseInfoConvert couponBaseInfoConvert;

    @Resource
    private CouponInfoCheck couponInfoCheck;

    @Resource
    private CouponConfigRepository couponConfigRepository;

    @Resource
    private CarCouponRepository carCouponRepository;

    @Resource
    private AfterSaleCouponLockToolsFactory afterSaleCouponLockToolsFactory;

    @Resource
    private AfterSaleCouponConsumeToolsFactory afterSaleCouponConsumeToolsFactory;

    @Resource
    private AfterSaleCouponReturnToolsFactory afterSaleCouponReturnToolsFactory;

    @Override
    protected Integer getFactoryKey() {
        return BizPlatformEnum.CAR_AFTER_SALE.getCode();
    }

    @Override
    protected List<CouponPo> getCouponPo(CheckoutCouponReqModel request, List<Long> couponIds) {
        // 当不传券ID的时候，获取用户所有未过期且未使用的劵
        if (CollectionUtils.isEmpty(couponIds)) {
            List<Integer> bizPlatformList = Lists.newArrayList(request.getBizPlatform());
            return carCouponRepository.getAllCouponForUnused(request.getVid(), TimeUtil.getNowUnixSecond(), bizPlatformList);
        }
        return carCouponRepository.getCouponInfos(request.getVid(), couponIds);
    }

    /**
     * 根据券id获取优惠券
     * 核销使用 读主库 防止存库数据延迟异常
     *
     * @param userId
     * @param vid
     * @param couponIds
     * @return
     * @throws BizError
     */
    @Override
    public List<CouponPo> getCouponPoList(long userId, String vid, List<Long> couponIds) throws BizError {
        return carCouponRepository.getCouponPoList(vid, couponIds);
    }

    @Override
    protected boolean initData(TradeCheckContext ctx) throws Exception {
        ctx.setCouponBaseInfo(couponBaseInfoConvert.convertNoCodeCouponPoConfig2BaseInfo(ctx.getCouponPo(), ctx.getConfig()));
        ctx.setCouponGroupNo(couponBaseInfoConvert.genCouponGroupNoForSaleAfter(ctx.getConfig()));
        ctx.setCouponOrgCode(couponInfoCheck.getCouponOrgCode(ctx.getCouponPo().getExtendInfo()));
        ctx.getCheckoutCouponInfo().setCouponSsuExtInfo(getValidSsuExtList(ctx.getConfig()));
        ServiceSceneItem serviceSceneConfig = couponConfigRepository.getCouponServiceScene(ctx.getConfig().getCouponConfigInfo().getServiceScene());
        if (Objects.isNull(serviceSceneConfig)) {
            ctx.getErrContext().setErrorCode(ErrCode.USE_COUPON_SERVICE_SCENE_MISMATCH.getCode());
            ctx.getErrContext().setErrorMsg("优惠券服务场景配置不存在");
            return false;
        }
        ctx.setServiceSceneConfig(serviceSceneConfig);
        return true;
    }

    @Override
    protected boolean check(TradeCheckContext ctx) {
        // 公共校验
        if (!super.commonCheck(ctx)) {
            return false;
        }

        // 需要加一个vid 的校验，非本vid的不能用
        if (!vidCheck(ctx)) {
            return false;
        }

        // 校验商品 (按配件和工时、及数量进行)
        if (!goodsIncludeCheck(ctx)) {
            return false;
        }
        return true;
    }

    /**
     * 批量锁定优惠券幂等性校验
     *
     * @param couponPos
     * @param orderId
     * @param vid
     * @return 是否幂等
     */
    @Override
    protected boolean couponListLockCheck(List<CouponPo> couponPos, long orderId, String vid) throws BizError {
        boolean isIdempotent = true;
        long timeNow = TimeUtil.getNowUnixSecond();

        Map<Integer, List<CouponPo>> couponPoMap = couponPos.stream().collect(Collectors.groupingBy(CouponPo::getServiceType));
        for (Map.Entry<Integer, List<CouponPo>> entry : couponPoMap.entrySet()) {
            CouponServiceTypeEnum serviceTypeEnum = CouponServiceTypeEnum.valueOf(entry.getKey());
            if (null == serviceTypeEnum) {
                throw ExceptionHelper.create(ErrCode.USE_COUPON_SERVICE_SCENE_MISMATCH, "服务场景不存在");
            }
            AfterSaleCouponLockTools couponLockTools = afterSaleCouponLockToolsFactory.getAfterSaleCouponLockTools(serviceTypeEnum);
            isIdempotent = isIdempotent && couponLockTools.couponListLockCheck(entry.getValue(), orderId, vid, timeNow);
        }

        return isIdempotent;
    }

    /**
     * 批量核销优惠券幂等性校验
     *
     * @param couponPoList
     * @param orderId
     * @param vid
     * @return 是否幂等
     */
    @Override
    protected boolean couponListConsumeCheck(List<CouponPo> couponPoList, long orderId, String vid) throws BizError {
        boolean isIdempotent = true;
        long timeNow = TimeUtil.getNowUnixSecond();

        Map<Integer, List<CouponPo>> couponPoMap = couponPoList.stream()
                .collect(Collectors.groupingBy(CouponPo::getServiceType));

        for (Map.Entry<Integer, List<CouponPo>> entry : couponPoMap.entrySet()) {
            CouponServiceTypeEnum serviceTypeEnum = CouponServiceTypeEnum.valueOf(entry.getKey());
            if (null == serviceTypeEnum) {
                throw ExceptionHelper.create(ErrCode.USE_COUPON_SERVICE_SCENE_MISMATCH, "服务场景不存在");
            }

            AfterSaleCouponConsumeTools couponConsumeTools = afterSaleCouponConsumeToolsFactory.getAfterSaleCouponConsumeTools(serviceTypeEnum);
            isIdempotent = isIdempotent && couponConsumeTools.couponListConsumeCheck(entry.getValue(), orderId, vid, timeNow);
        }

        return isIdempotent;
    }

    @Override
    protected boolean couponListReturnCheck(List<CouponPo> couponPoList, long orderId, String vid) throws BizError {
        boolean isIdempotent = true;
        Map<Integer, List<CouponPo>> couponPoMap = couponPoList.stream()
                .collect(Collectors.groupingBy(CouponPo::getServiceType));

        for (Map.Entry<Integer, List<CouponPo>> entry : couponPoMap.entrySet()) {
            CouponServiceTypeEnum serviceTypeEnum = CouponServiceTypeEnum.valueOf(entry.getKey());
            if (null == serviceTypeEnum) {
                throw ExceptionHelper.create(ErrCode.USE_COUPON_SERVICE_SCENE_MISMATCH, "服务场景不存在");
            }

            AfterSaleCouponReturnTools couponReturnTools = afterSaleCouponReturnToolsFactory.getAfterSaleCouponReturnTools(serviceTypeEnum);
            isIdempotent = isIdempotent && couponReturnTools.couponListReturnCheck(entry.getValue(), orderId, vid);
        }

        return isIdempotent;
    }

    /**
     * vid检查（仅限指定车辆可用）
     *
     * @param ctx TradeCheckContext
     * @return bool
     */
    private boolean vidCheck(TradeCheckContext ctx) {
        String requestVid = ctx.getCouponPo().getVid();
        String couponVid = ctx.getRequestVid();
        if (Strings.isNotBlank(couponVid) && couponVid.equals(requestVid)) {
            return true;
        }
        ctx.getErrContext().setErrorCode(ErrCode.USE_COUPON_VID_NOT_EQUATION.getCode());
        ctx.getErrContext().setErrorMsg("仅限指定车辆可用");
        return false;
    }

    @Override
    protected void checkAfter(TradeCheckContext ctx) {
        ctx.getCheckoutCouponInfo().setValidSsuList(getValidSsuList(ctx));
    }

    /**
     * 锁定优惠券
     * @param request
     * @param couponPoList
     * @throws Exception
     */
    @Override
    @Transactional(value = "xmPulseWriteTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void lockCoupon(LockCouponRequest request, List<CouponPo> couponPoList) throws BizError {
        String vid = request.getVid();
        long orderId = request.getOrderId();
        List<CouponLockItem> couponItems = request.getCouponItems();

        Map<Long, CouponPo> couponPoMap = couponPoList.stream().collect(Collectors.toMap(CouponPo::getId, Function.identity()));

        Map<CouponServiceTypeEnum, List<CouponLockItem>> couponLockItemMap = Maps.newHashMap();
        for (CouponLockItem couponItem : couponItems) {
            CouponPo couponPo = couponPoMap.get(couponItem.getCouponId());
            CouponServiceTypeEnum serviceTypeEnum = CouponServiceTypeEnum.valueOf(couponPo.getServiceType());
            List<CouponLockItem> couponLockItems = couponLockItemMap.getOrDefault(serviceTypeEnum, Lists.newArrayList());
            couponLockItems.add(couponItem);
            couponLockItemMap.put(serviceTypeEnum, couponLockItems);
        }

        lockAfterSaleCoupon(vid, request.getUserId(), orderId, request.getOffline(), couponLockItemMap);
    }

    public void lockAfterSaleCoupon(String vid, long userId, long orderId, Integer offline, Map<CouponServiceTypeEnum, List<CouponLockItem>> couponLockItemMap) throws BizError {
        CouponOptPo couponOptPo = carCouponRepository.getByCouponOpt(vid, orderId, OrderStatusEnum.ROLLBACK.getType());
        if (couponOptPo != null) {
            // 1、防止事务悬挂
            log.warn("NoCodeSaleAfterCheck.lockCoupon 已经回滚. vid:{}, orderId:{}", vid, orderId);
            return;
        }

        // 2、锁券
        for (Map.Entry<CouponServiceTypeEnum, List<CouponLockItem>> entry : couponLockItemMap.entrySet()) {
            CouponServiceTypeEnum serviceTypeEnum = entry.getKey();
            AfterSaleCouponLockTools couponLockTools = afterSaleCouponLockToolsFactory.getAfterSaleCouponLockTools(serviceTypeEnum);
            couponLockTools.lockCoupon(vid, userId, orderId, offline, entry.getValue());
        }

        // 3、记录操作日志
        carCouponRepository.insertCouponOptLog(vid, userId, orderId, OrderStatusEnum.LOCK);
    }

    @Override
    @Transactional(value = "xmPulseWriteTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void consumeCoupon(ConsumeCouponRequest request, List<CouponPo> couponPoList) throws BizError {
        String vid = request.getVid();
        long userId = request.getUserId();
        long orderId = request.getOrderId();

        Map<CouponServiceTypeEnum, List<CouponPo>> couponPosMap = couponPoList.stream()
                .collect(Collectors.groupingBy(couponPo -> CouponServiceTypeEnum.valueOf(couponPo.getServiceType())));

        consumeAfterSaleCoupon(vid, userId, orderId, request.getOffline(), couponPosMap);
    }

    public void consumeAfterSaleCoupon(String vid, long userId, long orderId, Integer offline, Map<CouponServiceTypeEnum, List<CouponPo>> couponPosMap) throws BizError {
        CouponOptPo couponOptPo = carCouponRepository.getByCouponOpt(vid, orderId, OrderStatusEnum.ROLLBACK.getType());
        if (couponOptPo != null) {
            // 1、防止事务悬挂
            log.warn("NoCodeSaleAfterCheck.consumeCoupon 已经回滚. vid:{}, orderId:{}", vid, orderId);
            return;
        }

        // 2、核销券
        for (Map.Entry<CouponServiceTypeEnum, List<CouponPo>> entry : couponPosMap.entrySet()) {
            CouponServiceTypeEnum serviceTypeEnum = entry.getKey();
            AfterSaleCouponConsumeTools couponConsumeTools = afterSaleCouponConsumeToolsFactory.getAfterSaleCouponConsumeTools(serviceTypeEnum);
            couponConsumeTools.consumeCoupon(vid, userId, orderId, offline, entry.getValue());
        }

        // 3、记录操作日志
        carCouponRepository.insertCouponOptLog(vid, userId, orderId, OrderStatusEnum.SUBMIT);
    }

    @Override
    @Transactional(value = "xmPulseWriteTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void returnCoupon(RollbackCouponRequest request, List<CouponPo> couponPoList) throws BizError {
        String vid = request.getVid();
        long userId = request.getUserId();
        long orderId = request.getOrderId();

        Map<CouponServiceTypeEnum, List<CouponPo>> couponPosMap = couponPoList.stream()
                .collect(Collectors.groupingBy(couponPo -> CouponServiceTypeEnum.valueOf(couponPo.getServiceType())));

        returnAfterSaleCoupon(vid, userId, orderId, request.getOffline(), couponPosMap);

    }

    public void returnAfterSaleCoupon(String vid, long userId, long orderId, Integer offline, Map<CouponServiceTypeEnum, List<CouponPo>> couponPosMap) throws BizError {
        // 1、退还券
        for (Map.Entry<CouponServiceTypeEnum, List<CouponPo>> entry : couponPosMap.entrySet()) {
            CouponServiceTypeEnum serviceTypeEnum = entry.getKey();
            AfterSaleCouponReturnTools couponReturnTools = afterSaleCouponReturnToolsFactory.getAfterSaleCouponReturnTools(serviceTypeEnum);
            couponReturnTools.returnCoupon(vid, userId, orderId, offline, entry.getValue());
        }

        // 2、记录操作日志
        carCouponRepository.insertCouponOptLog(vid, userId, orderId, OrderStatusEnum.ROLLBACK);
    }

    /**
     * 获取券的所有可用SSU扩展信息列表
     *
     * @param config 券配置信息
     * @return SKU列表
     */
    private Map<Long, SsuExtItemDto> getValidSsuExtList(CouponConfigItem config) {
        if (Objects.isNull(config) || Objects.isNull(config.getGoodScope()) || CollectionUtils.isEmpty(config.getGoodScope().getSsus())) {
            return Collections.emptyMap();
        }

        Map<Long, SsuExtItemDto> result = new HashMap<>();
        Map<Long, GoodExt> configSsuExtMap = config.getGoodScope().getGoodsExt();

        config.getGoodScope().getSsus().forEach(ssu -> {
            if (configSsuExtMap.containsKey(ssu)) {
                SsuExtItemDto item = new SsuExtItemDto();
                item.setTag(configSsuExtMap.get(ssu).getTag());
                item.setCount(configSsuExtMap.get(ssu).getCount());
                result.put(ssu, item);
            }
        });
        return result;
    }

    /**
     * 检查劵配置是否包含商品
     *
     * @param ctx TradeCheckContext
     * @return
     */
    private boolean goodsIncludeCheck(TradeCheckContext ctx) {
        CouponConfigItem couponConfig = ctx.getConfig();
        String couponModeType = ctx.getCouponModeType();
        List<GoodsInfo> goodsList = ctx.getRequestModel().getSkuPackageList();

        // 不传入商品则默认返回可用
        if (CollectionUtils.isEmpty(goodsList)) {
            return true;
        }

        if (Objects.isNull(couponConfig.getGoodScope())) {
            ctx.getErrContext().setErrorMsg(ModeTypeEnum.Code.getMysqlValue().equals(couponModeType) ? "优惠码不含可用商品" : "优惠券不含可用商品");
            ctx.getErrContext().setErrorCode(ErrCode.USE_COUPON_GOODS_MISMATCH.getCode());
            return false;
        }

        Map<Long, GoodsInfo> paramSsuMap = goodsList.stream()
                .filter(e -> GoodsLevelEnum.Ssu.getValue().equals(e.getLevel()))
                .collect(Collectors.toMap(GoodsInfo::getId, e -> e));

        //部分抵扣（只要ssu存在于券配置的可用商品表列中即可）
        if (ctx.getServiceSceneConfig().isDeductPart()) {
            for (Map.Entry<Long, GoodExt> item : couponConfig.getGoodScope().getGoodsExt().entrySet()) {
                GoodExt ext = item.getValue();
                GoodsInfo pItem = paramSsuMap.get(item.getKey());
                if (Objects.nonNull(pItem) && ext.getTag().equals(pItem.getTag())) {
                    return true;
                }
            }
            ctx.getErrContext().setErrorMsg(ModeTypeEnum.Code.getMysqlValue().equals(couponModeType) ? "不含可用优惠码商品" : "不含可用优惠券商品");
            ctx.getErrContext().setErrorCode(ErrCode.USE_COUPON_GOODS_MISMATCH.getCode());
            return false;
        }

        //全部抵扣（要求券配置的可用商品必须全部存在于传入的商品列中，且券上配置的商品数量要小于或等于传入的对应商品数量）
        for (Map.Entry<Long, GoodExt> item : couponConfig.getGoodScope().getGoodsExt().entrySet()) {
            GoodExt ext = item.getValue();
            GoodsInfo pItem = paramSsuMap.get(item.getKey());
            if (Objects.isNull(pItem) || !ext.getTag().equals(pItem.getTag()) || ext.getCount() > pItem.getCount()) {
                ctx.getErrContext().setErrorMsg(ModeTypeEnum.Code.getMysqlValue().equals(couponModeType) ? "不含可用优惠码商品" : "不含可用优惠券商品");
                ctx.getErrContext().setErrorCode(ErrCode.USE_COUPON_GOODS_MISMATCH.getCode());
                return false;
            }
        }
        return true;
    }

    /**
     * 获取可用SSU列表
     *
     * @param ctx TradeCheckContext
     * @return SKU列表
     */
    public List<Long> getValidSsuList(TradeCheckContext ctx) {
        CouponConfigItem config = ctx.getConfig();
        List<GoodsInfo> goodsList = ctx.getRequestModel().getSkuPackageList();
        if (Objects.isNull(config) || Objects.isNull(config.getGoodScope()) || Objects.isNull(config.getGoodScope().getSsus())) {
            return null;
        }

        // 不传入商品则返回所有可用商品
        if (CollectionUtils.isEmpty(goodsList)) {
            return new ArrayList<>(config.getGoodScope().getSsus());
        }

        Map<Long, GoodsInfo> paramSsuMap = goodsList.stream()
                .filter(e -> GoodsLevelEnum.Ssu.getValue().equals(e.getLevel()))
                .collect(Collectors.toMap(GoodsInfo::getId, e -> e));

        //部分抵扣（只要ssu存在于券配置的可用商品表列中即可）
        List<Long> result = new ArrayList<>();
        if (ctx.getServiceSceneConfig().isDeductPart()) {
            for (Map.Entry<Long, GoodExt> item : config.getGoodScope().getGoodsExt().entrySet()) {
                GoodExt ext = item.getValue();
                GoodsInfo pItem = paramSsuMap.get(item.getKey());
                if (Objects.nonNull(pItem) && ext.getTag().equals(pItem.getTag())) {
                    result.add(item.getKey());
                }
            }
            return result;
        }

        //全部抵扣（要求券配置的可用商品必须全部存在于传入的商品列中，且券上配置的商品数量要小于或等于传入的对应商品数量）
        for (Map.Entry<Long, GoodExt> item : config.getGoodScope().getGoodsExt().entrySet()) {
            GoodExt ext = item.getValue();
            GoodsInfo pItem = paramSsuMap.get(item.getKey());
            if (Objects.isNull(pItem) || !ext.getTag().equals(pItem.getTag()) || ext.getCount() > pItem.getCount()) {
                return Collections.emptyList();
            }
            result.add(item.getKey());
        }
        return result;
    }

}
