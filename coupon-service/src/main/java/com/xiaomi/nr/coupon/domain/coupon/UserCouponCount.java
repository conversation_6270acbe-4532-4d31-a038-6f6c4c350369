package com.xiaomi.nr.coupon.domain.coupon;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.enums.coupon.CouponUseChannelEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponslave.UserCouponCountMapper;
import com.xiaomi.nr.coupon.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户优惠券数量
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class UserCouponCount {

    @Resource
    private UserCouponCountMapper userCouponMapper;

    @Resource
    private CouponConfigRepository couponConfigRepository;

    /**
     * 查用户可用券总数
     *
     * @param userId long
     * @return Integer
     */
    public Integer getValidCount(long userId, List<Integer> bizPlatform) {
        List<CouponPo> list = userCouponMapper.getValidCouponIds(userId, bizPlatform, TimeUtil.getNowUnixSecond());
        if (list.isEmpty()) {
            return 0;
        }
        List<Long> typeIds = list.stream().map(CouponPo::getTypeId).collect(Collectors.toList());

        //获取券缓存信息
        Map<Long, CouponConfigItem> caches = couponConfigRepository.getCouponConfigs(typeIds);
        if (MapUtils.isEmpty(caches)) {
            log.error("getValidCount, 无法获取到券配置缓存信息, userId={}, ids={}", userId, typeIds);
            return 0;
        }
        return (int) list.stream().map(item -> caches.get(item.getTypeId())).filter(info -> info != null && bizPlatform.contains(info.getBizPlatform())).count();
    }

    /**
     * 查用户可用券总数
     * @param userId long
     * @param useChannel String
     * @return Integer
     */
    public Integer getValidCountByUseChannel(long userId, List<Integer> bizPlatform, String useChannel) {
        List<CouponPo> list = userCouponMapper.getValidCouponIds(userId, bizPlatform, TimeUtil.getNowUnixSecond());
        if(list.isEmpty()) {
            return 0;
        }

        String[] channels = useChannel.split(",");
        if(channels.length == 0) {
            return list.size();
        }

        List<Long> typeIds = list.stream().map(CouponPo::getTypeId).collect(Collectors.toList());

        //获取券缓存信息
        Map<Long, CouponConfigItem> caches = couponConfigRepository.getCouponConfigs(typeIds);
        if (caches == null || caches.isEmpty()) {
            log.error("getValidCountByUseChannel, 无法获取到券配置缓存信息, userId={}, ids={}", userId, typeIds);
            return 0;
        }

        int count = 0;
        for(CouponPo item : list) {
            CouponConfigItem info = caches.get(item.getTypeId());
            if(info == null || info.getConfigId() < 0) {
                continue;
            }

            if (!bizPlatform.contains(info.getBizPlatform())) {
                continue;
            }

            boolean allMatch = false;
            for(String ch : channels) {
                CouponUseChannelEnum chEnum = CouponUseChannelEnum.findByValue(ch);
                if(chEnum == null) {
                    continue;
                }
                if (info.getUseChannelStore().containsKey(chEnum.getId())) {
                    allMatch = true;
                    break;
                }
            }
            if(allMatch) {
                count++;
            }
        }
        return count;
    }
}
