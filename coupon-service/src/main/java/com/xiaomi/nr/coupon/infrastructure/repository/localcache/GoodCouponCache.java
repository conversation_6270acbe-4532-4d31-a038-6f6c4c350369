package com.xiaomi.nr.coupon.infrastructure.repository.localcache;

import java.util.List;
import java.util.Map;


/**
 * 本地缓存
 */
public interface GoodCouponCache {

    /**
     * 从localCache里取商品对应优惠券配置ID列表
     *
     * @param id    Long
     * @param level String
     * @return List<Integer>
     */
    List<Long> getGoodsRelationCoupon(Long id, String level);

    /**
     * 从localCache里取单个商品对应优惠券配置ID列表
     *
     * @param key
     * @return
     */
    List<Long> singleGet(String key);

    /**
     * 批量从localCache里取商品对应优惠券配置ID列表
     *
     * @param keys
     * @return
     */
    Map<String, List<Long>> batchGet(List<String> keys);

    /**
     * 批量设置商品对应优惠券配置ID列表到localCache里
     *
     * @param goodConfigIds
     */
    void batchSet(Map<Long, List<Long>> goodConfigIds, String levle);


}
