package com.xiaomi.nr.coupon.infrastructure.repository.localcache.impl;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponSceneItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.ServiceSceneItem;
import com.xiaomi.nr.coupon.infrastructure.repository.localcache.CouponSceneCache;
import com.xiaomi.nr.coupon.infrastructure.repository.localcache.ServiceSceneCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @description: 优惠券场景本地缓存
 * @author: hejiapeng
 * @Date 2022/2/26 8:35 下午
 * @Version: 1.0
 **/
@Component
@Slf4j
public class ServiceSceneCacheImpl implements ServiceSceneCache {

    private Map<Integer, ServiceSceneItem> localCache;

    private ServiceSceneCacheImpl() {
        localCache = new ConcurrentHashMap<>();
    }

    /**
     * 根据场景编码获取场景信息
     * @param id
     * @return
     */
    @Override
    public ServiceSceneItem getServiceSceneById(Integer id) {
        if(id == null) {
            return null;
        }
        return localCache.get(id);
    }

    /**
     * 添加场景信息
     * @param couponSceneItem
     */
    @Override
    public void addServiceSceneIntoCache(ServiceSceneItem couponSceneItem) {
        if (couponSceneItem == null) {
            return;
        }
        localCache.put(couponSceneItem.getId(), couponSceneItem);
    }
}
