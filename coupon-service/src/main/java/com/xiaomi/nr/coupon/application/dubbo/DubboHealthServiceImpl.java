package com.xiaomi.nr.coupon.application.dubbo;

import com.xiaomi.data.push.common.Health;
import com.xiaomi.nr.coupon.api.dto.BaseRequest;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.nr.coupon.api.service.DubboHealthService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import com.xiaomi.youpin.qps.QpsAop;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Service(timeout = 1000, group = "${dubbo.group}", version = "1.0")
@Slf4j
@Component
public class DubboHealthServiceImpl implements DubboHealthService {

    @Autowired
    private QpsAop qpsAop;

    /**
     * health
     *
     * @return Health
     */
    @Override
    public Result<Health> health(BaseRequest request) {
        long qps = qpsAop.getQps();
        return Result.success(new Health("0.0.1", "2019-11-11", qps));
    }
}
