package com.xiaomi.nr.coupon.domain.matchertool.core;

import com.xiaomi.nr.coupon.enums.matchertool.MatcherToolTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 匹配器工具
 *
 * <AUTHOR>
 * @date 2023/7/13
 */
@Component
public class MatcherToolFactory {

    private final Map<MatcherToolTypeEnum, MatcherToolInterface> matcherMap;

    @Autowired
    public MatcherToolFactory(List<MatcherToolInterface> matcherList) {
        matcherMap = new HashMap<>(matcherList.size());
        matcherList.forEach(e -> matcherMap.put(e.getMatcherToolTypeEnum(), e));
    }

    public MatcherToolInterface getProvider(MatcherToolTypeEnum type) {
        return matcherMap.get(type);
    }
}
