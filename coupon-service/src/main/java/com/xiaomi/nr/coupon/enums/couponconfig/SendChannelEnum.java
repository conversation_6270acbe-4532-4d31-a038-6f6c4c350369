package com.xiaomi.nr.coupon.enums.couponconfig;

/**
 * 优惠券发放渠道 枚举
 *
 * <AUTHOR>
 */
public enum SendChannelEnum {

    /**
     * 店长券渠道
     */
    StoreManager("store_manager","store_manager", "店长券","7C0938DFA6023303195221E669AFDE24"),

    /**
     * 门店下单赠券渠道
     */
    StoreOrderGift("store_order_gift","store_order_gift", "门店下单赠券","0FBBAE2FD347172EFF6669E3BA1F82D4"),

    /**
     * 异业券渠道
     */
    DiffBusiness("diff_business","diff_business", "异业券","504E45C3444F637331C8E6995F5725BE"),

    /**
     * 测试券渠道
     */
    Test("test","test", "测试券","DCCC8C085E79839661D9B93E9A996EC8"),

    /**
     * 其他
     */
    Others("other", "other", "其他","");

    private final String redisValue;
    private final String mysqlValue;
    private final String name;
    private final String code;

    SendChannelEnum(String redisValue, String mysqlValue, String name, String code) {
        this.redisValue = redisValue;
        this.mysqlValue = mysqlValue;
        this.name = name;
        this.code = code;
    }

    public String getRedisValue() {
        return this.redisValue;
    }

    public String getMysqlValue() {
        return this.mysqlValue;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public static String findNameByRedisValue(String value) {
        SendChannelEnum[] values = SendChannelEnum.values();
        for (SendChannelEnum item : values) {
            if (item.getRedisValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }

    public static String findRedisValueByMysqlValue(String value) {
        SendChannelEnum[] values = SendChannelEnum.values();
        for (SendChannelEnum item : values) {
            if (item.getMysqlValue().equals(value)) {
                return item.getRedisValue();
            }
        }
        return null;
    }

    public static SendChannelEnum findByRedisValue(String value) {
        SendChannelEnum[] values = SendChannelEnum.values();
        for (SendChannelEnum item : values) {
            if (item.getRedisValue().equals(value)) {
                return item;
            }
        }
        return null;
    }
}

