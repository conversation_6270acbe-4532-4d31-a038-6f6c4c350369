package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig;

import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;
import java.util.Map;

/**
 * @description: 商品可用券redis
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/3/6 1:14 下午
 * @Version: 1.0
 **/

public interface GoodCouponsRedisDao {

    /**
     * 获取商品可用券列表
     * @param good
     * @param level
     * @return
     * @throws BizError
     */
    List<String> get(List<Long> good, String level) throws BizError;
}
