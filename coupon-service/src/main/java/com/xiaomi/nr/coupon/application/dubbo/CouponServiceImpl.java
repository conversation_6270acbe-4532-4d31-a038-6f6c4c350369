package com.xiaomi.nr.coupon.application.dubbo;

import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.api.dto.PageBeanStream;
import com.xiaomi.nr.coupon.api.dto.coupon.*;
import com.xiaomi.nr.coupon.api.dto.coupon.info.CouponInfoRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.info.CouponInfoResponse;
import com.xiaomi.nr.coupon.api.dto.coupon.list.UserCouponListDto;
import com.xiaomi.nr.coupon.api.dto.coupon.list.UserCouponListRequest;
import com.xiaomi.nr.coupon.api.dto.couponcode.GetCouponCodeInfoRequest;
import com.xiaomi.nr.coupon.api.dto.couponcode.GetCouponCodeInfoResponse;
import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.api.dto.trade.CheckoutCouponReqModel;
import com.xiaomi.nr.coupon.api.dto.trade.CheckoutCouponResModel;
import com.xiaomi.nr.coupon.api.service.CouponService;
import com.xiaomi.nr.coupon.constant.CommonConstant;
import com.xiaomi.nr.coupon.domain.common.Common;
import com.xiaomi.nr.coupon.domain.coupon.*;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponConvert;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponFormatCommon;
import com.xiaomi.nr.coupon.domain.coupon.model.CouponCodeQueryParam;
import com.xiaomi.nr.coupon.domain.coupon.model.CouponInfoModel;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.coupontrade.UserCouponFlowFactory;
import com.xiaomi.nr.coupon.domain.coupontrade.UserCouponService;
import com.xiaomi.nr.coupon.domain.coupontrade.tradecheck.CouponInfoCheck;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherToolFactory;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherToolInterface;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherConfigItemDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherGoodsItemDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.nocodefetchable.NoCodeFetchableReqDo;
import com.xiaomi.nr.coupon.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.enums.matchertool.MatcherToolTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.UserCodeCouponRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponcode.po.CouponCodePo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import com.xiaomi.youpin.infra.utils.profiler.JProfilerUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Service(group = "${dubbo.group}", version = "1.0", timeout = 3000, delay = 10000)
@ApiModule(value = "优惠券服务", apiInterface = CouponService.class)
public class CouponServiceImpl implements CouponService {

    @Resource
    private UserCouponCount userCouponCount;

    @Resource
    private NearDaysList nearDaysList;

    @Resource
    private Assign assigner;

    @Resource
    private Common common;

    @Resource
    private UserCouponInfo userCouponInfo;

    @Resource
    private CouponInfoCheck couponInfoCheck;

    @Resource
    private ProductCouponService productCouponService;

    @Resource
    private UserCouponFlowFactory userCouponFlowFactory;

    @Resource
    private MatcherToolFactory matcherToolFactory;

    @Resource
    private CouponConvert couponConvert;

    @Autowired
    private CouponFormatCommon couponFormatCommon;

    @Resource
    private UserCouponListFactory userCouponListFactory;

    @Resource
    private UserCouponInfoFactory userCouponInfoFactory;

    @Autowired
    private UserCodeCouponRepository userCodeCouponRepository;

    /**
     * 查用户可用券总数
     *
     * @param request UserCouponValidCountRequest
     * @return UserCouponValidCountResponse
     */
    @Override
    public Result<UserCouponValidCountResponse> userCouponValidCount(@Valid UserCouponValidCountRequest request) {
        UserCouponValidCountResponse response = new UserCouponValidCountResponse();
        response.setCount(0);

        if (request == null) {
            return Result.success(response);
        }
        String useChannel = request.getUseChannel();
        long runStartTime = TimeUtil.getNowUnixMillis();
        try {
            if (request.getUserId() == null || request.getUserId() <= 0) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "小米ID不符合要求");
            }

            List<Integer> bizPlatform = Lists.newArrayList(BizPlatformEnum.RETAIL.getCode());
            if (CollectionUtils.isNotEmpty(request.getBizPlatform())) {
                bizPlatform = request.getBizPlatform();
            }

            Integer count = 0;
            if (StringUtils.isEmpty(useChannel)) {
                count = userCouponCount.getValidCount(request.getUserId(), bizPlatform);
            } else {
                count = userCouponCount.getValidCountByUseChannel(request.getUserId(), bizPlatform, useChannel);
            }
            log.info("redPacket.userValidMoney, 获取用户可用优惠券总数, userId={}, count={}, runTime={}ms", request.getUserId(), count, TimeUtil.sinceMillis(runStartTime));
            response.setCount(count);
            return Result.success(response);
        } catch (BizError e) {
            log.info("redPacket.userValidMoney, 获取用户可用优惠券总数失败, userId={}, runTime={}ms", request.getUserId(), TimeUtil.sinceMillis(runStartTime));
            return Result.success(response);
        } catch (Exception e) {
            log.error("coupon.CouponServiceImpl, 获取用户可用优惠券总数出错, userId={}, class={}, errMsg={}, runTime={}ms", request.getUserId(), e.getClass(), e.getMessage(), TimeUtil.sinceMillis(runStartTime));
            return Result.success(response);
        }
    }


    /**
     * 根据券配置ID批量查询用户近*天的券列表（支持批量）
     *
     * @param request NearDaysListByTypeIdsRequest
     * @return Result<NearDaysListByTypeIdsResponse>
     */
    @Override
    public Result<Map<String, NearDaysListByTypeIdsItemDto>> nearDaysListByTypeIds(@Valid NearDaysListByTypeIdsRequest request) {
        Map<String, NearDaysListByTypeIdsItemDto> list = Collections.emptyMap();
        try {
            if (request == null || request.getUserId() <= 0) {
                throw ExceptionHelper.create(ErrCode.COUPON, "小米ID不符合要求");
            }

            if (request.getTypeIds() == null || request.getTypeIds().isEmpty()) {
                throw ExceptionHelper.create(ErrCode.COUPON, "券配置ID不能为空");
            } else if (request.getTypeIds().size() > 20) {
                throw ExceptionHelper.create(ErrCode.COUPON, "券配置ID不能超过20个");
            }

            long nowTime = TimeUtil.getNowUnixSecond();
            if (request.getAddTime() == null || request.getAddTime() <= 0) {
                throw ExceptionHelper.create(ErrCode.COUPON, "生成券的时间必须要大于0");
            } else if (request.getAddTime() <= nowTime - 90 * 24 * 3600) {
                throw ExceptionHelper.create(ErrCode.COUPON, "只能取90天之内生成的券");
            } else if (request.getAddTime() >= nowTime) {
                throw ExceptionHelper.create(ErrCode.COUPON, "生成时间不能为未来的时间");
            }

            //取配置信息
            List<Long> configIds = new ArrayList<>();
            for (Integer id : request.getTypeIds()) {
                configIds.add(id.longValue());
            }
            list = nearDaysList.getNearDaysListByTypeIds(request.getUserId(), configIds, request.getAddTime());
            return Result.success(list);
        } catch (BizError e) {
            log.info("coupon.assign, 根据券配置ID批量查询用户近*天的券列表失败，{}， request={}", e.getMessage(), request);
            return Result.success(list);
        } catch (Exception e) {
            log.error("coupon.assign, 根据券配置ID批量查询用户近*天的券列表异常，request={}", request, e);
            return Result.success(list);
        }
    }


    /**
     * 单用户多任务券发放接口
     *
     * @param request AssignRequest
     * @return AssignResponse
     */
    @Override
    @Deprecated
    public Result<AssignResponse> assign(AssignRequest request) {
        try {
            AssignResponse result = assigner.send(request);
            log.info("coupon.assign, 优惠券发放成功，request={}, response={}", request, result);
            return Result.success(result);
        } catch (BizError e) {
            log.info("coupon.assign, {}， request={}", e.getMessage(), request);
            return Result.fromException(e, common.getReason(e.getMessage()));
        } catch (Exception e) {
            log.error("coupon.assign, 优惠券发放异常，request={}", request, e);
            return Result.fromException(e, "优惠券发放失败");
        }
    }


    /**
     * 获取用户优惠券列表接口
     *
     * @param request 用户优惠券列表请求参数
     * @return List<> 用户优惠券列表接口返回值
     */
    @Override
    public Result<PageBeanStream<UserCouponListDto>> userCouponList(@Valid UserCouponListRequest request) {
        //参数校验
        String checkResult = checkUserCouponRequest(request);
        if (!Objects.isNull(checkResult)) {
            return Result.fail(GeneralCodes.ParamError, checkResult);
        }

        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            JProfilerUtil.init("userCouponList", log);
            JProfilerUtil.startPoint("userCouponList");

            AbstractUserCouponList userCouponList = userCouponListFactory.getUserCouponList(request.getBizPlatform());
            PageBeanStream<UserCouponListDto> userCouponListResponse = userCouponList.getUserCouponList(request);

            log.info("coupon.CouponService.userCouponList(), execute success, request={}, runTime={}ms", request, stopwatch.elapsed(TimeUnit.MILLISECONDS));
            stopwatch.stop();
            return Result.success(userCouponListResponse);
        } catch (BizError e) {
            log.warn("BizError on coupon.CouponServiceImpl.userCouponList, request={}, error={}", request, e);
            return Result.fromException(e, common.getReason(e.getMessage()));
        } catch (Exception e) {
            log.error("Exception on coupon.CouponServiceImpl.userCouponList, request={}, error={}", request, e);
            return Result.fromException(e);
        } finally {
            JProfilerUtil.logWARNWithThreshold(100L);
        }
    }


    /**
     * 获取优惠券信息接口（支持批量查询，最多20个）
     *
     * @param request CouponInfoRequest
     * @return CouponInfoResponse
     */
    @Override
    public Result<CouponInfoResponse> couponInfo(@Valid CouponInfoRequest request) {
        try {

            // 1、入参校验
            checkCouponInfoRequest(request);

            // 2、获取优惠券信息
            Integer bizPlatform = request.getBizPlatform();
            AbstractUserCouponInfo userCouponInfo = userCouponInfoFactory.getUserCouponInfo(bizPlatform);
            CouponInfoResponse result = userCouponInfo.couponInfo(request);

            return Result.success(result);
        } catch (BizError e) {
            log.info("coupon.assign, {}， request={}", e.getMessage(), request);
            return Result.fromException(e, common.getReason(e.getMessage()));
        } catch (Exception e) {
            log.error("coupon.assign, 优惠券发放异常，request={}", request, e);
            return Result.fromException(e, "优惠券发放失败");
        }
    }

    /**
     * 入参校验
     *
     * @param request 入参
     * @throws BizError bizError
     */
    private void checkCouponInfoRequest(CouponInfoRequest request) throws BizError {

        List<Long> couponIds = request.getCouponIds();

        if (CollectionUtils.isEmpty(couponIds)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "必须要传优惠券ID");
        }

        if (couponIds.size() > 20) {
            throw ExceptionHelper.create(ErrCode.COUPON, "一次最多支持20个优惠券的查询");
        }

        if(couponIds.stream().anyMatch(id -> Objects.isNull(id) || id <= 0)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠券ID不符合要求");
        }

        BizPlatformEnum bizPlatform = BizPlatformEnum.valueOf(request.getBizPlatform());
        if (Objects.isNull(bizPlatform)) {
            throw ExceptionHelper.create(ErrCode.COUPON, "业务领域不符合要求");
        }

        if (BizPlatformEnum.CAR_AFTER_SALE.equals(bizPlatform) && StringUtils.isBlank(request.getVid())) {
            throw ExceptionHelper.create(ErrCode.COUPON, "vid非法");
        }

    }

    /**
     * 获取结算页优惠劵列表
     *
     * @param request 请求参数
     * @return 返回结果
     */
    @Override
    public Result<CheckoutCouponListResponse> checkoutCouponList(@Valid CheckoutCouponListRequest request) {
        CheckoutCouponListResponse response = new CheckoutCouponListResponse();
        try {
            log.info("coupon.checkoutCouponList, request={}", request);
            response.setCouponList(couponInfoCheck.getCheckoutCouponList(request));
            return Result.success(response);
        } catch (BizError e) {
            log.warn("coupon.checkoutCouponList, {}， request={}", e.getMessage(), request);
            return Result.fromException(e, common.getReason(e.getMessage()));
        } catch (Exception e) {
            log.error("coupon.checkoutCouponList, 获取优惠券列表异常，request={}", request, e);
            return Result.fromException(e, "获取优惠券列表失败");
        }
    }

    @Override
    public Result<ProductCouponResponse> getProductCoupon(@Valid ProductCouponRequest request) {
        ProductCouponResponse response = new ProductCouponResponse();
        try {

            if (CollectionUtils.isEmpty(request.getGoodsInfoList())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "所传商品信息不能为空");
            }

            if (Objects.isNull(request.getClientId())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "所传clientId不能为空");
            }

            log.info("coupon.getProductCoupon, request={}", request);
            Map<Long, List<GoodsCouponEvent>> canApplyCouponMap = productCouponService.getProductApplyCoupon(request);
            response.setCanApplyCoupons(canApplyCouponMap);

            Map<Long, List<CouponList>> userCoupons = productCouponService.getProductUserCoupon(request, getCouponAct(canApplyCouponMap));
            response.setUserCoupons(userCoupons);
            return Result.success(response);
        } catch (Exception e) {
            log.error("coupon.getProductCoupon, 获取商品可用券接口异常，request={}", request, e);
            return Result.fromException(e, "获取商品可用券接口异常");
        }
    }


    /**
     * 取出可领券中的活动信息
     *
     * @param canApplyCouponMap 可领券活动
     * @return Map<Long, CouponEventInfo>
     */
    private Map<Long, CouponEventInfo> getCouponAct(Map<Long, List<GoodsCouponEvent>> canApplyCouponMap) {
        if (MapUtils.isEmpty(canApplyCouponMap)) {
            return Collections.emptyMap();
        }

        Map<Long, CouponEventInfo> couponActMap = new HashMap<>();
        for (List<GoodsCouponEvent> goodsCouponEvents : canApplyCouponMap.values()) {
            for (GoodsCouponEvent goodsCouponEvent : goodsCouponEvents) {
                if (!Objects.isNull(goodsCouponEvent.getEvent())) {
                    couponActMap.put(goodsCouponEvent.getEvent().getCouponTypeId(), goodsCouponEvent.getEvent());
                }
            }
        }
        return couponActMap;
    }


    /**
     * 校验用户优惠券列表接口参数
     *
     * @param request 请求参数
     * @return reason 失败原因
     */
    private String checkUserCouponRequest(UserCouponListRequest request) {

        if (Objects.isNull(request)) {
            return "userCouponList() 请求参数request=null!";
        }

        // 业务领域校验
        List<Integer> bizPlatform = request.getBizPlatform();
        if(CollectionUtils.isEmpty(bizPlatform)) {
            return "userCouponList() 请求参数bizPlatform不符合要求!";
        }

        // 非法code校验
        if (bizPlatform.stream().anyMatch(code -> Objects.isNull(BizPlatformEnum.valueOf(code)))) {
            return String.format("存在非法的业务领域：%s", GsonUtil.toJson(bizPlatform));
        }

        // 业务领域去重
        Set<Integer> bizPlatformSet = new HashSet<>(bizPlatform);
        if (bizPlatformSet.contains(BizPlatformEnum.CAR_AFTER_SALE.getCode()) && bizPlatformSet.size() > 1) {
            // 跨主体校验
            return "不支持跨主体查询";
        }

        // 汽车售后服务券校验vid
        if (bizPlatformSet.contains(BizPlatformEnum.CAR_AFTER_SALE.getCode())) {
            if (StringUtils.isBlank(request.getVid())) {
                return "userCouponList() 请求参数vid不能为空!";
            }
        }
        // 非汽车售后服务券校验userId
        else {
            Long userId = request.getUserId();
            if (userId == null || userId < CommonConstant.ZERO_LONG || userId == CommonConstant.EXCLUDE_USER_ID) {
                return "userCouponList() 请求参数userId不合法!";
            }
        }

        // 状态校验
        if (StringUtils.isBlank(request.getStatus())) {
            return "userCouponList() 请求参数status不合法!";
        }

        return null;
    }

    /**
     * 结算校验接口
     *
     * @param request CheckoutCheckerRequest
     * @return CheckoutCheckerResponse
     */
    @Override
    public Result<CheckoutCheckerResponse> checkoutChecker(@Valid CheckoutCheckerRequest request) {
        CheckoutCheckerResponse response = new CheckoutCheckerResponse();
        try {
            if (request.getUserId() == null || request.getUserId() <= 0) {
                throw ExceptionHelper.create(ErrCode.COUPON, "小米ID不能为空");
            }
            if (CollectionUtils.isEmpty(request.getCouponCode())) {
                throw ExceptionHelper.create(ErrCode.COUPON, "优惠码不能为空");
            }
            for (String code : request.getCouponCode()) {
                if (StringUtils.isEmpty(code)) {
                    throw ExceptionHelper.create(ErrCode.COUPON, "优惠码不能为空");
                }
            }
            if (request.getSkuPackageList() == null || request.getSkuPackageList().isEmpty()) {
                throw ExceptionHelper.create(ErrCode.COUPON, "商品不能为空");
            }

            log.info("coupon.checkoutCouponList, request={}", request);
            response.setCouponCodes(couponInfoCheck.checkoutCouponChecker(request));
            return Result.success(response);
        } catch (BizError e) {
            log.warn("coupon.checkoutCouponList, {}， request={}", e.getMessage(), request);
            return Result.fromException(e, common.getReason(e.getMessage()));
        } catch (Exception e) {
            log.error("coupon.checkoutCouponList, 结算校验券遇到异常，request={}", request, e);
            return Result.fromException(e);
        }
    }

    @Override
    public Result<Void> couponUsePush(CouponUsePushRequest request) {
        try {
            userCouponInfo.couponUsePush(request.getUserId(), Lists.newArrayList(request.getCouponId()), request.getStatus(), request.getModifyTime(), request.getOrderId());
            return Result.success(null);
        } catch (Exception e) {
            log.error("coupon couponUsePush error，request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 价保获取已用券信息
     *
     * @param request UsedCouponRequest
     * @return UsedCouponResponse
     */
    @Override
    public Result<GetUsedCouponResponse> getUsedCoupons(@Valid GetUsedCouponRequest request) {
        try {
            getUsedCouponsChecker(request);

            CheckoutCouponReqModel req = couponConvert.convertGetUsedCouponReq2CheckoutModel(request);
            UserCouponService userCouponService = userCouponFlowFactory.getUserCouponService(request.getCouponCode(), null);
            CheckoutCouponResModel checkoutRes = userCouponService.checkoutCoupon(req);
            GetUsedCouponResponse response = new GetUsedCouponResponse();
            response.setNoCodeCoupons(checkoutRes.getNoCodeCoupons());
            response.setCodeCoupons(checkoutRes.getCodeCoupons());
            return Result.success(response);
        } catch (BizError e) {
            log.warn("coupon.getUsedCoupons, {}， request={}", e.getMessage(), request);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("coupon.getUsedCoupons, 结算校验券遇到异常，request={}", request, e);
            return Result.fromException(e);
        }
    }

    private void getUsedCouponsChecker(GetUsedCouponRequest request) throws BizError {
        if (request.getUserId() == null || request.getUserId() <= 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "小米ID不能为空");
        }

        if (request.getClientId() == null || request.getClientId() <= 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "应用ID不能为空");
        }

        if (CollectionUtils.isEmpty(request.getCouponIds()) && StringUtils.isEmpty(request.getCouponCode())) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠券ID和优惠码不能同时为空");
        }

        if (CollectionUtils.isNotEmpty(request.getCouponIds()) && StringUtils.isNotEmpty(request.getCouponCode())) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠券ID和优惠码不能同时使用");
        }

        if (CollectionUtils.isNotEmpty(request.getCouponIds())) {
            for (Long couponId : request.getCouponIds()) {
                if (Objects.isNull(couponId) || couponId <= 0) {
                    throw ExceptionHelper.create(ErrCode.COUPON, "优惠券ID存在不合法的值");
                }
            }
        }

        if (StringUtils.isNotEmpty(request.getCouponCode()) && StringUtils.isBlank(request.getCouponCode())) {
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠码存不合法的值");
        }

        if (CollectionUtils.isEmpty(request.getSkuPackageList())) {
            throw ExceptionHelper.create(ErrCode.COUPON, "商品列表不能为空");
        }

        if (Objects.isNull(BizPlatformEnum.valueOf(request.getBizPlatform()))) {
            throw ExceptionHelper.create(ErrCode.COUPON, "业务领域不符合要求");
        }
    }

    /**
     * 获取用户可领取且可用的券和商品信息接口（简版校验）
     * 针对《云店主推线索》这种特殊场景而设计
     *
     * @param request SimpleGoodsFetchUsableRequest
     * @return SimpleGoodsFetchUsableResponse
     */
    @ApiDoc("获取用户可领取且可用的券和商品信息接口（简版校验）")
    @Override
    public Result<SimpleGoodsFetchUsableResponse> getSimpleGoodsFetchUsable(@Valid SimpleGoodsFetchUsableRequest request) {
        try {
            SimpleGoodsFetchUsableResponse result = new SimpleGoodsFetchUsableResponse();
            result.setConfigIdList(Collections.emptyList());
            result.setConfigInfo(Collections.emptyMap());

            List<MatcherConfigItemDo> configs = request.getConfigIdList().stream().filter(Objects::nonNull).distinct().map(configId -> {
                MatcherConfigItemDo item = new MatcherConfigItemDo();
                item.setConfigId(configId);
                return item;
            }).collect(Collectors.toList());


            NoCodeFetchableReqDo fetchReq = new NoCodeFetchableReqDo();
            fetchReq.setUserId(request.getUserId());
            fetchReq.setBizPlatform(Lists.newArrayList(BizPlatformEnum.RETAIL.getCode()));
            fetchReq.setOrgCode(request.getOrgCode());
            fetchReq.setConfigList(configs);
            fetchReq.setGoodsList(convertReqDtoToMatcherGoodsItemDo(request.getGoodsList()));

            fetchReq.setCheckBizPlatform(true);
            fetchReq.setCheckConfigStatus(true);
            fetchReq.setCheckConfigFetchTime(true);
            fetchReq.setCheckUserFetchLimit(true);
            fetchReq.setCheckConfigFetchLimit(true);

            fetchReq.setCheckUserChannelAndOrgCode(true);
            fetchReq.setCheckConfigGoodsInclude(true);
            fetchReq.setCheckGlobalExcludeGoods(true);
            fetchReq.setCheckGlobalCouponExcludeGoods(true);

            fetchReq.setSort(true);

            MatcherToolInterface fetchMatcher = matcherToolFactory.getProvider(MatcherToolTypeEnum.PRODUCT_NOCODE_FETCHABLE);
            MatcherContextDo fetchResp = fetchMatcher.execute(fetchReq);
            if (Objects.isNull(fetchResp) || CollectionUtils.isEmpty(fetchResp.getKeyResp())) {
                return Result.success(result);
            }

            List<CouponInfoModel> sortedCoupon = convertMatcherContextDoToModelItem(fetchResp);
            List<Long> retConfigIdList = fetchResp.getKeyResp().stream().map(MatcherRespItemDo::getConfigId).distinct().collect(Collectors.toList());
            Map<Long, UsableConfigItemDto> retConfig = couponConvert.convertCouponInfoModelToUsableConfigItemDto(sortedCoupon);

            result.setConfigIdList(retConfigIdList);
            result.setConfigInfo(retConfig);
            return Result.success(result);
        } catch (Exception e) {
            log.error("getSimpleGoodsFetchUsable, 获取用户可领取且可用的券和商品信息-简版校验接口异常，request={}", request, e);
            return Result.fromException(e, "获取用户可领取且可用的券和商品信息-简版校验接口异常");
        }
    }

    /**
     * 获取用户已领取且可用的券和商品信息接口（简版校验）
     * 针对《云店主推线索》这种特殊场景而设计
     *
     * @param request SimpleGoodsUserUsableRequest
     * @return SimpleGoodsUserUsableResponse
     */
    @ApiDoc("获取用户已领取且可用的券和商品信息接口（简版校验）")
    @Override
    public Result<SimpleGoodsUserUsableResponse> getSimpleGoodsUserUsable(@Valid SimpleGoodsUserUsableRequest request) {
        try {
            SimpleGoodsUserUsableResponse result = new SimpleGoodsUserUsableResponse();
            result.setConfigIdList(Collections.emptyList());
            result.setConfigInfo(Collections.emptyMap());

            List<MatcherConfigItemDo> configs = request.getConfigIdList().stream().filter(Objects::nonNull).distinct().map(configId -> {
                MatcherConfigItemDo item = new MatcherConfigItemDo();
                item.setConfigId(configId);
                return item;
            }).collect(Collectors.toList());

            NoCodeFetchableReqDo fetchReq = new NoCodeFetchableReqDo();
            fetchReq.setBizPlatform(Lists.newArrayList(BizPlatformEnum.RETAIL.getCode()));
            fetchReq.setOrgCode(request.getOrgCode());
            fetchReq.setConfigList(configs);
            fetchReq.setGoodsList(convertReqDtoToMatcherGoodsItemDo(request.getGoodsList()));

            fetchReq.setCheckBizPlatform(true);
            fetchReq.setCheckUserChannelAndOrgCode(true);
            fetchReq.setCheckConfigGoodsInclude(true);
            fetchReq.setCheckGlobalExcludeGoods(true);
            fetchReq.setCheckGlobalCouponExcludeGoods(true);

            fetchReq.setSort(true);

            MatcherToolInterface fetchMatcher = matcherToolFactory.getProvider(MatcherToolTypeEnum.PRODUCT_NOCODE_FETCHABLE);
            MatcherContextDo fetchResp = fetchMatcher.execute(fetchReq);
            if (Objects.isNull(fetchResp) || CollectionUtils.isEmpty(fetchResp.getKeyResp())) {
                return Result.success(result);
            }

            List<CouponInfoModel> sortedCoupon = convertMatcherContextDoToModelItem(fetchResp);
            List<Long> retConfigIdList = fetchResp.getKeyResp().stream().map(MatcherRespItemDo::getConfigId).distinct().collect(Collectors.toList());
            Map<Long, UsableConfigItemDto> retConfig = couponConvert.convertCouponInfoModelToUsableConfigItemDto(sortedCoupon);

            result.setConfigIdList(retConfigIdList);
            result.setConfigInfo(retConfig);
            return Result.success(result);
        } catch (Exception e) {
            log.error("getSimpleGoodsUserUsable, 获取用户已领取且可用的券和商品信息-简版校验接口异常，request={}", request, e);
            return Result.fromException(e, "获取用户已领取且可用的券和商品信息-简版校验接口异常");
        }
    }

    /**
     * 查询用户优惠码信息（西瓜商超投放场景）
     *
     * @param request GetXGCouponCodeInfoRequest
     * @return GetXGCouponCodeInfoResponse
     */
    @Override
    public Result<GetCouponCodeInfoResponse> getCouponCodeInfo(@Valid GetCouponCodeInfoRequest request) {
        try {
            // 1、构造查询条件
            CouponCodeQueryParam queryParam = couponConvert.toCouponCodeQueryParam(request);

            // 2、查询用户优惠码信息（西瓜商超投放场景）
            CouponCodePo couponCodePo = userCodeCouponRepository.getCouponCodeInfo(queryParam);

            // 3、构造出参
            GetCouponCodeInfoResponse response = couponConvert.toGetXGCouponCodeInfoResponse(couponCodePo);

            log.info("CouponServiceImpl.getCouponCodeInfo finished, request={}, response={}", GsonUtil.toJson(request), GsonUtil.toJson(response));

            return Result.success(response);
        } catch (Exception e) {
            log.error("getUserCouponCodeInfo, 查询用户优惠码信息异常，request={}", GsonUtil.toJson(request), e);
            return Result.fromException(e, "查询用户优惠码信息异常接口异常");
        }
    }


    private List<MatcherGoodsItemDo> convertReqDtoToMatcherGoodsItemDo(List<SimpleGoodsItem> goodsList) {
        List<MatcherGoodsItemDo> result = new ArrayList<>();
        for (SimpleGoodsItem e : goodsList) {
            if (Objects.isNull(e) ||
                    Objects.isNull(e.getSkuPackageId()) ||
                    e.getSkuPackageId() <= 0 ||
                    (!GoodsLevelEnum.Sku.getValue().equals(e.getLevel())
                            && !GoodsLevelEnum.Package.getValue().equals(e.getLevel())
                            && !GoodsLevelEnum.Ssu.getValue().equals(e.getLevel()))) {
                continue;
            }
            MatcherGoodsItemDo item = new MatcherGoodsItemDo();
            item.setId(e.getSkuPackageId());
            item.setLevel(e.getLevel());
            result.add(item);
        }
        return result;
    }

    /**
     * 转换 MatcherContextDo 至 List<CouponInfoModel>
     *
     * @param ctx .
     * @return .
     */
    private List<CouponInfoModel> convertMatcherContextDoToModelItem(MatcherContextDo ctx) {
        List<CouponInfoModel> result = new ArrayList<>();
        Map<Long, List<MatcherGoodsItemDo>> goods = ctx.getValidGoods();
        for (MatcherRespItemDo item : ctx.getKeyResp()) {
            try {
                CouponConfigItem config = ctx.getConfigInfoMap().get(item.getConfigId());
                CouponInfoModel couponInfoModel;
                if (Objects.nonNull(item.getUserCouponId())) {
                    CouponPo coupon = ctx.getUserCouponMap().get(item.getUserCouponId());
                    if (Objects.isNull(coupon)) {
                        continue;
                    }
                    couponInfoModel = couponConvert.convertPoToModel(coupon, config);
                } else {
                    couponInfoModel = couponConvert.convertPoToModelItem(config, null);
                }
                couponFormatCommon.setCouponAIS(couponInfoModel);
                if (goods.containsKey(item.getConfigId())) {
                    couponInfoModel.setValidGoods(goods.get(item.getConfigId()));
                }
                result.add(couponInfoModel);
            } catch (Exception e) {
                log.error("convertMatcherContextDoToModelItem, keyResp item conver error, item:{}, ctx:{}", GsonUtil.toJson(item), GsonUtil.toJson(ctx), e);
            }
        }
        return result;
    }

}
