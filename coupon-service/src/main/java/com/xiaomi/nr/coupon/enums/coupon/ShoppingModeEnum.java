package com.xiaomi.nr.coupon.enums.coupon;

/**
 * 购物模式
 *
 * <AUTHOR>
 */
public enum ShoppingModeEnum {
    /**
     * 默认值
     */
    DEFAULT(0),
    /**
     * 物流
     */
    LOGISTICS(1),
    /**
     * 现场购
     */
    SCENE(2),
    /**
     * 物流和现场购混合
     */
    LOGISTICS_SCENE_MIX(3);

    private final Integer type;

    ShoppingModeEnum(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }
}
