package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;

/**
 * 可用商品
 *
 * <AUTHOR>
 */
@Data
public class ConfigCacheGoods implements Serializable {

    private static final long serialVersionUID = -1043079097969043634L;

    /**
     * SKU => product_id
     */
    private Map<String, Long> sku;

    /**
     * 货品 => product_id
     */
    private Map<String, Long> goods;

    /**
     * 套装 => product_id
     */
    private Map<String, Long> packages;

    /**
     * 品类 => 品类ID
     * 这里的品类是券配置里的原始数据，sku/goods/package里都
     */
    private Map<String, Long> groups;
}