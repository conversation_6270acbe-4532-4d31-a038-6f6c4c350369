package com.xiaomi.nr.coupon.util;

import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.auth.po.AppAuthInfo;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.auth.po.ClientInfo;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * gson 序列化反序列化工具类
 *
 * <AUTHOR>
 */
public class GsonUtil {

    private static final Gson GSON;

    static {
        GSON = new Gson();
    }

    /**
     * 将object对象转成json字符串
     *
     * @param object 具体对象
     * @return json字符串
     */
    public static String toJson(Object object) {
        if (GSON == null) {
            return null;
        }
        return GSON.toJson(object);
    }

    /**
     * 将json字符串转成泛型bean
     *
     * @param json json字符串
     * @param cls  需转化的类型
     * @return T类型对象
     */
    public static <T> T fromJson(String json, Class<T> cls) {
        if (GSON == null || json == null) {
            return null;
        }
        T t = null;
        try {
            t = GSON.fromJson(json, cls);
        } catch (JsonSyntaxException e) {
            e.printStackTrace();
        }
        return t;
    }

    /**
     * TypeToken序列化
     *
     * @param json json字符串
     * @param typeOfT 序列化方式
     * @param <T>
     * @return
     */
    public static <T> T fromJson(String json, Type typeOfT) {
        T t = GSON.fromJson(json, typeOfT);
        return t;
    }

    /**
     * 将json反序列化转成list
     *
     * @param json json 字符串
     * @param cls  需要转化为列表类型的class
     * @param <T>  元素类型
     * @return T类型对象列表
     */
    public static <T> List<T> fromListJson(String json, Class<T> cls) {
        if (GSON == null || json == null) {
            return null;
        }
        List<T> list = new ArrayList<>();
        try {
            JsonElement jsonElement = new JsonParser().parse(json);
            if (jsonElement == null) {
                return null;
            }
            JsonArray array = jsonElement.getAsJsonArray();
            for (final JsonElement elem : array) {
                list.add(GSON.fromJson(elem, cls));
            }
        } catch (JsonSyntaxException e) {
            e.printStackTrace();
        }
        return list;
    }

    /**
     * 将json 反序列化成Map
     *
     * @param json json字符串
     * @return map对象
     */
    public static <T> Map<T, T> fromMapJson(String json, Type type) {
        if (GSON == null || json == null) {
            return null;
        }
        Map<T, T> map = null;
        try {
            map = GSON.fromJson(json, type);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }


    /**
     * 解析Client缓存信息
     * @param json
     * @return Map<Long, ClientStc> Client缓存信息
     */
    public static Map<Long, ClientInfo> fromMapJsonClient(String json){
        if (GSON == null || json == null) {
            return null;
        }
        Map<String, ClientInfo> jsonMap = null;
        try{
            jsonMap = GSON.fromJson(json, new TypeToken<Map<String, ClientInfo>>(){}.getType());
        }catch (Exception e){
            e.printStackTrace();
        }
        Map<Long, ClientInfo> resultMap = jsonMap.entrySet().stream().collect(Collectors.toMap
                (key -> Long.parseLong(key.getKey()), Map.Entry::getValue));
        return resultMap;
    }


    /**
     * 解析AppAuth缓存信息
     * @param json
     * @return Map<Long, AppAuthInfo>
     */
    public static Map<String, AppAuthInfo> fromMapJsonAppAuth(String json){
        if (GSON == null || json == null) {
            return null;
        }
        Map<String,AppAuthInfo> jsonMap = null;
        try{
            jsonMap = GSON.fromJson(json, new TypeToken<Map<String, AppAuthInfo>>(){}.getType());
        }catch (Exception e){
            e.printStackTrace();
        }

        return jsonMap;
    }
}