package com.xiaomi.nr.coupon.domain.couponassign;

import com.xiaomi.nr.coupon.constant.CouponConstant;
import com.xiaomi.nr.coupon.domain.couponassign.model.SingleAssignRequestDo;
import com.xiaomi.nr.coupon.domain.couponassign.model.SingleAssignResponseDo;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponSceneItem;
import com.xiaomi.nr.coupon.enums.coupon.CouponSendTypeEnum;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.FetchLimitTypeEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.SceneAssignModeEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.UseChannelType;
import com.xiaomi.nr.coupon.enums.couponconfig.UseTimeTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CarCouponMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CouponLogMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CouponMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponLogPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.nacosconfig.NacosSwitchConfig;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponassign.CouponAssignRedisDao;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.xiaomi.nr.coupon.constant.CouponConstant.SEND_SCENE_CAR_SHOP_GIFT_EXCHANGE;

/**
 * 发券功能
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SingleAssigner {

    @Resource
    private AssignCommon assignCommon;

    @Resource
    private AssignChecker assignChecker;

    @Resource
    private CouponAssignRedisDao assignRedisDao;

    @Resource
    private CouponMapper couponMapper;

    @Resource
    private CouponLogMapper mapperCouponLog;

    @Resource
    private CarCouponMapper carCouponMapper;

    @Autowired
    private NacosSwitchConfig nacosSwitchConfig;

    @Resource
    private CouponAssignNotify couponAssignNotify;

    /**
     * 单张券发放接口
     *
     * @param request SingleAssignRequestDo
     * @return SingleAssignResponseDo
     * @throws BizError .
     */
    public SingleAssignResponseDo assign(SingleAssignRequestDo request) throws BizError {

        //基本参数检查
        assignChecker.paramsChecker(request);

        //获取券配置信息
        CouponConfigItem configInfo = assignCommon.getConfigInfo(request.getConfigId());

        //获取场景信息
        CouponSceneItem sceneInfo = assignCommon.getSceneInfo(request.getSceneCode());

        //条件检查
        assignChecker.conditionChecker(request, configInfo, sceneInfo);

        //从库里取幂等数据
        CouponPo idemData = assignCommon.getIdemData(request, configInfo.getCouponConfigInfo().getSendChannel());

        //判断是否存在幂等数据，如果幂等则直接组织数据并返回
        if (idemData != null) {
            return makeReturnData(idemData, true);
        }

        //限制数检查
        limitChecker(request, configInfo);

        //生成券ID
        List<Long> couponIds = assignCommon.getCouponIds(request.getUserId(), 1);

        //构建券落库数据
        CouponPo coupon = makeCouponData(request, configInfo, couponIds.get(0));

        //构建券日志落库数据
        CouponLogPo couponLog = makeCouponLogData(coupon);

        //落库
        core(request, configInfo, coupon, couponLog);

        //发送灌券消息
        assignNotify(configInfo, coupon);

        //构建返回数据
        return makeReturnData(coupon, false);
    }

    /**
     * 限制数检查
     *
     * @param request    SingleAssignRequestDo
     * @param configInfo CouponConfigItem
     */
    private void limitChecker(SingleAssignRequestDo request, CouponConfigItem configInfo) throws BizError {
        //检查vid或用户已达到最大可得券数量
        if (assignCommon.isCarVidCoupon(request.getBizPlatform())) {
            if (FetchLimitTypeEnum.isFetchLimitType(configInfo.getCouponConfigInfo().getFetchLimitType())) {
                assignChecker.vidAssignCountChecker(request.getVid(), configInfo.getConfigId(), configInfo.getCouponConfigInfo().getFetchLimit());
            }
        } else {
            assignChecker.userAssignCountChecker(request.getUserId(), configInfo.getConfigId(), configInfo.getCouponConfigInfo().getFetchLimit());
        }

        //检查券是否已达到最大可发放数量
        assignChecker.couponAssignedCountChecker(configInfo.getConfigId(), configInfo.getCouponConfigInfo().getApplyCount());
    }

    /**
     * 构建优惠券表数据
     *
     * @param request    SingleAssignRequestDo
     * @param configInfo CouponConfigItem
     * @param couponId   long
     * @return List<CouponPo>
     */
    private CouponPo makeCouponData(SingleAssignRequestDo request, CouponConfigItem configInfo, long couponId) {
        CouponPo coupon = new CouponPo();
        long nowTime = TimeUtil.getNowUnixSecond();

        Long fromOrderId = request.getOrderId();
        if (fromOrderId == null) {
            fromOrderId = 0L;
        }

        long useStartTime = 0L;
        long useEndTime = 0L;
        Integer useTimeType = configInfo.getCouponConfigInfo().getUseTimeType();
        if (UseTimeTypeEnum.Relative.getValue().equals(useTimeType)) {
            useStartTime = nowTime;
            useEndTime = nowTime + configInfo.getCouponConfigInfo().getUseDuration() * 3600;
        } else if (UseTimeTypeEnum.Fixed.getValue().equals(useTimeType)) {
            useStartTime = configInfo.getCouponConfigInfo().getStartUseTime();
            useEndTime = configInfo.getCouponConfigInfo().getEndUseTime();
        } else if (UseTimeTypeEnum.CUSTOM.getValue().equals(useTimeType)) {
            useStartTime = request.getStartUseTime();
            useEndTime = request.getEndUseTime();
        }

        coupon.setId(couponId);
        coupon.setUserId(Optional.ofNullable(request.getUserId()).orElse(0L));
        coupon.setRequestId(request.getRequestId());
        coupon.setTypeId(request.getConfigId());
        if (StringUtils.isEmpty(request.getActivityId())) {
            coupon.setActivityId(String.valueOf(request.getConfigId()));
        } else {
            coupon.setActivityId(request.getActivityId());
        }
        coupon.setSendChannel(configInfo.getCouponConfigInfo().getSendChannel());
        coupon.setStartTime(String.valueOf(useStartTime));
        coupon.setEndTime(String.valueOf(useEndTime));
        coupon.setFromOrderId(String.valueOf(fromOrderId));

        if (SceneAssignModeEnum.Fill.getValue().equals(request.getAssignMode())) {
            coupon.setSendType(CouponSendTypeEnum.MARKETING.getValue());
        } else {
            coupon.setSendType(CouponSendTypeEnum.EXTERNAL.getValue());
        }

        //状态
        String stat = CouponStatusEnum.UNUSED.getValue();

        //default
        coupon.setStat(stat);
        coupon.setOffline(Integer.parseInt(UseChannelType.OnlineOffline.getMysqlValue()));
        coupon.setDays(0);
        coupon.setOrderId(0L);
        coupon.setUseTime(0L);
        coupon.setExpireTime(0L);
        coupon.setIsPass(1);
        coupon.setAddTime(nowTime);
        coupon.setReplaceMoney(BigDecimal.valueOf(0));
        coupon.setInvalidTime(0L);
        coupon.setReduceExpress(BigDecimal.valueOf(0));
        coupon.setParentId(0L);
        coupon.setAdminId(0L);
        coupon.setAdminName("");
        coupon.setBizPlatform(configInfo.getBizPlatform());
        coupon.setVid(request.getVid());
        coupon.setServiceType(configInfo.getServiceType());
        coupon.setTimesLimit(configInfo.getTimesLimit());
        coupon.setUsedTimes(0);

        //extend
        Map<String, String> extend = new HashMap<>(8);
        if (request.getOrgCode() != null && !Strings.isEmpty(request.getOrgCode())) {
            extend.put("orgCode", request.getOrgCode());
        }
        if (request.getShareUserId() != null && request.getShareUserId() > 0L) {
            extend.put("shareUserId", String.valueOf(request.getShareUserId()));
        }
        extend.put("appId", request.getAppId());
        JSONObject jsonObj = new JSONObject(extend);
        coupon.setExtendInfo(jsonObj.toString());

        return coupon;
    }

    /**
     * 构建优惠券日志表数据
     *
     * @param coupon CouponPo
     * @return CouponLogPo
     */
    private CouponLogPo makeCouponLogData(CouponPo coupon) {
        CouponLogPo couponLog = new CouponLogPo();
        couponLog.setCouponId(coupon.getId());
        couponLog.setUserId(coupon.getUserId());
        couponLog.setType(String.valueOf(coupon.getTypeId()));
        couponLog.setOldStat(CouponConstant.STATUS_SEND);
        couponLog.setNewStat(CouponStatusEnum.UNUSED.getValue());
        couponLog.setAdminId(coupon.getAdminId());
        couponLog.setAdminName("");
        couponLog.setOffline(coupon.getOffline());
        couponLog.setCouponDesc("外部接口调用");
        couponLog.setAddTime(coupon.getAddTime());
        return couponLog;
    }

    /**
     * 发放核心
     *
     * @param request    SingleAssignRequestDo
     * @param configInfo CouponConfigItem
     * @param coupon     CouponPo
     * @param couponLog  CouponLogPo
     * @throws BizError .
     */
    private void core(SingleAssignRequestDo request, CouponConfigItem configInfo, CouponPo coupon, CouponLogPo couponLog) throws BizError {
        if (assignCommon.isCarVidCoupon(request.getBizPlatform())) {
            coreByVid(request, configInfo, coupon);
        } else {
            coreByUser(request, configInfo, coupon, couponLog);
        }
    }

    /**
     * 针对用户-发放核心
     *
     * @param request    SingleAssignRequestDo
     * @param couponInfo CouponConfigItem
     * @param coupon     CouponPo
     * @param couponLog  CouponLogPo
     * @throws BizError .
     */
    private void coreByUser(SingleAssignRequestDo request, CouponConfigItem couponInfo, CouponPo coupon, CouponLogPo couponLog) throws BizError {
        //用户领券计数成功标记
        boolean isUserIncr = false;

        //已发券计数成功标记
        boolean isCouponIncr = false;

        try {
            assignRedisDao.incrUserAssignCount(request.getUserId(), request.getConfigId(), couponInfo.getCouponConfigInfo().getFetchLimit(), couponInfo.getCouponConfigInfo().getEndFetchTime());
            isUserIncr = true;

            //新版的
            assignRedisDao.incrCouponAssignCount(request.getUserId(), request.getConfigId(), couponInfo.getCouponConfigInfo().getApplyCount());
            isCouponIncr = true;

            couponMapper.insert(coupon);
        } catch (Throwable e) {
            if (isCouponIncr) {
                assignRedisDao.decrCouponAssignCount(request.getUserId(), request.getConfigId());
            }
            if (isUserIncr) {
                assignRedisDao.decrUserAssignCount(request.getUserId(), request.getConfigId());
            }
            if (e instanceof BizError) {
                throw e;
            }
            log.error("coupon.singleAssign, 遇到异常，优惠券发放失败, userId={}, configId={}, requestId={}", request.getUserId(), request.getConfigId(), request.getRequestId(), e);
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠券发放失败");
        }

        //日志插入失败忽略即可，不影响最终结果
        try {
            mapperCouponLog.insert(couponLog);
        } catch (Exception e) {
            log.info("coupon.singleAssign, 优惠券发放插入日志失败（忽略）, userId={}, configId={}, requestId={}", request.getUserId(), request.getConfigId(), request.getRequestId(), e);
        }
    }

    /**
     * 针对汽车vid-发放核心
     *
     * @param request    SingleAssignRequestDo
     * @param couponInfo CouponConfigItem
     * @param coupon     CouponPo
     * @throws BizError .
     */
    private void coreByVid(SingleAssignRequestDo request, CouponConfigItem couponInfo, CouponPo coupon) throws BizError {
        //vid领券计数成功标记
        boolean isVidIncr = false;

        //已发券计数成功标记
        boolean isCouponIncr = false;

        try {
            if (FetchLimitTypeEnum.isFetchLimitType(couponInfo.getCouponConfigInfo().getFetchLimitType())) {
                assignRedisDao.incrVidAssignCount(request.getVid(), request.getConfigId(), couponInfo.getCouponConfigInfo().getFetchLimit(), couponInfo.getCouponConfigInfo().getEndFetchTime());
                isVidIncr = true;
            }

            //新版的
            assignRedisDao.incrCouponAssignCount(request.getUserId(), request.getConfigId(), couponInfo.getCouponConfigInfo().getApplyCount());
            isCouponIncr = true;

            carCouponMapper.insert(coupon);
        } catch (Throwable e) {
            if (isCouponIncr) {
                assignRedisDao.decrCouponAssignCount(request.getUserId(), request.getConfigId());
            }
            if (isVidIncr) {
                assignRedisDao.decrVidAssignCount(request.getVid(), request.getConfigId());
            }
            if (e instanceof BizError) {
                throw e;
            }
            log.error("coupon.singleAssign, 遇到异常，优惠券发放失败, vid={}, configId={}, requestId={}", request.getVid(), request.getConfigId(), request.getRequestId(), e);
            throw ExceptionHelper.create(ErrCode.COUPON, "优惠券发放失败");
        }
    }

    /**
     * 构建返回数据
     *
     * @param data   CouponPo
     * @param isIdem Boolean
     * @return SingleAssignResponseDo
     */
    private SingleAssignResponseDo makeReturnData(CouponPo data, Boolean isIdem) {
        SingleAssignResponseDo result = new SingleAssignResponseDo();
        result.setIsIdempotent(isIdem);
        result.setCouponId(data.getId());
        result.setStartTime(Long.parseLong(data.getStartTime()));
        result.setEndTime(Long.parseLong(data.getEndTime()));
        return result;
    }

    private void assignNotify(CouponConfigItem configInfo, CouponPo coupon) {
        // 车商城礼品兑换场景
        if (SEND_SCENE_CAR_SHOP_GIFT_EXCHANGE.equals(configInfo.getCouponConfigInfo().getSendScene())
            && nacosSwitchConfig.isCarShopGiftAssignNotify()
        ) {
            couponAssignNotify.couponAssignNotify(configInfo, coupon);
        }
    }

}
