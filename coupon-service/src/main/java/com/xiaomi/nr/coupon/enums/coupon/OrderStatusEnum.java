package com.xiaomi.nr.coupon.enums.coupon;

/**
 * 券码类型
 */
public enum OrderStatusEnum {
    /**
     * 锁定
     */
    LOCK(1, "锁定"),
    /**
     * 提交
     */
    SUBMIT(2, "下单"),
    /**
     * 回滚
     */
    ROLLBACK(3, "关单"),

    /**
     * 锁定
     */
    REP_LOCK(6, "锁定"),
    /**
     * 提交
     */
    REP_SUBMIT(7, "下单"),
    /**
     * 回滚
     */
    REP_ROLLBACK(8, "关单");

    private int type;
    private String desc;

    OrderStatusEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
