package com.xiaomi.nr.coupon.domain.coupon.model;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponcode.po.CouponCodePo;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 明码券po+config信息
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class CouponCodePoConfigInfo implements Serializable {

    private static final long serialVersionUID = -9025099803434363181L;

    /**
     * 优惠券po信息列表
     */
    private Map<String, CouponCodePo> couponPoList;

    /**
     * 优惠券配置信息map
     */
    private Map<Long, CouponConfigItem> configMap;

}
