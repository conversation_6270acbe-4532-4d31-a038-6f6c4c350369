package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponconfig.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * 优惠券缓存变更时的通知数据
 *
 * <AUTHOR>
 */
@Data
public class CacheToNotifyMessagePo implements Serializable {

    private static final long serialVersionUID = -8751631612474637315L;

    /**
     * 类型 couponConfig：券配置缓存，goodsRelationCouponConfig：商品对应券配置列表的缓存，couponMission：券发放任务
     */
    @SerializedName("type")
    private String type;

    /**
     * 缓存数据
     */
    @SerializedName("data")
    private byte[] data;

    /**
     * 本消息生成时间戳-秒
     */
    @SerializedName("message_time")
    private long messageTime;
}
