package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 校验投放场景码
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Component
public class SceneCode extends CheckerAbstract {

    @Override
    public boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {
        if (!ctx.isCheckSceneCode()) {
            return true;
        }

        errCtx.setInvalidConfig(true);

        CouponConfigItem config = ctx.getConfigInfoMap().get(respItem.getConfigId());

        if (StringUtils.isEmpty(config.getCouponConfigInfo().getSendScene())) {
            errCtx.setErrCode(ErrCode.ASSIGN_COUPON_SCENE_INVALID.getCode());
            errCtx.setErrMsg("无效的投放场景");
            return false;
        }

        if (!config.getCouponConfigInfo().getSendScene().equals(respItem.getSceneCode())) {
            errCtx.setErrCode(ErrCode.ASSIGN_COUPON_SCENE_MISMATCH.getCode());
            errCtx.setErrMsg("没有权限发放此场景的券");
            return false;
        }

        return true;
    }
}
