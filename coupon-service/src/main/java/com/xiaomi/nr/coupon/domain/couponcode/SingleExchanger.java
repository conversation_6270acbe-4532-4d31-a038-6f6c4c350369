package com.xiaomi.nr.coupon.domain.couponcode;

import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.coupontrade.tradecheck.CouponInfoCheck;
import com.xiaomi.nr.coupon.domain.coupon.convert.CouponConvert;
import com.xiaomi.nr.coupon.domain.coupon.model.CheckoutCouponModel;
import com.xiaomi.nr.coupon.domain.coupon.model.ErrContext;
import com.xiaomi.nr.coupon.domain.couponcode.model.SingleExchangeRequestDo;
import com.xiaomi.nr.coupon.domain.couponcode.model.SingleExchangeResponseDo;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.enums.coupon.CouponSendTypeEnum;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.couponcode.UseModeEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.ModeTypeEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.UseChannelType;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponcode.po.CouponCodePo;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 券码兑换功能
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SingleExchanger {

    @Resource
    private ExchangeCommon exchangeCommon;

    @Resource
    private ExchangeChecker exchangeChecker;

    @Resource
    private ExchangeTransaction exchangeTransaction;

    @Resource
    private CouponConvert couponConvert;

    @Resource
    private CouponInfoCheck couponInfo;

    /**
     * 单位时间内最多兑换几次
     */
    private static final int USER_EXCHANGE_MAX_LIMIT = 5;

    /**
     * 单个券码兑换接口
     *
     * @param request SingleExchangeRequestDo
     * @return SingleExchangeResponseDo
     * @throws BizError .
     */
    public SingleExchangeResponseDo exchange(SingleExchangeRequestDo request) throws BizError {
        // 基本参数检查
        exchangeChecker.paramsChecker(request);

        // 防刷校验
        exchangeChecker.userExchangeCountChecker(request.getUserId(), USER_EXCHANGE_MAX_LIMIT);

        String index = DigestUtils.md5Hex(request.getCouponCode());

        // 获取券码信息
        CouponCodePo codeInfo = exchangeCommon.getCouponCodeInfo(request.getUserId(), index);

        // 获取券配置信息
        CouponConfigItem configInfo = exchangeCommon.getConfigInfo(codeInfo.getTypeId());

        // 时间/状态校验
        exchangeChecker.conditionChecker(codeInfo, configInfo, UseModeEnum.EXCHANGE.getValue(), false);

        // 校验渠道
        CheckoutCouponModel checkoutModel = couponConvert.convertExchangeCodeCouponToModel(request);
        ErrContext errContext = new ErrContext();
        if (!couponInfo.checkChannel(configInfo, checkoutModel, errContext, ModeTypeEnum.Code.getMysqlValue())) {
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_INVALID, errContext.getErrorMsg());
        }

        // 生成券ID
        List<Long> couponIds = exchangeCommon.getCouponIds(request.getUserId(), 1);

        // 构建券落库数据
        CouponPo coupon = makeCouponData(request, codeInfo, configInfo, couponIds.get(0));

        // 落库
        core(request, codeInfo, coupon);

        // 构建返回数据
        return makeReturnData(coupon.getId(), false);
    }

    /**
     * 构建优惠券表数据
     *
     * @param request    SingleExchangeRequestDo
     * @param codeInfo   CouponCodePo
     * @param configInfo CouponConfigItem
     * @param couponId   long
     * @return List<CouponPo>
     */
    private CouponPo makeCouponData(SingleExchangeRequestDo request, CouponCodePo codeInfo, CouponConfigItem configInfo, long couponId) {
        CouponPo coupon = new CouponPo();
        long nowTime = TimeUtil.getNowUnixSecond();

        coupon.setId(couponId);
        coupon.setUserId(request.getUserId());
        coupon.setRequestId(String.valueOf(codeInfo.getId()));
        coupon.setTypeId(codeInfo.getTypeId());
        coupon.setActivityId(String.valueOf(codeInfo.getBatchId()));
        coupon.setSendChannel(configInfo.getCouponConfigInfo().getSendChannel());
        coupon.setStartTime(String.valueOf(configInfo.getCouponConfigInfo().getStartUseTime()));
        coupon.setEndTime(String.valueOf(configInfo.getCouponConfigInfo().getEndUseTime()));


        //default
        coupon.setFromOrderId("0");
        coupon.setOffline(Integer.parseInt(UseChannelType.OnlineOffline.getMysqlValue()));
        coupon.setSendType(CouponSendTypeEnum.EXTERNAL.getValue());
        coupon.setStat(CouponStatusEnum.UNUSED.getValue());
        coupon.setDays(0);
        coupon.setOrderId(0L);
        coupon.setUseTime(0L);
        coupon.setExpireTime(0L);
        coupon.setIsPass(1);
        coupon.setAddTime(nowTime);
        coupon.setReplaceMoney(BigDecimal.valueOf(0));
        coupon.setInvalidTime(0L);
        coupon.setReduceExpress(BigDecimal.valueOf(0));
        coupon.setParentId(0L);
        coupon.setAdminId(0L);
        coupon.setAdminName("");
        // 业务领域：3C
        coupon.setBizPlatform(BizPlatformEnum.RETAIL.getCode());


        //extend
        Map<String, String> extend = new HashMap<>(8);
        if (!StringUtils.isEmpty(request.getOrgCode())) {
            extend.put("orgCode", request.getOrgCode());
        }
        extend.put("codeIndex", codeInfo.getCouponIndex());
        extend.put("appId", request.getAppId());
        JSONObject jsonObj = new JSONObject(extend);
        coupon.setExtendInfo(jsonObj.toString());

        return coupon;
    }

    /**
     * 兑换核心
     *
     * @param request  SingleExchangeRequestDo
     * @param codeInfo CouponCodePo
     * @param coupon   CouponPo
     * @throws BizError .
     */
    private void core(SingleExchangeRequestDo request, CouponCodePo codeInfo, CouponPo coupon) throws BizError {
        try {
            exchangeTransaction.single(codeInfo.getCouponIndex(), codeInfo.getId(), request.getOrgCode(), coupon);
        } catch (Throwable e) {
            log.warn("couponCode.singleExchange, 遇到异常，优惠券码兑换失败, userId={}, configId={}, couponId={}, index={}", request.getUserId(), codeInfo.getTypeId(), coupon.getId(), codeInfo.getCouponIndex(), e);
            throw ExceptionHelper.create(ErrCode.COUPON_CODE_EXCHANGE_FAIL, "兑换失败");
        }
    }

    /**
     * 构建返回数据
     *
     * @param couponId Long
     * @param isIdem   Boolean
     * @return SingleExchangeResponseDo
     */
    private SingleExchangeResponseDo makeReturnData(Long couponId, Boolean isIdem) {
        SingleExchangeResponseDo result = new SingleExchangeResponseDo();
        result.setIsIdempotent(isIdem);
        result.setCouponId(couponId);
        return result;
    }


}
