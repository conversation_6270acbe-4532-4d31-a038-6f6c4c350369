package com.xiaomi.nr.coupon.util;

import com.xiaomi.nr.coupon.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SystemHelper {

    @Value("${server.type}")
    private String serverType;

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 本地环境
     */
    private static final String LOCAL_PROFILE = "dev";

    /**
     * 测试环境
     */
    private static final String TEST_PROFILE = "staging";

    /**
     * 预发环境
     */
    private static final String PRE_PROFILE = "preview";

    /**
     * 线上环境
     */
    private static final String ONLINE_PROFILE = "online";


    /**
     * 当前是否为测试环境
     * @return bool
     */
    public boolean isTestSystem(){
        return StringUtils.equals(TEST_PROFILE, serverType);
    }


    /**
     * 获取到当前的环境
     * @return String
     */
    private String getActiveProfile() {
        return applicationContext.getEnvironment().getActiveProfiles()[CommonConstant.ZERO_INT];
    }
}
