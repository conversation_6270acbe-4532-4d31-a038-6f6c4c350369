package com.xiaomi.nr.coupon.infrastructure.annotation.aspect;

import com.google.common.base.Stopwatch;
import com.xiaomi.nr.coupon.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * */
@Slf4j
@Aspect
@Component
public class MethodDescAop {
    /**
     * 切点定义为注解@annotation(注解类路径)
     */
    @Pointcut("@annotation(com.xiaomi.nr.coupon.infrastructure.annotation.MethodDesc)")
    public void consume(){
    }

    @Around(value = "consume()")
    public  Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        //方法开始执行时间
        Stopwatch stopwatch = Stopwatch.createStarted();
        Object[] args = joinPoint.getArgs();
        Object result = null;

        try {
            //执行方法
            result = joinPoint.proceed(args);
        }catch (Exception e){
            long version = TimeUtil.getNowUnixMillis();
            log.info("MethodDescAop annotation Exception, method={}, version={}, error={}", joinPoint.getSignature().getName(), version, e);
        } finally {
            //方法执行时间
            log.info("method {}() runTime:{}ms",joinPoint.getSignature().getName(),stopwatch.elapsed(TimeUnit.MILLISECONDS));
        }
        return result;
    }

}
