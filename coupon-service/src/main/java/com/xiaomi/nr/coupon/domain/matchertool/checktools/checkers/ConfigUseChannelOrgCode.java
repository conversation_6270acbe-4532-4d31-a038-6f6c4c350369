package com.xiaomi.nr.coupon.domain.matchertool.checktools.checkers;

import com.alibaba.nacos.common.utils.StringUtils;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.UseChannel;
import com.xiaomi.nr.coupon.domain.matchertool.core.CheckerAbstract;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherErrContextDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherRespItemDo;
import com.xiaomi.nr.coupon.enums.coupon.CouponUseChannelEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.UseChannelEnum;
import com.xiaomi.nr.coupon.enums.store.StoreTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.storeinfo.po.OrgInfo;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 校验可用渠道&门店
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Component
public class ConfigUseChannelOrgCode extends CheckerAbstract {

    @Override
    public boolean check(MatcherContextDo ctx, MatcherRespItemDo respItem, MatcherErrContextDo errCtx) {
        if (!ctx.isCheckUserChannelAndOrgCode()) {
            return true;
        }

        errCtx.setInvalidConfig(true);

        CouponConfigItem config = ctx.getConfigInfoMap().get(respItem.getConfigId());
        String orgCode = ctx.getOrgCode();
        OrgInfo orgInfo = ctx.getOrgInfo();
        Map<Integer, UseChannel> channelMap = config.getUseChannelStore();

        // 线上
        if (StringUtils.isEmpty(orgCode)) {
            if (channelMap.containsKey(CouponUseChannelEnum.MI_SHOP.getId()) && isMiShopChannelClientId(ctx.getClientId())) {
                return true;
            }
            errCtx.setErrCode(ErrCode.USE_COUPON_CHANNEL_MISMATCH.getCode());
            errCtx.setErrMsg("优惠券不适用此渠道");
            return false;
        }

        // 线下
        Integer orgType = Objects.nonNull(orgInfo) ? orgInfo.getOrgType() : null;
        if (Objects.isNull(orgType)) {
            errCtx.setErrCode(ErrCode.USE_COUPON_ORGCODE_MISMATCH.getCode());
            errCtx.setErrMsg("优惠券不适用此门店");
            return false;
        }

        // 直营店和专卖店属于米家渠道
        UseChannelEnum useChannelEnum = null;
        if (StoreTypeEnum.isDirect(orgType) || StoreTypeEnum.isSpecialty(orgType)) {
            useChannelEnum = UseChannelEnum.MiHome;
        } else if (StoreTypeEnum.isAuthorized(orgType)) {
            useChannelEnum = UseChannelEnum.MiAuthorized;
        }

        if (Objects.isNull(useChannelEnum)) {
            errCtx.setErrCode(ErrCode.USE_COUPON_CHANNEL_MISMATCH.getCode());
            errCtx.setErrMsg("优惠券不适用此渠道");
            return false;
        }

        UseChannel useChannelConfig = channelMap.get(useChannelEnum.getCode());
        if (Objects.isNull(useChannelConfig)) {
            errCtx.setErrCode(ErrCode.USE_COUPON_CHANNEL_MISMATCH.getCode());
            errCtx.setErrMsg("优惠券不适用此渠道");
            return false;
        }

        if (useChannelConfig.getAll() || useChannelConfig.getLimitIds().contains(orgCode)) {
            return true;
        }

        errCtx.setErrCode(ErrCode.USE_COUPON_ORGCODE_MISMATCH.getCode());
        errCtx.setErrMsg("优惠券不适用此门店");
        return false;
    }

}
