package com.xiaomi.nr.coupon.infrastructure.repository.nacosconfig;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * @description: nacos开关配置
 **/
@Component
@Data
@NacosConfigurationProperties(dataId = NacosSwitchConfig.dataId, autoRefreshed = true, ignoreNestedProperties = true, type = ConfigType.JSON)
public class NacosSwitchConfig {

    public static final String dataId = "coupon_service_switch";

    private boolean writeOldUserCouponCount = true;

    private boolean readNewCouponCount = false;

    private long onlineCouponConfigId = 0L;

    private boolean carShopGiftAssignNotify = false;

}
