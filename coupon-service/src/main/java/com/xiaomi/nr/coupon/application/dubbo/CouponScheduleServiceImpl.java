package com.xiaomi.nr.coupon.application.dubbo;

import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.api.service.CouponScheduleService;
import com.xiaomi.nr.coupon.infrastructure.repository.CarCouponRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2024/5/21 14:51
 */
@Component
@Slf4j
@Service(group = "${dubbo.group}", version = "1.0", timeout = 10000)
public class CouponScheduleServiceImpl implements CouponScheduleService {
    @Autowired
    private CarCouponRepository carCouponRepository;
    
    private static final int BATH_UPDATE_SIZE = 100;

    /**
     * 更新过期补胎券状态
     */
    @Override
    public Result<Void> expireRepairTairCoupon(String param) {
        log.info("CouponScheduleServiceImpl.expireRepairTairCoupon begin");
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            // 1、获取当前所有已过期的补胎券
            long nowTime = TimeUtil.getNowUnixSecond();

            List<CouponPo> couponPos = carCouponRepository.getExpireRepairTairCoupon(nowTime);

            if (CollectionUtils.isNotEmpty(couponPos)) {
                // 2、批量更新券状态
                for (List<CouponPo> subList : Lists.partition(couponPos, BATH_UPDATE_SIZE)) {
                    List<Long> couponIds = subList.stream().map(CouponPo::getId).collect(Collectors.toList());
                    int effectRows = carCouponRepository.updateRepairTairCouponStat(couponIds);
                    log.info("CouponScheduleServiceImpl.expireRepairTairCoupon effectRows = {}, subList = {}", effectRows, subList);
                }
            }

            log.info("CouponScheduleServiceImpl.expireRepairTairCoupon finished, costTime = {}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

            return Result.success(null);
        } catch (Exception e) {
            log.error("CouponScheduleServiceImpl.expireRepairTairCoupon Exception, ", e);
            return Result.fromException(e, "更新过期补胎券状态失败");
        } finally {
            stopwatch.stop();
        }
    }
}
