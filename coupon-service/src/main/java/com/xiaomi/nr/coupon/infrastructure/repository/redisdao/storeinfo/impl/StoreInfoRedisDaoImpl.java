package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.storeinfo.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.xiaomi.nr.coupon.constant.CacheKeyConstant;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.storeinfo.StoreInfoRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.storeinfo.po.OrgInfo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 获取门店信息
 *
 */
@Component
@Slf4j
public class StoreInfoRedisDaoImpl implements StoreInfoRedisDao {

    /**
     * 信息本地缓存. 缓存1小时
     * key: orgCode val: OrgInfo
     */
    private static final Cache<String, OrgInfo> localCache = Caffeine.newBuilder()
            .initialCapacity(1000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .recordStats()
            .build();

    /**
     * 缓存操作对象
     */
    @Autowired
    @Qualifier("stringKarosRedisTemplate")
    private StringRedisTemplate redisTemplate;

    @Override
    public OrgInfo getOrgInfo(String orgCode) {
        OrgInfo info = localCache.getIfPresent(orgCode);
        if (info != null) {
            return info;
        }

        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_STORE_ORGINFO, orgCode);
        String redisOrgInfoJson = operations.get(key);
        info = GsonUtil.fromJson(redisOrgInfoJson, OrgInfo.class);
        if (info != null) {
            localCache.put(orgCode, info);
        }
        return info;
    }
}
