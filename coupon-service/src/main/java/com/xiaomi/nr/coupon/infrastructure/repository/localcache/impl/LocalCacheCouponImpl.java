package com.xiaomi.nr.coupon.infrastructure.repository.localcache.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.xiaomi.nr.coupon.infrastructure.repository.localcache.LocalCacheCoupon;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.MissionCacheDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.po.MissionCacheItemPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 本地缓存
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class LocalCacheCouponImpl implements LocalCacheCoupon {

    @Autowired
    private MissionCacheDao missionCacheDao;


    private final static MissionCacheItemPo emptyMissionCacheItemPo = new MissionCacheItemPo();

    /**
     * 本地缓存-优惠券发放任务信息. 缓存600秒
     * concurrencyLevel并发级别
     * initialCapacity缓存初始容量
     * maximumSize最大能缓存的容量
     * expireAfterAccess过期策略：多久没被访问后过期
     * key：{missionId}
     */
    private final Cache<Long, MissionCacheItemPo> couponMissionLocalCache = CacheBuilder.newBuilder()
            .initialCapacity(2000)
            .expireAfterWrite(1L, TimeUnit.HOURS)
            .build();

    /**
     * 批量从localCache里取优惠券发放任务信息
     *
     * @param missionIds List<Long>
     * @return Map<Long, MissionCacheItemPo>
     */
    @Override
    public Map<Long, MissionCacheItemPo> getCouponMission(List<Long> missionIds) {
        if (missionIds == null || missionIds.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<Long, MissionCacheItemPo> result = new HashMap<>(missionIds.size());
        List<Long> notFinds = new ArrayList<>();

        for (Long missionId : missionIds) {
            if (missionId == null) {
                log.error("localCache.getCouponMission, 任务ID列表里存在为null的值，missionIds={}", missionIds);
                continue;
            }

            MissionCacheItemPo cache = couponMissionLocalCache.getIfPresent(missionId);
            if (cache != null) {
                result.put(missionId, cache);
            } else {
                notFinds.add(missionId);
            }
        }

        //不存在未发现的
        if (notFinds.size() == 0) {
            return result;
        }

        log.info("localCache.getCouponMission, 本地缓存里未查到优惠券任务缓存，需要从redis里查找, notFinds={}", notFinds);

        Map<Long, MissionCacheItemPo> caches = missionCacheDao.get(notFinds);
        if (caches == null) {
            log.info("localCache.getCouponMission, redis缓存里未查到优惠券任务缓存, notFinds={}", notFinds);
        }

        for (Long key : notFinds) {
            MissionCacheItemPo info = emptyMissionCacheItemPo;
            if (caches != null && caches.containsKey(key)) {
                info = caches.get(key);
            }
            result.put(key, info);
            couponMissionLocalCache.put(key, info);
        }
        return result;
    }
}
