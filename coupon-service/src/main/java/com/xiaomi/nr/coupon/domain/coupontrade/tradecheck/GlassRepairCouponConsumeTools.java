package com.xiaomi.nr.coupon.domain.coupontrade.tradecheck;

import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.CouponServiceTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.CarCouponRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/1/20 15:59
 */
@Component
public class GlassRepairCouponConsumeTools extends AfterSaleCouponConsumeTools {

    @PostConstruct
    public void init() {
        AfterSaleCouponConsumeToolsFactory.register(CouponServiceTypeEnum.GLASS_REPAIR, this);
    }

    @Resource
    private CarCouponRepository carCouponRepository;

    @Override
    public boolean couponListConsumeCheck(List<CouponPo> couponPos, long orderId, String vid, long timeNow)
            throws BizError {
        int isIdempotentCnt = 0;
        for (CouponPo couponPo : couponPos) {
            if (CouponStatusEnum.USED.getValue().equals(couponPo.getStat()) && couponPo.getOrderId() == orderId) {
                isIdempotentCnt++;
                continue;
            }
            if (!CouponStatusEnum.LOCKED.getValue().equals(couponPo.getStat())) {
                throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券状态不正确");
            }
        }
        return isIdempotentCnt == couponPos.size();
    }

    @Override
    public void consumeCoupon(String vid, long userId, long orderId, Integer offline, List<CouponPo> couponPos)
            throws BizError {
        if (CollectionUtils.isEmpty(couponPos)) {
            return;
        }

        List<Long> couponIds = couponPos.stream().map(CouponPo::getId).collect(Collectors.toList());
        carCouponRepository.consumeCoupon(vid, userId, couponIds, orderId);
    }
}
