package com.xiaomi.nr.coupon.application.dubbo;


import com.xiaomi.nr.coupon.api.dto.redpacket.UserValidMoneyRequest;
import com.xiaomi.nr.coupon.api.dto.redpacket.UserValidMoneyResponse;
import com.xiaomi.nr.coupon.api.service.RedPacketService;
import com.xiaomi.nr.coupon.domain.redpacket.UserRedPacketMoney;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Service(group = "${dubbo.group}", version = "1.0", timeout = 3000, delay = 5000)
public class RedPacketServiceImpl implements RedPacketService {

    @Resource
    private UserRedPacketMoney userRedPacketMoney;

    /**
     * 查询用户可用红包总额接口
     *
     * @param request UserValidMoneyRequest
     * @return UserValidMoneyResponse
     */
    @Override
    public Result<UserValidMoneyResponse> userValidMoney(UserValidMoneyRequest request) {
        UserValidMoneyResponse response = new UserValidMoneyResponse();
        response.setMoney(0L);
        if (request == null) {
            return Result.success(response);
        }
        long runStartTime = TimeUtil.getNowUnixMillis();
        try {
            if (request.getUserId() == null || request.getUserId() <= 0) {
                throw ExceptionHelper.create(ErrCode.COUPON, "小米ID不符合要求");
            }
            long money = userRedPacketMoney.getValidMoneyByMysql(request.getUserId());
            log.info("redPacket.userValidMoney, 获取用户可用红包总额, userId={}, money={}, runTime={}ms", request.getUserId(), money, TimeUtil.sinceMillis(runStartTime));
            response.setMoney(money);
            return Result.success(response);
        } catch (BizError e) {
            log.info("redPacket.userValidMoney, 获取用户可用红包总额失败, userId={}, runTime={}ms", request.getUserId(), TimeUtil.sinceMillis(runStartTime));
            return Result.success(response);
        } catch (Exception e) {
            log.error("redPacket.userValidMoney, 获取用户可用红包总额出错, userId={}, class={}, errMsg={}, runTime={}ms", request.getUserId(), e.getClass(), e.getMessage(), TimeUtil.sinceMillis(runStartTime));
            return Result.success(response);
        }
    }
}
