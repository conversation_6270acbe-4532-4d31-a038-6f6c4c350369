package com.xiaomi.nr.coupon.domain.coupontrade.tradecheck;

import com.xiaomi.nr.coupon.enums.coupon.CarCouponLogTypeEnum;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.CouponServiceTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.CarCouponRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CarCouponLogPo;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 汽车售后服务-补胎券 券核销校验工具
 *
 * <AUTHOR>
 * @date 2024/5/11 14:59
 */
@Component
public class RepairTireCouponConsumeTools extends AfterSaleCouponConsumeTools {
    @Resource
    private CarCouponRepository carCouponRepository;

    @PostConstruct
    public void init() {
        AfterSaleCouponConsumeToolsFactory.register(CouponServiceTypeEnum.REPAIR_TAIR, this);
    }


    /**
     * 优惠券核销校验 + 幂等性校验
     *
     * @param couponPos
     * @param orderId
     * @param vid
     * @param timeNow
     * @return 是否幂等，券锁定校验失败时抛出异常
     */
    @Override
    public boolean couponListConsumeCheck(List<CouponPo> couponPos, long orderId, String vid, long timeNow) throws BizError {
        // 补胎券只能使用一张
        CouponPo couponPo = couponPos.get(0);

        long nowTime = TimeUtil.getNowUnixSecond();

        // 获取核销流水
        CarCouponLogPo couponLogPo = carCouponRepository.getCouponLogPoByLogType(vid, couponPo.getId(), orderId, CarCouponLogTypeEnum.CONSUME.getCode());
        if (null != couponLogPo) {
            // 幂等
            return true;
        }

        // 状态校验
        if (!Objects.equals(CouponStatusEnum.UNUSED.getValue(), couponPo.getStat())) {
            // 补胎券核销前状态需为-未使用
            throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券状态不正确");
        }

        if (nowTime < Long.parseLong(couponPo.getStartTime()) || nowTime > Long.parseLong(couponPo.getEndTime())) {
            // 不在有效期内
            throw ExceptionHelper.create(ErrCode.USE_COUPON_TIME_INVALID, "优惠券不在有效期内");
        }

        return false;
    }

    /**
     * 优惠券核销
     *
     * @param vid
     * @param userId
     * @param orderId
     * @param offline
     * @param couponPos
     */
    @Override
    public void consumeCoupon(String vid, long userId, long orderId, Integer offline, List<CouponPo> couponPos) throws BizError {
        if (CollectionUtils.isEmpty(couponPos)) {
            return;
        }

        // 补胎券只能使用一张
        CouponPo couponPo = couponPos.get(0);

        // 补胎券核销，使用次数+1
        Long couponId = couponPo.getId();
        Integer oldUsedTimes = couponPo.getUsedTimes();
        Integer newUsedTimes = oldUsedTimes + 1;
        carCouponRepository.updateCouponUsedTimes(vid, orderId, couponId, oldUsedTimes, newUsedTimes, Lists.newArrayList(CouponStatusEnum.UNUSED.getValue()));

        // 记录核销流水
        carCouponRepository.insertCarCouponLogPo(vid, userId, orderId, couponPo, CarCouponLogTypeEnum.CONSUME, oldUsedTimes, newUsedTimes);
    }
}
