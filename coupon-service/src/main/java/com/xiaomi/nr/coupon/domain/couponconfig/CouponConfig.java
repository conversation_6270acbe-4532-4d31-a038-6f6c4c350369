package com.xiaomi.nr.coupon.domain.couponconfig;

import com.xiaomi.nr.coupon.api.dto.couponconfig.CouponConfigSurplusDto;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponassign.CouponAssignRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.po.ConfigCacheItemPo;
import com.xiaomi.nr.coupon.util.StringUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 优惠券配置
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CouponConfig {

    @Resource
    private CouponConfigRepository couponConfigRepository;

    @Resource
    private CouponAssignRedisDao couponAssignRedisDao;

    /**
     * 根据券配置ID查询券配置发放剩余量(批量)
     *
     * @param configIds 券配置ID列表
     * @return Map<Integer, CouponConfigSurplusDto>      券配置发放剩余量列表
     */
    public Map<Integer, CouponConfigSurplusDto> getSendSurplus(List<Integer> configIds) throws Exception {
        //根据券配置id查询缓存中券配置的相关信息
        List<Long> tmpIds = new ArrayList<>();
        configIds.forEach(id -> tmpIds.add((long) id));
        Map<Long, CouponConfigItem> configCacheList = couponConfigRepository.getCouponConfigsOnlyBaseInfo(tmpIds);
        Map<Integer, CouponConfigSurplusDto> result = new HashMap<>(configCacheList.size());
        for (Map.Entry<Long, CouponConfigItem> item : configCacheList.entrySet()) {
            int maxCount = item.getValue().getCouponConfigInfo().getApplyCount();
            int sendCount = couponAssignRedisDao.getCouponAssignCount(item.getValue().getConfigId());
            CouponConfigSurplusDto obj = new CouponConfigSurplusDto();
            obj.setMaxCount(maxCount);
            obj.setSendCount(sendCount);
            obj.setSurplusCount(Math.max((maxCount - sendCount), 0));
            result.put(item.getKey().intValue(), obj);
        }
        return result;
    }
}
