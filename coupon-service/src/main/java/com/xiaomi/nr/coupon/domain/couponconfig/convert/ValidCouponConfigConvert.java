package com.xiaomi.nr.coupon.domain.couponconfig.convert;

import com.xiaomi.nr.coupon.api.dto.couponconfig.ValidConfigInfoDto;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponmission.po.MissionCacheItemPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class ValidCouponConfigConvert extends CouponConfigBaseConvert{

    public ValidConfigInfoDto  convertValidConfigInfoDto(CouponConfigItem configCache, List<MissionCacheItemPo> missionCacheList) throws BizError {


        return (ValidConfigInfoDto) convertCouponConfigBaseInfo(configCache, () -> {
            ValidConfigInfoDto validConfigInfoDto = new ValidConfigInfoDto();

            //子类特有属性单独赋值
            validConfigInfoDto.setMissions(getValidMissionDtoList(missionCacheList));
            return validConfigInfoDto;
        });
    }



}
