package com.xiaomi.nr.coupon.application.scheduler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @description: lettuce链接有效性校验定时任务
 * @author: hejiapeng
 * @Date 2022/12/1 11:36 上午
 * @Version: 1.0
 **/
@Component
@Slf4j
public class LettuceConnectionValidScheduler {

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    @Scheduled(cron="0/5 * * * * ?")
    public void task() {
        if (redisConnectionFactory instanceof LettuceConnectionFactory) {
            LettuceConnectionFactory c = (LettuceConnectionFactory) redisConnectionFactory;
            c.validateConnection();
        }
    }
}
