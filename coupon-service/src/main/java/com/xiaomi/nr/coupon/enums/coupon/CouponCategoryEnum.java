package com.xiaomi.nr.coupon.enums.coupon;

/**
 * 券码类型
 */
public enum CouponCategoryEnum {
    /**
     * 无码券
     */
    NO_CODE(1),
    /**
     * 旧版有码券
     */
    OLD_CODE(2),
    /**
     * 新版有码
     */
    NEW_CODE(3),

    /**
     * 改配下单
     */
    REP_COUPON(4);


    private final int type;

    CouponCategoryEnum(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    /**
     * 根据值获取类型
     *
     * @param val 值
     * @return enum
     */
    public static CouponCategoryEnum get(int val) {
        CouponCategoryEnum[] values = CouponCategoryEnum.values();
        for (CouponCategoryEnum typeEnum : values) {
            if (val == typeEnum.getType()) {
                return typeEnum;
            }
        }
        return null;
    }
}
