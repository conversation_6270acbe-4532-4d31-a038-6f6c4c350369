package com.xiaomi.nr.coupon.enums.couponconfig;

/**
 * 优惠券投放场景编码 枚举
 *
 * <AUTHOR>
 */
public enum SceneCodeEnum {

    /**
     * 店长券渠道
     */
    StoreManager("store_manager","店长券"),

    /**
     * 门店下单赠券渠道
     */
    StoreOrderGift("store_order_gift","门店下单赠券"),

    /**
     * 异业券渠道
     */
    DiffBusiness("diff_business","异业券");

    private final String value;
    private final String name;

    SceneCodeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static String findNameByValue(String value) {
        SceneCodeEnum[] values = SceneCodeEnum.values();
        for (SceneCodeEnum item : values) {
            if (item.getValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }
}

