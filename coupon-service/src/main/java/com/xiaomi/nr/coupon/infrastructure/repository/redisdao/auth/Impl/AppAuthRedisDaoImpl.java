package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.auth.Impl;

import com.xiaomi.nr.coupon.constant.CommonConstant;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.auth.AppAuthRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.auth.po.AppAuthInfo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.StringUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Map;


/**
 * AppAuth 缓存操作类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AppAuthRedisDaoImpl implements AppAuthRedisDao {

    /**
     * 新缓存操作对象
     */
    @Autowired
    @Qualifier("stringNewCouponRedisTemplate")
    private StringRedisTemplate redisNewTemplate;

    /**
     * 新的AppAuth缓存信息key
     */
    private static final String APPID_NEW_REDIS_KEY = "minos:app:auth:{appId}";


    /**
     * 获取新缓存的AppAuth
     *
     * @param appId  appId
     * @return AppAuthInfo
     */
    @Override
    public AppAuthInfo getNewAppAuth(String appId){
        if(StringUtils.isEmpty(appId)){
            return null;
        }

        String key = StringUtil.formatContent(APPID_NEW_REDIS_KEY, appId);
        ValueOperations<String, String> operations = redisNewTemplate.opsForValue();
        String appStr = operations.get(key);
        if(StringUtils.isEmpty(appStr)){
            log.error("AppAuthRedisDao.get, getNewAppAuth is null, appId={}", appId);
            return new AppAuthInfo();
        }

        return GsonUtil.fromJson(appStr, AppAuthInfo.class);
    }

}
