package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponcodeslave;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.couponcode.po.CouponCodePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 券码读取mapper
 *
 * <AUTHOR>
 */
@Mapper
@Component
public interface CouponCodeReadMapper {

    String QUERY_FIELD = "id, coupon_code, coupon_index, type_id, batch_id, start_time, end_time, stat, order_id, user_id, coupon_id, use_mode, use_time, org_code";

    /**
     * 获取券码信息
     *
     * @param index String
     * @return List<TbCodecoupon>
     */
    @Select("select " + QUERY_FIELD +
            " from tb_codecoupon" +
            " where coupon_index=#{index}")
    List<CouponCodePo> getCouponCode(@Param("index") String index);

    /**
     * 获取券码信息
     *
     * @param couponIndexList List<String>
     * @return List<TbCodecoupon>
     */
    @Select("<script>" +
            " select " + QUERY_FIELD +
            " from tb_codecoupon" +
            " where coupon_index in <foreach collection='couponIndexList' item='couponIndex' index='index' open='(' close=')' separator=','>#{couponIndex}</foreach>" +
            "</script>")
    List<CouponCodePo> getCouponCodeList(@Param("couponIndexList") List<String> couponIndexList);


}
