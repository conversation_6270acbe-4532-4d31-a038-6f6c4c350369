package com.xiaomi.nr.coupon.domain.matchertool.entity.nocodefetchable;

import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherBaseReqDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherConfigItemDo;
import com.xiaomi.nr.coupon.domain.matchertool.entity.common.MatcherGoodsItemDo;
import lombok.Data;

import java.util.List;

/**
 * NoCodeFetchableReqDo
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Data
public class NoCodeFetchableReqDo extends MatcherBaseReqDo {

    /**
     * 小米ID
     */
    private Long userId;

    /**
     * 所属业务领域　0-3C零售，3-汽车整车销售，4-汽车售后
     */
    private List<Integer> bizPlatform;

    /**
     * client id（仅当不传门店ID时才用到）
     */
    private Long clientId;

    /**
     * 门店ID（以它作为商城/门店渠道的唯一判断条件，它与clientId至少要传一个）
     */
    private String orgCode;

    /**
     * 券配置信息
     */
    private List<MatcherConfigItemDo> configList;

    /**
     * 商品信息
     */
    private List<MatcherGoodsItemDo> goodsList;

    /**
     * 发放方式  1:外部系统发券（默认）, 2:内部系统灌券
     */
    private Integer assignMode = 1;

    /**
     * 使用渠道
     */
    private Integer useChannel;

    /**
     * 是否校验所属业务场景
     */
    private boolean checkBizPlatform = false;

    /**
     * 是否校验券配置状态
     */
    private boolean checkConfigStatus = false;

    /**
     * 是否校验券可领时间
     */
    private boolean checkConfigFetchTime = false;

    /**
     * 是否校验用户的限领数
     */
    private boolean checkUserFetchLimit = false;

    /**
     * 是否校验券的限领数
     */
    private boolean checkConfigFetchLimit = false;


    /**
     * 是否校验投放场景码
     */
    private boolean checkSceneCode = false;

    /**
     * 是否校验投放场景状态
     */
    private boolean checkSceneStatus = false;

    /**
     * 是否校验券发放方式 外部接口/内部灌券
     */
    private boolean checkSceneAssignMode = false;

    /**
     * 是否校验券投放方式 优惠券/优惠码
     */
    private boolean checkSceneSendMode = false;

    /**
     * 是否校验发放权限
     */
    private boolean checkSceneAssignRights = false;


    /**
     * 是否校验渠道、门店在券配置的可用范围之内
     */
    private boolean checkUserChannelAndOrgCode = false;

    /**
     * 是否校验券可用商品
     */
    private boolean checkConfigGoodsInclude = false;

    /**
     * 是否校验全局排除商品
     */
    private boolean checkGlobalExcludeGoods = false;

    /**
     * 是否校验优惠券全局排除商品
     */
    private boolean checkGlobalCouponExcludeGoods = false;

    /**
     * 是否校验所属业务场景
     */
    private boolean checkUseChannel = false;

    /**
     * 可领校验参数：是否校验公开推广
     */
    private boolean checkPublicPromotion = false;

    /**
     * 是否对结果进行排序
     */
    private boolean sort = false;

}
