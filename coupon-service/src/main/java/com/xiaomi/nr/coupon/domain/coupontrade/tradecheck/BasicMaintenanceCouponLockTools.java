package com.xiaomi.nr.coupon.domain.coupontrade.tradecheck;

import com.xiaomi.nr.coupon.api.dto.trade.CouponLockItem;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.couponconfig.CouponServiceTypeEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.CarCouponRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * 汽车售后服务-基础保养券 券锁定校验工具
 *
 * <AUTHOR>
 * @date 2024/5/10 15:36
 */
@Component
public class BasicMaintenanceCouponLockTools extends AfterSaleCouponLockTools {
    @Autowired
    private CarCouponRepository carCouponRepository;

    @PostConstruct
    public void init() {
        AfterSaleCouponLockToolsFactory.register(CouponServiceTypeEnum.BASIC_MAINTENANCE, this);
    }

    /**
     * 优惠券锁定校验 + 幂等性校验
     *
     * @param couponPos
     * @param orderId
     * @param vid
     * @param timeNow
     * @return 是否幂等，券锁定校验失败时抛出异常
     */
    @Override
    public boolean couponListLockCheck(List<CouponPo> couponPos, long orderId, String vid, long timeNow) throws BizError {
        int isIdempotentCnt = 0;

        for (CouponPo couponPo : couponPos) {
            if (couponPo == null) {
                throw ExceptionHelper.create(ErrCode.USE_COUPON_NOT_FOUND, "优惠券不存在");
            }
            if (Long.parseLong(couponPo.getEndTime()) < timeNow || Long.parseLong(couponPo.getStartTime()) > timeNow) {
                throw ExceptionHelper.create(ErrCode.USE_COUPON_TIME_INVALID, "优惠券未在有效期内");
            }

            if (StringUtils.isNotBlank(couponPo.getVid()) && !couponPo.getVid().equals(vid)) {
                throw ExceptionHelper.create(ErrCode.USE_COUPON_VID_NOT_EQUATION, "仅限指定车辆可用");
            }

            // 基础保养券、漆面修复券
            if (CouponStatusEnum.LOCKED.getValue().equals(couponPo.getStat()) && couponPo.getOrderId() == orderId) {
                isIdempotentCnt++;
                continue;
            }

            if (!CouponStatusEnum.UNUSED.getValue().equals(couponPo.getStat())) {
                throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券不可用");
            }
        }

        return isIdempotentCnt == couponPos.size();
    }

    /**
     * 优惠券锁定
     *
     * @param vid
     * @param userId
     * @param orderId
     * @param offline
     * @param couponLockItems
     */
    @Override
    public void lockCoupon(String vid, long userId, long orderId, Integer offline, List<CouponLockItem> couponLockItems) throws BizError {
        if (CollectionUtils.isEmpty(couponLockItems)) {
            return;
        }

        // 锁定优惠券
        carCouponRepository.lockAfterSaleCoupon(vid, userId, orderId, couponLockItems, offline);
    }
}
