package com.xiaomi.nr.coupon.domain.matchertool.matchers;

import com.xiaomi.nr.coupon.domain.matchertool.checktools.ProductNoCodeFetchableCheckTool;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherAbstractFetchable;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherCheckToolAbstract;
import com.xiaomi.nr.coupon.enums.matchertool.MatcherToolTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 产品站-无码券可领匹配器
 *
 * <AUTHOR>
 * @date 2023/7/13
 */
@Slf4j
@Component
public class ProductMatcherNoCodeFetchable extends MatcherAbstractFetchable {

    @Resource
    private ProductNoCodeFetchableCheckTool checkTool;

    @Override
    public MatcherCheckToolAbstract getCheckTool() {
        return checkTool;
    }

    @Override
    public MatcherToolTypeEnum getMatcherToolTypeEnum() {
        return MatcherToolTypeEnum.PRODUCT_NOCODE_FETCHABLE;
    }
}
