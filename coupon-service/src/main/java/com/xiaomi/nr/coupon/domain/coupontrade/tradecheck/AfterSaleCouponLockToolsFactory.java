package com.xiaomi.nr.coupon.domain.coupontrade.tradecheck;

import com.xiaomi.nr.coupon.enums.couponconfig.CouponServiceTypeEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/10 15:28
 */
@Component
public class AfterSaleCouponLockToolsFactory {
    /**
     * AfterSaleCouponLockTools 工厂
     * Map<CouponServiceTypeEnum, AfterSaleCouponLockTools>
     */
    private static final Map<CouponServiceTypeEnum, AfterSaleCouponLockTools> AFTER_SALE_COUPON_LOCK_TOOLS_MAP = new HashMap<>();

    public static void register(CouponServiceTypeEnum serviceTypeEnum, AfterSaleCouponLockTools afterSaleCouponLockTools) {
        if (Objects.nonNull(serviceTypeEnum)) {
            AFTER_SALE_COUPON_LOCK_TOOLS_MAP.put(serviceTypeEnum, afterSaleCouponLockTools);
        }
    }

    public AfterSaleCouponLockTools getAfterSaleCouponLockTools(CouponServiceTypeEnum serviceTypeEnum) {
        return AFTER_SALE_COUPON_LOCK_TOOLS_MAP.get(serviceTypeEnum);

    }
}
