//package com.xiaomi.nr.coupon.service.couponconfig.convert;
//
//import com.xiaomi.nr.coupon.api.dto.coupon.info.CustomDetailDto;
//import com.xiaomi.nr.coupon.dao.localcache.LocalCacheCoupon;
//import com.xiaomi.nr.coupon.dao.redisdao.couponconfig.po.ConfigCacheItemPo;
//import com.xiaomi.nr.coupon.dao.redisdao.couponconfig.po.ConfigCacheTargetGoodsItem;
//import com.xiaomi.nr.coupon.enums.couponconfig.DeductTypeEnum;
//import com.xiaomi.nr.coupon.enums.couponconfig.UseTypeEnum;
//import com.xiaomi.youpin.infra.rpc.errors.BizError;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.util.CollectionUtils;
//
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.util.*;
//
//@Slf4j
//@Component
//public class CouponfigTmpConvert {
//
//    @Autowired
//    private LocalCacheCoupon localCacheCoupon;
//
//    /**
//     * 格式化优惠券规则名称
//     *
//     * @param typeId Long
//     * @return CustomDetailDto
//     */
//    public CustomDetailDto getCustomDetail(Long typeId) throws BizError {
//        ConfigCacheItemPo configCache = localCacheCoupon.getCouponConfig(typeId);
//        long couponId = configCache.getId();
//        String useType = configCache.getUseType();
//        String quotaType = configCache.getQuotaType();
//        Long quotaCount = configCache.getQuotaCount();
//        Long quotaMoney = configCache.getQuotaMoney();
//        Long reduceMoney = configCache.getReduceMoney();
//
//        //现金券
//        if (StringUtils.equals(UseTypeEnum.Cash.getRedisValue(), useType)) {
//            return getCouponCashRuleName(couponId, reduceMoney, quotaType, quotaCount, quotaMoney);
//        }
//
//        //折扣券
//        Long reduceDiscount = configCache.getReduceDiscount();
//        if (StringUtils.equals(UseTypeEnum.Discount.getRedisValue(), useType)) {
//            return getCouponDiscountRuleName(couponId, reduceDiscount, quotaType, quotaCount, quotaMoney);
//        }
//
//        //抵扣券
//        String deductType = configCache.getDeductType();
//        List<ConfigCacheTargetGoodsItem> deductTargetGoods = configCache.getDeductTargetGoods();
//        if (StringUtils.equals(UseTypeEnum.Deduction.getRedisValue(), useType)) {
//            return getCouponDeductibleRuleName(couponId, deductTargetGoods, deductType);
//        }
//
//        return null;
//    }
//
//
//    /**
//     * 获取抵扣券规则名称
//     *
//     * @param couponId          优惠券id
//     * @param deductTargetGoods 券可抵扣的货品SKU
//     * @param deductType        抵扣类型
//     * @return CustomDetailDto  优惠券规则信息
//     */
//    private CustomDetailDto getCouponDeductibleRuleName(long couponId, List<ConfigCacheTargetGoodsItem> deductTargetGoods, String deductType) {
//        CustomDetailDto customDetailDto = new CustomDetailDto();
//        if (CollectionUtils.isEmpty(deductTargetGoods)) {
//            log.info("coupopn.CouponfigTmpConvert.getCouponCashRuleName(), 要抵扣的商品为空, couponId={}", couponId);
//            return customDetailDto;
//        }
//
//        String quotasMoneyStr = "0元购买";
//        if (StringUtils.equals(DeductTypeEnum.OneCent.getRedisValue(), deductType)) {
//            quotasMoneyStr = "0.01元购买";
//        }
//
//        customDetailDto.setDesc(quotasMoneyStr);
//        customDetailDto.setBenefit("商品抵扣");
//        customDetailDto.setBenefitPre("");
//        customDetailDto.setBenefitUnit("");
//        customDetailDto.setUseTypeDesc("商品抵扣");
//        customDetailDto.setCouponRuleDesc("商品抵扣券");
//        return customDetailDto;
//    }
//
//
//    /**
//     * 格式化折扣券规则名称
//     *
//     * @param reduceDiscount 优惠折扣
//     * @param quotaType      配额类型(满元/满件)
//     * @param quotaCount     满*件
//     * @param quotaMoney     满*元
//     * @return CustomDetailDto 优惠券规则信息
//     */
//    private CustomDetailDto getCouponDiscountRuleName(long couponId, Long reduceDiscount, String quotaType, Long quotaCount, Long quotaMoney) {
//        CustomDetailDto customDetailDto = new CustomDetailDto();
//        if (Objects.isNull(reduceDiscount) || reduceDiscount < 0) {
//            log.info("coupopn.CouponfigTmpConvert.getCouponCashRuleName(), 错误的折扣, couponId={}, reduceDiscount={}", couponId, reduceDiscount);
//            return customDetailDto;
//        }
//
//        String reduceDiscountStr = tenCentToYuan(reduceDiscount);
//        StringBuilder desc = new StringBuilder("");
//
//        //满件
//        if (StringUtils.equals(quotaType, "count")) {
//            if (quotaCount == 1) {
//                desc.append("无门槛");
//            } else {
//                desc.append("满").append(quotaCount).append("件可用");
//            }
//        }
//
//        //满元
//        if (StringUtils.equals(quotaType, "money")) {
//            desc.append("满").append(centToYuan(quotaMoney)).append("元可用");
//
//        }
//
//        customDetailDto.setDesc(String.valueOf(desc));
//        customDetailDto.setBenefit(reduceDiscountStr);
//        customDetailDto.setBenefitPre("");
//        customDetailDto.setBenefitUnit("折");
//        customDetailDto.setUseTypeDesc("限时折扣");
//        customDetailDto.setCouponRuleDesc(reduceDiscountStr + "折扣优惠券");
//        return customDetailDto;
//    }
//
//
//    /**
//     * 格式化现金券规则名称
//     *
//     * @param reduceMoney 优惠金额
//     * @param quotaType   配额类型(满元/满件)
//     * @param quotaCount  满*件
//     * @param quotaMoney  满*元
//     * @return CustomDetailDto 优惠券规则信息
//     */
//    private CustomDetailDto getCouponCashRuleName(long couponId, Long reduceMoney, String quotaType, Long quotaCount, Long quotaMoney) {
//        CustomDetailDto customDetailDto = new CustomDetailDto();
//        if (Objects.isNull(reduceMoney) || reduceMoney < 0) {
//            log.info("coupopn.CouponfigTmpConvert.getCouponCashRuleName(), 错误的减*元, couponId={}, reduceMoney={}", couponId, reduceMoney);
//            return customDetailDto;
//        }
//
//        String reduceMoneyStr = centToYuan(reduceMoney);
//        String quotaMoneyStr = centToYuan(quotaMoney);
//        StringBuilder desc = new StringBuilder("");
//        StringBuilder couponRuleDesc = new StringBuilder("");
//
//        //满件
//        if (StringUtils.equals(quotaType, "count")) {
//            if (quotaCount == 1) {
//                desc.append("无门槛");
//            } else {
//                desc.append("满").append(quotaCount).append("件可用");
//            }
//            couponRuleDesc.append("满").append(quotaCount).append("件减").append(reduceMoneyStr);
//        }
//
//        //满元
//        if (StringUtils.equals(quotaType, "money")) {
//            desc.append("满").append(quotaMoneyStr).append("元可用");
//            couponRuleDesc.append("满").append(quotaMoneyStr).append("元减").append(reduceMoneyStr);
//        }
//
//        customDetailDto.setDesc(String.valueOf(desc));
//        customDetailDto.setBenefit(reduceMoneyStr);
//        customDetailDto.setBenefitPre("￥");
//        customDetailDto.setBenefitUnit("元");
//        customDetailDto.setUseTypeDesc("限时满减");
//        customDetailDto.setCouponRuleDesc(String.valueOf(couponRuleDesc.append("元优惠券")));
//        return customDetailDto;
//    }
//
//
//    /**
//     * 金额单位转换(分转元)
//     *
//     * @param value 单位是分的金额
//     * @return String
//     */
//    private String centToYuan(long value) {
//        BigDecimal showMoney = new BigDecimal(String.valueOf(value));
//        return showMoney.divide(new BigDecimal("100"), 2, RoundingMode.DOWN).stripTrailingZeros().toPlainString();
//    }
//
//
//    /**
//     * 金额单位转换(角转元)
//     *
//     * @param value 单位是角的金额
//     * @return String
//     */
//    private String tenCentToYuan(long value) {
//        BigDecimal showDiscount = new BigDecimal(String.valueOf(value));
//        return showDiscount.divide(new BigDecimal("10"), 2, RoundingMode.DOWN).stripTrailingZeros().toPlainString();
//    }
//}
