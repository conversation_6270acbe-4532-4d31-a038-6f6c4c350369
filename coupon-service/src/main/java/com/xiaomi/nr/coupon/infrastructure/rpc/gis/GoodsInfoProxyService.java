package com.xiaomi.nr.coupon.infrastructure.rpc.gis;

import com.xiaomi.goods.gis.api.BatchedInfoService;
import com.xiaomi.goods.gis.api.GoodsInfoNewService;
import com.xiaomi.goods.gis.api.SkuInfoService;
import com.xiaomi.goods.gis.dto.batched.BatchedMultiInfoRequest;
import com.xiaomi.goods.gis.dto.batched.BatchedMultiInfoResponse;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsMultiInfoRequest;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsMultiInfoResponse;
import com.xiaomi.goods.gis.dto.sku.SkuInfoRequest;
import com.xiaomi.goods.gis.dto.sku.SkuInfoResponse;
import com.xiaomi.goods.gis.dto.sku.SkuMultiInfoV2Request;
import com.xiaomi.goods.gis.dto.sku.SkuMultiInfoV2Response;
import com.xiaomi.nr.coupon.util.ResultValidator;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 调用gis相关的rpc服务
 * */
@Slf4j
@Component
public class GoodsInfoProxyService {

    @Reference(check = false, interfaceClass = SkuInfoService.class, timeout = 3000, group = "${dubbo.group.rpc}",version = "0.1")
    private SkuInfoService skuInfoService;

    @Reference(check = false, interfaceClass = BatchedInfoService.class, timeout = 3000, group = "${dubbo.group.rpc}",version = "0.1")
    private BatchedInfoService batchedInfoService;

    @Reference(check = false, interfaceClass = GoodsInfoNewService.class, timeout = 3000, group = "${dubbo.group.rpc}",version = "0.1")
    private GoodsInfoNewService goodsInfoNewService;

    /**
     * 调用gis的接口，根据sku得到GoodsInfo信息
     * @param request SkuInfoRequest
     * @return response
     * */
    public Optional<SkuInfoResponse> getGoodsInfoBySku(SkuInfoRequest request) throws BizError {

        long start = System.currentTimeMillis();
        Result<SkuInfoResponse> response = null;
        try {
            response = skuInfoService.getSkuInfo(request);

            if (response.getCode() != GeneralCodes.OK.getCode()) {
                log.error("Call gis.getGoodsInfoBySku failed. skuList={}, code={}, message={}",
                        request.getSkuList(), response.getCode(), response.getMessage());
                throw ExceptionHelper.create(GeneralCodes.InternalError, "获取商品信息失败");
            }


        } catch (Exception e) {
            log.error("Call gis.getGoodsInfoBySku failed. skuList:{} code:{} message:{}",
                    request.getSkuList(), response.getCode(), response.getMessage(),e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }

        return Optional.ofNullable(response).map(Result::getData);
    }


    /**
     * 根据sku获取商品信息(v2版本)
     * @param sku         sku list
     * @param isNeedPrice 是否需要价格信息
     * @param isNeedSpec  是否规格价格信息
     * @return SkuMultiInfoV2Response
     * @throws BizError 业务异常
     */
    public SkuMultiInfoV2Response getSkuMultiInfoV2(List<Long> sku, boolean isNeedPrice, boolean isNeedSpec) throws BizError {

        SkuMultiInfoV2Request request = new SkuMultiInfoV2Request();
        request.setNeedPrice(isNeedPrice);
        request.setNeedSpec(isNeedSpec);
        request.setSkuList(sku);

        try{
            Result<SkuMultiInfoV2Response> result = skuInfoService.getSkuMultiInfoV2(request);
            ResultValidator.validate(result, "gis getSkuMultiInfoV2");
            return result.getData();
        }catch (BizError bizError){
            log.error("GisProxyService pageAvailableSkuGis bizError request:{}", request, bizError);
            throw bizError;
        }catch (Exception e){
            log.error("GisProxyService pageAvailableSkuGis Exception request:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "gis分页查询SKU信息异常");
        }
    }


    /**
     * 根据套装id获取套装信息
     * @param packages 套装id list
     * @param isNeedPrice 是否需要价格信息
     * @return BatchedMultiInfoResponse
     * @throws BizError 业务异常
     */
    public BatchedMultiInfoResponse getBatchedMultiInfo(List<Long> packages, boolean isNeedPrice) throws BizError {

        BatchedMultiInfoRequest request = new BatchedMultiInfoRequest();
        request.setBatchedIdList(packages);
        request.setNeedPrice(isNeedPrice);

        try{
            Result<BatchedMultiInfoResponse> result = batchedInfoService.getBatchedMultiInfo(request);
            ResultValidator.validate(result, "gis getBatchedMultiInfo");
            return result.getData();
        }catch (BizError bizError){
            log.error("GisProxyService pageAvailableSkuGis bizError request:{}", request, bizError);
            throw bizError;
        }catch (Exception e){
            log.error("GisProxyService pageAvailableSkuGis Exception request:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "gis分页查询SKU信息异常");
        }
    }


    /**
     * 根据goodsId获取商品信息
     * @param goods    货品id list
     * @param isNeedPrice 是否需要价格信息
     * @param isNeedSpec  是否需要规格信息
     * @return GoodsMultiInfoResponse
     * @throws BizError 业务异常
     */
    public GoodsMultiInfoResponse getGoodsMultiInfo(List<Long> goods, boolean isNeedPrice, boolean isNeedSpec) throws BizError {

        GoodsMultiInfoRequest request = new GoodsMultiInfoRequest();
        request.setNeedPrice(isNeedPrice);
        request.setNeedSpec(isNeedSpec);
        request.setGoodsIdList(goods);

        try{
            Result<GoodsMultiInfoResponse> result = goodsInfoNewService.getGoodsMultiInfo(request);
            ResultValidator.validate(result, "gis getGoodsMultiInfo");
            return result.getData();
        }catch (BizError bizError){
            log.error("GisProxyService pageAvailableSkuGis bizError request:{}", request, bizError);
            throw bizError;
        }catch (Exception e){
            log.error("GisProxyService pageAvailableSkuGis Exception request:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "gis分页查询SKU信息异常");
        }
    }

}
