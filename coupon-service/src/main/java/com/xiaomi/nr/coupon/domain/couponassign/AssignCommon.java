package com.xiaomi.nr.coupon.domain.couponassign;

import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.domain.couponassign.model.SingleAssignRequestDo;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.domain.couponconfig.model.CouponSceneItem;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CarCouponMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CouponMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.infrastructure.rpc.sid.SidProxy;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 发券公共方法
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AssignCommon {

    @Resource
    private CouponConfigRepository configCache;

    @Resource
    private CouponMapper couponMapper;

    @Resource
    private CarCouponMapper carCouponMapper;

    @Resource
    private SidProxy sidProxy;

    /**
     * 是否为汽车vid专用券（目前只有售后服务券，以vid为主，而不是以userId为主）
     *
     * @param bizPlatform 业务领域
     * @return bool
     */
    public boolean isCarVidCoupon(Integer bizPlatform) {
        return BizPlatformEnum.CAR_AFTER_SALE.getCode().equals(bizPlatform);
    }

    /**
     * 获取券配置缓存信息
     *
     * @param id Long
     * @return CouponConfigItem
     * @throws BizError .
     */
    public CouponConfigItem getConfigInfo(Long id) throws BizError {
        CouponConfigItem cache = configCache.getCouponConfig(id);
        if (cache == null || cache.getConfigId() <= 0 || cache.getCouponConfigInfo() == null || id.intValue() != cache.getConfigId()) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_NOT_FOUND, "无法获取到有效的券配置信息");
        }
        return cache;
    }

    /**
     * 获取投放场景信息
     *
     * @param code String
     * @return CouponSceneItem
     * @throws BizError .
     */
    public CouponSceneItem getSceneInfo(String code) throws BizError {
        CouponSceneItem cache = configCache.getCouponScene(code);
        if (cache == null || cache.getSceneCode().isEmpty() || !code.equals(cache.getSceneCode())) {
            throw ExceptionHelper.create(ErrCode.ASSIGN_COUPON_SCENE_INVALID, "无法获取投放场景信息");
        }
        return cache;
    }

    /**
     * 获取幂等数据
     *
     * @param request SingleAssignRequestDo
     * @return CouponPo
     */
    public CouponPo getIdemData(SingleAssignRequestDo request, String sendChannel) {
        if (isCarVidCoupon(request.getBizPlatform())) {
            return getIdemDataByVid(request, sendChannel);
        } else {
            return getIdemDataByUserId(request, sendChannel);
        }
    }

    /**
     * 根据vid获取幂等数据
     *
     * @param request SingleAssignRequestDo
     * @return CouponPo
     */
    public CouponPo getIdemDataByVid(SingleAssignRequestDo request, String sendChannel) {
        long runStartTime = TimeUtil.getNowUnixMillis();
        List<CouponPo> data = carCouponMapper.getIdemData(request.getVid(), sendChannel, request.getRequestId(), request.getConfigId());
        log.info("coupon.singleAssign, 根据vid获取幂等数据, runTime={}毫秒, vid={}, sendChannel={}, requestId={}, missionId={}", TimeUtil.sinceMillis(runStartTime), request.getVid(), sendChannel, request.getRequestId(), request.getConfigId());
        if (data == null || data.isEmpty()) {
            return null;
        }
        return data.get(0);
    }

    /**
     * 根据用户id获取幂等数据
     *
     * @param request SingleAssignRequestDo
     * @return CouponPo
     */
    public CouponPo getIdemDataByUserId(SingleAssignRequestDo request, String sendChannel) {
        long runStartTime = TimeUtil.getNowUnixMillis();
        List<CouponPo> data = couponMapper.getIdemData(request.getUserId(), sendChannel, request.getRequestId(), request.getConfigId());
        log.info("coupon.singleAssign, 根据用户id获取幂等数据, runTime={}毫秒, userId={}, sendChannel={}, requestId={}, missionId={}", TimeUtil.sinceMillis(runStartTime), request.getUserId(), sendChannel, request.getRequestId(), request.getConfigId());
        if (data == null || data.isEmpty()) {
            return null;
        }
        return data.get(0);
    }

    /**
     * 调sid服务生成优惠ID
     *
     * @param userId long
     * @param count  int
     * @return List<Long>
     * @throws BizError .
     */
    public List<Long> getCouponIds(Long userId, int count) throws BizError {
        long runStartTime = TimeUtil.getNowUnixMillis();
        List<Long> ids;
        try {
            ids = sidProxy.get(userId, count);
            log.info("coupon.singleAssign, 调sid服务生成优惠ID, runTime={}毫秒, userId={}, count={}, ids={}", TimeUtil.sinceMillis(runStartTime), userId, count, ids);
        } catch (Exception e) {
            log.error("coupon.singleAssign, 获取优惠券ID失败, userId={}, err={}", userId, e.getMessage());
            throw ExceptionHelper.create(ErrCode.COUPON, "无法连接SID服务");
        }
        if (ids != null && ids.size() == count && ids.get(0) > 0) {
            return ids;
        }
        log.error("coupon.singleAssign, SID服务返回的券ID有误, userId={}, ids={}", userId, ids);
        throw ExceptionHelper.create(ErrCode.COUPON, "SID服务返回的券ID有误");
    }
}
