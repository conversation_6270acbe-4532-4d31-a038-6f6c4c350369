package com.xiaomi.nr.coupon.infrastructure.repository;

import com.xiaomi.nr.coupon.api.dto.trade.CouponLockItem;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CouponMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CouponOptMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@Slf4j
public class UserRepCouponRepository extends UserCouponRepository{


    @Autowired
    private CouponMapper couponMapper;

    @Autowired
    private CouponOptMapper couponOptMapper;

    /**
     * 锁定优惠券
     * @param userId
     * @param orderId
     * @param couponItems
     * @param offline
     * @throws Exception
     */
    @Transactional(value = "xmPulseWriteTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void lockCoupon(long userId, long orderId, List<CouponLockItem> couponItems, String curStatus, Integer offline) throws Exception {
        /*CouponOptPo couponOptPo = couponOptMapper.getByCouponOpt(userId, orderId, OrderStatusEnum.REP_ROLLBACK.getType());
        if (couponOptPo != null) {
            log.warn("lockCouponV2 abandon. userId:{}, orderId:{}", userId, orderId);
            return;
        }*/
        //锁券
        for (CouponLockItem couponItem : couponItems) {
            int affected = couponMapper.lockCoupon(couponItem.getCouponId(), userId, curStatus, CouponStatusEnum.LOCKED.getValue(), orderId, couponItem.getReplaceMoney(), couponItem.getReduceExpress(), offline);
            if (affected <= 0) {
                log.error("lockCouponV2 fail. userId:{},couponId:{},orderId:{}", userId, couponItem.getCouponId(), orderId);
                throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券已使用");
            }
        }
        //couponOptMapper.insert(buildCouponOptPo(userId, orderId, OrderStatusEnum.REP_LOCK));
    }

    /**
     * 核销优惠券
     * @param userId
     * @param couponIds
     * @param orderId
     * @throws Exception
     */
    @Transactional(value = "xmPulseWriteTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void consumeCoupon(long userId, List<Long> couponIds, long usedCoupon, long orderId) throws Exception {
        /*CouponOptPo couponOptPo = couponOptMapper.getByCouponOpt(userId, orderId, OrderStatusEnum.REP_ROLLBACK.getType());
        if (couponOptPo != null) {
            log.warn("consumeCoupon abandon. userId:{}, orderId:{}", userId, orderId);
            return;
        }*/

        int affected;
        if(CollectionUtils.isNotEmpty(couponIds)){
            affected = couponMapper.consumeCoupon(StringUtils.join(couponIds,","), userId, CouponStatusEnum.LOCKED.getValue(), CouponStatusEnum.USED.getValue(), orderId, TimeUtil.getNowUnixSecond());
            if (affected < couponIds.size()) {
                log.error("consumeCoupon fail. userId:{},couponId:{},orderId:{}", userId, couponIds, orderId);
                throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券已使用");
            }
        }

        // 退还老券
        if(CollectionUtils.isEmpty(couponIds) || (couponIds.get(0) != usedCoupon && usedCoupon>0)) {
            affected = couponMapper.returnCouponV2(StringUtils.join(Lists.newArrayList(usedCoupon), ","), userId, CouponStatusEnum.USED.getValue(), CouponStatusEnum.UNUSED.getValue(), orderId);
            if (affected < 1) {
                log.error("return old coupon fail. userId:{},couponId:{},orderId:{}", userId, couponIds, orderId);
                throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "退还原始使用优惠券失败");
            }
        }
        //couponOptMapper.insert(buildCouponOptPo(userId, orderId, OrderStatusEnum.REP_SUBMIT));
    }

    /**
     * 退还优惠券
     * @param userId
     * @param couponIds
     * @param orderId
     * @param offline
     * @param currentStatus
     * @return
     * @throws Exception
     */
    @Transactional(value = "xmPulseWriteTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public boolean returnCoupon(long userId, List<Long> couponIds, long oldCoupon, long orderId, Integer offline, String currentStatus, String oldCouponStatus) throws Exception {
        // 回滚老券状态
        int affected = 0;
        if(CollectionUtils.isEmpty(couponIds) || (couponIds.get(0) != oldCoupon && oldCoupon>0)) {
            affected = couponMapper.returnCouponV2(StringUtils.join(Lists.newArrayList(oldCoupon),","), userId, oldCouponStatus, CouponStatusEnum.USED.getValue(), orderId);
            if(affected <= 0){
                List<CouponPo> couponPos = couponMapper.getByCouponIdList(userId, Lists.newArrayList(oldCoupon));
                if(CollectionUtils.isEmpty(couponPos)){
                    log.error("returnCoupon fail  userId:{},orderId:{},oldCoupon:{},err:{}", userId, orderId, oldCoupon, "查询优惠券为空");
                    throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "回滚原始使用优惠券失败");
                }
                if(!CouponStatusEnum.USED.getValue().equals(couponPos.get(0).getStat())){
                    log.error("returnCoupon fail  userId:{},orderId:{},oldCoupon:{}, status:{}, err:{}", userId, orderId, oldCoupon, couponPos.get(0).getStat(), "查询优惠券为空");
                    throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "回滚原始使用优惠券失败");
                }
            }
        }

        if(CollectionUtils.isNotEmpty(couponIds)){
            affected = couponMapper.returnCouponV2(StringUtils.join(couponIds, ","), userId, currentStatus, CouponStatusEnum.UNUSED.getValue(), orderId);
        }

        /*try {
            couponOptMapper.insert(buildCouponOptPo(userId, orderId, OrderStatusEnum.REP_ROLLBACK));
        } catch (DuplicateKeyException e) {
            log.warn("returnCoupon insert optType fail  userId:{},orderId:{},affected:{},err:{}", userId, orderId, affected, "优惠券已退券或优惠券信息不正确");
        }*/

        if (affected <= 0) {
            log.warn("returnCoupon userId:{},orderId:{},affected:{},err:{}", userId, orderId, affected, "优惠券已退券或优惠券信息不正确");
            return false;
        } else {
            return true;
        }
    }
}
