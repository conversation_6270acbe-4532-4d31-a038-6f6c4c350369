package com.xiaomi.nr.coupon.domain.common.mqpush;

import com.xiaomi.nr.coupon.api.dto.enums.BizPlatformEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025-01-08 19:31
 */
@Component
public class CouponUsePushFactory {

    private static final Map<BizPlatformEnum, CouponUsePushService> COUPON_USE_PUSH_SERVICE_MAP = new HashMap<>();

    private static final Set<String> SCENE_SET = new HashSet<>();

    public static void register(BizPlatformEnum bizPlatformEnum, CouponUsePushService couponUsePushService) {
        if (Objects.nonNull(bizPlatformEnum)) {
            COUPON_USE_PUSH_SERVICE_MAP.put(bizPlatformEnum, couponUsePushService);
        }
        if (CollectionUtils.isNotEmpty(couponUsePushService.getSceneSet())) {
            SCENE_SET.addAll(couponUsePushService.getSceneSet());
        }
    }

    public CouponUsePushService getCouponUsePushService(BizPlatformEnum bizPlatformEnum) {
        return COUPON_USE_PUSH_SERVICE_MAP.get(bizPlatformEnum);
    }

    public boolean isValidScene(String scene) {
        return scene != null && SCENE_SET.contains(scene);
    }

}
