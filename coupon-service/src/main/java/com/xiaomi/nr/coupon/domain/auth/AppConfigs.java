package com.xiaomi.nr.coupon.domain.auth;

import com.xiaomi.nr.coupon.util.GsonUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 应用权限配置
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "auth.config")
@Data
public class AppConfigs {
    /**
     * 这个名称不能改变，它是与配置文件里面的一致
     */
    private List<AppConfig> apps;

    /**
     * 用于存储所有权限
     */
    private static Map<String, Map<String, List<String>>> appPermissions = new HashMap<>();

    static {
        //所有权限人工配置（非常重要），如果权限有调整，目前只能在这里配置
        String conf = "{" +
                    //新零售支持部-换新中台
                    "XM2102:{" +
                        "com.xiaomi.nr.coupon.api.service.RecycleEcardDubboService:[" +
                            "assign," +
                            "cancel," +
                            "query" +
                        "]" +
                    "}," +
                    //新零售中台_会员中台
                    "XM2103:{" +
                        "com.xiaomi.nr.coupon.api.service.CouponService:[" +
                            "userCouponValidCount" +
                        "]" +
                    "}" +
                "}";

        appPermissions = GsonUtil.fromJson(conf, appPermissions.getClass());
    }

    /**
     * 获取所有不需要验证token和appId的服务方法
     * 背景是权限上线前接入的服务没传这些字段
     *
     * @return List<String>
     */

/*
    public static final Set<String> notCheckSet = new HashSet<>(10);
    static {
        notCheckSet.add("com.xiaomi.nr.coupon.api.service.DubboHealthService.health");
        notCheckSet.add("com.xiaomi.nr.coupon.api.service.CouponConfigService.changeNotify");
        notCheckSet.add("com.xiaomi.nr.coupon.api.service.CouponConfigService.getGoodsList");
        notCheckSet.add("com.xiaomi.nr.coupon.api.service.CouponConfigService.getGoodsListByIds");
        notCheckSet.add("com.xiaomi.nr.coupon.api.service.CouponConfigService.getConfigList");
        notCheckSet.add("com.xiaomi.nr.coupon.api.service.CouponConfigService.assignGenCouponConfigCache");
        notCheckSet.add("com.xiaomi.nr.coupon.api.service.CouponConfigService.getCouponConfigInfoList");
        notCheckSet.add("com.xiaomi.nr.coupon.api.service.CouponConfigService.getValidConfigList");
        notCheckSet.add("com.xiaomi.nr.coupon.api.service.CouponService.userCouponValidCount");
        notCheckSet.add("com.xiaomi.nr.coupon.api.service.RecycleEcardDubboService.assign");
        notCheckSet.add("com.xiaomi.nr.coupon.api.service.RecycleEcardDubboService.cancel");
        notCheckSet.add("com.xiaomi.nr.coupon.api.service.RecycleEcardDubboService.query");
        notCheckSet.add("com.xiaomi.nr.coupon.api.service.CouponService.couponInfo");
    }
*/


    /**
     * 获取所有权限配置
     *
     * @return Map<String, Map < String, List < String>>>
     */
    public static Map<String, Map<String, List<String>>> getPermissions() {
        return appPermissions;
    }


    /**
     * 获取需要验证token和appId的服务方法
     * 接口校验白名单
     *
     * @return List<String>
     */

    public static final Set<String> needCheckSet = new HashSet<>(3);
    static {
        needCheckSet.add("com.xiaomi.nr.coupon.api.service.CouponAssignService.single");
        needCheckSet.add("com.xiaomi.nr.coupon.api.service.CouponAssignService.singleExchange");
        needCheckSet.add("com.xiaomi.nr.coupon.api.service.CouponService.assign");
        needCheckSet.add("com.xiaomi.nr.coupon.api.service.CouponAssignService.fillSingle");
    }

}