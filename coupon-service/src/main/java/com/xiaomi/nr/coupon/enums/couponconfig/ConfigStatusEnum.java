package com.xiaomi.nr.coupon.enums.couponconfig;

/**
 * 优惠券配置状态（缓存里的） 枚举
 *
 * <AUTHOR>
 */
public enum ConfigStatusEnum {

    /**
     * 上线
     */
    Online(1, "上线"),

    /**
     * 下线
     */
    Offline(2, "下线"),

    /**
     * 终止
     */
    Stop(3, "终止");

    private final Integer value;
    private final String name;

    ConfigStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static String findNameByValue(Integer value) {
        ConfigStatusEnum[] values = ConfigStatusEnum.values();
        for (ConfigStatusEnum item : values) {
            if (item.getValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }
}

