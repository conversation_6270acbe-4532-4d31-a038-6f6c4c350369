package com.xiaomi.nr.coupon.domain.common.model;

import com.xiaomi.nr.coupon.enums.coupon.OrderStatusEnum;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description 
 * <AUTHOR>
 * @date 2025-01-09 09:48
*/
@Data
@AllArgsConstructor
public class MessageBuildContext {

    private CouponUsePushContext couponUsePushContext;

    private CouponPo couponPo;

    private String sceneCode;

    private String profile;

    private Integer orderStatus;
}
