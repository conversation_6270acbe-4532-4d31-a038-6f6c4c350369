package com.xiaomi.nr.coupon.domain.matchertool.matchers;

import com.xiaomi.nr.coupon.domain.matchertool.checktools.ProductNoCodeUsableCheckTool;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherAbstractUsable;
import com.xiaomi.nr.coupon.domain.matchertool.core.MatcherCheckToolAbstract;
import com.xiaomi.nr.coupon.enums.matchertool.MatcherToolTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 产品站-可用匹配器
 *
 * <AUTHOR>
 * @date 2023/7/13
 */
@Slf4j
@Component
public class ProductMatcherNoCodeUsable extends MatcherAbstractUsable {

    @Resource
    private ProductNoCodeUsableCheckTool checkTool;

    @Override
    public MatcherCheckToolAbstract getCheckTool() {
        return checkTool;
    }

    @Override
    public MatcherToolTypeEnum getMatcherToolTypeEnum() {
        return MatcherToolTypeEnum.PRODUCT_NOCODE_USABLE;
    }

}
