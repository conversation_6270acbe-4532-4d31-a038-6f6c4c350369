package com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.impl;

import com.xiaomi.nr.coupon.constant.CommonConstant;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.GoodsConfigRedisDao;
import com.xiaomi.nr.coupon.infrastructure.repository.redisdao.couponconfig.po.GoodsConfigRelationPo;
import com.xiaomi.nr.coupon.util.GsonUtil;
import com.xiaomi.nr.coupon.util.StringUtil;
import com.xiaomi.nr.coupon.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 商品对应券配置的redis缓存操作对象
 * <AUTHOR>
 */
@Component
@Slf4j
public class GoodsConfigRedisDaoImpl implements GoodsConfigRedisDao {
    /**
     * 缓存操作对象
     */
    @Autowired
    @Qualifier("stringNewCouponRedisTemplate")
    private StringRedisTemplate redisTemplate;

    /**
     * 是level和id两个值拼起来的
     */
    private static final String KEY_GOODS_COUPON_CONFIG_CACHE = "nr_coupon_goods_relation_config_cache_{level_id}";

    /**
     * 每次读取100个缓存
     */
    private static final int LIMIT_REDIS_GET_COUNT = 100;


    /**
     * 从redis读取单个SKU/套装对应优惠券配置列表
     *
     * @param id Long
     * @param level String
     * @return GoodsConfigRelationPo
     */
    @Override
    public GoodsConfigRelationPo get(Long id, String level) {
        if (id == null || id <= 0 || level == null || level.isEmpty()) {
            return null;
        }

        String key = String.format("%s_%s", level, id);
        List<String> params = new ArrayList<>();
        params.add(key);
        Map<String, GoodsConfigRelationPo> r = get(params);

        if (r == null || r.isEmpty() || r.get(key) == null) {
            return null;
        }

        if (r.get(key).getId().equals(id) && r.get(key).getLevel().equals(level)) {
            return r.get(key);
        }
        return null;
    }

    /**
     * 批量获取商品可用券配置列表缓存
     *
     * @param params List<String> {level}_{id}
     * @return Map<String, GoodsConfigRelationPo>
     */
    @Override
    public Map<String, GoodsConfigRelationPo> get(List<String> params){
        if (params == null || params.isEmpty()) {
            return Collections.emptyMap();
        }

        //参数去重
        params = params.stream().distinct().collect(Collectors.toList());

        long runStartTime = TimeUtil.getNowUnixMillis();

        Map<String, GoodsConfigRelationPo> result = new HashMap<>(params.size());
        ValueOperations<String, String> operations = redisTemplate.opsForValue();

        List<String> keys = new ArrayList<>();
        for (String item : params) {
            if(item == null || item.isEmpty()) {
                continue;
            }

            keys.add(StringUtil.formatContent(KEY_GOODS_COUPON_CONFIG_CACHE, item));

            if (keys.size() < LIMIT_REDIS_GET_COUNT) {
                continue;
            }

            List<String> jsonStrList = operations.multiGet(keys);
            List<GoodsConfigRelationPo> infos = decodeBaseInfo(params, jsonStrList);
            if(infos.size() > 0){
                infos.forEach(v->result.put(String.format("%s_%s", v.getLevel(), v.getId()), v));
            }
            keys.clear();
        }

        if (!CollectionUtils.isEmpty(keys)) {
            List<String> jsonStrList = new ArrayList<>();
            if (keys.size() > 1) {
                jsonStrList = operations.multiGet(keys);
            } else {
                jsonStrList.add(operations.get(keys.get(0)));
            }
            List<GoodsConfigRelationPo> infos = decodeBaseInfo(params, jsonStrList);
            if(infos.size() > 0){
                infos.forEach(v->result.put(String.format("%s_%s", v.getLevel(), v.getId()), v));
            }
        }

        StringBuilder noFindIds = new StringBuilder();
        for(String id : params) {
            if(!result.containsKey(id)) {
                noFindIds.append(id).append(",");
            }
        }
        if(!Strings.isNullOrEmpty(String.valueOf(noFindIds))) {
            log.info("GoodsConfigRedisDaoImpl.get, 以下商品可用券配置列表key在redis里未找到, noFindIds={}", noFindIds);
        }

        long runCostTime = TimeUtil.sinceMillis(runStartTime);
        if(runCostTime > CommonConstant.REDIS_TIME_OUT) {
            log.info("GoodsConfigRedisDaoImpl.get, 获取商品可用券配置列表缓存时间比较长, runTime={}ms, params={}", runCostTime, params);
        }

        return result;
    }

    /**
     * 解析券缓存基本信息
     *
     * @param keys List<String>
     * @param data List<String>
     * @return List<GoodsConfigRelationPo>
     */
    private List<GoodsConfigRelationPo> decodeBaseInfo(List<String> keys, List<String> data) {
        List<GoodsConfigRelationPo> result = new ArrayList<>();
        if (data == null) {
            log.info("goodsRelationConfig.cache, 从redis里取到的数据为null, keys={}", keys);
            return result;
        }
        if (data.size() == 0) {
            return result;
        }

        for (String resJson : data) {
            if (resJson == null || resJson.isEmpty()) {
                log.warn("goodsRelationConfig.cache, 从redis里取到的数据存在为空的情况, keys={}, resJson={}", keys, resJson);
                continue;
            }

            GoodsConfigRelationPo info = GsonUtil.fromJson(resJson, GoodsConfigRelationPo.class);
            if (info == null || info.getId() <= 0) {
                log.warn("goodsRelationConfig.cache, 从redis里取到的数据解析后发现有存在不符合要求的, keys={}, resJson={}", keys, resJson);
                continue;
            }
            result.add(info);
        }
        return result;
    }
}