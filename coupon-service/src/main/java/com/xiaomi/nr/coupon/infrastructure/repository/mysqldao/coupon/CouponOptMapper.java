package com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon;

import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponOptPo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * 券日志写入
 * <AUTHOR>
 */
@Mapper
@Repository
public interface CouponOptMapper {

    /**
     * 插入操作记录
     * @param couponOptPo
     * @return
     */
    @Insert("insert into tb_coupon_opt (id, user_id, order_id, opt_type, add_time) values (#{couponOptPo.id},#{couponOptPo.userId},#{couponOptPo.orderId},#{couponOptPo.optType},#{couponOptPo.addTime})")
    Integer insert(@Param("couponOptPo") CouponOptPo couponOptPo);

    /**
     * 查询操作记录
     * @param uid
     * @param orderId
     * @param opt
     * @return
     */
    @Select("select user_id,order_id,opt_type from tb_coupon_opt where user_id=#{uid} and order_id = #{orderId} and opt_type = #{opt} for update")
    CouponOptPo getByCouponOpt(@Param("uid") long uid, @Param("orderId") long orderId, @Param("opt") long opt);
}