package com.xiaomi.nr.coupon.util;

import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ResultValidator {

    /**
     * 判断rpc调用是否成功，允许抛出异常，当无法处理错误信息，需要抛给调用者时使用
     *
     * @param result
     * @param errorMsg
     */
    public static void validate(Result result, String errorMsg) throws BizError {
        if (result == null) {
            throw ExceptionHelper.create(GeneralCodes.InternalError, " rpc result is null, errorMsg:"+ errorMsg);
        }
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("rpc invoke failed, result:{}", result);
            throw ExceptionHelper.create(GeneralCodes.InternalError, errorMsg + " " + result.getMessage() + "," + result.getCode());
        }
    }

}
