package com.xiaomi.nr.coupon.domain.coupontrade;

import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 结算检查工厂
 *
 * <AUTHOR>
 * @date 2024/1/4
 */
@Component
public class TradeCheckFactory {

    private final Map<Integer, TradeCheckAbstract> toolMap;

    @Autowired
    public TradeCheckFactory(List<TradeCheckAbstract> toolList) {
        toolMap = new HashMap<>(toolList.size());
        toolList.forEach(e -> toolMap.put(e.getFactoryKey(), e));
    }

    /**
     * 获取业务领域处理类
     * @param bizPlatform
     * @return
     * @throws BizError
     */
    public TradeCheckAbstract getProvider(Integer bizPlatform) throws BizError {
        Integer key = Optional.ofNullable(bizPlatform).orElse(-1);
        TradeCheckAbstract provider = toolMap.get(key);
        if (Objects.isNull(provider)) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "业务领域不符合要求");
        }
        return provider;
    }
}
