package com.xiaomi.nr.coupon.infrastructure.repository;

import com.xiaomi.nr.coupon.api.dto.trade.CouponLockItem;
import com.xiaomi.nr.coupon.enums.coupon.CouponStatusEnum;
import com.xiaomi.nr.coupon.enums.coupon.OrderStatusEnum;
import com.xiaomi.nr.coupon.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CouponMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.CouponOptMapper;
import com.xiaomi.nr.coupon.infrastructure.repository.mysqldao.coupon.po.CouponOptPo;
import com.xiaomi.nr.coupon.util.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

@Component
@Slf4j
public class UserUseCouponRepository extends UserCouponRepository{

    @Autowired
    private CouponMapper couponMapper;

    @Autowired
    private CouponOptMapper couponOptMapper;

    /**
     * 锁定优惠券
     * @param userId
     * @param orderId
     * @param couponItems
     * @param offline
     * @throws Exception
     */
    @Transactional(value = "xmPulseWriteTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void lockCoupon(long userId, long orderId, List<CouponLockItem> couponItems, Integer offline) throws BizError {
        CouponOptPo couponOptPo = couponOptMapper.getByCouponOpt(userId, orderId, OrderStatusEnum.ROLLBACK.getType());
        if (couponOptPo != null) {
            log.warn("lockCoupon abandon. userId:{}, orderId:{}", userId, orderId);
            return;
        }
        //锁券
        for (CouponLockItem couponItem : couponItems) {
            int affected = couponMapper.lockCoupon(couponItem.getCouponId(), userId, CouponStatusEnum.UNUSED.getValue(), CouponStatusEnum.LOCKED.getValue(), orderId, couponItem.getReplaceMoney(), couponItem.getReduceExpress(), offline);
            if (affected <= 0) {
                log.error("lockCoupon fail. userId:{},couponId:{},orderId:{}", userId, couponItem.getCouponId(), orderId);
                throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券已使用");
            }
        }
        couponOptMapper.insert(buildCouponOptPo(userId, orderId, OrderStatusEnum.LOCK));
    }

    /**
     * 核销优惠券
     * @param userId
     * @param couponIds
     * @param orderId
     * @throws Exception
     */
    @Transactional(value = "xmPulseWriteTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void consumeCoupon(long userId, List<Long> couponIds, long orderId) throws BizError {

        CouponOptPo couponOptPo = couponOptMapper.getByCouponOpt(userId, orderId, OrderStatusEnum.ROLLBACK.getType());
        if (couponOptPo != null) {
            log.warn("consumeCoupon abandon. userId:{}, orderId:{}", userId, orderId);
            return;
        }
        int affected = couponMapper.consumeCoupon(StringUtils.join(couponIds, ","), userId, CouponStatusEnum.LOCKED.getValue(), CouponStatusEnum.USED.getValue(), orderId, TimeUtil.getNowUnixSecond());
        if (affected < couponIds.size()) {
            log.error("consumeCoupon fail. userId:{},couponId:{},orderId:{}", userId, couponIds, orderId);
            throw ExceptionHelper.create(ErrCode.USE_COUPON_STAT_ERROR, "优惠券已使用");
        }
        couponOptMapper.insert(buildCouponOptPo(userId, orderId, OrderStatusEnum.SUBMIT));
    }


    /**
     * 退还优惠券
     * @param userId
     * @param couponIds
     * @param orderId
     * @param offline
     * @param currentStatus
     * @return
     * @throws Exception
     */
    @Transactional(value = "xmPulseWriteTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public boolean returnCoupon(long userId, List<Long> couponIds, long orderId,Integer offline, String currentStatus) throws BizError {

        int affected = couponMapper.returnCoupon(StringUtils.join(couponIds, ","), userId, currentStatus, CouponStatusEnum.UNUSED.getValue(), orderId,
                BigDecimal.valueOf(0.0), BigDecimal.valueOf(0), offline);

        try {
            couponOptMapper.insert(buildCouponOptPo(userId, orderId, OrderStatusEnum.ROLLBACK));
        } catch (DuplicateKeyException e) {
            log.warn("returnCoupon insert optType fail  userId:{},orderId:{},affected:{},err:{}", userId, orderId, affected, "优惠券已退券或优惠券信息不正确");
        }
        if (affected <= 0) {
            log.warn("returnCoupon userId:{},orderId:{},affected:{},err:{}", userId, orderId, affected, "优惠券已退券或优惠券信息不正确");
            return false;
        } else {
            return true;
        }
    }

}
