package com.xiaomi.nr.coupon.util;

import java.util.Objects;
import java.util.function.Function;

/**
 * 转换工具类
 *
 * <AUTHOR>
 * @date 2025/1/20 15:34
 */
public class ObjectFuncUtil {

    private ObjectFuncUtil() {
    }

    public static <T, E> E convert(T t, Function<T, E> func) {
        return convert(t, func, null);
    }

    public static <T, E> E convert(T t, Function<T, E> func, E defaultValue) {
        if(Objects.isNull(t) || Objects.isNull(func)){
            return defaultValue;
        }
        E ret = func.apply(t);
        return ret == null ? defaultValue : ret;
    }

    public static <T, M, E> E multiConvert(T t, Function<T, M> func1, Function<M, E> func2) {
        return multiConvert(t, func1, func2, null);
    }

    public static <T, M, E> E multiConvert(T t, Function<T, M> func1, Function<M, E> func2, E defaultValue) {
        M m = convert(t, func1, null);
        return convert(m, func2, defaultValue);
    }

}
