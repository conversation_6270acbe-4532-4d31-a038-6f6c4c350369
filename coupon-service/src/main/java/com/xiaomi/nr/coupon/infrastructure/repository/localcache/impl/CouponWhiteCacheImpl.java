package com.xiaomi.nr.coupon.infrastructure.repository.localcache.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import com.xiaomi.nr.coupon.infrastructure.repository.localcache.CouponWhiteCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * @description: 优惠券用户白名单本地缓存
 * @author: hejiapeng
 * @Date 2022/2/26 8:35 下午
 * @Version: 1.0
 **/
@Component
@Slf4j
public class CouponWhiteCacheImpl implements CouponWhiteCache {

    private static final long EXPIRE_TIME = 60;

    private static Cache<Long, Boolean> whiteCache = Caffeine.newBuilder()
            .expireAfterWrite(EXPIRE_TIME, TimeUnit.MINUTES)
            .maximumSize(1000000)
            .recordStats()
            .build();


    @Override
    public boolean checkWhiteListContain(Long userId) {
        if (userId == null) {
            return false;
        }
        return whiteCache.getIfPresent(userId) != null;
    }


    @Override
    public void addWhiteListCache(Long userId) {
        if (userId == null) {
            return;
        }
        whiteCache.put(userId, true);
    }

    @Override
    public CacheStats getCacheStatistics() {
        return whiteCache.stats();
    }

}
