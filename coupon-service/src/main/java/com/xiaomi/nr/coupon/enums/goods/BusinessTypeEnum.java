package com.xiaomi.nr.coupon.enums.goods;

/**
 * 商家类型 枚举
 * 商品赵悦飞书提供的枚举定义
 *
 * <AUTHOR>
 * @date 2023/7/24
 */
public enum BusinessTypeEnum {

    /**
     * 普通自营商品
     */
    CN_ORDER(1, "普通自营商品"),

    /**
     * 有品商品、Pop商品
     */
    CN_WOMAI(2, "三方商品"),

    /**
     * 门店商品
     */
    CN_FAMILYS(3, "门店商品"),

    /**
     * 有品小米网
     */
    CN_YPORDER(4, "有品小米网"),

    /**
     * 有品VMI
     */
    CN_YP_VMI(5, "有品VMI"),

    /**
     * 有品VMI自营
     */
    CN_YP_VMI_SELF(6, "有品VMI自营");

    private final Integer value;
    private final String name;

    BusinessTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return this.value;
    }

    public String getName() {
        return name;
    }

    public static boolean findByValue(Integer value) {
        BusinessTypeEnum[] values = BusinessTypeEnum.values();
        for (BusinessTypeEnum item : values) {
            if (item.getValue().equals(value)) {
                return true;
            }
        }
        return false;
    }

}
